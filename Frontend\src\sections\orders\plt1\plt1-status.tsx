import { EnumConfigMap } from "src/layouts/components/app-data-grid/components/enum-tag/types";

export const PLT1_ORDER_STATUS = {
  Created: 'Created',
  Scanning: 'Scanning',
  Scanned: 'Scanned',
  Draft: 'Draft',
  Ordered: 'Ordered',
  Confirmed: 'Confirmed',
  ForwardedToTheCustomsOffice: 'ForwardedToTheCustomsOffice',
  Completed: 'Completed',
  MerchandisePositionGeneration: 'MerchandisePositionGeneration'
} as const;

export type PLT1OrderStatus = (typeof PLT1_ORDER_STATUS)[keyof typeof PLT1_ORDER_STATUS];

export const PLT1_ORDER_STATUS_CONFIG: EnumConfigMap = {
  [PLT1_ORDER_STATUS.Created]: {
    color: 'default',
    icon: 'eva:attach-outline',
    translationKey: 'created'
  },
  [PLT1_ORDER_STATUS.Scanning]: {
    color: 'warning',
    icon: 'eva:eye-outline',
    translationKey: 'scanning'
  },
  [PLT1_ORDER_STATUS.Scanned]: {
    color: 'info',
    icon: 'eva:checkmark-circle-2-outline',
    translationKey: 'scanned'
  },
  [PLT1_ORDER_STATUS.Draft]: {
    color: 'default',
    icon: 'eva:file-outline',
    translationKey: 'draft'
  },
  [PLT1_ORDER_STATUS.Ordered]: {
    color: 'primary',
    icon: 'eva:paper-plane-outline',
    translationKey: 'ordered'
  },
  [PLT1_ORDER_STATUS.Confirmed]: {
    color: 'success',
    icon: 'eva:checkmark-circle-outline',
    translationKey: 'confirmed'
  },
  [PLT1_ORDER_STATUS.ForwardedToTheCustomsOffice]: {
    color: 'secondary',
    icon: 'eva:arrow-forward-outline',
    translationKey: 'forwardedToTheCustomsOffice'
  },
  [PLT1_ORDER_STATUS.Completed]: {
    color: 'success',
    icon: 'eva:checkmark-circle-2-fill',
    translationKey: 'completed'
  },
    [PLT1_ORDER_STATUS.MerchandisePositionGeneration]: {
    color: 'error',
    icon: 'eva:eva:funnel-outline',
    translationKey: 'merchandisePositionGeneration'
  }
}