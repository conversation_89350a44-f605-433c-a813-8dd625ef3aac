'use client';

import { useFormContext, useFieldArray } from 'react-hook-form';
import { useState, useRef, useEffect } from 'react';

import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { useTranslate } from 'src/locales';
import PLT1DocumentTabBase from './plt1-document-tab-base';
import PLT1TransitForm, { PLT1TransitDocumentData } from '../forms/plt1-transit-form';
import { PLT1Order } from '../types/plt1-details.types';

// ----------------------------------------------------------------------

interface PLT1TransitTabProps {
  t1OrderId: string;
  order?: PLT1Order;
  readOnly?: boolean;
}

interface FormValues {
  transitDocuments: PLT1TransitDocumentData[];
}

export default function PLT1TransitTab({
  t1OrderId,
  order,
  readOnly = false,
}: PLT1TransitTabProps) {
  const { t } = useTranslate();
  const { control } = useFormContext<FormValues>();

  // Try to restore the expanded state from sessionStorage
  const [expandedDocument, setExpandedDocument] = useState<number | null>(() => {
    const saved = sessionStorage.getItem(`plt1-expanded-transit-${t1OrderId}`);
    return saved ? parseInt(saved, 10) : null;
  });

  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const deleteButtonRef = useRef<HTMLButtonElement>(null);

  const fieldArray = useFieldArray({
    control,
    name: 'transitDocuments',
  });

  // Save expanded state to sessionStorage whenever it changes
  useEffect(() => {
    if (expandedDocument !== null) {
      sessionStorage.setItem(`plt1-expanded-transit-${t1OrderId}`, expandedDocument.toString());
    } else {
      sessionStorage.removeItem(`plt1-expanded-transit-${t1OrderId}`);
    }
  }, [expandedDocument, t1OrderId]);

  const handleAddDocument = (): void => {
    const newDocument: PLT1TransitDocumentData = {
      id: null,
      mrnNumber: '',
      totalWeight: 0,
      weightUnit: 'kg',
      totalPackages: 0,
      validityEndDate: null,
      lrnNumber: '',
      previousDocument: '',
      storageLocationCode: '',
      transportDocument: '',
      attachedDocument: '',
      dispatchCountry: '',
      destinationCustomsOffice: '',
      transportMeansAtExit: '',
      shipper: {
        name: '',
        taxNumber: '',
        eoriNumber: '',
        address: {
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          postCode: '',
          country: '',
          countryCode: '',
        },
      },
      consignee: {
        name: '',
        taxNumber: '',
        eoriNumber: '',
        address: {
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          postCode: '',
          country: '',
          countryCode: '',
        },
      },
      items: [],
      t1OrderId,
    };

    fieldArray.append(newDocument);
    setExpandedDocument(fieldArray.fields.length);
  };

  const handleToggleExpand = (index: number): void => {
    setExpandedDocument(expandedDocument === index ? null : index);
  };

  const handleOpenConfirm = (index: number): void => {
    setDeleteIndex(index);
    setOpenConfirm(true);
  };

  const handleCloseConfirm = (): void => {
    setOpenConfirm(false);
    if (deleteIndex !== null) {
      deleteButtonRef.current?.focus();
    }
  };

  const handleDeleteDocument = (): void => {
    if (deleteIndex !== null) {
      fieldArray.remove(deleteIndex);
      if (expandedDocument === deleteIndex) {
        setExpandedDocument(null);
      }
    }
    handleCloseConfirm();
  };

  // Render preview of the transit document item when collapsed
  const renderPreview = (document: any, _index: number) => (
    <Stack
      direction="row"
      spacing={2}
      sx={{
        px: 3,
        pb: 2,
        display: 'flex',
        flexWrap: 'wrap',
        '& > *': { mr: 3, mb: 1 },
      }}
    >
      {document.totalPackages !== undefined &&
        document.totalPackages !== null &&
        document.totalPackages > 0 && (
          <Typography variant="body2" color="text.secondary">
            {t('plt1.details.documents.transit.preview.totalPackages')}: {document.totalPackages}
          </Typography>
        )}

      {document.totalWeight !== undefined &&
        document.totalWeight !== null &&
        document.totalWeight > 0 && (
          <Typography variant="body2" color="text.secondary">
            {t('plt1.details.documents.transit.preview.totalWeight')}: {document.totalWeight}{' '}
            {document.weightUnit || 'kg'}
          </Typography>
        )}

      {document.items && document.items.length > 0 && (
        <Typography variant="body2" color="text.secondary">
          {t('plt1.details.documents.transit.preview.items')}: {document.items.length}
        </Typography>
      )}
    </Stack>
  );

  // Render the form when expanded
  const renderForm = (index: number) => (
    <PLT1TransitForm formPath="transitDocuments" index={index} readOnly={readOnly} />
  );

  // Render the title of each item
  const getItemTitle = (document: any) => (
    <Stack direction="row" alignItems="center" spacing={1}>
      <Typography variant="subtitle1">
        {document.mrnNumber || t('plt1.details.documents.transit.preview.newDocument')}
      </Typography>
    </Stack>
  );

  return (
    <PLT1DocumentTabBase
      t1OrderId={t1OrderId}
      order={order}
      readOnly={readOnly}
      title={t('plt1.details.documents.transit.heading')}
      emptyTitle={t('plt1.details.documents.transit.noData')}
      emptyDescription={t('plt1.details.documents.transit.addYourFirst')}
      expandedIndex={expandedDocument}
      deleteIndex={deleteIndex}
      openConfirm={openConfirm}
      fieldArray={fieldArray}
      fieldArrayName="transitDocuments"
      onToggleExpand={handleToggleExpand}
      onOpenConfirm={handleOpenConfirm}
      onCloseConfirm={handleCloseConfirm}
      onDelete={handleDeleteDocument}
      onAdd={handleAddDocument}
      renderPreview={renderPreview}
      renderForm={renderForm}
      getItemTitle={getItemTitle}
    />
  );
}
