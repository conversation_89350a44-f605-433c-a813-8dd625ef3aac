import type { ButtonProps } from '@mui/material/Button';

import { useCallback } from 'react';
import { useAuth0 } from '@auth0/auth0-react';

import Button from '@mui/material/Button';

import { useRouter } from 'src/routes/hooks';
import { useTranslate } from 'src/locales';
import { endSession } from 'src/auth/context/jwt';

// ----------------------------------------------------------------------

type Props = ButtonProps & {
  onClose?: () => void;
};

export function SignOutButton({ onClose, sx, ...other }: Props) {
  const router = useRouter();

  const { logout: signOutAuth0 } = useAuth0();
  const { t } = useTranslate();

  const handleLogoutAuth0 = useCallback(async () => {
    try {
      await signOutAuth0();
      endSession();

      onClose?.();
      router.refresh();
    } catch (error) {
      console.error(error);
    }
  }, [onClose, router, signOutAuth0]);

  return (
    <Button
      fullWidth
      variant="soft"
      size="large"
      color="error"
      onClick={handleLogoutAuth0}
      sx={sx}
      {...other}
    >
      {t('components.signOutButton.label')}
    </Button>
  );
}
