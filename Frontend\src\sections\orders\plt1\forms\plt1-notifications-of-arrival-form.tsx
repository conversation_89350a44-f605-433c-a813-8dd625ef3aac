'use client';

import { useFormContext } from 'react-hook-form';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Divider from '@mui/material/Divider';
import CardHeader from '@mui/material/CardHeader';
import { Field } from 'src/components/hook-form';
import { useTranslate } from 'src/locales';
import { PartyAddressData } from 'src/components/party-address/party-address-form';

// ----------------------------------------------------------------------

export interface T1NotificationOfArrivalData {
  id?: string;
  identificationNumber: string;
  flightNumber: string;
  customsOrigin: string;
  shipper: PartyAddressData;
  description: string;
  storageLocationCode: string;
  dskTemporaryStorageNumber: string;
  remarks: string;
  referenceNumber: string;
  pieces: number;
  weight: number;
  chargedWeight: number;
  unitOfWeight: string;
  arrivalDate: Date | null;
  t1OrderId: string;
}

// ----------------------------------------------------------------------

interface T1NotificationOfArrivalFormProps {
  formPath: string;
  index?: number;
  readOnly?: boolean;
}

export default function PLT1NotificationOfArrivalForm({
  formPath,
  index,
  readOnly = false,
}: T1NotificationOfArrivalFormProps) {
  const { t } = useTranslate();

  // If index is provided, this form is part of a field array
  const fieldPrefix = index !== undefined ? `${formPath}[${index}]` : formPath;

  const renderShipmentInfo = () => (
    <Card sx={{ boxShadow: 'none' }}>
      <CardHeader
        title={t('plt1.details.documents.notificationOfArrival.form.shipmentInfoTitle')}
        sx={{ mb: 2 }}
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Stack direction="row" spacing={2}>
          <Field.Text
            name={`${fieldPrefix}.identificationNumber`}
            label={t('plt1.details.documents.notificationOfArrival.form.identificationNumber')}
            disabled={readOnly}
          />
          <Field.Text
            name={`${fieldPrefix}.flightNumber`}
            label={t('plt1.details.documents.notificationOfArrival.form.flightNumber')}
            disabled={readOnly}
          />
        </Stack>
        <Stack direction="row" spacing={2}>
          <Field.DatePicker
            name={`${fieldPrefix}.arrivalDate`}
            label={t('plt1.details.documents.notificationOfArrival.form.arrivalDate')}
            disabled={readOnly}
          />
          <Field.Text
            name={`${fieldPrefix}.customsOrigin`}
            label={t('plt1.details.documents.notificationOfArrival.form.customsOrigin')}
            disabled={readOnly}
          />
        </Stack>
      </Stack>
    </Card>
  );

  const renderWeightInfo = () => (
    <Card sx={{ mt: 3, boxShadow: 'none' }}>
      <CardHeader
        title={t('plt1.details.documents.notificationOfArrival.form.weightInfoTitle')}
        sx={{ mb: 2 }}
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Stack direction="row" spacing={2}>
          <Field.Text
            name={`${fieldPrefix}.pieces`}
            label={t('plt1.details.documents.notificationOfArrival.form.pieces')}
            type="number"
            disabled={readOnly}
          />
          <Field.Text
            name={`${fieldPrefix}.weight`}
            label={t('plt1.details.documents.notificationOfArrival.form.weight')}
            type="number"
            disabled={readOnly}
          />
        </Stack>

        <Stack direction="row" spacing={2}>
          <Field.Text
            name={`${fieldPrefix}.chargedWeight`}
            label={t('plt1.details.documents.notificationOfArrival.form.chargedWeight')}
            type="number"
            disabled={readOnly}
          />
          <Field.Text
            name={`${fieldPrefix}.unitOfWeight`}
            label={t('plt1.details.documents.notificationOfArrival.form.unitOfWeight')}
            disabled={readOnly}
          />
        </Stack>
      </Stack>
    </Card>
  );

  const renderCustomsInfo = () => (
    <Card sx={{ mt: 3, boxShadow: 'none' }}>
      <CardHeader
        title={t('plt1.details.documents.notificationOfArrival.form.customsInfoTitle')}
        sx={{ mb: 2 }}
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Stack direction="row" spacing={2}>
          <Field.Text
            name={`${fieldPrefix}.storageLocationCode`}
            label={t('plt1.details.documents.notificationOfArrival.form.storageLocationCode')}
            disabled={readOnly}
          />
        </Stack>

        <Stack direction="row" spacing={2}>
          <Field.Text
            name={`${fieldPrefix}.dskTemporaryStorageNumber`}
            label={t('plt1.details.documents.notificationOfArrival.form.dskTemporaryStorageNumber')}
            disabled={readOnly}
          />
          <Field.Text
            name={`${fieldPrefix}.referenceNumber`}
            label={t('plt1.details.documents.notificationOfArrival.form.referenceNumber')}
            disabled={readOnly}
          />
        </Stack>

        <Field.Text
          name={`${fieldPrefix}.remarks`}
          label={t('plt1.details.documents.notificationOfArrival.form.remarks')}
          multiline
          rows={3}
          disabled={readOnly}
        />
      </Stack>
    </Card>
  );

  return (
    <>
      <Stack spacing={3}>
        {renderShipmentInfo()}
        {renderWeightInfo()}
        {renderCustomsInfo()}
      </Stack>
    </>
  );
}
