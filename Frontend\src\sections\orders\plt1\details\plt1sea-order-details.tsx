// PLT1SeaOrderDetailsView.tsx
'use client';

import { useCallback, useState, useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { endpoints } from 'src/lib/axios';
import { useTranslate } from 'src/locales';
import { usePLT1OrderStatus } from '../hooks/use-plt1-status';
import { PLT1OrderDetailsBase } from './plt1-order-details-base';
import PLT1SeaDocumentsTabs from '../plt1sea-document-tabs';
import {
  PLT1CommercialInvoice,
  PLT1CustomsOffice,
  PLT1MerchandisePositions,
  PLT1NotificationOfArrival,
  PLT1PackingList,
  PLT1SeaBillOfLading,
  PLT1SeaWaybill,
  PLT1TransitDocument,
  PLT1VehicleRegistration
} from '../types/plt1-details.types';
import { usePLT1SeaOrderDetails } from '../hooks/use-plt1sea-details';
import { handlePLT1OrderSave, hasFormChanges } from '../utils/order-details-utils';

// ----------------------------------------------------------------------

interface PLT1SeaOrderDetailsViewProps {
  orderId: string;
  readOnly?: boolean;
}

// Define specific form values type for Sea orders
export interface SeaFormValues {
  billsOfLading: PLT1SeaBillOfLading[];
  seaWaybills: PLT1SeaWaybill[];
  commercialInvoices: PLT1CommercialInvoice[];
  packingLists: PLT1PackingList[];
  notificationsOfArrivals: PLT1NotificationOfArrival[];
  transitDocuments: PLT1TransitDocument[];
  merchandisePositions?: PLT1MerchandisePositions;
  customsOffice?: PLT1CustomsOffice;
  vehicleRegistration?: PLT1VehicleRegistration;
}

export function PLT1SeaOrderDetailsView({
  orderId,
  readOnly: propReadOnly = false,
}: PLT1SeaOrderDetailsViewProps) {
  const { t } = useTranslate();
  const [isSaving, setIsSaving] = useState(false);
  const [formChanged, setFormChanged] = useState(false);
  const [initialFormValues, setInitialFormValues] = useState<SeaFormValues | null>(null);

  // Create form methods with the specific SeaFormValues type
  const methods = useForm<SeaFormValues>({
    // Initialize default values in the derived component
    defaultValues: {
      billsOfLading: [],
      seaWaybills: [],
      commercialInvoices: [],
      packingLists: [],
      notificationsOfArrivals: [],
      transitDocuments: [],
      merchandisePositions: {
        id: null,
        positions: [],
        t1OrderId: orderId,
      },
      customsOffice: { customsOfficeCode: '' },
      vehicleRegistration: {
        vehicleRegistrationNumber: '',
        vehicleCountryCode: '',
        trailerRegistrationNumber: '',
        trailerCountryCode: '',
      },
    },
    mode: 'onChange',
  });

  const { formState, watch, reset } = methods;
  const { isValid } = formState;

  const {
    order,
    reload,
    error: orderError,
    isLoading: isOrderLoading,
  } = usePLT1SeaOrderDetails(orderId);

  // Update form values when order data is loaded
  useEffect(() => {
    if (order) {
      const formData: SeaFormValues = {
        billsOfLading: order.billsOfLading || [],
        seaWaybills: order.seaWaybills || [],
        commercialInvoices: order.commercialInvoices || [],
        packingLists: order.packingLists || [],
        notificationsOfArrivals: order.notificationsOfArrivals || [],
        transitDocuments: order.transitDocuments || [],
        customsOffice: order.customsOffice || { customsOfficeCode: '' },
        merchandisePositions: order.merchandisePositions || {
          id: null,
          positions: [],
          t1OrderId: orderId,
        },
        vehicleRegistration: order.vehicleRegistration || {
          vehicleRegistrationNumber: '',
          vehicleCountryCode: '',
          trailerRegistrationNumber: '',
          trailerCountryCode: '',
        },
      };
      reset(formData);
      setInitialFormValues(formData);
      setFormChanged(false);
    }
  }, [order, reset]);

  // Helper function to check if values are actually different
  const hasRealChanges = useCallback(() => {
    if (!initialFormValues) return false;
    const currentValues = methods.getValues();
    return hasFormChanges(currentValues, initialFormValues);
  }, [initialFormValues, methods]);

  // Watch for form changes
  useEffect(() => {
    const subscription = watch(() => {
      setFormChanged(hasRealChanges());
    });

    return () => subscription.unsubscribe();
  }, [watch, hasRealChanges]);

  // Handle status change callback
  const handleStatusChange = useCallback(() => {
    reload();
  }, [reload, t]);

  // Get order status
  const {
    status: orderStatus,
    orderNumber,
    error: statusError,
  } = usePLT1OrderStatus(orderId, 1000, handleStatusChange);

  // Save order data
  const handleSaveOrder = async (formData: SeaFormValues): Promise<void> => {
    await handlePLT1OrderSave(
      formData,
      {
        orderId,
        endpoint: endpoints.plt1.seaUpdate,
        idField: 'PLT1SeaId',
        t,
      },
      setIsSaving,
      setFormChanged,
      reload
    );
  };

  // Handle form cancel
  const handleCancel = () => {
    if (initialFormValues) {
      reset(initialFormValues);
      setFormChanged(false);
    }
  };

  return (
    <FormProvider {...methods}>
      <PLT1OrderDetailsBase<SeaFormValues>
        orderId={orderId}
        readOnly={propReadOnly}
        order={order}
        isLoading={isOrderLoading}
        error={orderError}
        orderStatus={orderStatus}
        orderNumber={orderNumber}
        statusError={statusError}
        onSaveOrder={handleSaveOrder}
        formChanged={formChanged}
        isSaving={isSaving}
        onCancel={handleCancel}
        isValid={isValid}
        documentTabs={
          <PLT1SeaDocumentsTabs
            t1OrderId={orderId}
            order={order}
            readOnly={propReadOnly || orderStatus === 'Scanning'}
          />
        }
      />
    </FormProvider>
  );
}