import { Metadata } from 'next';
import { CONFIG } from 'src/global-config';
import { getServerTranslations } from 'src/locales/server';

import { NotFoundView } from 'src/sections/error';

// ----------------------------------------------------------------------

export async function generateMetadata(): Promise<Metadata> {
  const { t } = await getServerTranslations();
  return {
    title: `${t('notFound.pageTitle')} - ${CONFIG.appName}`
  };
}

export default async function Page() {
  return <NotFoundView />;
}
