"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx":
/*!*******************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1PackingListForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/custom-dialog */ \"(app-pages-browser)/./src/components/custom-dialog/index.ts\");\n/* harmony import */ var src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/components/party-address/hooks/usePartyAddressForm */ \"(app-pages-browser)/./src/components/party-address/hooks/usePartyAddressForm.tsx\");\n/* harmony import */ var src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/components/party-address/party-address-dialog */ \"(app-pages-browser)/./src/components/party-address/party-address-dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst DEFAULT_PACKED_ITEM = {\n    id: '',\n    name: '',\n    hsCode: '',\n    modelNumber: '',\n    quantity: 0,\n    itemNetWeight: 0,\n    itemNetWeightUnit: 'KGM',\n    itemGrossWeight: 0,\n    hsCodeHints: '',\n    packingListPositionId: ''\n};\nconst DEFAULT_POSITION = {\n    id: '',\n    positionNumber: 1,\n    commercialInvoiceNumber: '',\n    packageNetWeight: 0,\n    packageNetWeightUnit: 'KGM',\n    packageGrossWeight: 0,\n    packageGrossWeightUnit: 'KGM',\n    packagesNetWeight: 0,\n    packagesNetWeightUnit: 'KGM',\n    packagesGrossWeight: 0,\n    packagesGrossWeightUnit: 'KGM',\n    packageUnit: 'CTNS',\n    packageSize: '',\n    packageVolume: 0,\n    packageVolumeUnit: 'CBM',\n    numberOfPackages: 0,\n    packedItems: [],\n    packingListId: ''\n};\nconst DEFAULT_TOTAL = {\n    id: '',\n    shipmentOrPackingId: '',\n    totalQuantity: 0,\n    totalPackagesUnit: 'CTNS',\n    totalNumberOfPackages: 0,\n    totalNumberOfPallets: 0,\n    totalNetWeight: 0,\n    totalNetWeightUnit: 'KGM',\n    totalGrossWeight: 0,\n    totalGrossWeightUnit: 'KGM',\n    totalVolume: 0,\n    totalVolumeMeasurementUnit: 'CBM',\n    packingListId: ''\n};\nfunction PLT1PackingListForm(param) {\n    let { formPath, index, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    const { control, getValues, watch, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext)();\n    const fieldPrefix = index !== undefined ? \"\".concat(formPath, \".\").concat(index) : formPath;\n    const itemsFieldName = \"\".concat(fieldPrefix, \".listSummary\");\n    const totalFieldName = \"\".concat(fieldPrefix, \".listTotal\");\n    // Use the party address form hook\n    const { openPartyDialog, currentPartyType, handleClosePartyDialog, handleUpdateParty } = (0,src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm)({\n        fieldPrefix\n    });\n    // State for item dialog\n    const [openItemDialog, setOpenItemDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteItemIndex, setDeleteItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize listTotal if it doesn't exist\n    const listTotal = watch(totalFieldName);\n    if (!listTotal) {\n        setValue(totalFieldName, DEFAULT_TOTAL);\n    }\n    // UseFieldArray hook to manage the items array\n    const { fields, append, remove, update } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray)({\n        control,\n        name: itemsFieldName\n    });\n    // Handle opening the item dialog for adding a new item\n    const handleAddItem = ()=>{\n        setCurrentItemIndex(null);\n        // Add a new item with default values\n        const newItem = {\n            ...DEFAULT_ITEM\n        };\n        append(newItem);\n        setCurrentItemIndex(fields.length); // Set to the new index\n        setOpenItemDialog(true);\n    };\n    // Handle opening the item dialog for editing an existing item\n    const handleEditItem = (index)=>{\n        setCurrentItemIndex(index);\n        setOpenItemDialog(true);\n    };\n    // Handle closing the item dialog\n    const handleCloseItemDialog = ()=>{\n        setOpenItemDialog(false);\n        // If we were adding a new item and user cancels, remove the empty item\n        if (currentItemIndex !== null && currentItemIndex === fields.length - 1) {\n            const item = getValues(\"\".concat(itemsFieldName, \".\").concat(currentItemIndex));\n            // Check if it's an empty item (all fields are default values)\n            const isEmpty = !item.name && !item.modelNumber && !item.purchaseOrderNumber && !item.commercialInvoiceNumber && item.quantity === 0 && item.packageNetWeight === 0 && item.packageGrossWeight === 0 && item.volume === 0;\n            if (isEmpty) {\n                remove(currentItemIndex);\n            }\n        }\n        setCurrentItemIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeleteItemIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deleteItemIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting an item\n    const handleDeleteItem = ()=>{\n        if (deleteItemIndex !== null) {\n            remove(deleteItemIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving an item (just close the dialog since form is already updated)\n    const handleSaveItem = ()=>{\n        handleCloseItemDialog();\n    };\n    // Render the item form in the dialog - using React Hook Form fields\n    const renderItemForm = ()=>{\n        if (currentItemIndex === null) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".name\"),\n                    label: t('plt1.details.documents.packingList.item.name'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".modelNumber\"),\n                    label: t('plt1.details.documents.packingList.item.modelNumber'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".purchaseOrderNumber\"),\n                            label: t('plt1.details.documents.packingList.item.purchaseOrderNumber'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".commercialInvoiceNumber\"),\n                            label: t('plt1.details.documents.packingList.item.commercialInvoiceNumber'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageNetWeight\"),\n                            label: t('plt1.details.documents.packingList.item.packageNetWeight'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageNetWeightUnit\"),\n                            label: t('plt1.details.documents.packingList.item.packageNetWeightUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageGrossWeight\"),\n                            label: t('plt1.details.documents.packingList.item.packageGrossWeight'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageGrossWeightUnit\"),\n                            label: t('plt1.details.documents.packingList.item.packageGrossWeightUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".quantity\"),\n                            label: t('plt1.details.documents.packingList.item.quantity'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".volume\"),\n                            label: t('plt1.details.documents.packingList.item.volume'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 243,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the item table\n    const renderItemsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            component: _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"packing list items table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.name')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.modelNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.quantity')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.packageNetWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.packageGrossWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                colSpan: 6,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.packingList.item.noItems')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this) : fields.map((field, index)=>{\n                            const item = getValues(\"\".concat(itemsFieldName, \".\").concat(index)) || {};\n                            var _item_quantity, _item_packageNetWeight, _item_packageGrossWeight;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: item.name || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: item.modelNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: (_item_quantity = item.quantity) !== null && _item_quantity !== void 0 ? _item_quantity : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: [\n                                            (_item_packageNetWeight = item.packageNetWeight) !== null && _item_packageNetWeight !== void 0 ? _item_packageNetWeight : '-',\n                                            \" \",\n                                            item.packageNetWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: [\n                                            (_item_packageGrossWeight = item.packageGrossWeight) !== null && _item_packageGrossWeight !== void 0 ? _item_packageGrossWeight : '-',\n                                            \" \",\n                                            item.packageGrossWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditItem(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deleteItemIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 318,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 317,\n            columnNumber: 5\n        }, this);\n    const renderPartyDialog = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            open: openPartyDialog,\n            onClose: handleClosePartyDialog,\n            onSave: handleUpdateParty,\n            formPath: fieldPrefix,\n            currentPartyType: currentPartyType,\n            readOnly: readOnly,\n            titlePrefix: \"plt1.details.documents.packingList.partyAddress\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 391,\n            columnNumber: 5\n        }, this);\n    // Handle change for totals fields\n    const handleTotalChange = (field, value)=>{\n        setValue(\"\".concat(totalFieldName, \".\").concat(field), value);\n    };\n    // Render the totals section\n    const renderTotalsSection = ()=>{\n        const total = watch(totalFieldName) || DEFAULT_TOTAL;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n            sx: {\n                boxShadow: 'none',\n                mt: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    title: t('plt1.details.documents.packingList.form.totalsTitle')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    sx: {\n                        borderStyle: 'dashed'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        spacing: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalQuantity\"),\n                                        label: t('plt1.details.documents.packingList.total.totalQuantity'),\n                                        type: \"number\",\n                                        value: total.totalQuantity,\n                                        onChange: (e)=>handleTotalChange('totalQuantity', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPallets\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPallets'),\n                                        type: \"number\",\n                                        value: total.totalNumberOfPallets,\n                                        onChange: (e)=>handleTotalChange('totalNumberOfPallets', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPackages\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPackages'),\n                                        type: \"number\",\n                                        value: total.totalNumberOfPackages,\n                                        onChange: (e)=>handleTotalChange('totalNumberOfPackages', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalPackagesUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalPackagesUnit'),\n                                        value: total.totalPackagesUnit,\n                                        onChange: (e)=>handleTotalChange('totalPackagesUnit', e.target.value),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeight'),\n                                        type: \"number\",\n                                        value: total.totalNetWeight,\n                                        onChange: (e)=>handleTotalChange('totalNetWeight', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeightUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeightUnit'),\n                                        value: total.totalNetWeightUnit,\n                                        onChange: (e)=>handleTotalChange('totalNetWeightUnit', e.target.value || 'kg'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeight'),\n                                        type: \"number\",\n                                        value: total.totalGrossWeight,\n                                        onChange: (e)=>handleTotalChange('totalGrossWeight', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeightUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeightUnit'),\n                                        value: total.totalGrossWeightUnit,\n                                        onChange: (e)=>handleTotalChange('totalGrossWeightUnit', e.target.value || 'kg'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolume\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolume'),\n                                        type: \"number\",\n                                        value: total.totalVolume,\n                                        onChange: (e)=>handleTotalChange('totalVolume', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolumeMeasurementUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolumeMeasurementUnit'),\n                                        value: total.totalVolumeMeasurementUnit,\n                                        onChange: (e)=>handleTotalChange('totalVolumeMeasurementUnit', e.target.value),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 410,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        spacing: 3,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        title: t('plt1.details.documents.packingList.form.itemsTitle'),\n                        action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            variant: \"contained\",\n                            size: \"small\",\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                icon: \"eva:plus-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: handleAddItem,\n                            sx: {\n                                mb: 2\n                            },\n                            disabled: readOnly,\n                            children: t('common.addItem')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: [\n                            renderItemsTable(),\n                            fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                sx: {\n                                    mt: 2,\n                                    display: 'flex',\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    variant: \"soft\",\n                                    color: \"primary\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                        icon: \"eva:plus-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    onClick: handleAddItem,\n                                    disabled: readOnly,\n                                    children: t('common.addItem')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 520,\n                columnNumber: 7\n            }, this),\n            renderTotalsSection(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                open: openItemDialog,\n                onClose: handleCloseItemDialog,\n                fullWidth: true,\n                maxWidth: \"md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        children: currentItemIndex === null ? t('plt1.details.documents.packingList.item.addNew') : t('plt1.details.documents.packingList.item.edit')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            sx: {\n                                pt: 3\n                            },\n                            children: renderItemForm()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                onClick: handleCloseItemDialog,\n                                color: \"inherit\",\n                                children: t('common.cancel')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 570,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                onClick: handleSaveItem,\n                                variant: \"contained\",\n                                children: t('common.save')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 569,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 560,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__.ConfirmDialog, {\n                open: openDeleteConfirm,\n                onClose: handleCloseDeleteConfirm,\n                title: t('plt1.details.documents.packingList.item.confirmDeleteDialog.title'),\n                content: t('plt1.details.documents.packingList.item.confirmDeleteDialog.content'),\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                    variant: \"contained\",\n                    color: \"error\",\n                    onClick: handleDeleteItem,\n                    children: t('common.delete')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 586,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 580,\n                columnNumber: 7\n            }, this),\n            renderPartyDialog()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n        lineNumber: 518,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1PackingListForm, \"eC5q2XofSeE2jFgvJlQweZHYME0=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext,\n        src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray\n    ];\n});\n_c = PLT1PackingListForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1PackingListForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZWN0aW9ucy9vcmRlcnMvcGx0MS9mb3Jtcy9wbHQxLXBhY2tpbmctbGlzdC1mb3JtLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBeUM7QUFDdUI7QUFzQnpDO0FBQ29CO0FBQ007QUFDQTtBQUNZO0FBQ2dDO0FBQ1Y7QUEyRG5GLE1BQU04QixzQkFBc0I7SUFDMUJDLElBQUk7SUFDSkMsTUFBTTtJQUNOQyxRQUFRO0lBQ1JDLGFBQWE7SUFDYkMsVUFBVTtJQUNWQyxlQUFlO0lBQ2ZDLG1CQUFtQjtJQUNuQkMsaUJBQWlCO0lBQ2pCQyxhQUFhO0lBQ2JDLHVCQUF1QjtBQUN6QjtBQUVBLE1BQU1DLG1CQUFtQjtJQUN2QlYsSUFBSTtJQUNKVyxnQkFBZ0I7SUFDaEJDLHlCQUF5QjtJQUN6QkMsa0JBQWtCO0lBQ2xCQyxzQkFBc0I7SUFDdEJDLG9CQUFvQjtJQUNwQkMsd0JBQXdCO0lBQ3hCQyxtQkFBbUI7SUFDbkJDLHVCQUF1QjtJQUN2QkMscUJBQXFCO0lBQ3JCQyx5QkFBeUI7SUFDekJDLGFBQWE7SUFDYkMsYUFBYTtJQUNiQyxlQUFlO0lBQ2ZDLG1CQUFtQjtJQUNuQkMsa0JBQWtCO0lBQ2xCQyxhQUFhLEVBQUU7SUFDZkMsZUFBZTtBQUNqQjtBQUVBLE1BQU1DLGdCQUFnQjtJQUNwQjVCLElBQUk7SUFDSjZCLHFCQUFxQjtJQUNyQkMsZUFBZTtJQUNmQyxtQkFBbUI7SUFDbkJDLHVCQUF1QjtJQUN2QkMsc0JBQXNCO0lBQ3RCQyxnQkFBZ0I7SUFDaEJDLG9CQUFvQjtJQUNwQkMsa0JBQWtCO0lBQ2xCQyxzQkFBc0I7SUFDdEJDLGFBQWE7SUFDYkMsNEJBQTRCO0lBQzVCWixlQUFlO0FBQ2pCO0FBTWUsU0FBU2Esb0JBQW9CLEtBQStEO1FBQS9ELEVBQUVDLFFBQVEsRUFBRUMsS0FBSyxFQUFFQyxXQUFXLEtBQUssRUFBNEIsR0FBL0Q7O0lBQzFDLE1BQU0sRUFBRUMsQ0FBQyxFQUFFLEdBQUduRCx5REFBWUE7SUFDMUIsTUFBTSxFQUFFb0QsT0FBTyxFQUFFQyxTQUFTLEVBQUVDLEtBQUssRUFBRUMsUUFBUSxFQUFFLEdBQUc3RSwrREFBY0E7SUFFOUQsTUFBTThFLGNBQWNQLFVBQVVRLFlBQVksR0FBZVIsT0FBWkQsVUFBUyxLQUFTLE9BQU5DLFNBQVVEO0lBQ25FLE1BQU1VLGlCQUFpQixHQUFlLE9BQVpGLGFBQVk7SUFDdEMsTUFBTUcsaUJBQWlCLEdBQWUsT0FBWkgsYUFBWTtJQUV0QyxrQ0FBa0M7SUFDbEMsTUFBTSxFQUNKSSxlQUFlLEVBQ2ZDLGdCQUFnQixFQUNoQkMsc0JBQXNCLEVBQ3RCQyxpQkFBaUIsRUFDbEIsR0FBRzNELDJHQUFtQkEsQ0FBQztRQUFFb0Q7SUFBWTtJQUV0Qyx3QkFBd0I7SUFDeEIsTUFBTSxDQUFDUSxnQkFBZ0JDLGtCQUFrQixHQUFHekYsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDMEYsa0JBQWtCQyxvQkFBb0IsR0FBRzNGLCtDQUFRQSxDQUFnQjtJQUN4RSxNQUFNLENBQUM0RixpQkFBaUJDLG1CQUFtQixHQUFHN0YsK0NBQVFBLENBQWdCO0lBQ3RFLE1BQU0sQ0FBQzhGLG1CQUFtQkMscUJBQXFCLEdBQUcvRiwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNZ0csa0JBQWtCL0YsNkNBQU1BLENBQW9CO0lBRWxELDJDQUEyQztJQUMzQyxNQUFNZ0csWUFBWW5CLE1BQU1LO0lBQ3hCLElBQUksQ0FBQ2MsV0FBVztRQUNkbEIsU0FBU0ksZ0JBQWdCeEI7SUFDM0I7SUFFQSwrQ0FBK0M7SUFDL0MsTUFBTSxFQUFFdUMsTUFBTSxFQUFFQyxNQUFNLEVBQUVDLE1BQU0sRUFBRUMsTUFBTSxFQUFFLEdBQUdsRyw4REFBYUEsQ0FBQztRQUN2RHlFO1FBQ0E1QyxNQUFNa0Q7SUFDUjtJQUVBLHVEQUF1RDtJQUN2RCxNQUFNb0IsZ0JBQWdCO1FBQ3BCWCxvQkFBb0I7UUFDcEIscUNBQXFDO1FBQ3JDLE1BQU1ZLFVBQVU7WUFBRSxHQUFHQyxZQUFZO1FBQUM7UUFDbENMLE9BQU9JO1FBQ1BaLG9CQUFvQk8sT0FBT08sTUFBTSxHQUFHLHVCQUF1QjtRQUMzRGhCLGtCQUFrQjtJQUNwQjtJQUVBLDhEQUE4RDtJQUM5RCxNQUFNaUIsaUJBQWlCLENBQUNqQztRQUN0QmtCLG9CQUFvQmxCO1FBQ3BCZ0Isa0JBQWtCO0lBQ3BCO0lBRUEsaUNBQWlDO0lBQ2pDLE1BQU1rQix3QkFBd0I7UUFDNUJsQixrQkFBa0I7UUFDbEIsdUVBQXVFO1FBQ3ZFLElBQUlDLHFCQUFxQixRQUFRQSxxQkFBcUJRLE9BQU9PLE1BQU0sR0FBRyxHQUFHO1lBQ3ZFLE1BQU1HLE9BQU8vQixVQUFVLEdBQXFCYSxPQUFsQlIsZ0JBQWUsS0FBb0IsT0FBakJRO1lBQzVDLDhEQUE4RDtZQUM5RCxNQUFNbUIsVUFBVSxDQUFDRCxLQUFLNUUsSUFBSSxJQUFJLENBQUM0RSxLQUFLMUUsV0FBVyxJQUFJLENBQUMwRSxLQUFLRSxtQkFBbUIsSUFDN0QsQ0FBQ0YsS0FBS2pFLHVCQUF1QixJQUFJaUUsS0FBS3pFLFFBQVEsS0FBSyxLQUNuRHlFLEtBQUtoRSxnQkFBZ0IsS0FBSyxLQUFLZ0UsS0FBSzlELGtCQUFrQixLQUFLLEtBQUs4RCxLQUFLRyxNQUFNLEtBQUs7WUFDL0YsSUFBSUYsU0FBUztnQkFDWFQsT0FBT1Y7WUFDVDtRQUNGO1FBQ0FDLG9CQUFvQjtJQUN0QjtJQUVBLGdEQUFnRDtJQUNoRCxNQUFNcUIsMEJBQTBCLENBQUN2QztRQUMvQm9CLG1CQUFtQnBCO1FBQ25Cc0IscUJBQXFCO0lBQ3ZCO0lBRUEsZ0RBQWdEO0lBQ2hELE1BQU1rQiwyQkFBMkI7UUFDL0JsQixxQkFBcUI7UUFDckIsSUFBSUgsb0JBQW9CLE1BQU07Z0JBQzVCSTthQUFBQSwyQkFBQUEsZ0JBQWdCa0IsT0FBTyxjQUF2QmxCLCtDQUFBQSx5QkFBeUJtQixLQUFLO1FBQ2hDO0lBQ0Y7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTUMsbUJBQW1CO1FBQ3ZCLElBQUl4QixvQkFBb0IsTUFBTTtZQUM1QlEsT0FBT1I7UUFDVDtRQUNBcUI7SUFDRjtJQUVBLDhFQUE4RTtJQUM5RSxNQUFNSSxpQkFBaUI7UUFDckJWO0lBQ0Y7SUFFQSxvRUFBb0U7SUFDcEUsTUFBTVcsaUJBQWlCO1FBQ3JCLElBQUk1QixxQkFBcUIsTUFBTSxPQUFPO1FBRXRDLHFCQUNFLDhEQUFDdEYsbVFBQUtBO1lBQUNtSCxTQUFTOzs4QkFDZCw4REFBQzlGLDJEQUFLQSxDQUFDK0YsSUFBSTtvQkFDVHhGLE1BQU0sR0FBcUIwRCxPQUFsQlIsZ0JBQWUsS0FBb0IsT0FBakJRLGtCQUFpQjtvQkFDNUMrQixPQUFPOUMsRUFBRTtvQkFDVCtDLFVBQVVoRDs7Ozs7OzhCQUdaLDhEQUFDakQsMkRBQUtBLENBQUMrRixJQUFJO29CQUNUeEYsTUFBTSxHQUFxQjBELE9BQWxCUixnQkFBZSxLQUFvQixPQUFqQlEsa0JBQWlCO29CQUM1QytCLE9BQU85QyxFQUFFO29CQUNUK0MsVUFBVWhEOzs7Ozs7OEJBR1osOERBQUN0RSxtUUFBS0E7b0JBQUN1SCxXQUFVO29CQUFNSixTQUFTOztzQ0FDOUIsOERBQUM5RiwyREFBS0EsQ0FBQytGLElBQUk7NEJBQ1R4RixNQUFNLEdBQXFCMEQsT0FBbEJSLGdCQUFlLEtBQW9CLE9BQWpCUSxrQkFBaUI7NEJBQzVDK0IsT0FBTzlDLEVBQUU7NEJBQ1QrQyxVQUFVaEQ7Ozs7OztzQ0FFWiw4REFBQ2pELDJEQUFLQSxDQUFDK0YsSUFBSTs0QkFDVHhGLE1BQU0sR0FBcUIwRCxPQUFsQlIsZ0JBQWUsS0FBb0IsT0FBakJRLGtCQUFpQjs0QkFDNUMrQixPQUFPOUMsRUFBRTs0QkFDVCtDLFVBQVVoRDs7Ozs7Ozs7Ozs7OzhCQUlkLDhEQUFDdEUsbVFBQUtBO29CQUFDdUgsV0FBVTtvQkFBTUosU0FBUzs7c0NBQzlCLDhEQUFDOUYsMkRBQUtBLENBQUMrRixJQUFJOzRCQUNUeEYsTUFBTSxHQUFxQjBELE9BQWxCUixnQkFBZSxLQUFvQixPQUFqQlEsa0JBQWlCOzRCQUM1QytCLE9BQU85QyxFQUFFOzRCQUNUaUQsTUFBSzs0QkFDTEYsVUFBVWhEOzs7Ozs7c0NBRVosOERBQUNqRCwyREFBS0EsQ0FBQytGLElBQUk7NEJBQ1R4RixNQUFNLEdBQXFCMEQsT0FBbEJSLGdCQUFlLEtBQW9CLE9BQWpCUSxrQkFBaUI7NEJBQzVDK0IsT0FBTzlDLEVBQUU7NEJBQ1QrQyxVQUFVaEQ7Ozs7Ozs7Ozs7Ozs4QkFJZCw4REFBQ3RFLG1RQUFLQTtvQkFBQ3VILFdBQVU7b0JBQU1KLFNBQVM7O3NDQUM5Qiw4REFBQzlGLDJEQUFLQSxDQUFDK0YsSUFBSTs0QkFDVHhGLE1BQU0sR0FBcUIwRCxPQUFsQlIsZ0JBQWUsS0FBb0IsT0FBakJRLGtCQUFpQjs0QkFDNUMrQixPQUFPOUMsRUFBRTs0QkFDVGlELE1BQUs7NEJBQ0xGLFVBQVVoRDs7Ozs7O3NDQUVaLDhEQUFDakQsMkRBQUtBLENBQUMrRixJQUFJOzRCQUNUeEYsTUFBTSxHQUFxQjBELE9BQWxCUixnQkFBZSxLQUFvQixPQUFqQlEsa0JBQWlCOzRCQUM1QytCLE9BQU85QyxFQUFFOzRCQUNUK0MsVUFBVWhEOzs7Ozs7Ozs7Ozs7OEJBSWQsOERBQUN0RSxtUUFBS0E7b0JBQUN1SCxXQUFVO29CQUFNSixTQUFTOztzQ0FDOUIsOERBQUM5RiwyREFBS0EsQ0FBQytGLElBQUk7NEJBQ1R4RixNQUFNLEdBQXFCMEQsT0FBbEJSLGdCQUFlLEtBQW9CLE9BQWpCUSxrQkFBaUI7NEJBQzVDK0IsT0FBTzlDLEVBQUU7NEJBQ1RpRCxNQUFLOzRCQUNMRixVQUFVaEQ7Ozs7OztzQ0FFWiw4REFBQ2pELDJEQUFLQSxDQUFDK0YsSUFBSTs0QkFDVHhGLE1BQU0sR0FBcUIwRCxPQUFsQlIsZ0JBQWUsS0FBb0IsT0FBakJRLGtCQUFpQjs0QkFDNUMrQixPQUFPOUMsRUFBRTs0QkFDVGlELE1BQUs7NEJBQ0xGLFVBQVVoRDs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS3BCO0lBRUEsd0JBQXdCO0lBQ3hCLE1BQU1tRCxtQkFBbUIsa0JBQ3ZCLDhEQUFDL0csb1FBQWNBO1lBQUNnSCxXQUFXN0csb1FBQUtBO3NCQUM5Qiw0RUFBQ04sb1FBQUtBO2dCQUFDb0gsSUFBSTtvQkFBRUMsVUFBVTtnQkFBSTtnQkFBR0MsY0FBVzs7a0NBQ3ZDLDhEQUFDbEgsb1FBQVNBO2tDQUNSLDRFQUFDQyxvUUFBUUE7OzhDQUNQLDhEQUFDSCxvUUFBU0E7OENBQUU4RCxFQUFFOzs7Ozs7OENBQ2QsOERBQUM5RCxvUUFBU0E7OENBQUU4RCxFQUFFOzs7Ozs7OENBQ2QsOERBQUM5RCxvUUFBU0E7OENBQUU4RCxFQUFFOzs7Ozs7OENBQ2QsOERBQUM5RCxvUUFBU0E7OENBQ1A4RCxFQUFFOzs7Ozs7OENBRUwsOERBQUM5RCxvUUFBU0E7OENBQ1A4RCxFQUFFOzs7Ozs7OENBRUwsOERBQUM5RCxvUUFBU0E7b0NBQUNxSCxPQUFNOzhDQUFTdkQsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBR2hDLDhEQUFDL0Qsb1FBQVNBO2tDQUNQc0YsT0FBT08sTUFBTSxLQUFLLGtCQUNqQiw4REFBQ3pGLG9RQUFRQTtzQ0FDUCw0RUFBQ0gsb1FBQVNBO2dDQUFDc0gsU0FBUztnQ0FBR0QsT0FBTTswQ0FDM0IsNEVBQUMxSCxvUUFBVUE7b0NBQUM0SCxTQUFRO29DQUFRQyxPQUFNOzhDQUMvQjFELEVBQUU7Ozs7Ozs7Ozs7Ozs7OzttQ0FLVHVCLE9BQU9vQyxHQUFHLENBQUMsQ0FBQ0MsT0FBTzlEOzRCQUNqQixNQUFNbUMsT0FBTy9CLFVBQVUsR0FBcUJKLE9BQWxCUyxnQkFBZSxLQUFTLE9BQU5ULFdBQVksQ0FBQztnQ0FLekNtQyxnQkFFVEEsd0JBR0FBOzRCQVRQLHFCQUNFLDhEQUFDNUYsb1FBQVFBOztrREFDUCw4REFBQ0gsb1FBQVNBO2tEQUFFK0YsS0FBSzVFLElBQUksSUFBSTs7Ozs7O2tEQUN6Qiw4REFBQ25CLG9RQUFTQTtrREFBRStGLEtBQUsxRSxXQUFXLElBQUk7Ozs7OztrREFDaEMsOERBQUNyQixvUUFBU0E7a0RBQUUrRixDQUFBQSxpQkFBQUEsS0FBS3pFLFFBQVEsY0FBYnlFLDRCQUFBQSxpQkFBaUI7Ozs7OztrREFDN0IsOERBQUMvRixvUUFBU0E7OzRDQUNQK0YsQ0FBQUEseUJBQUFBLEtBQUtoRSxnQkFBZ0IsY0FBckJnRSxvQ0FBQUEseUJBQXlCOzRDQUFJOzRDQUFFQSxLQUFLL0Qsb0JBQW9CLElBQUk7Ozs7Ozs7a0RBRS9ELDhEQUFDaEMsb1FBQVNBOzs0Q0FDUCtGLENBQUFBLDJCQUFBQSxLQUFLOUQsa0JBQWtCLGNBQXZCOEQsc0NBQUFBLDJCQUEyQjs0Q0FBSTs0Q0FBRUEsS0FBSzdELHNCQUFzQixJQUFJOzs7Ozs7O2tEQUVuRSw4REFBQ2xDLG9RQUFTQTt3Q0FBQ3FILE9BQU07a0RBQ2YsNEVBQUM5SCxtUUFBS0E7NENBQUN1SCxXQUFVOzRDQUFNSixTQUFTOzRDQUFHaUIsZ0JBQWU7OzhEQUNoRCw4REFBQ2pILG9RQUFPQTtvREFBQ2tILE9BQU85RCxFQUFFOzhEQUNoQiw0RUFBQ3JELG9RQUFVQTt3REFDVG9ILE1BQUs7d0RBQ0xMLE9BQU07d0RBQ05NLFNBQVMsSUFBTWpDLGVBQWVqQzt3REFDOUJpRCxVQUFVaEQ7a0VBRVYsNEVBQUNoRCwyREFBT0E7NERBQUNrSCxNQUFLOzs7Ozs7Ozs7Ozs7Ozs7OzhEQUdsQiw4REFBQ3JILG9RQUFPQTtvREFBQ2tILE9BQU85RCxFQUFFOzhEQUNoQiw0RUFBQ3JELG9RQUFVQTt3REFDVG9ILE1BQUs7d0RBQ0xMLE9BQU07d0RBQ05NLFNBQVMsSUFBTTNCLHdCQUF3QnZDO3dEQUN2Q29FLEtBQUtqRCxvQkFBb0JuQixRQUFRdUIsa0JBQWtCO3dEQUNuRDBCLFVBQVVoRDtrRUFFViw0RUFBQ2hELDJEQUFPQTs0REFBQ2tILE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7K0JBOUJUTCxNQUFNeEcsRUFBRTs7Ozs7d0JBcUMzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFPVixNQUFNK0csb0JBQW9CLGtCQUN4Qiw4REFBQ2pILHlGQUFrQkE7WUFDakJrSCxNQUFNM0Q7WUFDTjRELFNBQVMxRDtZQUNUMkQsUUFBUTFEO1lBQ1JmLFVBQVVRO1lBQ1ZLLGtCQUFrQkE7WUFDbEJYLFVBQVVBO1lBQ1Z3RSxhQUFZOzs7Ozs7SUFHaEIsa0NBQWtDO0lBQ2xDLE1BQU1DLG9CQUFvQixDQUFDWixPQUFlYTtRQUN4Q3JFLFNBQVMsR0FBcUJ3RCxPQUFsQnBELGdCQUFlLEtBQVMsT0FBTm9ELFFBQVNhO0lBQ3pDO0lBQ0EsNEJBQTRCO0lBQzVCLE1BQU1DLHNCQUFzQjtRQUMxQixNQUFNQyxRQUFReEUsTUFBTUssbUJBQW1CeEI7UUFFdkMscUJBQ0UsOERBQUN0RCxvUUFBSUE7WUFBQzBILElBQUk7Z0JBQUV3QixXQUFXO2dCQUFRQyxJQUFJO1lBQUU7OzhCQUNuQyw4REFBQ2xKLG9RQUFVQTtvQkFBQ21JLE9BQU85RCxFQUFFOzs7Ozs7OEJBQ3JCLDhEQUFDcEUsb1FBQU9BO29CQUFDd0gsSUFBSTt3QkFBRTBCLGFBQWE7b0JBQVM7Ozs7Ozs4QkFDckMsOERBQUMvSSxvUUFBR0E7b0JBQUNxSCxJQUFJO3dCQUFFMkIsR0FBRztvQkFBRTs4QkFDZCw0RUFBQ3RKLG1RQUFLQTt3QkFBQ21ILFNBQVM7OzBDQUNkLDhEQUFDbkgsbVFBQUtBO2dDQUFDdUgsV0FBVTtnQ0FBTUosU0FBUzs7a0RBQzlCLDhEQUFDOUYsMkRBQUtBLENBQUMrRixJQUFJO3dDQUNUeEYsTUFBTSxHQUFrQixPQUFmbUQsZ0JBQWU7d0NBQ3hCc0MsT0FBTzlDLEVBQUU7d0NBQ1RpRCxNQUFLO3dDQUNMd0IsT0FBT0UsTUFBTXpGLGFBQWE7d0NBQzFCOEYsVUFBVSxDQUFDQyxJQUNUVCxrQkFBa0IsaUJBQWlCVSxXQUFXRCxFQUFFRSxNQUFNLENBQUNWLEtBQUssS0FBSzt3Q0FFbkUxQixVQUFVaEQ7Ozs7OztrREFFWiw4REFBQ2pELDJEQUFLQSxDQUFDK0YsSUFBSTt3Q0FDVHhGLE1BQU0sR0FBa0IsT0FBZm1ELGdCQUFlO3dDQUN4QnNDLE9BQU85QyxFQUFFO3dDQUNUaUQsTUFBSzt3Q0FDTHdCLE9BQU9FLE1BQU10RixvQkFBb0I7d0NBQ2pDMkYsVUFBVSxDQUFDQyxJQUNUVCxrQkFBa0Isd0JBQXdCVSxXQUFXRCxFQUFFRSxNQUFNLENBQUNWLEtBQUssS0FBSzt3Q0FFMUUxQixVQUFVaEQ7Ozs7Ozs7Ozs7OzswQ0FHZCw4REFBQ3RFLG1RQUFLQTtnQ0FBQ3VILFdBQVU7Z0NBQU1KLFNBQVM7O2tEQUM5Qiw4REFBQzlGLDJEQUFLQSxDQUFDK0YsSUFBSTt3Q0FDVHhGLE1BQU0sR0FBa0IsT0FBZm1ELGdCQUFlO3dDQUN4QnNDLE9BQU85QyxFQUFFO3dDQUNUaUQsTUFBSzt3Q0FDTHdCLE9BQU9FLE1BQU12RixxQkFBcUI7d0NBQ2xDNEYsVUFBVSxDQUFDQyxJQUNUVCxrQkFBa0IseUJBQXlCVSxXQUFXRCxFQUFFRSxNQUFNLENBQUNWLEtBQUssS0FBSzt3Q0FFM0UxQixVQUFVaEQ7Ozs7OztrREFFWiw4REFBQ2pELDJEQUFLQSxDQUFDK0YsSUFBSTt3Q0FDVHhGLE1BQU0sR0FBa0IsT0FBZm1ELGdCQUFlO3dDQUN4QnNDLE9BQU85QyxFQUFFO3dDQUNUeUUsT0FBT0UsTUFBTXhGLGlCQUFpQjt3Q0FDOUI2RixVQUFVLENBQUNDLElBQU1ULGtCQUFrQixxQkFBcUJTLEVBQUVFLE1BQU0sQ0FBQ1YsS0FBSzt3Q0FDdEUxQixVQUFVaEQ7Ozs7Ozs7Ozs7OzswQ0FHZCw4REFBQ3RFLG1RQUFLQTtnQ0FBQ3VILFdBQVU7Z0NBQU1KLFNBQVM7O2tEQUM5Qiw4REFBQzlGLDJEQUFLQSxDQUFDK0YsSUFBSTt3Q0FDVHhGLE1BQU0sR0FBa0IsT0FBZm1ELGdCQUFlO3dDQUN4QnNDLE9BQU85QyxFQUFFO3dDQUNUaUQsTUFBSzt3Q0FDTHdCLE9BQU9FLE1BQU1yRixjQUFjO3dDQUMzQjBGLFVBQVUsQ0FBQ0MsSUFDVFQsa0JBQWtCLGtCQUFrQlUsV0FBV0QsRUFBRUUsTUFBTSxDQUFDVixLQUFLLEtBQUs7d0NBRXBFMUIsVUFBVWhEOzs7Ozs7a0RBRVosOERBQUNqRCwyREFBS0EsQ0FBQytGLElBQUk7d0NBQ1R4RixNQUFNLEdBQWtCLE9BQWZtRCxnQkFBZTt3Q0FDeEJzQyxPQUFPOUMsRUFBRTt3Q0FDVHlFLE9BQU9FLE1BQU1wRixrQkFBa0I7d0NBQy9CeUYsVUFBVSxDQUFDQyxJQUFNVCxrQkFBa0Isc0JBQXNCUyxFQUFFRSxNQUFNLENBQUNWLEtBQUssSUFBSTt3Q0FDM0UxQixVQUFVaEQ7Ozs7Ozs7Ozs7OzswQ0FHZCw4REFBQ3RFLG1RQUFLQTtnQ0FBQ3VILFdBQVU7Z0NBQU1KLFNBQVM7O2tEQUM5Qiw4REFBQzlGLDJEQUFLQSxDQUFDK0YsSUFBSTt3Q0FDVHhGLE1BQU0sR0FBa0IsT0FBZm1ELGdCQUFlO3dDQUN4QnNDLE9BQU85QyxFQUFFO3dDQUNUaUQsTUFBSzt3Q0FDTHdCLE9BQU9FLE1BQU1uRixnQkFBZ0I7d0NBQzdCd0YsVUFBVSxDQUFDQyxJQUNUVCxrQkFBa0Isb0JBQW9CVSxXQUFXRCxFQUFFRSxNQUFNLENBQUNWLEtBQUssS0FBSzt3Q0FFdEUxQixVQUFVaEQ7Ozs7OztrREFFWiw4REFBQ2pELDJEQUFLQSxDQUFDK0YsSUFBSTt3Q0FDVHhGLE1BQU0sR0FBa0IsT0FBZm1ELGdCQUFlO3dDQUN4QnNDLE9BQU85QyxFQUFFO3dDQUNUeUUsT0FBT0UsTUFBTWxGLG9CQUFvQjt3Q0FDakN1RixVQUFVLENBQUNDLElBQU1ULGtCQUFrQix3QkFBd0JTLEVBQUVFLE1BQU0sQ0FBQ1YsS0FBSyxJQUFJO3dDQUM3RTFCLFVBQVVoRDs7Ozs7Ozs7Ozs7OzBDQUdkLDhEQUFDdEUsbVFBQUtBO2dDQUFDdUgsV0FBVTtnQ0FBTUosU0FBUzs7a0RBQzlCLDhEQUFDOUYsMkRBQUtBLENBQUMrRixJQUFJO3dDQUNUeEYsTUFBTSxHQUFrQixPQUFmbUQsZ0JBQWU7d0NBQ3hCc0MsT0FBTzlDLEVBQUU7d0NBQ1RpRCxNQUFLO3dDQUNMd0IsT0FBT0UsTUFBTWpGLFdBQVc7d0NBQ3hCc0YsVUFBVSxDQUFDQyxJQUFNVCxrQkFBa0IsZUFBZVUsV0FBV0QsRUFBRUUsTUFBTSxDQUFDVixLQUFLLEtBQUs7d0NBQ2hGMUIsVUFBVWhEOzs7Ozs7a0RBRVosOERBQUNqRCwyREFBS0EsQ0FBQytGLElBQUk7d0NBQ1R4RixNQUFNLEdBQWtCLE9BQWZtRCxnQkFBZTt3Q0FDeEJzQyxPQUFPOUMsRUFBRTt3Q0FDVHlFLE9BQU9FLE1BQU1oRiwwQkFBMEI7d0NBQ3ZDcUYsVUFBVSxDQUFDQyxJQUFNVCxrQkFBa0IsOEJBQThCUyxFQUFFRSxNQUFNLENBQUNWLEtBQUs7d0NBQy9FMUIsVUFBVWhEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU94QjtJQUVBLHFCQUNFLDhEQUFDdEUsbVFBQUtBO1FBQUNtSCxTQUFTOzswQkFFZCw4REFBQ2xILG9RQUFJQTtnQkFBQzBILElBQUk7b0JBQUV3QixXQUFXO2dCQUFPOztrQ0FDNUIsOERBQUNqSixvUUFBVUE7d0JBQ1RtSSxPQUFPOUQsRUFBRTt3QkFDVG9GLHNCQUNFLDhEQUFDdEosb1FBQU1BOzRCQUNMMkgsU0FBUTs0QkFDUk0sTUFBSzs0QkFDTHNCLHlCQUFXLDhEQUFDdEksMkRBQU9BO2dDQUFDa0gsTUFBSzs7Ozs7OzRCQUN6QkQsU0FBU3JDOzRCQUNUeUIsSUFBSTtnQ0FBRWtDLElBQUk7NEJBQUU7NEJBQ1p2QyxVQUFVaEQ7c0NBRVRDLEVBQUU7Ozs7Ozs7Ozs7O2tDQUlULDhEQUFDcEUsb1FBQU9BO3dCQUFDd0gsSUFBSTs0QkFBRTBCLGFBQWE7d0JBQVM7Ozs7OztrQ0FDckMsOERBQUMvSSxvUUFBR0E7d0JBQUNxSCxJQUFJOzRCQUFFMkIsR0FBRzt3QkFBRTs7NEJBQ2I3Qjs0QkFFQTNCLE9BQU9PLE1BQU0sR0FBRyxtQkFDZiw4REFBQy9GLG9RQUFHQTtnQ0FBQ3FILElBQUk7b0NBQUV5QixJQUFJO29DQUFHVSxTQUFTO29DQUFRMUIsZ0JBQWdCO2dDQUFTOzBDQUMxRCw0RUFBQy9ILG9RQUFNQTtvQ0FDTDJILFNBQVE7b0NBQ1JDLE9BQU07b0NBQ04yQix5QkFBVyw4REFBQ3RJLDJEQUFPQTt3Q0FBQ2tILE1BQUs7Ozs7OztvQ0FDekJELFNBQVNyQztvQ0FDVG9CLFVBQVVoRDs4Q0FFVEMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFRWjBFOzBCQUdELDhEQUFDbkksb1FBQU1BO2dCQUFDNkgsTUFBTXZEO2dCQUFnQndELFNBQVNyQztnQkFBdUJ3RCxTQUFTO2dCQUFDQyxVQUFTOztrQ0FDL0UsOERBQUNqSixvUUFBV0E7a0NBQ1R1RSxxQkFBcUIsT0FDbEJmLEVBQUUsb0RBQ0ZBLEVBQUU7Ozs7OztrQ0FFUiw4REFBQ3ZELG9RQUFhQTtrQ0FDWiw0RUFBQ1Ysb1FBQUdBOzRCQUFDcUgsSUFBSTtnQ0FBRXNDLElBQUk7NEJBQUU7c0NBQUkvQzs7Ozs7Ozs7Ozs7a0NBRXZCLDhEQUFDakcsb1FBQWFBOzswQ0FDWiw4REFBQ1osb1FBQU1BO2dDQUFDa0ksU0FBU2hDO2dDQUF1QjBCLE9BQU07MENBQzNDMUQsRUFBRTs7Ozs7OzBDQUVMLDhEQUFDbEUsb1FBQU1BO2dDQUFDa0ksU0FBU3RCO2dDQUFnQmUsU0FBUTswQ0FDdEN6RCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTVQsOERBQUNoRCx1RUFBYUE7Z0JBQ1pvSCxNQUFNakQ7Z0JBQ05rRCxTQUFTL0I7Z0JBQ1R3QixPQUFPOUQsRUFBRTtnQkFDVDJGLFNBQVMzRixFQUFFO2dCQUNYb0Ysc0JBQ0UsOERBQUN0SixvUUFBTUE7b0JBQUMySCxTQUFRO29CQUFZQyxPQUFNO29CQUFRTSxTQUFTdkI7OEJBQ2hEekMsRUFBRTs7Ozs7Ozs7Ozs7WUFNUm1FOzs7Ozs7O0FBR1A7R0FyY3dCdkU7O1FBQ1IvQyxxREFBWUE7UUFDc0J0QiwyREFBY0E7UUFZMUQwQix1R0FBbUJBO1FBZ0JvQnpCLDBEQUFhQTs7O0tBOUJsQ29FIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG0ubWFsaWtcXHNvdXJjZVxccmVwb3NcXFJvc3NldGFcXEZyb250ZW5kXFxzcmNcXHNlY3Rpb25zXFxvcmRlcnNcXHBsdDFcXGZvcm1zXFxwbHQxLXBhY2tpbmctbGlzdC1mb3JtLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyB1c2VGb3JtQ29udGV4dCwgdXNlRmllbGRBcnJheSB9IGZyb20gJ3JlYWN0LWhvb2stZm9ybSc7XHJcbmltcG9ydCB7XHJcbiAgU3RhY2ssXHJcbiAgQ2FyZCxcclxuICBDYXJkSGVhZGVyLFxyXG4gIERpdmlkZXIsXHJcbiAgVHlwb2dyYXBoeSxcclxuICBCdXR0b24sXHJcbiAgQm94LFxyXG4gIFRhYmxlLFxyXG4gIFRhYmxlQm9keSxcclxuICBUYWJsZUNlbGwsXHJcbiAgVGFibGVDb250YWluZXIsXHJcbiAgVGFibGVIZWFkLFxyXG4gIFRhYmxlUm93LFxyXG4gIFBhcGVyLFxyXG4gIERpYWxvZyxcclxuICBEaWFsb2dUaXRsZSxcclxuICBEaWFsb2dDb250ZW50LFxyXG4gIERpYWxvZ0FjdGlvbnMsXHJcbiAgSWNvbkJ1dHRvbixcclxuICBUb29sdGlwLFxyXG59IGZyb20gJ0BtdWkvbWF0ZXJpYWwnO1xyXG5pbXBvcnQgeyB1c2VUcmFuc2xhdGUgfSBmcm9tICdzcmMvbG9jYWxlcyc7XHJcbmltcG9ydCB7IEZpZWxkIH0gZnJvbSAnc3JjL2NvbXBvbmVudHMvaG9vay1mb3JtJztcclxuaW1wb3J0IHsgSWNvbmlmeSB9IGZyb20gJ3NyYy9jb21wb25lbnRzL2ljb25pZnknO1xyXG5pbXBvcnQgeyBDb25maXJtRGlhbG9nIH0gZnJvbSAnc3JjL2NvbXBvbmVudHMvY3VzdG9tLWRpYWxvZyc7XHJcbmltcG9ydCB7IHVzZVBhcnR5QWRkcmVzc0Zvcm0gfSBmcm9tICdzcmMvY29tcG9uZW50cy9wYXJ0eS1hZGRyZXNzL2hvb2tzL3VzZVBhcnR5QWRkcmVzc0Zvcm0nO1xyXG5pbXBvcnQgUGFydHlBZGRyZXNzRGlhbG9nIGZyb20gJ3NyYy9jb21wb25lbnRzL3BhcnR5LWFkZHJlc3MvcGFydHktYWRkcmVzcy1kaWFsb2cnO1xyXG5cclxuZXhwb3J0IGludGVyZmFjZSBQTFQxUGFja2VkSXRlbURhdGEge1xyXG4gIGlkPzogc3RyaW5nO1xyXG4gIG5hbWU6IHN0cmluZztcclxuICBoc0NvZGU6IHN0cmluZztcclxuICBtb2RlbE51bWJlcjogc3RyaW5nO1xyXG4gIHF1YW50aXR5OiBudW1iZXI7XHJcbiAgaXRlbU5ldFdlaWdodD86IG51bWJlcjtcclxuICBpdGVtTmV0V2VpZ2h0VW5pdD86IHN0cmluZztcclxuICBpdGVtR3Jvc3NXZWlnaHQ/OiBudW1iZXI7XHJcbiAgaHNDb2RlSGludHM/OiBzdHJpbmc7XHJcbiAgcGFja2luZ0xpc3RQb3NpdGlvbklkPzogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFBMVDFQYWNraW5nTGlzdFBvc2l0aW9uRGF0YSB7XHJcbiAgaWQ/OiBzdHJpbmc7XHJcbiAgcG9zaXRpb25OdW1iZXI6IG51bWJlcjtcclxuICBjb21tZXJjaWFsSW52b2ljZU51bWJlcjogc3RyaW5nO1xyXG4gIHBhY2thZ2VOZXRXZWlnaHQ6IG51bWJlcjtcclxuICBwYWNrYWdlTmV0V2VpZ2h0VW5pdD86IHN0cmluZztcclxuICBwYWNrYWdlR3Jvc3NXZWlnaHQ6IG51bWJlcjtcclxuICBwYWNrYWdlR3Jvc3NXZWlnaHRVbml0Pzogc3RyaW5nO1xyXG4gIHBhY2thZ2VzTmV0V2VpZ2h0OiBudW1iZXI7XHJcbiAgcGFja2FnZXNOZXRXZWlnaHRVbml0Pzogc3RyaW5nO1xyXG4gIHBhY2thZ2VzR3Jvc3NXZWlnaHQ6IG51bWJlcjtcclxuICBwYWNrYWdlc0dyb3NzV2VpZ2h0VW5pdD86IHN0cmluZztcclxuICBwYWNrYWdlVW5pdDogc3RyaW5nO1xyXG4gIHBhY2thZ2VTaXplOiBzdHJpbmc7XHJcbiAgcGFja2FnZVZvbHVtZTogbnVtYmVyO1xyXG4gIHBhY2thZ2VWb2x1bWVVbml0OiBzdHJpbmc7XHJcbiAgbnVtYmVyT2ZQYWNrYWdlczogbnVtYmVyO1xyXG4gIHBhY2tlZEl0ZW1zOiBQTFQxUGFja2VkSXRlbURhdGFbXTtcclxuICBwYWNraW5nTGlzdElkPzogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFBMVDFQYWNraW5nTGlzdFRvdGFsRGF0YSB7XHJcbiAgaWQ/OiBzdHJpbmc7XHJcbiAgc2hpcG1lbnRPclBhY2tpbmdJZD86IHN0cmluZztcclxuICB0b3RhbFF1YW50aXR5OiBudW1iZXI7XHJcbiAgdG90YWxQYWNrYWdlc1VuaXQ6IHN0cmluZztcclxuICB0b3RhbE51bWJlck9mUGFja2FnZXM6IG51bWJlcjtcclxuICB0b3RhbE51bWJlck9mUGFsbGV0cz86IG51bWJlcjtcclxuICB0b3RhbE5ldFdlaWdodDogbnVtYmVyO1xyXG4gIHRvdGFsTmV0V2VpZ2h0VW5pdD86IHN0cmluZztcclxuICB0b3RhbEdyb3NzV2VpZ2h0OiBudW1iZXI7XHJcbiAgdG90YWxHcm9zc1dlaWdodFVuaXQ/OiBzdHJpbmc7XHJcbiAgdG90YWxWb2x1bWU6IG51bWJlcjtcclxuICB0b3RhbFZvbHVtZU1lYXN1cmVtZW50VW5pdDogc3RyaW5nO1xyXG4gIHBhY2tpbmdMaXN0SWQ/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgUExUMVBhY2tpbmdMaXN0RGF0YSB7XHJcbiAgaWQ/OiBzdHJpbmc7XHJcbiAgcGFja2luZ0xpc3RQb3NpdGlvbnM6IFBMVDFQYWNraW5nTGlzdFBvc2l0aW9uRGF0YVtdO1xyXG4gIGxpc3RUb3RhbD86IFBMVDFQYWNraW5nTGlzdFRvdGFsRGF0YTtcclxuICB0MU9yZGVySWQ/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmNvbnN0IERFRkFVTFRfUEFDS0VEX0lURU0gPSB7XHJcbiAgaWQ6ICcnLFxyXG4gIG5hbWU6ICcnLFxyXG4gIGhzQ29kZTogJycsXHJcbiAgbW9kZWxOdW1iZXI6ICcnLFxyXG4gIHF1YW50aXR5OiAwLFxyXG4gIGl0ZW1OZXRXZWlnaHQ6IDAsXHJcbiAgaXRlbU5ldFdlaWdodFVuaXQ6ICdLR00nLFxyXG4gIGl0ZW1Hcm9zc1dlaWdodDogMCxcclxuICBoc0NvZGVIaW50czogJycsXHJcbiAgcGFja2luZ0xpc3RQb3NpdGlvbklkOiAnJyxcclxufTtcclxuXHJcbmNvbnN0IERFRkFVTFRfUE9TSVRJT04gPSB7XHJcbiAgaWQ6ICcnLFxyXG4gIHBvc2l0aW9uTnVtYmVyOiAxLFxyXG4gIGNvbW1lcmNpYWxJbnZvaWNlTnVtYmVyOiAnJyxcclxuICBwYWNrYWdlTmV0V2VpZ2h0OiAwLFxyXG4gIHBhY2thZ2VOZXRXZWlnaHRVbml0OiAnS0dNJyxcclxuICBwYWNrYWdlR3Jvc3NXZWlnaHQ6IDAsXHJcbiAgcGFja2FnZUdyb3NzV2VpZ2h0VW5pdDogJ0tHTScsXHJcbiAgcGFja2FnZXNOZXRXZWlnaHQ6IDAsXHJcbiAgcGFja2FnZXNOZXRXZWlnaHRVbml0OiAnS0dNJyxcclxuICBwYWNrYWdlc0dyb3NzV2VpZ2h0OiAwLFxyXG4gIHBhY2thZ2VzR3Jvc3NXZWlnaHRVbml0OiAnS0dNJyxcclxuICBwYWNrYWdlVW5pdDogJ0NUTlMnLFxyXG4gIHBhY2thZ2VTaXplOiAnJyxcclxuICBwYWNrYWdlVm9sdW1lOiAwLFxyXG4gIHBhY2thZ2VWb2x1bWVVbml0OiAnQ0JNJyxcclxuICBudW1iZXJPZlBhY2thZ2VzOiAwLFxyXG4gIHBhY2tlZEl0ZW1zOiBbXSxcclxuICBwYWNraW5nTGlzdElkOiAnJyxcclxufTtcclxuXHJcbmNvbnN0IERFRkFVTFRfVE9UQUwgPSB7XHJcbiAgaWQ6ICcnLFxyXG4gIHNoaXBtZW50T3JQYWNraW5nSWQ6ICcnLFxyXG4gIHRvdGFsUXVhbnRpdHk6IDAsXHJcbiAgdG90YWxQYWNrYWdlc1VuaXQ6ICdDVE5TJyxcclxuICB0b3RhbE51bWJlck9mUGFja2FnZXM6IDAsXHJcbiAgdG90YWxOdW1iZXJPZlBhbGxldHM6IDAsXHJcbiAgdG90YWxOZXRXZWlnaHQ6IDAsXHJcbiAgdG90YWxOZXRXZWlnaHRVbml0OiAnS0dNJyxcclxuICB0b3RhbEdyb3NzV2VpZ2h0OiAwLFxyXG4gIHRvdGFsR3Jvc3NXZWlnaHRVbml0OiAnS0dNJyxcclxuICB0b3RhbFZvbHVtZTogMCxcclxuICB0b3RhbFZvbHVtZU1lYXN1cmVtZW50VW5pdDogJ0NCTScsXHJcbiAgcGFja2luZ0xpc3RJZDogJycsXHJcbn07XHJcbmludGVyZmFjZSBQTFQxUGFja2luZ0xpc3RGb3JtUHJvcHMge1xyXG4gIGZvcm1QYXRoOiBzdHJpbmc7XHJcbiAgaW5kZXg/OiBudW1iZXI7XHJcbiAgcmVhZE9ubHk/OiBib29sZWFuO1xyXG59XHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBMVDFQYWNraW5nTGlzdEZvcm0oeyBmb3JtUGF0aCwgaW5kZXgsIHJlYWRPbmx5ID0gZmFsc2UgfTogUExUMVBhY2tpbmdMaXN0Rm9ybVByb3BzKSB7XHJcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGUoKTtcclxuICBjb25zdCB7IGNvbnRyb2wsIGdldFZhbHVlcywgd2F0Y2gsIHNldFZhbHVlIH0gPSB1c2VGb3JtQ29udGV4dCgpO1xyXG5cclxuICBjb25zdCBmaWVsZFByZWZpeCA9IGluZGV4ICE9PSB1bmRlZmluZWQgPyBgJHtmb3JtUGF0aH0uJHtpbmRleH1gIDogZm9ybVBhdGg7XHJcbiAgY29uc3QgaXRlbXNGaWVsZE5hbWUgPSBgJHtmaWVsZFByZWZpeH0ubGlzdFN1bW1hcnlgO1xyXG4gIGNvbnN0IHRvdGFsRmllbGROYW1lID0gYCR7ZmllbGRQcmVmaXh9Lmxpc3RUb3RhbGA7XHJcblxyXG4gIC8vIFVzZSB0aGUgcGFydHkgYWRkcmVzcyBmb3JtIGhvb2tcclxuICBjb25zdCB7XHJcbiAgICBvcGVuUGFydHlEaWFsb2csXHJcbiAgICBjdXJyZW50UGFydHlUeXBlLFxyXG4gICAgaGFuZGxlQ2xvc2VQYXJ0eURpYWxvZyxcclxuICAgIGhhbmRsZVVwZGF0ZVBhcnR5XHJcbiAgfSA9IHVzZVBhcnR5QWRkcmVzc0Zvcm0oeyBmaWVsZFByZWZpeCB9KTtcclxuXHJcbiAgLy8gU3RhdGUgZm9yIGl0ZW0gZGlhbG9nXHJcbiAgY29uc3QgW29wZW5JdGVtRGlhbG9nLCBzZXRPcGVuSXRlbURpYWxvZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2N1cnJlbnRJdGVtSW5kZXgsIHNldEN1cnJlbnRJdGVtSW5kZXhdID0gdXNlU3RhdGU8bnVtYmVyIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2RlbGV0ZUl0ZW1JbmRleCwgc2V0RGVsZXRlSXRlbUluZGV4XSA9IHVzZVN0YXRlPG51bWJlciB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtvcGVuRGVsZXRlQ29uZmlybSwgc2V0T3BlbkRlbGV0ZUNvbmZpcm1dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IGRlbGV0ZUJ1dHRvblJlZiA9IHVzZVJlZjxIVE1MQnV0dG9uRWxlbWVudD4obnVsbCk7XHJcblxyXG4gIC8vIEluaXRpYWxpemUgbGlzdFRvdGFsIGlmIGl0IGRvZXNuJ3QgZXhpc3RcclxuICBjb25zdCBsaXN0VG90YWwgPSB3YXRjaCh0b3RhbEZpZWxkTmFtZSk7XHJcbiAgaWYgKCFsaXN0VG90YWwpIHtcclxuICAgIHNldFZhbHVlKHRvdGFsRmllbGROYW1lLCBERUZBVUxUX1RPVEFMKTtcclxuICB9XHJcblxyXG4gIC8vIFVzZUZpZWxkQXJyYXkgaG9vayB0byBtYW5hZ2UgdGhlIGl0ZW1zIGFycmF5XHJcbiAgY29uc3QgeyBmaWVsZHMsIGFwcGVuZCwgcmVtb3ZlLCB1cGRhdGUgfSA9IHVzZUZpZWxkQXJyYXkoe1xyXG4gICAgY29udHJvbCxcclxuICAgIG5hbWU6IGl0ZW1zRmllbGROYW1lLFxyXG4gIH0pO1xyXG5cclxuICAvLyBIYW5kbGUgb3BlbmluZyB0aGUgaXRlbSBkaWFsb2cgZm9yIGFkZGluZyBhIG5ldyBpdGVtXHJcbiAgY29uc3QgaGFuZGxlQWRkSXRlbSA9ICgpID0+IHtcclxuICAgIHNldEN1cnJlbnRJdGVtSW5kZXgobnVsbCk7XHJcbiAgICAvLyBBZGQgYSBuZXcgaXRlbSB3aXRoIGRlZmF1bHQgdmFsdWVzXHJcbiAgICBjb25zdCBuZXdJdGVtID0geyAuLi5ERUZBVUxUX0lURU0gfTtcclxuICAgIGFwcGVuZChuZXdJdGVtKTtcclxuICAgIHNldEN1cnJlbnRJdGVtSW5kZXgoZmllbGRzLmxlbmd0aCk7IC8vIFNldCB0byB0aGUgbmV3IGluZGV4XHJcbiAgICBzZXRPcGVuSXRlbURpYWxvZyh0cnVlKTtcclxuICB9O1xyXG5cclxuICAvLyBIYW5kbGUgb3BlbmluZyB0aGUgaXRlbSBkaWFsb2cgZm9yIGVkaXRpbmcgYW4gZXhpc3RpbmcgaXRlbVxyXG4gIGNvbnN0IGhhbmRsZUVkaXRJdGVtID0gKGluZGV4OiBudW1iZXIpID0+IHtcclxuICAgIHNldEN1cnJlbnRJdGVtSW5kZXgoaW5kZXgpO1xyXG4gICAgc2V0T3Blbkl0ZW1EaWFsb2codHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIGNsb3NpbmcgdGhlIGl0ZW0gZGlhbG9nXHJcbiAgY29uc3QgaGFuZGxlQ2xvc2VJdGVtRGlhbG9nID0gKCkgPT4ge1xyXG4gICAgc2V0T3Blbkl0ZW1EaWFsb2coZmFsc2UpO1xyXG4gICAgLy8gSWYgd2Ugd2VyZSBhZGRpbmcgYSBuZXcgaXRlbSBhbmQgdXNlciBjYW5jZWxzLCByZW1vdmUgdGhlIGVtcHR5IGl0ZW1cclxuICAgIGlmIChjdXJyZW50SXRlbUluZGV4ICE9PSBudWxsICYmIGN1cnJlbnRJdGVtSW5kZXggPT09IGZpZWxkcy5sZW5ndGggLSAxKSB7XHJcbiAgICAgIGNvbnN0IGl0ZW0gPSBnZXRWYWx1ZXMoYCR7aXRlbXNGaWVsZE5hbWV9LiR7Y3VycmVudEl0ZW1JbmRleH1gKTtcclxuICAgICAgLy8gQ2hlY2sgaWYgaXQncyBhbiBlbXB0eSBpdGVtIChhbGwgZmllbGRzIGFyZSBkZWZhdWx0IHZhbHVlcylcclxuICAgICAgY29uc3QgaXNFbXB0eSA9ICFpdGVtLm5hbWUgJiYgIWl0ZW0ubW9kZWxOdW1iZXIgJiYgIWl0ZW0ucHVyY2hhc2VPcmRlck51bWJlciAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAhaXRlbS5jb21tZXJjaWFsSW52b2ljZU51bWJlciAmJiBpdGVtLnF1YW50aXR5ID09PSAwICYmXHJcbiAgICAgICAgICAgICAgICAgICAgIGl0ZW0ucGFja2FnZU5ldFdlaWdodCA9PT0gMCAmJiBpdGVtLnBhY2thZ2VHcm9zc1dlaWdodCA9PT0gMCAmJiBpdGVtLnZvbHVtZSA9PT0gMDtcclxuICAgICAgaWYgKGlzRW1wdHkpIHtcclxuICAgICAgICByZW1vdmUoY3VycmVudEl0ZW1JbmRleCk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgIHNldEN1cnJlbnRJdGVtSW5kZXgobnVsbCk7XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIG9wZW5pbmcgdGhlIGRlbGV0ZSBjb25maXJtYXRpb24gZGlhbG9nXHJcbiAgY29uc3QgaGFuZGxlT3BlbkRlbGV0ZUNvbmZpcm0gPSAoaW5kZXg6IG51bWJlcikgPT4ge1xyXG4gICAgc2V0RGVsZXRlSXRlbUluZGV4KGluZGV4KTtcclxuICAgIHNldE9wZW5EZWxldGVDb25maXJtKHRydWUpO1xyXG4gIH07XHJcblxyXG4gIC8vIEhhbmRsZSBjbG9zaW5nIHRoZSBkZWxldGUgY29uZmlybWF0aW9uIGRpYWxvZ1xyXG4gIGNvbnN0IGhhbmRsZUNsb3NlRGVsZXRlQ29uZmlybSA9ICgpID0+IHtcclxuICAgIHNldE9wZW5EZWxldGVDb25maXJtKGZhbHNlKTtcclxuICAgIGlmIChkZWxldGVJdGVtSW5kZXggIT09IG51bGwpIHtcclxuICAgICAgZGVsZXRlQnV0dG9uUmVmLmN1cnJlbnQ/LmZvY3VzKCk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIGRlbGV0aW5nIGFuIGl0ZW1cclxuICBjb25zdCBoYW5kbGVEZWxldGVJdGVtID0gKCkgPT4ge1xyXG4gICAgaWYgKGRlbGV0ZUl0ZW1JbmRleCAhPT0gbnVsbCkge1xyXG4gICAgICByZW1vdmUoZGVsZXRlSXRlbUluZGV4KTtcclxuICAgIH1cclxuICAgIGhhbmRsZUNsb3NlRGVsZXRlQ29uZmlybSgpO1xyXG4gIH07XHJcblxyXG4gIC8vIEhhbmRsZSBzYXZpbmcgYW4gaXRlbSAoanVzdCBjbG9zZSB0aGUgZGlhbG9nIHNpbmNlIGZvcm0gaXMgYWxyZWFkeSB1cGRhdGVkKVxyXG4gIGNvbnN0IGhhbmRsZVNhdmVJdGVtID0gKCkgPT4ge1xyXG4gICAgaGFuZGxlQ2xvc2VJdGVtRGlhbG9nKCk7XHJcbiAgfTtcclxuXHJcbiAgLy8gUmVuZGVyIHRoZSBpdGVtIGZvcm0gaW4gdGhlIGRpYWxvZyAtIHVzaW5nIFJlYWN0IEhvb2sgRm9ybSBmaWVsZHNcclxuICBjb25zdCByZW5kZXJJdGVtRm9ybSA9ICgpID0+IHtcclxuICAgIGlmIChjdXJyZW50SXRlbUluZGV4ID09PSBudWxsKSByZXR1cm4gbnVsbDtcclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8U3RhY2sgc3BhY2luZz17M30+XHJcbiAgICAgICAgPEZpZWxkLlRleHRcclxuICAgICAgICAgIG5hbWU9e2Ake2l0ZW1zRmllbGROYW1lfS4ke2N1cnJlbnRJdGVtSW5kZXh9Lm5hbWVgfVxyXG4gICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QuaXRlbS5uYW1lJyl9XHJcbiAgICAgICAgICBkaXNhYmxlZD17cmVhZE9ubHl9XHJcbiAgICAgICAgLz5cclxuXHJcbiAgICAgICAgPEZpZWxkLlRleHRcclxuICAgICAgICAgIG5hbWU9e2Ake2l0ZW1zRmllbGROYW1lfS4ke2N1cnJlbnRJdGVtSW5kZXh9Lm1vZGVsTnVtYmVyYH1cclxuICAgICAgICAgIGxhYmVsPXt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnBhY2tpbmdMaXN0Lml0ZW0ubW9kZWxOdW1iZXInKX1cclxuICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAvPlxyXG5cclxuICAgICAgICA8U3RhY2sgZGlyZWN0aW9uPVwicm93XCIgc3BhY2luZz17Mn0+XHJcbiAgICAgICAgICA8RmllbGQuVGV4dFxyXG4gICAgICAgICAgICBuYW1lPXtgJHtpdGVtc0ZpZWxkTmFtZX0uJHtjdXJyZW50SXRlbUluZGV4fS5wdXJjaGFzZU9yZGVyTnVtYmVyYH1cclxuICAgICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QuaXRlbS5wdXJjaGFzZU9yZGVyTnVtYmVyJyl9XHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8RmllbGQuVGV4dFxyXG4gICAgICAgICAgICBuYW1lPXtgJHtpdGVtc0ZpZWxkTmFtZX0uJHtjdXJyZW50SXRlbUluZGV4fS5jb21tZXJjaWFsSW52b2ljZU51bWJlcmB9XHJcbiAgICAgICAgICAgIGxhYmVsPXt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnBhY2tpbmdMaXN0Lml0ZW0uY29tbWVyY2lhbEludm9pY2VOdW1iZXInKX1cclxuICAgICAgICAgICAgZGlzYWJsZWQ9e3JlYWRPbmx5fVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L1N0YWNrPlxyXG5cclxuICAgICAgICA8U3RhY2sgZGlyZWN0aW9uPVwicm93XCIgc3BhY2luZz17Mn0+XHJcbiAgICAgICAgICA8RmllbGQuVGV4dFxyXG4gICAgICAgICAgICBuYW1lPXtgJHtpdGVtc0ZpZWxkTmFtZX0uJHtjdXJyZW50SXRlbUluZGV4fS5wYWNrYWdlTmV0V2VpZ2h0YH1cclxuICAgICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QuaXRlbS5wYWNrYWdlTmV0V2VpZ2h0Jyl9XHJcbiAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxyXG4gICAgICAgICAgICBkaXNhYmxlZD17cmVhZE9ubHl9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICAgPEZpZWxkLlRleHRcclxuICAgICAgICAgICAgbmFtZT17YCR7aXRlbXNGaWVsZE5hbWV9LiR7Y3VycmVudEl0ZW1JbmRleH0ucGFja2FnZU5ldFdlaWdodFVuaXRgfVxyXG4gICAgICAgICAgICBsYWJlbD17dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5wYWNraW5nTGlzdC5pdGVtLnBhY2thZ2VOZXRXZWlnaHRVbml0Jyl9XHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9TdGFjaz5cclxuXHJcbiAgICAgICAgPFN0YWNrIGRpcmVjdGlvbj1cInJvd1wiIHNwYWNpbmc9ezJ9PlxyXG4gICAgICAgICAgPEZpZWxkLlRleHRcclxuICAgICAgICAgICAgbmFtZT17YCR7aXRlbXNGaWVsZE5hbWV9LiR7Y3VycmVudEl0ZW1JbmRleH0ucGFja2FnZUdyb3NzV2VpZ2h0YH1cclxuICAgICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QuaXRlbS5wYWNrYWdlR3Jvc3NXZWlnaHQnKX1cclxuICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8RmllbGQuVGV4dFxyXG4gICAgICAgICAgICBuYW1lPXtgJHtpdGVtc0ZpZWxkTmFtZX0uJHtjdXJyZW50SXRlbUluZGV4fS5wYWNrYWdlR3Jvc3NXZWlnaHRVbml0YH1cclxuICAgICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QuaXRlbS5wYWNrYWdlR3Jvc3NXZWlnaHRVbml0Jyl9XHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9TdGFjaz5cclxuXHJcbiAgICAgICAgPFN0YWNrIGRpcmVjdGlvbj1cInJvd1wiIHNwYWNpbmc9ezJ9PlxyXG4gICAgICAgICAgPEZpZWxkLlRleHRcclxuICAgICAgICAgICAgbmFtZT17YCR7aXRlbXNGaWVsZE5hbWV9LiR7Y3VycmVudEl0ZW1JbmRleH0ucXVhbnRpdHlgfVxyXG4gICAgICAgICAgICBsYWJlbD17dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5wYWNraW5nTGlzdC5pdGVtLnF1YW50aXR5Jyl9XHJcbiAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxyXG4gICAgICAgICAgICBkaXNhYmxlZD17cmVhZE9ubHl9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICAgPEZpZWxkLlRleHRcclxuICAgICAgICAgICAgbmFtZT17YCR7aXRlbXNGaWVsZE5hbWV9LiR7Y3VycmVudEl0ZW1JbmRleH0udm9sdW1lYH1cclxuICAgICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QuaXRlbS52b2x1bWUnKX1cclxuICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9TdGFjaz5cclxuICAgICAgPC9TdGFjaz5cclxuICAgICk7XHJcbiAgfTtcclxuXHJcbiAgLy8gUmVuZGVyIHRoZSBpdGVtIHRhYmxlXHJcbiAgY29uc3QgcmVuZGVySXRlbXNUYWJsZSA9ICgpID0+IChcclxuICAgIDxUYWJsZUNvbnRhaW5lciBjb21wb25lbnQ9e1BhcGVyfT5cclxuICAgICAgPFRhYmxlIHN4PXt7IG1pbldpZHRoOiA2NTAgfX0gYXJpYS1sYWJlbD1cInBhY2tpbmcgbGlzdCBpdGVtcyB0YWJsZVwiPlxyXG4gICAgICAgIDxUYWJsZUhlYWQ+XHJcbiAgICAgICAgICA8VGFibGVSb3c+XHJcbiAgICAgICAgICAgIDxUYWJsZUNlbGw+e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QuaXRlbS5uYW1lJyl9PC9UYWJsZUNlbGw+XHJcbiAgICAgICAgICAgIDxUYWJsZUNlbGw+e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QuaXRlbS5tb2RlbE51bWJlcicpfTwvVGFibGVDZWxsPlxyXG4gICAgICAgICAgICA8VGFibGVDZWxsPnt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnBhY2tpbmdMaXN0Lml0ZW0ucXVhbnRpdHknKX08L1RhYmxlQ2VsbD5cclxuICAgICAgICAgICAgPFRhYmxlQ2VsbD5cclxuICAgICAgICAgICAgICB7dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5wYWNraW5nTGlzdC5pdGVtLnBhY2thZ2VOZXRXZWlnaHQnKX1cclxuICAgICAgICAgICAgPC9UYWJsZUNlbGw+XHJcbiAgICAgICAgICAgIDxUYWJsZUNlbGw+XHJcbiAgICAgICAgICAgICAge3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QuaXRlbS5wYWNrYWdlR3Jvc3NXZWlnaHQnKX1cclxuICAgICAgICAgICAgPC9UYWJsZUNlbGw+XHJcbiAgICAgICAgICAgIDxUYWJsZUNlbGwgYWxpZ249XCJyaWdodFwiPnt0KCdjb21tb24uYWN0aW9ucycpfTwvVGFibGVDZWxsPlxyXG4gICAgICAgICAgPC9UYWJsZVJvdz5cclxuICAgICAgICA8L1RhYmxlSGVhZD5cclxuICAgICAgICA8VGFibGVCb2R5PlxyXG4gICAgICAgICAge2ZpZWxkcy5sZW5ndGggPT09IDAgPyAoXHJcbiAgICAgICAgICAgIDxUYWJsZVJvdz5cclxuICAgICAgICAgICAgICA8VGFibGVDZWxsIGNvbFNwYW49ezZ9IGFsaWduPVwiY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBjb2xvcj1cInRleHQuc2Vjb25kYXJ5XCI+XHJcbiAgICAgICAgICAgICAgICAgIHt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnBhY2tpbmdMaXN0Lml0ZW0ubm9JdGVtcycpfVxyXG4gICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxyXG4gICAgICAgICAgICA8L1RhYmxlUm93PlxyXG4gICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgZmllbGRzLm1hcCgoZmllbGQsIGluZGV4KSA9PiB7XHJcbiAgICAgICAgICAgICAgY29uc3QgaXRlbSA9IGdldFZhbHVlcyhgJHtpdGVtc0ZpZWxkTmFtZX0uJHtpbmRleH1gKSB8fCB7fTtcclxuICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgPFRhYmxlUm93IGtleT17ZmllbGQuaWR9PlxyXG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPntpdGVtLm5hbWUgfHwgJy0nfTwvVGFibGVDZWxsPlxyXG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPntpdGVtLm1vZGVsTnVtYmVyIHx8ICctJ308L1RhYmxlQ2VsbD5cclxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD57aXRlbS5xdWFudGl0eSA/PyAnLSd9PC9UYWJsZUNlbGw+XHJcbiAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XHJcbiAgICAgICAgICAgICAgICAgICAge2l0ZW0ucGFja2FnZU5ldFdlaWdodCA/PyAnLSd9IHtpdGVtLnBhY2thZ2VOZXRXZWlnaHRVbml0IHx8ICcnfVxyXG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cclxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5cclxuICAgICAgICAgICAgICAgICAgICB7aXRlbS5wYWNrYWdlR3Jvc3NXZWlnaHQgPz8gJy0nfSB7aXRlbS5wYWNrYWdlR3Jvc3NXZWlnaHRVbml0IHx8ICcnfVxyXG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cclxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBhbGlnbj1cInJpZ2h0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPFN0YWNrIGRpcmVjdGlvbj1cInJvd1wiIHNwYWNpbmc9ezF9IGp1c3RpZnlDb250ZW50PVwiZmxleC1lbmRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwIHRpdGxlPXt0KCdjb21tb24uZWRpdCcpfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEljb25CdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yPVwicHJpbWFyeVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRWRpdEl0ZW0oaW5kZXgpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uaWZ5IGljb249XCJldmE6ZWRpdC1maWxsXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9JY29uQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXAgdGl0bGU9e3QoJ2NvbW1vbi5kZWxldGUnKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uQnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtYWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj1cImVycm9yXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVPcGVuRGVsZXRlQ29uZmlybShpbmRleCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmVmPXtkZWxldGVJdGVtSW5kZXggPT09IGluZGV4ID8gZGVsZXRlQnV0dG9uUmVmIDogbnVsbH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17cmVhZE9ubHl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvbmlmeSBpY29uPVwiZXZhOnRyYXNoLTItb3V0bGluZVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvSWNvbkJ1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcD5cclxuICAgICAgICAgICAgICAgICAgICA8L1N0YWNrPlxyXG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cclxuICAgICAgICAgICAgICAgIDwvVGFibGVSb3c+XHJcbiAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9UYWJsZUJvZHk+XHJcbiAgICAgIDwvVGFibGU+XHJcbiAgICA8L1RhYmxlQ29udGFpbmVyPlxyXG4gICk7XHJcblxyXG4gIGNvbnN0IHJlbmRlclBhcnR5RGlhbG9nID0gKCkgPT4gKFxyXG4gICAgPFBhcnR5QWRkcmVzc0RpYWxvZ1xyXG4gICAgICBvcGVuPXtvcGVuUGFydHlEaWFsb2d9XHJcbiAgICAgIG9uQ2xvc2U9e2hhbmRsZUNsb3NlUGFydHlEaWFsb2d9XHJcbiAgICAgIG9uU2F2ZT17aGFuZGxlVXBkYXRlUGFydHl9XHJcbiAgICAgIGZvcm1QYXRoPXtmaWVsZFByZWZpeH1cclxuICAgICAgY3VycmVudFBhcnR5VHlwZT17Y3VycmVudFBhcnR5VHlwZX1cclxuICAgICAgcmVhZE9ubHk9e3JlYWRPbmx5fVxyXG4gICAgICB0aXRsZVByZWZpeD1cInBsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QucGFydHlBZGRyZXNzXCJcclxuICAgIC8+XHJcbiAgKTtcclxuICAvLyBIYW5kbGUgY2hhbmdlIGZvciB0b3RhbHMgZmllbGRzXHJcbiAgY29uc3QgaGFuZGxlVG90YWxDaGFuZ2UgPSAoZmllbGQ6IHN0cmluZywgdmFsdWU6IHN0cmluZyB8IG51bWJlcikgPT4ge1xyXG4gICAgc2V0VmFsdWUoYCR7dG90YWxGaWVsZE5hbWV9LiR7ZmllbGR9YCwgdmFsdWUpO1xyXG4gIH07XHJcbiAgLy8gUmVuZGVyIHRoZSB0b3RhbHMgc2VjdGlvblxyXG4gIGNvbnN0IHJlbmRlclRvdGFsc1NlY3Rpb24gPSAoKSA9PiB7XHJcbiAgICBjb25zdCB0b3RhbCA9IHdhdGNoKHRvdGFsRmllbGROYW1lKSB8fCBERUZBVUxUX1RPVEFMO1xyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxDYXJkIHN4PXt7IGJveFNoYWRvdzogJ25vbmUnLCBtdDogMyB9fT5cclxuICAgICAgICA8Q2FyZEhlYWRlciB0aXRsZT17dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5wYWNraW5nTGlzdC5mb3JtLnRvdGFsc1RpdGxlJyl9IC8+XHJcbiAgICAgICAgPERpdmlkZXIgc3g9e3sgYm9yZGVyU3R5bGU6ICdkYXNoZWQnIH19IC8+XHJcbiAgICAgICAgPEJveCBzeD17eyBwOiAzIH19PlxyXG4gICAgICAgICAgPFN0YWNrIHNwYWNpbmc9ezN9PlxyXG4gICAgICAgICAgICA8U3RhY2sgZGlyZWN0aW9uPVwicm93XCIgc3BhY2luZz17Mn0+XHJcbiAgICAgICAgICAgICAgPEZpZWxkLlRleHRcclxuICAgICAgICAgICAgICAgIG5hbWU9e2Ake3RvdGFsRmllbGROYW1lfS50b3RhbFF1YW50aXR5YH1cclxuICAgICAgICAgICAgICAgIGxhYmVsPXt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnBhY2tpbmdMaXN0LnRvdGFsLnRvdGFsUXVhbnRpdHknKX1cclxuICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e3RvdGFsLnRvdGFsUXVhbnRpdHl9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XHJcbiAgICAgICAgICAgICAgICAgIGhhbmRsZVRvdGFsQ2hhbmdlKCd0b3RhbFF1YW50aXR5JywgcGFyc2VGbG9hdChlLnRhcmdldC52YWx1ZSkgfHwgMClcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDxGaWVsZC5UZXh0XHJcbiAgICAgICAgICAgICAgICBuYW1lPXtgJHt0b3RhbEZpZWxkTmFtZX0udG90YWxOdW1iZXJPZlBhbGxldHNgfVxyXG4gICAgICAgICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QudG90YWwudG90YWxOdW1iZXJPZlBhbGxldHMnKX1cclxuICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e3RvdGFsLnRvdGFsTnVtYmVyT2ZQYWxsZXRzfVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PlxyXG4gICAgICAgICAgICAgICAgICBoYW5kbGVUb3RhbENoYW5nZSgndG90YWxOdW1iZXJPZlBhbGxldHMnLCBwYXJzZUZsb2F0KGUudGFyZ2V0LnZhbHVlKSB8fCAwKVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3JlYWRPbmx5fVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvU3RhY2s+XHJcbiAgICAgICAgICAgIDxTdGFjayBkaXJlY3Rpb249XCJyb3dcIiBzcGFjaW5nPXsyfT5cclxuICAgICAgICAgICAgICA8RmllbGQuVGV4dFxyXG4gICAgICAgICAgICAgICAgbmFtZT17YCR7dG90YWxGaWVsZE5hbWV9LnRvdGFsTnVtYmVyT2ZQYWNrYWdlc2B9XHJcbiAgICAgICAgICAgICAgICBsYWJlbD17dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5wYWNraW5nTGlzdC50b3RhbC50b3RhbE51bWJlck9mUGFja2FnZXMnKX1cclxuICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e3RvdGFsLnRvdGFsTnVtYmVyT2ZQYWNrYWdlc31cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT5cclxuICAgICAgICAgICAgICAgICAgaGFuZGxlVG90YWxDaGFuZ2UoJ3RvdGFsTnVtYmVyT2ZQYWNrYWdlcycsIHBhcnNlRmxvYXQoZS50YXJnZXQudmFsdWUpIHx8IDApXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17cmVhZE9ubHl9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8RmllbGQuVGV4dFxyXG4gICAgICAgICAgICAgICAgbmFtZT17YCR7dG90YWxGaWVsZE5hbWV9LnRvdGFsUGFja2FnZXNVbml0YH1cclxuICAgICAgICAgICAgICAgIGxhYmVsPXt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnBhY2tpbmdMaXN0LnRvdGFsLnRvdGFsUGFja2FnZXNVbml0Jyl9XHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17dG90YWwudG90YWxQYWNrYWdlc1VuaXR9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVRvdGFsQ2hhbmdlKCd0b3RhbFBhY2thZ2VzVW5pdCcsIGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L1N0YWNrPlxyXG4gICAgICAgICAgICA8U3RhY2sgZGlyZWN0aW9uPVwicm93XCIgc3BhY2luZz17Mn0+XHJcbiAgICAgICAgICAgICAgPEZpZWxkLlRleHRcclxuICAgICAgICAgICAgICAgIG5hbWU9e2Ake3RvdGFsRmllbGROYW1lfS50b3RhbE5ldFdlaWdodGB9XHJcbiAgICAgICAgICAgICAgICBsYWJlbD17dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5wYWNraW5nTGlzdC50b3RhbC50b3RhbE5ldFdlaWdodCcpfVxyXG4gICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17dG90YWwudG90YWxOZXRXZWlnaHR9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XHJcbiAgICAgICAgICAgICAgICAgIGhhbmRsZVRvdGFsQ2hhbmdlKCd0b3RhbE5ldFdlaWdodCcsIHBhcnNlRmxvYXQoZS50YXJnZXQudmFsdWUpIHx8IDApXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17cmVhZE9ubHl9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8RmllbGQuVGV4dFxyXG4gICAgICAgICAgICAgICAgbmFtZT17YCR7dG90YWxGaWVsZE5hbWV9LnRvdGFsTmV0V2VpZ2h0VW5pdGB9XHJcbiAgICAgICAgICAgICAgICBsYWJlbD17dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5wYWNraW5nTGlzdC50b3RhbC50b3RhbE5ldFdlaWdodFVuaXQnKX1cclxuICAgICAgICAgICAgICAgIHZhbHVlPXt0b3RhbC50b3RhbE5ldFdlaWdodFVuaXR9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVRvdGFsQ2hhbmdlKCd0b3RhbE5ldFdlaWdodFVuaXQnLCBlLnRhcmdldC52YWx1ZSB8fCAna2cnKX1cclxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L1N0YWNrPlxyXG4gICAgICAgICAgICA8U3RhY2sgZGlyZWN0aW9uPVwicm93XCIgc3BhY2luZz17Mn0+XHJcbiAgICAgICAgICAgICAgPEZpZWxkLlRleHRcclxuICAgICAgICAgICAgICAgIG5hbWU9e2Ake3RvdGFsRmllbGROYW1lfS50b3RhbEdyb3NzV2VpZ2h0YH1cclxuICAgICAgICAgICAgICAgIGxhYmVsPXt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnBhY2tpbmdMaXN0LnRvdGFsLnRvdGFsR3Jvc3NXZWlnaHQnKX1cclxuICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e3RvdGFsLnRvdGFsR3Jvc3NXZWlnaHR9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XHJcbiAgICAgICAgICAgICAgICAgIGhhbmRsZVRvdGFsQ2hhbmdlKCd0b3RhbEdyb3NzV2VpZ2h0JywgcGFyc2VGbG9hdChlLnRhcmdldC52YWx1ZSkgfHwgMClcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDxGaWVsZC5UZXh0XHJcbiAgICAgICAgICAgICAgICBuYW1lPXtgJHt0b3RhbEZpZWxkTmFtZX0udG90YWxHcm9zc1dlaWdodFVuaXRgfVxyXG4gICAgICAgICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QudG90YWwudG90YWxHcm9zc1dlaWdodFVuaXQnKX1cclxuICAgICAgICAgICAgICAgIHZhbHVlPXt0b3RhbC50b3RhbEdyb3NzV2VpZ2h0VW5pdH1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlVG90YWxDaGFuZ2UoJ3RvdGFsR3Jvc3NXZWlnaHRVbml0JywgZS50YXJnZXQudmFsdWUgfHwgJ2tnJyl9XHJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17cmVhZE9ubHl9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9TdGFjaz5cclxuICAgICAgICAgICAgPFN0YWNrIGRpcmVjdGlvbj1cInJvd1wiIHNwYWNpbmc9ezJ9PlxyXG4gICAgICAgICAgICAgIDxGaWVsZC5UZXh0XHJcbiAgICAgICAgICAgICAgICBuYW1lPXtgJHt0b3RhbEZpZWxkTmFtZX0udG90YWxWb2x1bWVgfVxyXG4gICAgICAgICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QudG90YWwudG90YWxWb2x1bWUnKX1cclxuICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e3RvdGFsLnRvdGFsVm9sdW1lfVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVUb3RhbENoYW5nZSgndG90YWxWb2x1bWUnLCBwYXJzZUZsb2F0KGUudGFyZ2V0LnZhbHVlKSB8fCAwKX1cclxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDxGaWVsZC5UZXh0XHJcbiAgICAgICAgICAgICAgICBuYW1lPXtgJHt0b3RhbEZpZWxkTmFtZX0udG90YWxWb2x1bWVNZWFzdXJlbWVudFVuaXRgfVxyXG4gICAgICAgICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QudG90YWwudG90YWxWb2x1bWVNZWFzdXJlbWVudFVuaXQnKX1cclxuICAgICAgICAgICAgICAgIHZhbHVlPXt0b3RhbC50b3RhbFZvbHVtZU1lYXN1cmVtZW50VW5pdH1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlVG90YWxDaGFuZ2UoJ3RvdGFsVm9sdW1lTWVhc3VyZW1lbnRVbml0JywgZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3JlYWRPbmx5fVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvU3RhY2s+XHJcbiAgICAgICAgICA8L1N0YWNrPlxyXG4gICAgICAgIDwvQm94PlxyXG4gICAgICA8L0NhcmQ+XHJcbiAgICApO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8U3RhY2sgc3BhY2luZz17M30+XHJcbiAgICAgIHsvKiBJdGVtcyBTZWN0aW9uICovfVxyXG4gICAgICA8Q2FyZCBzeD17eyBib3hTaGFkb3c6ICdub25lJyB9fT5cclxuICAgICAgICA8Q2FyZEhlYWRlclxyXG4gICAgICAgICAgdGl0bGU9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QuZm9ybS5pdGVtc1RpdGxlJyl9XHJcbiAgICAgICAgICBhY3Rpb249e1xyXG4gICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgdmFyaWFudD1cImNvbnRhaW5lZFwiXHJcbiAgICAgICAgICAgICAgc2l6ZT1cInNtYWxsXCJcclxuICAgICAgICAgICAgICBzdGFydEljb249ezxJY29uaWZ5IGljb249XCJldmE6cGx1cy1maWxsXCIgLz59XHJcbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQWRkSXRlbX1cclxuICAgICAgICAgICAgICBzeD17eyBtYjogMiB9fVxyXG4gICAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHt0KCdjb21tb24uYWRkSXRlbScpfVxyXG4gICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgIH1cclxuICAgICAgICAvPlxyXG4gICAgICAgIDxEaXZpZGVyIHN4PXt7IGJvcmRlclN0eWxlOiAnZGFzaGVkJyB9fSAvPlxyXG4gICAgICAgIDxCb3ggc3g9e3sgcDogMyB9fT5cclxuICAgICAgICAgIHtyZW5kZXJJdGVtc1RhYmxlKCl9XHJcblxyXG4gICAgICAgICAge2ZpZWxkcy5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgPEJveCBzeD17eyBtdDogMiwgZGlzcGxheTogJ2ZsZXgnLCBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicgfX0+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cInNvZnRcIlxyXG4gICAgICAgICAgICAgICAgY29sb3I9XCJwcmltYXJ5XCJcclxuICAgICAgICAgICAgICAgIHN0YXJ0SWNvbj17PEljb25pZnkgaWNvbj1cImV2YTpwbHVzLWZpbGxcIiAvPn1cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUFkZEl0ZW19XHJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17cmVhZE9ubHl9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAge3QoJ2NvbW1vbi5hZGRJdGVtJyl9XHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICA8L0JveD5cclxuICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgey8qIFRvdGFscyBTZWN0aW9uICovfVxyXG4gICAgICB7cmVuZGVyVG90YWxzU2VjdGlvbigpfVxyXG5cclxuICAgICAgey8qIEl0ZW0gRGlhbG9nICovfVxyXG4gICAgICA8RGlhbG9nIG9wZW49e29wZW5JdGVtRGlhbG9nfSBvbkNsb3NlPXtoYW5kbGVDbG9zZUl0ZW1EaWFsb2d9IGZ1bGxXaWR0aCBtYXhXaWR0aD1cIm1kXCI+XHJcbiAgICAgICAgPERpYWxvZ1RpdGxlPlxyXG4gICAgICAgICAge2N1cnJlbnRJdGVtSW5kZXggPT09IG51bGxcclxuICAgICAgICAgICAgPyB0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnBhY2tpbmdMaXN0Lml0ZW0uYWRkTmV3JylcclxuICAgICAgICAgICAgOiB0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnBhY2tpbmdMaXN0Lml0ZW0uZWRpdCcpfVxyXG4gICAgICAgIDwvRGlhbG9nVGl0bGU+XHJcbiAgICAgICAgPERpYWxvZ0NvbnRlbnQ+XHJcbiAgICAgICAgICA8Qm94IHN4PXt7IHB0OiAzIH19PntyZW5kZXJJdGVtRm9ybSgpfTwvQm94PlxyXG4gICAgICAgIDwvRGlhbG9nQ29udGVudD5cclxuICAgICAgICA8RGlhbG9nQWN0aW9ucz5cclxuICAgICAgICAgIDxCdXR0b24gb25DbGljaz17aGFuZGxlQ2xvc2VJdGVtRGlhbG9nfSBjb2xvcj1cImluaGVyaXRcIj5cclxuICAgICAgICAgICAge3QoJ2NvbW1vbi5jYW5jZWwnKX1cclxuICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVTYXZlSXRlbX0gdmFyaWFudD1cImNvbnRhaW5lZFwiPlxyXG4gICAgICAgICAgICB7dCgnY29tbW9uLnNhdmUnKX1cclxuICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgIDwvRGlhbG9nQWN0aW9ucz5cclxuICAgICAgPC9EaWFsb2c+XHJcblxyXG4gICAgICB7LyogRGVsZXRlIENvbmZpcm1hdGlvbiBEaWFsb2cgKi99XHJcbiAgICAgIDxDb25maXJtRGlhbG9nXHJcbiAgICAgICAgb3Blbj17b3BlbkRlbGV0ZUNvbmZpcm19XHJcbiAgICAgICAgb25DbG9zZT17aGFuZGxlQ2xvc2VEZWxldGVDb25maXJtfVxyXG4gICAgICAgIHRpdGxlPXt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnBhY2tpbmdMaXN0Lml0ZW0uY29uZmlybURlbGV0ZURpYWxvZy50aXRsZScpfVxyXG4gICAgICAgIGNvbnRlbnQ9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QuaXRlbS5jb25maXJtRGVsZXRlRGlhbG9nLmNvbnRlbnQnKX1cclxuICAgICAgICBhY3Rpb249e1xyXG4gICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiY29udGFpbmVkXCIgY29sb3I9XCJlcnJvclwiIG9uQ2xpY2s9e2hhbmRsZURlbGV0ZUl0ZW19PlxyXG4gICAgICAgICAgICB7dCgnY29tbW9uLmRlbGV0ZScpfVxyXG4gICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgfVxyXG4gICAgICAvPlxyXG5cclxuICAgICAgey8qIFBhcnR5IERpYWxvZyAqL31cclxuICAgICAge3JlbmRlclBhcnR5RGlhbG9nKCl9XHJcbiAgICA8L1N0YWNrPlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VSZWYiLCJ1c2VGb3JtQ29udGV4dCIsInVzZUZpZWxkQXJyYXkiLCJTdGFjayIsIkNhcmQiLCJDYXJkSGVhZGVyIiwiRGl2aWRlciIsIlR5cG9ncmFwaHkiLCJCdXR0b24iLCJCb3giLCJUYWJsZSIsIlRhYmxlQm9keSIsIlRhYmxlQ2VsbCIsIlRhYmxlQ29udGFpbmVyIiwiVGFibGVIZWFkIiwiVGFibGVSb3ciLCJQYXBlciIsIkRpYWxvZyIsIkRpYWxvZ1RpdGxlIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0FjdGlvbnMiLCJJY29uQnV0dG9uIiwiVG9vbHRpcCIsInVzZVRyYW5zbGF0ZSIsIkZpZWxkIiwiSWNvbmlmeSIsIkNvbmZpcm1EaWFsb2ciLCJ1c2VQYXJ0eUFkZHJlc3NGb3JtIiwiUGFydHlBZGRyZXNzRGlhbG9nIiwiREVGQVVMVF9QQUNLRURfSVRFTSIsImlkIiwibmFtZSIsImhzQ29kZSIsIm1vZGVsTnVtYmVyIiwicXVhbnRpdHkiLCJpdGVtTmV0V2VpZ2h0IiwiaXRlbU5ldFdlaWdodFVuaXQiLCJpdGVtR3Jvc3NXZWlnaHQiLCJoc0NvZGVIaW50cyIsInBhY2tpbmdMaXN0UG9zaXRpb25JZCIsIkRFRkFVTFRfUE9TSVRJT04iLCJwb3NpdGlvbk51bWJlciIsImNvbW1lcmNpYWxJbnZvaWNlTnVtYmVyIiwicGFja2FnZU5ldFdlaWdodCIsInBhY2thZ2VOZXRXZWlnaHRVbml0IiwicGFja2FnZUdyb3NzV2VpZ2h0IiwicGFja2FnZUdyb3NzV2VpZ2h0VW5pdCIsInBhY2thZ2VzTmV0V2VpZ2h0IiwicGFja2FnZXNOZXRXZWlnaHRVbml0IiwicGFja2FnZXNHcm9zc1dlaWdodCIsInBhY2thZ2VzR3Jvc3NXZWlnaHRVbml0IiwicGFja2FnZVVuaXQiLCJwYWNrYWdlU2l6ZSIsInBhY2thZ2VWb2x1bWUiLCJwYWNrYWdlVm9sdW1lVW5pdCIsIm51bWJlck9mUGFja2FnZXMiLCJwYWNrZWRJdGVtcyIsInBhY2tpbmdMaXN0SWQiLCJERUZBVUxUX1RPVEFMIiwic2hpcG1lbnRPclBhY2tpbmdJZCIsInRvdGFsUXVhbnRpdHkiLCJ0b3RhbFBhY2thZ2VzVW5pdCIsInRvdGFsTnVtYmVyT2ZQYWNrYWdlcyIsInRvdGFsTnVtYmVyT2ZQYWxsZXRzIiwidG90YWxOZXRXZWlnaHQiLCJ0b3RhbE5ldFdlaWdodFVuaXQiLCJ0b3RhbEdyb3NzV2VpZ2h0IiwidG90YWxHcm9zc1dlaWdodFVuaXQiLCJ0b3RhbFZvbHVtZSIsInRvdGFsVm9sdW1lTWVhc3VyZW1lbnRVbml0IiwiUExUMVBhY2tpbmdMaXN0Rm9ybSIsImZvcm1QYXRoIiwiaW5kZXgiLCJyZWFkT25seSIsInQiLCJjb250cm9sIiwiZ2V0VmFsdWVzIiwid2F0Y2giLCJzZXRWYWx1ZSIsImZpZWxkUHJlZml4IiwidW5kZWZpbmVkIiwiaXRlbXNGaWVsZE5hbWUiLCJ0b3RhbEZpZWxkTmFtZSIsIm9wZW5QYXJ0eURpYWxvZyIsImN1cnJlbnRQYXJ0eVR5cGUiLCJoYW5kbGVDbG9zZVBhcnR5RGlhbG9nIiwiaGFuZGxlVXBkYXRlUGFydHkiLCJvcGVuSXRlbURpYWxvZyIsInNldE9wZW5JdGVtRGlhbG9nIiwiY3VycmVudEl0ZW1JbmRleCIsInNldEN1cnJlbnRJdGVtSW5kZXgiLCJkZWxldGVJdGVtSW5kZXgiLCJzZXREZWxldGVJdGVtSW5kZXgiLCJvcGVuRGVsZXRlQ29uZmlybSIsInNldE9wZW5EZWxldGVDb25maXJtIiwiZGVsZXRlQnV0dG9uUmVmIiwibGlzdFRvdGFsIiwiZmllbGRzIiwiYXBwZW5kIiwicmVtb3ZlIiwidXBkYXRlIiwiaGFuZGxlQWRkSXRlbSIsIm5ld0l0ZW0iLCJERUZBVUxUX0lURU0iLCJsZW5ndGgiLCJoYW5kbGVFZGl0SXRlbSIsImhhbmRsZUNsb3NlSXRlbURpYWxvZyIsIml0ZW0iLCJpc0VtcHR5IiwicHVyY2hhc2VPcmRlck51bWJlciIsInZvbHVtZSIsImhhbmRsZU9wZW5EZWxldGVDb25maXJtIiwiaGFuZGxlQ2xvc2VEZWxldGVDb25maXJtIiwiY3VycmVudCIsImZvY3VzIiwiaGFuZGxlRGVsZXRlSXRlbSIsImhhbmRsZVNhdmVJdGVtIiwicmVuZGVySXRlbUZvcm0iLCJzcGFjaW5nIiwiVGV4dCIsImxhYmVsIiwiZGlzYWJsZWQiLCJkaXJlY3Rpb24iLCJ0eXBlIiwicmVuZGVySXRlbXNUYWJsZSIsImNvbXBvbmVudCIsInN4IiwibWluV2lkdGgiLCJhcmlhLWxhYmVsIiwiYWxpZ24iLCJjb2xTcGFuIiwidmFyaWFudCIsImNvbG9yIiwibWFwIiwiZmllbGQiLCJqdXN0aWZ5Q29udGVudCIsInRpdGxlIiwic2l6ZSIsIm9uQ2xpY2siLCJpY29uIiwicmVmIiwicmVuZGVyUGFydHlEaWFsb2ciLCJvcGVuIiwib25DbG9zZSIsIm9uU2F2ZSIsInRpdGxlUHJlZml4IiwiaGFuZGxlVG90YWxDaGFuZ2UiLCJ2YWx1ZSIsInJlbmRlclRvdGFsc1NlY3Rpb24iLCJ0b3RhbCIsImJveFNoYWRvdyIsIm10IiwiYm9yZGVyU3R5bGUiLCJwIiwib25DaGFuZ2UiLCJlIiwicGFyc2VGbG9hdCIsInRhcmdldCIsImFjdGlvbiIsInN0YXJ0SWNvbiIsIm1iIiwiZGlzcGxheSIsImZ1bGxXaWR0aCIsIm1heFdpZHRoIiwicHQiLCJjb250ZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx\n"));

/***/ })

});