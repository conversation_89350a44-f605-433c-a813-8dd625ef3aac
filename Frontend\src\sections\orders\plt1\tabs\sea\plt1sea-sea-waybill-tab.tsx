// tabs/sea/plt1sea-master-bill-of-lading-tab.tsx
'use client';

import { useRef, useState, useEffect } from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';

import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { useTranslate } from 'src/locales';
import PLT1DocumentTabBase from '../plt1-document-tab-base';
import PLT1SeaWaybillOfLadingForm, {
  PLT1SeaWaybillLadingData as PLT1SeaWaybillData,
} from '../../forms/sea/plt1sea-master-bill-of-lading-form';
import { PLT1Order } from '../../types/plt1-details.types';

// ----------------------------------------------------------------------

interface PLT1SeaWaybillTabProps {
  t1OrderId: string;
  order?: PLT1Order;
  readOnly?: boolean;
}

interface FormValues {
  seaWaybills: PLT1SeaWaybillData[];
}

export default function PLT1SeaWaybillTab({
  t1OrderId,
  order,
  readOnly = false,
}: PLT1SeaWaybillTabProps) {
  const { t } = useTranslate();
  const { control } = useFormContext<FormValues>();

  // Try to restore the expanded state from sessionStorage
  const [expandedMBL, setExpandedMBL] = useState<number | null>(() => {
    const saved = sessionStorage.getItem(`plt1-expanded-mbl-${t1OrderId}`);
    return saved ? parseInt(saved, 10) : null;
  });

  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const deleteButtonRef = useRef<HTMLButtonElement>(null);

  const fieldArray = useFieldArray({
    control,
    name: 'seaWaybills'
  });

  // Save expanded state to sessionStorage whenever it changes
  useEffect(() => {
    if (expandedMBL !== null) {
      sessionStorage.setItem(`plt1-expanded-mbl-${t1OrderId}`, expandedMBL.toString());
    } else {
      sessionStorage.removeItem(`plt1-expanded-mbl-${t1OrderId}`);
    }
  }, [expandedMBL, t1OrderId]);

  const handleAddMBL = (): void => {
    const newMBL: PLT1SeaWaybillData = {
      id: undefined,
      seaWaybillNumber: '',
      containerNumber: '',
      containerType: '',
      grossWeight: 0,
      grossWeightUnit: 'kg',
      numberOfPieces: 0,
      volumeMeasurement: 0,
      shipper: {
        name: '',
        taxNumber: '',
        eoriNumber: '',
        address: {
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          postCode: '',
          country: '',
          countryCode: '',
        },
      },
      consignee: {
        name: '',
        taxNumber: '',
        eoriNumber: '',
        address: {
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          postCode: '',
          country: '',
          countryCode: '',
        },
      },
      plT1SeaOrderId: t1OrderId,
    };

    fieldArray.append(newMBL);
    setExpandedMBL(fieldArray.fields.length);
  };

  const handleToggleExpand = (index: number): void => {
    setExpandedMBL(expandedMBL === index ? null : index);
  };

  const handleOpenConfirm = (index: number): void => {
    setDeleteIndex(index);
    setOpenConfirm(true);
  };

  const handleCloseConfirm = (): void => {
    setOpenConfirm(false);
    if (deleteIndex !== null) {
      deleteButtonRef.current?.focus();
    }
  };

  const handleDeleteMBL = (): void => {
    if (deleteIndex !== null) {
      fieldArray.remove(deleteIndex);
      if (expandedMBL === deleteIndex) {
        setExpandedMBL(null);
      }
    }
    handleCloseConfirm();
  };

  // Render preview of the MBL item when collapsed
  const renderPreview = (mbl: any, _index: number) => (
    <Stack
      direction="row"
      spacing={2}
      sx={{
        px: 3,
        pb: 2,
        display: 'flex',
        flexWrap: 'wrap',
        '& > *': { mr: 3, mb: 1 },
      }}
    >
      {mbl.containerNumber && (
        <Typography variant="body2" color="text.secondary">
          {t('plt1.details.documents.seaWaybill.preview.containerNumber')}: {mbl.containerNumber}
        </Typography>
      )}

      {mbl.numberOfPieces !== undefined &&
        mbl.numberOfPieces !== null &&
        mbl.numberOfPieces > 0 && (
          <Typography variant="body2" color="text.secondary">
            {t('plt1.details.documents.seaWaybill.preview.numberOfPieces')}:{' '}
            {mbl.numberOfPieces}
          </Typography>
        )}

      {mbl.grossWeight !== undefined && mbl.grossWeight !== null && mbl.grossWeight > 0 && (
        <Typography variant="body2" color="text.secondary">
          {t('plt1.details.documents.seaWaybill.preview.grossWeight')}: {mbl.grossWeight}{' '}
          {mbl.grossWeightUnit}
        </Typography>
      )}
    </Stack>
  );

  // Render the form when expanded
  const renderForm = (index: number) => (
    <PLT1SeaWaybillOfLadingForm formPath="seaWaybills" index={index} readOnly={readOnly} />
  );

  // Render the title of each item
  const getItemTitle = (mbl: any) => (
    <Typography variant="subtitle1">
      {mbl.seaWaybillNumber || t('plt1.details.documents.seaWaybill.preview.newSWB')}
    </Typography>
  );

  return (
    <PLT1DocumentTabBase
      t1OrderId={t1OrderId}
      order={order}
      readOnly={readOnly}
      title={t('plt1.details.documents.seaWaybill.heading')}
      emptyTitle={t('plt1.details.documents.seaWaybill.noData')}
      emptyDescription={t('plt1.details.documents.seaWaybill.addYourFirst')}
      expandedIndex={expandedMBL}
      deleteIndex={deleteIndex}
      openConfirm={openConfirm}
      fieldArray={fieldArray}
      fieldArrayName="seaWaybills"
      onToggleExpand={handleToggleExpand}
      onOpenConfirm={handleOpenConfirm}
      onCloseConfirm={handleCloseConfirm}
      onDelete={handleDeleteMBL}
      onAdd={handleAddMBL}
      renderPreview={renderPreview}
      renderForm={renderForm}
      getItemTitle={getItemTitle}
    />
  );
}