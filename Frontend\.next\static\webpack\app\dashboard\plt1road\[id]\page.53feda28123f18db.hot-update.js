"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1road/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/plt1-road-document-tabs.tsx":
/*!**************************************************************!*\
  !*** ./src/sections/orders/plt1/plt1-road-document-tabs.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1RoadDocumentsTabs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material/Box */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _mui_material_Tab__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/Tab */ \"(app-pages-browser)/./node_modules/@mui/material/Tab/Tab.js\");\n/* harmony import */ var _mui_material_Tabs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/Tabs */ \"(app-pages-browser)/./node_modules/@mui/material/Tabs/Tabs.js\");\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material/Card */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material/Divider */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/CardHeader */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var _tabs_road_plt1_cmr_tab__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tabs/road/plt1-cmr-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/road/plt1-cmr-tab.tsx\");\n/* harmony import */ var _tabs_plt1_commercial_invoice_tab__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tabs/plt1-commercial-invoice-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-commercial-invoice-tab.tsx\");\n/* harmony import */ var _tabs_plt1_packing_list_tab__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tabs/plt1-packing-list-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-packing-list-tab.tsx\");\n/* harmony import */ var _tabs_plt1_notifications_arrival_tab__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./tabs/plt1-notifications-arrival-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-notifications-arrival-tab.tsx\");\n/* harmony import */ var _tabs_plt1_transit_tab__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./tabs/plt1-transit-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-transit-tab.tsx\");\n/* harmony import */ var _tabs_plt1_merchandise_positions_tab__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./tabs/plt1-merchandise-positions-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-merchandise-positions-tab.tsx\");\n// src/sections/plt1-road-order-details/plt1-road-document-tabs.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// ----------------------------------------------------------------------\nvar TabNames = /*#__PURE__*/ function(TabNames) {\n    TabNames[\"CMR\"] = \"cmr\";\n    TabNames[\"COMMERCIAL_INVOICES\"] = \"commercialInvoices\";\n    TabNames[\"PACKING_LISTS\"] = \"packingLists\";\n    TabNames[\"NOTIFICATIONS\"] = \"notifications\";\n    TabNames[\"TRANSIT_DOCUMENTS\"] = \"transitDocuments\";\n    TabNames[\"MERCHANDISE_POSITIONS\"] = \"merchandisePositions\";\n    return TabNames;\n}(TabNames || {});\nfunction PLT1RoadDocumentsTabs(param) {\n    let { t1OrderId, order, readOnly = false, reload } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    // Try to restore the expanded state from sessionStorage\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"PLT1RoadDocumentsTabs.useState\": ()=>{\n            const savedTab = sessionStorage.getItem(\"plt1-road-current-tab-\".concat(t1OrderId));\n            return savedTab && Object.values(TabNames).includes(savedTab) ? savedTab : \"cmr\";\n        }\n    }[\"PLT1RoadDocumentsTabs.useState\"]);\n    // Save tab selection to sessionStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1RoadDocumentsTabs.useEffect\": ()=>{\n            sessionStorage.setItem(\"plt1-road-current-tab-\".concat(t1OrderId), currentTab);\n        }\n    }[\"PLT1RoadDocumentsTabs.useEffect\"], [\n        currentTab,\n        t1OrderId\n    ]);\n    const handleChangeTab = (event, newValue)=>{\n        setCurrentTab(newValue);\n    };\n    const TABS = [\n        {\n            value: \"cmr\",\n            label: t('plt1.details.documents.tabs.cmr'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_road_plt1_cmr_tab__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 64,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"commercialInvoices\",\n            label: t('plt1.details.documents.tabs.commercialInvoices'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_commercial_invoice_tab__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 69,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"packingLists\",\n            label: t('plt1.details.documents.tabs.packingLists'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_packing_list_tab__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 74,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"notifications\",\n            label: t('plt1.details.documents.tabs.notificationsOfArrival'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_notifications_arrival_tab__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 79,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"transitDocuments\",\n            label: t('plt1.details.documents.tabs.transitDocuments'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_transit_tab__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 84,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"merchandisePositions\",\n            label: t('plt1.details.documents.tabs.merchandisePositions'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_merchandise_positions_tab__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly,\n                reload: reload\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 89,\n                columnNumber: 24\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                title: t('plt1.details.documents.heading'),\n                sx: {\n                    mb: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 95,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                sx: {\n                    borderStyle: 'dashed'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 99,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tabs__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                value: currentTab,\n                onChange: handleChangeTab,\n                sx: {\n                    px: 2.5,\n                    boxShadow: (theme)=>\"inset 0 -2px 0 0 \".concat(theme.palette.divider)\n                },\n                children: TABS.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tab__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        value: tab.value,\n                        label: tab.label\n                    }, tab.value, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 21\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 100,\n                columnNumber: 13\n            }, this),\n            TABS.map((tab)=>tab.value === currentTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: tab.component\n                }, tab.value, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 25\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n        lineNumber: 94,\n        columnNumber: 9\n    }, this);\n}\n_s(PLT1RoadDocumentsTabs, \"xZNw/UDA+Vc50l0DEB7kcPEGJZo=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate\n    ];\n});\n_c = PLT1RoadDocumentsTabs;\nvar _c;\n$RefreshReg$(_c, \"PLT1RoadDocumentsTabs\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/plt1-road-document-tabs.tsx\n"));

/***/ })

});