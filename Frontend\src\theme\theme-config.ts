import type { CommonColors } from '@mui/material/styles/createPalette';

import type { PaletteColorNoChannels } from './core/palette';
import type { ThemeDirection, ThemeColorScheme, ThemeCssVariables } from './types';

// ----------------------------------------------------------------------

type ThemeConfig = {
  classesPrefix: string;
  modeStorageKey: string;
  direction: ThemeDirection;
  defaultMode: ThemeColorScheme;
  cssVariables: ThemeCssVariables;
  fontFamily: Record<'primary' | 'secondary', string>;
  palette: Record<
    'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'error',
    PaletteColorNoChannels
  > & {
    common: Pick<CommonColors, 'black' | 'white'>;
    grey: Record<
      '50' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900',
      string
    >;
  };
};

export const themeConfig: ThemeConfig = {
  /** **************************************
   * Base
   *************************************** */
  direction: 'ltr',
  defaultMode: 'dark',
  modeStorageKey: 'theme-mode',
  classesPrefix: 'minimal',
  /** **************************************
   * Typography
   *************************************** */
  fontFamily: {
    primary: 'Helvetica',
    secondary: 'Nunito, sans-serif',
  },
  /** **************************************
   * Palette
   *************************************** */
  palette: {
    primary: {
      lighter: '#DDFDE0',
      light: '#98F7B4',
      main: '#53E79C',
      dark: '#29A680',
      darker: '#0F6E64',
      contrastText: '#06182d',
    },
    secondary: {
      lighter: '#D8E4FF',
      light: '#8AA9FF',
      main: '#3C67FF',
      dark: '#1E39B7',
      darker: '#0B197A',
      contrastText: '#FFFFFF',
    },
    info: {
      lighter: '#D0EDFF',
      light: '#73BEFF',
      main: '#167FFF',
      dark: '#0B49B7',
      darker: '#04237A',
      contrastText: '#FFFFFF',
    },
    success: {
      lighter: '#EEFCD5',
      light: '#B9EE80',
      main: '#6CC92E',
      dark: '#399017',
      darker: '#176008',
      contrastText: '#ffffff',
    },
    warning: {
      lighter: '#FFF6D1',
      light: '#FFD666',
      main: '#FFBA19',
      dark: '#B7790C',
      darker: '#7A4804',
      contrastText: '#1C252E',
    },
    error: {
      lighter: '#FFE9D5',
      light: '#FFAC82',
      main: '#FF9572',
      dark: '#B71D18',
      darker: '#7A0916',
      contrastText: '#FFFFFF',
    },
    grey: {
      '50': '#f1f2f4',
      '100': '#e2e6e9',
      '200': '#e0e6eb',
      '300': '#c2ccd6',
      '400': '#a3b3c2',
      '500': '#758ca3',
      '600': '#597da6',
      '700': '#52667a',
      '800': '#06182d',
      '900': '#021225',
    },
    common: { black: '#000000', white: '#FFFFFF' },
    
  },
  /** **************************************
   * Css variables
   *************************************** */
  cssVariables: {
    cssVarPrefix: '',
    colorSchemeSelector: 'data-color-scheme',
  },
};
