"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx":
/*!*******************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1PackingListForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/material/Grid2 */ \"(app-pages-browser)/./node_modules/@mui/material/Grid2/Grid2.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/custom-dialog */ \"(app-pages-browser)/./src/components/custom-dialog/index.ts\");\n/* harmony import */ var src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/components/party-address/hooks/usePartyAddressForm */ \"(app-pages-browser)/./src/components/party-address/hooks/usePartyAddressForm.tsx\");\n/* harmony import */ var src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/components/party-address/party-address-dialog */ \"(app-pages-browser)/./src/components/party-address/party-address-dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst DEFAULT_PACKED_ITEM = {\n    id: '',\n    name: '',\n    hsCode: '',\n    modelNumber: '',\n    quantity: 0,\n    itemNetWeight: 0,\n    itemNetWeightUnit: 'KGM',\n    itemGrossWeight: 0,\n    hsCodeHints: '',\n    packingListPositionId: ''\n};\nconst DEFAULT_POSITION = {\n    id: '',\n    positionNumber: 1,\n    commercialInvoiceNumber: '',\n    packageNetWeight: 0,\n    packageNetWeightUnit: 'KGM',\n    packageGrossWeight: 0,\n    packageGrossWeightUnit: 'KGM',\n    packagesNetWeight: 0,\n    packagesNetWeightUnit: 'KGM',\n    packagesGrossWeight: 0,\n    packagesGrossWeightUnit: 'KGM',\n    packageUnit: 'CTNS',\n    packageSize: '',\n    packageVolume: 0,\n    packageVolumeUnit: 'CBM',\n    numberOfPackages: 0,\n    packedItems: [],\n    packingListId: ''\n};\nconst DEFAULT_TOTAL = {\n    id: '',\n    shipmentOrPackingId: '',\n    totalQuantity: 0,\n    totalPackagesUnit: 'CTNS',\n    totalNumberOfPackages: 0,\n    totalNumberOfPallets: 0,\n    totalNetWeight: 0,\n    totalNetWeightUnit: 'KGM',\n    totalGrossWeight: 0,\n    totalGrossWeightUnit: 'KGM',\n    totalVolume: 0,\n    totalVolumeMeasurementUnit: 'CBM',\n    packingListId: ''\n};\nfunction PLT1PackingListForm(param) {\n    let { formPath, index, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    const { control, getValues, watch, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext)();\n    const fieldPrefix = index !== undefined ? \"\".concat(formPath, \".\").concat(index) : formPath;\n    const positionsFieldName = \"\".concat(fieldPrefix, \".packingListPositions\");\n    const totalFieldName = \"\".concat(fieldPrefix, \".listTotal\");\n    // Use the party address form hook\n    const { openPartyDialog, currentPartyType, handleClosePartyDialog, handleUpdateParty } = (0,src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm)({\n        fieldPrefix\n    });\n    // State for position dialog\n    const [openPositionDialog, setOpenPositionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPositionIndex, setCurrentPositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletePositionIndex, setDeletePositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize listTotal if it doesn't exist\n    const listTotal = watch(totalFieldName);\n    if (!listTotal) {\n        setValue(totalFieldName, DEFAULT_TOTAL);\n    }\n    // UseFieldArray hook to manage the positions array\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray)({\n        control,\n        name: positionsFieldName\n    });\n    // UseFieldArray for packed items within the current position\n    const packedItemsFieldName = currentPositionIndex !== null ? \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packedItems\") : '';\n    const packedItemsFieldArray = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray)({\n        control,\n        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packedItems\")\n    });\n    // Handle opening the position dialog for adding a new position\n    const handleAddPosition = ()=>{\n        setCurrentPositionIndex(null);\n        // Add a new position with default values\n        const newPosition = {\n            ...DEFAULT_POSITION,\n            positionNumber: fields.length + 1\n        };\n        append(newPosition);\n        setCurrentPositionIndex(fields.length); // Set to the new index\n        setOpenPositionDialog(true);\n    };\n    // Handle opening the position dialog for editing an existing position\n    const handleEditPosition = (index)=>{\n        setCurrentPositionIndex(index);\n        setOpenPositionDialog(true);\n    };\n    // Handle closing the position dialog\n    const handleClosePositionDialog = ()=>{\n        setOpenPositionDialog(false);\n        // If we were adding a new position and user cancels, remove the empty position\n        if (currentPositionIndex !== null && currentPositionIndex === fields.length - 1) {\n            const position = getValues(\"\".concat(positionsFieldName, \".\").concat(currentPositionIndex));\n            // Check if it's an empty position (all fields are default values)\n            const isEmpty = !position.commercialInvoiceNumber && position.numberOfPackages === 0 && position.packageNetWeight === 0 && position.packageGrossWeight === 0 && position.packageVolume === 0 && (!position.packedItems || position.packedItems.length === 0);\n            if (isEmpty) {\n                remove(currentPositionIndex);\n            }\n        }\n        setCurrentPositionIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeletePositionIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deletePositionIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting a position\n    const handleDeletePosition = ()=>{\n        if (deletePositionIndex !== null) {\n            remove(deletePositionIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving a position (just close the dialog since form is already updated)\n    const handleSavePosition = ()=>{\n        handleClosePositionDialog();\n    };\n    // Handle adding a new packed item\n    const handleAddPackedItem = ()=>{\n        if (currentPositionIndex !== null) {\n            const newPackedItem = {\n                ...DEFAULT_PACKED_ITEM\n            };\n            packedItemsFieldArray.append(newPackedItem);\n        }\n    };\n    // Handle removing a packed item\n    const handleRemovePackedItem = (itemIndex)=>{\n        if (currentPositionIndex !== null) {\n            packedItemsFieldArray.remove(itemIndex);\n        }\n    };\n    // Render the position form in the dialog\n    const renderPositionForm = ()=>{\n        if (currentPositionIndex === null) return null;\n        // Render inline editable table for packed items\n        const renderPackedItemsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        direction: \"row\",\n                        justifyContent: \"space-between\",\n                        alignItems: \"center\",\n                        sx: {\n                            mb: 2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                variant: \"h6\",\n                                children: t('plt1.details.documents.packingList.packedItem.title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                variant: \"outlined\",\n                                size: \"small\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                    icon: \"eva:plus-fill\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 24\n                                }, void 0),\n                                onClick: handleAddPackedItem,\n                                disabled: readOnly,\n                                children: t('plt1.details.documents.packingList.packedItem.addNew')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        component: _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                        sx: {\n                            maxHeight: 400\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            size: \"small\",\n                            stickyHeader: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.name')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.hsCode')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.modelNumber')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.quantity')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.itemNetWeight')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                align: \"right\",\n                                                children: t('common.actions')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    children: packedItemsFieldArray.fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            colSpan: 6,\n                                            align: \"center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"body2\",\n                                                color: \"text.secondary\",\n                                                children: t('plt1.details.documents.packingList.packedItem.noItems')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 17\n                                    }, this) : packedItemsFieldArray.fields.map((field, itemIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 150\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".name\"),\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 120\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".hsCode\"),\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 120\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".modelNumber\"),\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 100\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".quantity\"),\n                                                        type: \"number\",\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 120\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".itemNetWeight\"),\n                                                        type: \"number\",\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    align: \"right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        title: t('common.delete'),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            size: \"small\",\n                                                            color: \"error\",\n                                                            onClick: ()=>handleRemovePackedItem(itemIndex),\n                                                            disabled: readOnly,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                                icon: \"eva:trash-2-outline\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, field.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            variant: \"h6\",\n                            sx: {\n                                mb: 2\n                            },\n                            children: t('plt1.details.documents.packingList.position.details')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".positionNumber\"),\n                                        label: t('plt1.details.documents.packingList.position.positionNumber'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".commercialInvoiceNumber\"),\n                                        label: t('plt1.details.documents.packingList.position.commercialInvoiceNumber'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".numberOfPackages\"),\n                                        label: t('plt1.details.documents.packingList.position.numberOfPackages'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageUnit\"),\n                                        label: t('plt1.details.documents.packingList.position.packageUnit'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageSize\"),\n                                        label: t('plt1.details.documents.packingList.position.packageSize'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageVolume\"),\n                                        label: t('plt1.details.documents.packingList.position.packageVolume'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.position.packageNetWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.position.packageGrossWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packagesNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.position.packagesNetWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packagesGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.position.packagesGrossWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, this),\n                renderPackedItemsTable()\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 377,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the positions table\n    const renderPositionsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            component: _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"packing list positions table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.positionNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.commercialInvoiceNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.numberOfPackages')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.packageNetWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.packageGrossWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        children: fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                colSpan: 6,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.packingList.position.noPositions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 13\n                        }, this) : fields.map((field, index)=>{\n                            const position = getValues(\"\".concat(positionsFieldName, \".\").concat(index)) || {};\n                            console.log(index);\n                            var _position_numberOfPackages, _position_packageNetWeight, _position_packageGrossWeight;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: position.positionNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: position.commercialInvoiceNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: (_position_numberOfPackages = position.numberOfPackages) !== null && _position_numberOfPackages !== void 0 ? _position_numberOfPackages : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: [\n                                            (_position_packageNetWeight = position.packageNetWeight) !== null && _position_packageNetWeight !== void 0 ? _position_packageNetWeight : '-',\n                                            \" \",\n                                            position.packageNetWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: [\n                                            (_position_packageGrossWeight = position.packageGrossWeight) !== null && _position_packageGrossWeight !== void 0 ? _position_packageGrossWeight : '-',\n                                            \" \",\n                                            position.packageGrossWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditPosition(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deletePositionIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 488,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 487,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        spacing: 3,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        title: t('plt1.details.documents.packingList.form.positionsTitle'),\n                        action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"contained\",\n                            size: \"small\",\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                icon: \"eva:plus-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: handleAddPosition,\n                            sx: {\n                                mb: 2\n                            },\n                            disabled: readOnly,\n                            children: t('plt1.details.documents.packingList.position.addNew')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: [\n                            renderPositionsTable(),\n                            fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    mt: 2,\n                                    display: 'flex',\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    variant: \"soft\",\n                                    color: \"primary\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                        icon: \"eva:plus-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    onClick: handleAddPosition,\n                                    disabled: readOnly,\n                                    children: t('plt1.details.documents.packingList.position.addNew')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 577,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 560,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        title: t('plt1.details.documents.packingList.form.totalsTitle')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 598,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 599,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalQuantity\"),\n                                        label: t('plt1.details.documents.packingList.total.totalQuantity'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPackages\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPackages'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalPackagesUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalPackagesUnit'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolume\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolume'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 601,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 600,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 597,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                open: openPositionDialog,\n                onClose: handleClosePositionDialog,\n                fullWidth: true,\n                maxWidth: \"lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        children: currentPositionIndex === null ? t('plt1.details.documents.packingList.position.addNew') : t('plt1.details.documents.packingList.position.edit')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            sx: {\n                                pt: 3\n                            },\n                            children: renderPositionForm()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                onClick: handleClosePositionDialog,\n                                color: \"inherit\",\n                                children: t('common.cancel')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 664,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                onClick: handleSavePosition,\n                                variant: \"contained\",\n                                children: t('common.save')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 667,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 663,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 654,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__.ConfirmDialog, {\n                open: openDeleteConfirm,\n                onClose: handleCloseDeleteConfirm,\n                title: t('plt1.details.documents.packingList.position.confirmDeleteDialog.title'),\n                content: t('plt1.details.documents.packingList.position.confirmDeleteDialog.content'),\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    variant: \"contained\",\n                    color: \"error\",\n                    onClick: handleDeletePosition,\n                    children: t('common.delete')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 680,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 674,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                open: openPartyDialog,\n                onClose: handleClosePartyDialog,\n                onSave: handleUpdateParty,\n                formPath: fieldPrefix,\n                currentPartyType: currentPartyType,\n                readOnly: readOnly,\n                titlePrefix: \"plt1.details.documents.packingList.partyAddress\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 687,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n        lineNumber: 558,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1PackingListForm, \"jbyTJ/5RFz4AqmM1furakeA1SSM=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext,\n        src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray\n    ];\n});\n_c = PLT1PackingListForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1PackingListForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx\n"));

/***/ })

});