import type { DialogProps } from '@mui/material/Dialog';
import { useState, useEffect, useCallback } from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import TextField from '@mui/material/TextField';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';
import { LoadingButton } from '@mui/lab';
import { Upload } from 'src/components/upload';
import { Iconify } from 'src/components/iconify';
import { useTranslate } from 'src/locales';
import { toast } from 'src/components/snackbar';
import { axiosInstance, endpoints } from 'src/lib/axios';
import { PLT1_ORDER_TYPE, PLT1OrderType } from './plt1-order-type';

// ----------------------------------------------------------------------
type Props = DialogProps & {
    open: boolean;
    title?: string;
    folderName?: string;
    onClose: () => void;
    onCreate?: () => void;
    onUpdate?: () => void;
    onSuccess?: () => void;
    onChangeFolderName?: (event: React.ChangeEvent<HTMLInputElement>) => void;
};

export function PLT1BeginningDialog({
    open,
    onClose,
    onCreate,
    onUpdate,
    onSuccess,
    folderName,
    onChangeFolderName,
    title = 'Upload files',
    ...other
}: Props) {
    const { t } = useTranslate();
    const [files, setFiles] = useState<File[]>([]);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [orderType, setOrderType] = useState<PLT1OrderType>(PLT1_ORDER_TYPE.Air);

    useEffect(() => {
        if (!open) {
            setFiles([]);
            setIsSubmitting(false);
            setOrderType(PLT1_ORDER_TYPE.Air); // Reset to default on close
        }
    }, [open]);

    const handleDrop = useCallback(
        (acceptedFiles: File[]) => {
            setFiles([...files, ...acceptedFiles]);
        },
        [files]
    );

    const handleOrderTypeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setOrderType(event.target.value as PLT1OrderType);
    };

    const getEndpointForOrderType = (type: PLT1OrderType) => {
        switch (type) {
            case PLT1_ORDER_TYPE.Air:
                return '/plt1air/begin-order';
            case PLT1_ORDER_TYPE.Road:
                return '/plt1road/begin-order';
            case PLT1_ORDER_TYPE.Sea:
                return '/plt1sea/begin-order';
            default:
                return '/plt1air/begin-order'; // Fallback to Air
        }
    };

    const handleUpload = async () => {
        try {
            setIsSubmitting(true);

            const formData = new FormData();

            files.forEach((file) => {
                formData.append('Files', file);
            });

            // Use the endpoint based on selected order type
            const endpoint = getEndpointForOrderType(orderType);
            const response = await axiosInstance.post(endpoint, formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });

            if (response.status === 200) {
                toast.success(t('plt1.beginningDialog.uploadSuccess'));

                if (onSuccess) {
                    onSuccess();
                }
            }

            onClose();
        } catch (error) {
            // Show error notification
            toast.error(t('plt1.beginningDialog.uploadError'));
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleRemoveFile = (inputFile: File | string) => {
        const filtered = files.filter((file) => file !== inputFile);
        setFiles(filtered);
    };

    const handleRemoveAllFiles = () => {
        setFiles([]);
    };

    return (
        <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose} {...other}>
            <DialogTitle sx={[(theme) => ({ p: theme.spacing(3, 3, 2, 3) })]}>{title}</DialogTitle>
            <DialogContent dividers sx={{ pt: 3, pb: 0, border: 'none' }}>
                {(onCreate || onUpdate) && (
                    <TextField
                        fullWidth
                        label="Folder name"
                        value={folderName}
                        onChange={onChangeFolderName}
                        sx={{ mb: 3 }}
                    />
                )}

                {/* Order Type Selection */}
                <FormControl component="fieldset" sx={{ mb: 3, width: '100%' }}>
                    <FormLabel component="legend">{t('plt1.beginningDialog.orderType')}:</FormLabel>
                    <RadioGroup
                        aria-label="order-type"
                        name="order-type"
                        value={orderType}
                        onChange={handleOrderTypeChange}
                        row
                    >
                        <FormControlLabel
                            value={PLT1_ORDER_TYPE.Air}
                            control={<Radio />}
                            label={(
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <Iconify icon="mingcute:airplane-line" width={20} height={20} sx={{ mr: 1 }} />
                                    {t('plt1.type.air')}
                                </Box>
                            )}
                        />
                        <FormControlLabel
                            value={PLT1_ORDER_TYPE.Road}
                            control={<Radio />}
                            label={(
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <Iconify icon="mingcute:truck-line" width={20} height={20} sx={{ mr: 1 }} />
                                    {t('plt1.type.road')}
                                </Box>
                            )}
                        />
                        <FormControlLabel
                            value={PLT1_ORDER_TYPE.Sea}
                            control={<Radio />}
                            label={(
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <Iconify icon="mingcute:anchor-line" width={20} height={20} sx={{ mr: 1 }} />
                                    {t('plt1.type.sea')}
                                </Box>
                            )}
                        />
                    </RadioGroup>
                </FormControl>

                <Upload
                    multiple
                    value={files}
                    onDrop={handleDrop}
                    onRemove={handleRemoveFile}
                    helperText={ 
                        orderType === PLT1_ORDER_TYPE.Air 
                            ? t('plt1.beginningDialog.uploadHelperAir') 
                            : orderType === PLT1_ORDER_TYPE.Sea 
                                ? t('plt1.beginningDialog.uploadHelperSea') 
                                : t('plt1.beginningDialog.uploadHelperRoad') 
                    }
                />
            </DialogContent>

            <DialogActions>
                <LoadingButton
                    loading={isSubmitting}
                    disabled={files.length === 0}
                    variant="contained"
                    startIcon={<Iconify icon="eva:cloud-upload-fill" />}
                    onClick={handleUpload}
                >
                    {t('plt1.beginningDialog.upload')}
                </LoadingButton>

                {!!files.length && (
                    <Button
                        variant="outlined"
                        color="inherit"
                        onClick={handleRemoveAllFiles}
                        disabled={isSubmitting}
                    >
                        {t('plt1.beginningDialog.removeAll')}
                    </Button>
                )}

                {(onCreate || onUpdate) && (
                    <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: 'flex-end' }}>
                        <Button
                            variant="soft"
                            onClick={onCreate || onUpdate}
                            disabled={isSubmitting}
                        >
                            {onUpdate ? 'Save' : 'Create'}
                        </Button>
                    </Box>
                )}
            </DialogActions>
        </Dialog>
    );
}