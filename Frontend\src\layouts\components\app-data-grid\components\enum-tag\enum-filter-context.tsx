import React, { createContext, useContext } from 'react';

interface EnumFilterContextValue {
  options: Record<string, Array<{ value: string; label: string }>>;
}

const EnumFilterContext = createContext<EnumFilterContextValue>({ options: {} });

interface EnumFilterProviderProps {
  children: React.ReactNode;
  field: string;
  options: Array<{ value: string; label: string }>;
}

export const EnumFilterProvider = ({ 
  children, 
  field,
  options 
}: EnumFilterProviderProps) => {
  const parentContext = useContext(EnumFilterContext);
  
  const value = {
    options: {
      ...parentContext.options,
      [field]: options
    }
  };

  return (
    <EnumFilterContext.Provider value={value}>
      {children}
    </EnumFilterContext.Provider>
  );
};

export const useEnumFilterContext = (field: string) => {
  const context = useContext(EnumFilterContext);
  return { options: context.options[field] || [] };
};