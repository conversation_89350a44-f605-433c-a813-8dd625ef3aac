'use client';

import { useRef, useState, useEffect } from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';

import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { useTranslate } from 'src/locales';
import { formatters } from 'src/lib/formatters';
import PLT1DocumentTabBase from './plt1-document-tab-base';
import { PLT1Order } from '../types/plt1-details.types';
import PLT1NotificationOfArrivalForm, {
  T1NotificationOfArrivalData as PLT1NotificationOfArrivalData,
} from '../forms/plt1-notifications-of-arrival-form';

// ----------------------------------------------------------------------

interface PLT1NotificationsOfArrivalTabProps {
  t1OrderId: string;
  order?: PLT1Order;
  readOnly?: boolean;
}

interface FormValues {
  notificationsOfArrivals: PLT1NotificationOfArrivalData[];
}

export default function PLT1NotificationsOfArrivalTab({
  t1OrderId,
  order,
  readOnly = false,
}: PLT1NotificationsOfArrivalTabProps) {
  const { t } = useTranslate();
  const { control } = useFormContext<FormValues>();

  // Try to restore the expanded state from sessionStorage
  const [expandedNotification, setExpandedNotification] = useState<number | null>(() => {
    const saved = sessionStorage.getItem(`plt1-expanded-notification-${t1OrderId}`);
    return saved ? parseInt(saved, 10) : null;
  });

  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const deleteButtonRef = useRef<HTMLButtonElement>(null);

  const fieldArray = useFieldArray({
    control,
    name: 'notificationsOfArrivals',
  });

  // Save expanded state to sessionStorage whenever it changes
  useEffect(() => {
    if (expandedNotification !== null) {
      sessionStorage.setItem(
        `plt1-expanded-notification-${t1OrderId}`,
        expandedNotification.toString()
      );
    } else {
      sessionStorage.removeItem(`plt1-expanded-notification-${t1OrderId}`);
    }
  }, [expandedNotification, t1OrderId]);

  const handleAddNotification = (): void => {
    const newNotification: PLT1NotificationOfArrivalData = {
      id: undefined,
      identificationNumber: '',
      arrivalDate: null,
      flightNumber: '',
      customsOrigin: '',
      shipper: {
        name: '',
        taxNumber: '',
        eoriNumber: '',
        address: {
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          postCode: '',
          country: '',
          countryCode: '',
        },
      },
      description: '',
      storageLocationCode: '',
      dskTemporaryStorageNumber: '',
      remarks: '',
      referenceNumber: '',
      pieces: 0,
      weight: 0,
      chargedWeight: 0,
      unitOfWeight: 'kg',
      t1OrderId,
    };

    fieldArray.append(newNotification);
    setExpandedNotification(fieldArray.fields.length);
  };

  const handleToggleExpand = (index: number): void => {
    setExpandedNotification(expandedNotification === index ? null : index);
  };

  const handleOpenConfirm = (index: number): void => {
    setDeleteIndex(index);
    setOpenConfirm(true);
  };

  const handleCloseConfirm = (): void => {
    setOpenConfirm(false);
    if (deleteIndex !== null) {
      deleteButtonRef.current?.focus();
    }
  };

  const handleDeleteNotification = (): void => {
    if (deleteIndex !== null) {
      fieldArray.remove(deleteIndex);
      if (expandedNotification === deleteIndex) {
        setExpandedNotification(null);
      }
    }
    handleCloseConfirm();
  };

  // Render preview of the notification item when collapsed
  const renderPreview = (notification: any, _index: number) => (
    <Stack
      direction="row"
      spacing={2}
      sx={{
        px: 3,
        pb: 2,
        display: 'flex',
        flexWrap: 'wrap',
        '& > *': { mr: 3, mb: 1 },
      }}
    >
      {notification.pieces !== undefined &&
        notification.pieces !== null &&
        notification.pieces > 0 && (
          <Typography variant="body2" color="text.secondary">
            {t('plt1.details.documents.notificationOfArrival.preview.pieces')}:{' '}
            {notification.pieces}
          </Typography>
        )}

      {notification.weight !== undefined &&
        notification.weight !== null &&
        notification.weight > 0 && (
          <Typography variant="body2" color="text.secondary">
            {t('plt1.details.documents.notificationOfArrival.preview.weight')}:{' '}
            {notification.weight} {notification.unitOfWeight || 'kg'}
          </Typography>
        )}

      {notification.arrivalDate && (
        <Typography variant="body2" color="text.secondary">
          {t('plt1.details.documents.notificationOfArrival.preview.arrivalDate')}:{' '}
          {formatters.plainDate(notification.arrivalDate)}
        </Typography>
      )}
    </Stack>
  );

  // Render the form when expanded
  const renderForm = (index: number) => (
    <PLT1NotificationOfArrivalForm
      formPath="notificationsOfArrivals"
      index={index}
      readOnly={readOnly}
    />
  );

  // Render the title of each item
  const getItemTitle = (notification: any) => (
    <Stack direction="row" alignItems="center" spacing={1}>
      <Typography variant="subtitle1">
        {notification.identificationNumber ||
          t('plt1.details.documents.notificationOfArrival.preview.newNotification')}
      </Typography>

      {notification.flightNumber && (
        <Typography variant="body2" color="text.secondary">
          ({t('plt1.details.documents.notificationOfArrival.preview.flight')}:{' '}
          {notification.flightNumber})
        </Typography>
      )}
    </Stack>
  );

  return (
    <PLT1DocumentTabBase
      t1OrderId={t1OrderId}
      order={order}
      readOnly={readOnly}
      title={t('plt1.details.documents.notificationOfArrival.heading')}
      emptyTitle={t('plt1.details.documents.notificationOfArrival.noData')}
      emptyDescription={t('plt1.details.documents.notificationOfArrival.addYourFirst')}
      expandedIndex={expandedNotification}
      deleteIndex={deleteIndex}
      openConfirm={openConfirm}
      fieldArray={fieldArray}
      fieldArrayName="notificationsOfArrivals"
      onToggleExpand={handleToggleExpand}
      onOpenConfirm={handleOpenConfirm}
      onCloseConfirm={handleCloseConfirm}
      onDelete={handleDeleteNotification}
      onAdd={handleAddNotification}
      renderPreview={renderPreview}
      renderForm={renderForm}
      getItemTitle={getItemTitle}
    />
  );
}
