"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/linkifyjs";
exports.ids = ["vendor-chunks/linkifyjs"];
exports.modules = {

/***/ "(ssr)/./node_modules/linkifyjs/dist/linkify.es.js":
/*!***************************************************!*\
  !*** ./node_modules/linkifyjs/dist/linkify.es.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultiToken: () => (/* binding */ MultiToken),\n/* harmony export */   Options: () => (/* binding */ Options),\n/* harmony export */   State: () => (/* binding */ State),\n/* harmony export */   createTokenClass: () => (/* binding */ createTokenClass),\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   init: () => (/* binding */ init),\n/* harmony export */   multi: () => (/* binding */ multi),\n/* harmony export */   options: () => (/* binding */ options),\n/* harmony export */   regexp: () => (/* binding */ regexp),\n/* harmony export */   registerCustomProtocol: () => (/* binding */ registerCustomProtocol),\n/* harmony export */   registerPlugin: () => (/* binding */ registerPlugin),\n/* harmony export */   registerTokenPlugin: () => (/* binding */ registerTokenPlugin),\n/* harmony export */   reset: () => (/* binding */ reset),\n/* harmony export */   stringToArray: () => (/* binding */ stringToArray),\n/* harmony export */   test: () => (/* binding */ test),\n/* harmony export */   text: () => (/* binding */ multi),\n/* harmony export */   tokenize: () => (/* binding */ tokenize)\n/* harmony export */ });\n// THIS FILE IS AUTOMATICALLY GENERATED DO NOT EDIT DIRECTLY\n// See update-tlds.js for encoding/decoding format\n// https://data.iana.org/TLD/tlds-alpha-by-domain.txt\nconst encodedTlds = 'aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2ntley5rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6logistics9properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3ncaster6d0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2psy3ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2';\n// Internationalized domain names containing non-ASCII\nconst encodedUtlds = 'ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2';\n\n/**\n * @template A\n * @template B\n * @param {A} target\n * @param {B} properties\n * @return {A & B}\n */\nconst assign = (target, properties) => {\n  for (const key in properties) {\n    target[key] = properties[key];\n  }\n  return target;\n};\n\n/**\n * Finite State Machine generation utilities\n */\n\n/**\n * @template T\n * @typedef {{ [group: string]: T[] }} Collections\n */\n\n/**\n * @typedef {{ [group: string]: true }} Flags\n */\n\n// Keys in scanner Collections instances\nconst numeric = 'numeric';\nconst ascii = 'ascii';\nconst alpha = 'alpha';\nconst asciinumeric = 'asciinumeric';\nconst alphanumeric = 'alphanumeric';\nconst domain = 'domain';\nconst emoji = 'emoji';\nconst scheme = 'scheme';\nconst slashscheme = 'slashscheme';\nconst whitespace = 'whitespace';\n\n/**\n * @template T\n * @param {string} name\n * @param {Collections<T>} groups to register in\n * @returns {T[]} Current list of tokens in the given collection\n */\nfunction registerGroup(name, groups) {\n  if (!(name in groups)) {\n    groups[name] = [];\n  }\n  return groups[name];\n}\n\n/**\n * @template T\n * @param {T} t token to add\n * @param {Collections<T>} groups\n * @param {Flags} flags\n */\nfunction addToGroups(t, flags, groups) {\n  if (flags[numeric]) {\n    flags[asciinumeric] = true;\n    flags[alphanumeric] = true;\n  }\n  if (flags[ascii]) {\n    flags[asciinumeric] = true;\n    flags[alpha] = true;\n  }\n  if (flags[asciinumeric]) {\n    flags[alphanumeric] = true;\n  }\n  if (flags[alpha]) {\n    flags[alphanumeric] = true;\n  }\n  if (flags[alphanumeric]) {\n    flags[domain] = true;\n  }\n  if (flags[emoji]) {\n    flags[domain] = true;\n  }\n  for (const k in flags) {\n    const group = registerGroup(k, groups);\n    if (group.indexOf(t) < 0) {\n      group.push(t);\n    }\n  }\n}\n\n/**\n * @template T\n * @param {T} t token to check\n * @param {Collections<T>} groups\n * @returns {Flags} group flags that contain this token\n */\nfunction flagsForToken(t, groups) {\n  const result = {};\n  for (const c in groups) {\n    if (groups[c].indexOf(t) >= 0) {\n      result[c] = true;\n    }\n  }\n  return result;\n}\n\n/**\n * @template T\n * @typedef {null | T } Transition\n */\n\n/**\n * Define a basic state machine state. j is the list of character transitions,\n * jr is the list of regex-match transitions, jd is the default state to\n * transition to t is the accepting token type, if any. If this is the terminal\n * state, then it does not emit a token.\n *\n * The template type T represents the type of the token this state accepts. This\n * should be a string (such as of the token exports in `text.js`) or a\n * MultiToken subclass (from `multi.js`)\n *\n * @template T\n * @param {T} [token] Token that this state emits\n */\nfunction State(token = null) {\n  // this.n = null; // DEBUG: State name\n  /** @type {{ [input: string]: State<T> }} j */\n  this.j = {}; // IMPLEMENTATION 1\n  // this.j = []; // IMPLEMENTATION 2\n  /** @type {[RegExp, State<T>][]} jr */\n  this.jr = [];\n  /** @type {?State<T>} jd */\n  this.jd = null;\n  /** @type {?T} t */\n  this.t = token;\n}\n\n/**\n * Scanner token groups\n * @type Collections<string>\n */\nState.groups = {};\nState.prototype = {\n  accepts() {\n    return !!this.t;\n  },\n  /**\n   * Follow an existing transition from the given input to the next state.\n   * Does not mutate.\n   * @param {string} input character or token type to transition on\n   * @returns {?State<T>} the next state, if any\n   */\n  go(input) {\n    const state = this;\n    const nextState = state.j[input];\n    if (nextState) {\n      return nextState;\n    }\n    for (let i = 0; i < state.jr.length; i++) {\n      const regex = state.jr[i][0];\n      const nextState = state.jr[i][1]; // note: might be empty to prevent default jump\n      if (nextState && regex.test(input)) {\n        return nextState;\n      }\n    }\n    // Nowhere left to jump! Return default, if any\n    return state.jd;\n  },\n  /**\n   * Whether the state has a transition for the given input. Set the second\n   * argument to true to only look for an exact match (and not a default or\n   * regular-expression-based transition)\n   * @param {string} input\n   * @param {boolean} exactOnly\n   */\n  has(input, exactOnly = false) {\n    return exactOnly ? input in this.j : !!this.go(input);\n  },\n  /**\n   * Short for \"transition all\"; create a transition from the array of items\n   * in the given list to the same final resulting state.\n   * @param {string | string[]} inputs Group of inputs to transition on\n   * @param {Transition<T> | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   */\n  ta(inputs, next, flags, groups) {\n    for (let i = 0; i < inputs.length; i++) {\n      this.tt(inputs[i], next, flags, groups);\n    }\n  },\n  /**\n   * Short for \"take regexp transition\"; defines a transition for this state\n   * when it encounters a token which matches the given regular expression\n   * @param {RegExp} regexp Regular expression transition (populate first)\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   * @returns {State<T>} taken after the given input\n   */\n  tr(regexp, next, flags, groups) {\n    groups = groups || State.groups;\n    let nextState;\n    if (next && next.j) {\n      nextState = next;\n    } else {\n      // Token with maybe token groups\n      nextState = new State(next);\n      if (flags && groups) {\n        addToGroups(next, flags, groups);\n      }\n    }\n    this.jr.push([regexp, nextState]);\n    return nextState;\n  },\n  /**\n   * Short for \"take transitions\", will take as many sequential transitions as\n   * the length of the given input and returns the\n   * resulting final state.\n   * @param {string | string[]} input\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   * @returns {State<T>} taken after the given input\n   */\n  ts(input, next, flags, groups) {\n    let state = this;\n    const len = input.length;\n    if (!len) {\n      return state;\n    }\n    for (let i = 0; i < len - 1; i++) {\n      state = state.tt(input[i]);\n    }\n    return state.tt(input[len - 1], next, flags, groups);\n  },\n  /**\n   * Short for \"take transition\", this is a method for building/working with\n   * state machines.\n   *\n   * If a state already exists for the given input, returns it.\n   *\n   * If a token is specified, that state will emit that token when reached by\n   * the linkify engine.\n   *\n   * If no state exists, it will be initialized with some default transitions\n   * that resemble existing default transitions.\n   *\n   * If a state is given for the second argument, that state will be\n   * transitioned to on the given input regardless of what that input\n   * previously did.\n   *\n   * Specify a token group flags to define groups that this token belongs to.\n   * The token will be added to corresponding entires in the given groups\n   * object.\n   *\n   * @param {string} input character, token type to transition on\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of groups\n   * @returns {State<T>} taken after the given input\n   */\n  tt(input, next, flags, groups) {\n    groups = groups || State.groups;\n    const state = this;\n\n    // Check if existing state given, just a basic transition\n    if (next && next.j) {\n      state.j[input] = next;\n      return next;\n    }\n    const t = next;\n\n    // Take the transition with the usual default mechanisms and use that as\n    // a template for creating the next state\n    let nextState,\n      templateState = state.go(input);\n    if (templateState) {\n      nextState = new State();\n      assign(nextState.j, templateState.j);\n      nextState.jr.push.apply(nextState.jr, templateState.jr);\n      nextState.jd = templateState.jd;\n      nextState.t = templateState.t;\n    } else {\n      nextState = new State();\n    }\n    if (t) {\n      // Ensure newly token is in the same groups as the old token\n      if (groups) {\n        if (nextState.t && typeof nextState.t === 'string') {\n          const allFlags = assign(flagsForToken(nextState.t, groups), flags);\n          addToGroups(t, allFlags, groups);\n        } else if (flags) {\n          addToGroups(t, flags, groups);\n        }\n      }\n      nextState.t = t; // overwrite anything that was previously there\n    }\n    state.j[input] = nextState;\n    return nextState;\n  }\n};\n\n// Helper functions to improve minification (not exported outside linkifyjs module)\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string | string[]} input\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst ta = (state, input, next, flags, groups) => state.ta(input, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {RegExp} regexp\n * @param {T | State<T>} [next]\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst tr = (state, regexp, next, flags, groups) => state.tr(regexp, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string | string[]} input\n * @param {T | State<T>} [next]\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst ts = (state, input, next, flags, groups) => state.ts(input, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string} input\n * @param {T | State<T>} [next]\n * @param {Collections<T>} [groups]\n * @param {Flags} [flags]\n */\nconst tt = (state, input, next, flags, groups) => state.tt(input, next, flags, groups);\n\n/******************************************************************************\nText Tokens\nIdentifiers for token outputs from the regexp scanner\n******************************************************************************/\n\n// A valid web domain token\nconst WORD = 'WORD'; // only contains a-z\nconst UWORD = 'UWORD'; // contains letters other than a-z, used for IDN\nconst ASCIINUMERICAL = 'ASCIINUMERICAL'; // contains a-z, 0-9\nconst ALPHANUMERICAL = 'ALPHANUMERICAL'; // contains numbers and letters other than a-z, used for IDN\n\n// Special case of word\nconst LOCALHOST = 'LOCALHOST';\n\n// Valid top-level domain, special case of WORD (see tlds.js)\nconst TLD = 'TLD';\n\n// Valid IDN TLD, special case of UWORD (see tlds.js)\nconst UTLD = 'UTLD';\n\n// The scheme portion of a web URI protocol. Supported types include: `mailto`,\n// `file`, and user-defined custom protocols. Limited to schemes that contain\n// only letters\nconst SCHEME = 'SCHEME';\n\n// Similar to SCHEME, except makes distinction for schemes that must always be\n// followed by `://`, not just `:`. Supported types include `http`, `https`,\n// `ftp`, `ftps`\nconst SLASH_SCHEME = 'SLASH_SCHEME';\n\n// Any sequence of digits 0-9\nconst NUM = 'NUM';\n\n// Any number of consecutive whitespace characters that are not newline\nconst WS = 'WS';\n\n// New line (unix style)\nconst NL = 'NL'; // \\n\n\n// Opening/closing bracket classes\n// TODO: Rename OPEN -> LEFT and CLOSE -> RIGHT in v5 to fit with Unicode names\n// Also rename angle brackes to LESSTHAN and GREATER THAN\nconst OPENBRACE = 'OPENBRACE'; // {\nconst CLOSEBRACE = 'CLOSEBRACE'; // }\nconst OPENBRACKET = 'OPENBRACKET'; // [\nconst CLOSEBRACKET = 'CLOSEBRACKET'; // ]\nconst OPENPAREN = 'OPENPAREN'; // (\nconst CLOSEPAREN = 'CLOSEPAREN'; // )\nconst OPENANGLEBRACKET = 'OPENANGLEBRACKET'; // <\nconst CLOSEANGLEBRACKET = 'CLOSEANGLEBRACKET'; // >\nconst FULLWIDTHLEFTPAREN = 'FULLWIDTHLEFTPAREN'; // （\nconst FULLWIDTHRIGHTPAREN = 'FULLWIDTHRIGHTPAREN'; // ）\nconst LEFTCORNERBRACKET = 'LEFTCORNERBRACKET'; // 「\nconst RIGHTCORNERBRACKET = 'RIGHTCORNERBRACKET'; // 」\nconst LEFTWHITECORNERBRACKET = 'LEFTWHITECORNERBRACKET'; // 『\nconst RIGHTWHITECORNERBRACKET = 'RIGHTWHITECORNERBRACKET'; // 』\nconst FULLWIDTHLESSTHAN = 'FULLWIDTHLESSTHAN'; // ＜\nconst FULLWIDTHGREATERTHAN = 'FULLWIDTHGREATERTHAN'; // ＞\n\n// Various symbols\nconst AMPERSAND = 'AMPERSAND'; // &\nconst APOSTROPHE = 'APOSTROPHE'; // '\nconst ASTERISK = 'ASTERISK'; // *\nconst AT = 'AT'; // @\nconst BACKSLASH = 'BACKSLASH'; // \\\nconst BACKTICK = 'BACKTICK'; // `\nconst CARET = 'CARET'; // ^\nconst COLON = 'COLON'; // :\nconst COMMA = 'COMMA'; // ,\nconst DOLLAR = 'DOLLAR'; // $\nconst DOT = 'DOT'; // .\nconst EQUALS = 'EQUALS'; // =\nconst EXCLAMATION = 'EXCLAMATION'; // !\nconst HYPHEN = 'HYPHEN'; // -\nconst PERCENT = 'PERCENT'; // %\nconst PIPE = 'PIPE'; // |\nconst PLUS = 'PLUS'; // +\nconst POUND = 'POUND'; // #\nconst QUERY = 'QUERY'; // ?\nconst QUOTE = 'QUOTE'; // \"\nconst FULLWIDTHMIDDLEDOT = 'FULLWIDTHMIDDLEDOT'; // ・\n\nconst SEMI = 'SEMI'; // ;\nconst SLASH = 'SLASH'; // /\nconst TILDE = 'TILDE'; // ~\nconst UNDERSCORE = 'UNDERSCORE'; // _\n\n// Emoji symbol\nconst EMOJI$1 = 'EMOJI';\n\n// Default token - anything that is not one of the above\nconst SYM = 'SYM';\n\nvar tk = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tWORD: WORD,\n\tUWORD: UWORD,\n\tASCIINUMERICAL: ASCIINUMERICAL,\n\tALPHANUMERICAL: ALPHANUMERICAL,\n\tLOCALHOST: LOCALHOST,\n\tTLD: TLD,\n\tUTLD: UTLD,\n\tSCHEME: SCHEME,\n\tSLASH_SCHEME: SLASH_SCHEME,\n\tNUM: NUM,\n\tWS: WS,\n\tNL: NL,\n\tOPENBRACE: OPENBRACE,\n\tCLOSEBRACE: CLOSEBRACE,\n\tOPENBRACKET: OPENBRACKET,\n\tCLOSEBRACKET: CLOSEBRACKET,\n\tOPENPAREN: OPENPAREN,\n\tCLOSEPAREN: CLOSEPAREN,\n\tOPENANGLEBRACKET: OPENANGLEBRACKET,\n\tCLOSEANGLEBRACKET: CLOSEANGLEBRACKET,\n\tFULLWIDTHLEFTPAREN: FULLWIDTHLEFTPAREN,\n\tFULLWIDTHRIGHTPAREN: FULLWIDTHRIGHTPAREN,\n\tLEFTCORNERBRACKET: LEFTCORNERBRACKET,\n\tRIGHTCORNERBRACKET: RIGHTCORNERBRACKET,\n\tLEFTWHITECORNERBRACKET: LEFTWHITECORNERBRACKET,\n\tRIGHTWHITECORNERBRACKET: RIGHTWHITECORNERBRACKET,\n\tFULLWIDTHLESSTHAN: FULLWIDTHLESSTHAN,\n\tFULLWIDTHGREATERTHAN: FULLWIDTHGREATERTHAN,\n\tAMPERSAND: AMPERSAND,\n\tAPOSTROPHE: APOSTROPHE,\n\tASTERISK: ASTERISK,\n\tAT: AT,\n\tBACKSLASH: BACKSLASH,\n\tBACKTICK: BACKTICK,\n\tCARET: CARET,\n\tCOLON: COLON,\n\tCOMMA: COMMA,\n\tDOLLAR: DOLLAR,\n\tDOT: DOT,\n\tEQUALS: EQUALS,\n\tEXCLAMATION: EXCLAMATION,\n\tHYPHEN: HYPHEN,\n\tPERCENT: PERCENT,\n\tPIPE: PIPE,\n\tPLUS: PLUS,\n\tPOUND: POUND,\n\tQUERY: QUERY,\n\tQUOTE: QUOTE,\n\tFULLWIDTHMIDDLEDOT: FULLWIDTHMIDDLEDOT,\n\tSEMI: SEMI,\n\tSLASH: SLASH,\n\tTILDE: TILDE,\n\tUNDERSCORE: UNDERSCORE,\n\tEMOJI: EMOJI$1,\n\tSYM: SYM\n});\n\n// Note that these two Unicode ones expand into a really big one with Babel\nconst ASCII_LETTER = /[a-z]/;\nconst LETTER = /\\p{L}/u; // Any Unicode character with letter data type\nconst EMOJI = /\\p{Emoji}/u; // Any Unicode emoji character\nconst EMOJI_VARIATION$1 = /\\ufe0f/;\nconst DIGIT = /\\d/;\nconst SPACE = /\\s/;\n\nvar regexp = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tASCII_LETTER: ASCII_LETTER,\n\tLETTER: LETTER,\n\tEMOJI: EMOJI,\n\tEMOJI_VARIATION: EMOJI_VARIATION$1,\n\tDIGIT: DIGIT,\n\tSPACE: SPACE\n});\n\n/**\n\tThe scanner provides an interface that takes a string of text as input, and\n\toutputs an array of tokens instances that can be used for easy URL parsing.\n*/\nconst CR = '\\r'; // carriage-return character\nconst LF = '\\n'; // line-feed character\nconst EMOJI_VARIATION = '\\ufe0f'; // Variation selector, follows heart and others\nconst EMOJI_JOINER = '\\u200d'; // zero-width joiner\nconst OBJECT_REPLACEMENT = '\\ufffc'; // whitespace placeholder that sometimes appears in rich text editors\n\nlet tlds = null,\n  utlds = null; // don't change so only have to be computed once\n\n/**\n * Scanner output token:\n * - `t` is the token name (e.g., 'NUM', 'EMOJI', 'TLD')\n * - `v` is the value of the token (e.g., '123', '❤️', 'com')\n * - `s` is the start index of the token in the original string\n * - `e` is the end index of the token in the original string\n * @typedef {{t: string, v: string, s: number, e: number}} Token\n */\n\n/**\n * @template T\n * @typedef {{ [collection: string]: T[] }} Collections\n */\n\n/**\n * Initialize the scanner character-based state machine for the given start\n * state\n * @param {[string, boolean][]} customSchemes List of custom schemes, where each\n * item is a length-2 tuple with the first element set to the string scheme, and\n * the second element set to `true` if the `://` after the scheme is optional\n */\nfunction init$2(customSchemes = []) {\n  // Frequently used states (name argument removed during minification)\n  /** @type Collections<string> */\n  const groups = {}; // of tokens\n  State.groups = groups;\n  /** @type State<string> */\n  const Start = new State();\n  if (tlds == null) {\n    tlds = decodeTlds(encodedTlds);\n  }\n  if (utlds == null) {\n    utlds = decodeTlds(encodedUtlds);\n  }\n\n  // States for special URL symbols that accept immediately after start\n  tt(Start, \"'\", APOSTROPHE);\n  tt(Start, '{', OPENBRACE);\n  tt(Start, '}', CLOSEBRACE);\n  tt(Start, '[', OPENBRACKET);\n  tt(Start, ']', CLOSEBRACKET);\n  tt(Start, '(', OPENPAREN);\n  tt(Start, ')', CLOSEPAREN);\n  tt(Start, '<', OPENANGLEBRACKET);\n  tt(Start, '>', CLOSEANGLEBRACKET);\n  tt(Start, '（', FULLWIDTHLEFTPAREN);\n  tt(Start, '）', FULLWIDTHRIGHTPAREN);\n  tt(Start, '「', LEFTCORNERBRACKET);\n  tt(Start, '」', RIGHTCORNERBRACKET);\n  tt(Start, '『', LEFTWHITECORNERBRACKET);\n  tt(Start, '』', RIGHTWHITECORNERBRACKET);\n  tt(Start, '＜', FULLWIDTHLESSTHAN);\n  tt(Start, '＞', FULLWIDTHGREATERTHAN);\n  tt(Start, '&', AMPERSAND);\n  tt(Start, '*', ASTERISK);\n  tt(Start, '@', AT);\n  tt(Start, '`', BACKTICK);\n  tt(Start, '^', CARET);\n  tt(Start, ':', COLON);\n  tt(Start, ',', COMMA);\n  tt(Start, '$', DOLLAR);\n  tt(Start, '.', DOT);\n  tt(Start, '=', EQUALS);\n  tt(Start, '!', EXCLAMATION);\n  tt(Start, '-', HYPHEN);\n  tt(Start, '%', PERCENT);\n  tt(Start, '|', PIPE);\n  tt(Start, '+', PLUS);\n  tt(Start, '#', POUND);\n  tt(Start, '?', QUERY);\n  tt(Start, '\"', QUOTE);\n  tt(Start, '/', SLASH);\n  tt(Start, ';', SEMI);\n  tt(Start, '~', TILDE);\n  tt(Start, '_', UNDERSCORE);\n  tt(Start, '\\\\', BACKSLASH);\n  tt(Start, '・', FULLWIDTHMIDDLEDOT);\n  const Num = tr(Start, DIGIT, NUM, {\n    [numeric]: true\n  });\n  tr(Num, DIGIT, Num);\n  const Asciinumeric = tr(Num, ASCII_LETTER, ASCIINUMERICAL, {\n    [asciinumeric]: true\n  });\n  const Alphanumeric = tr(Num, LETTER, ALPHANUMERICAL, {\n    [alphanumeric]: true\n  });\n\n  // State which emits a word token\n  const Word = tr(Start, ASCII_LETTER, WORD, {\n    [ascii]: true\n  });\n  tr(Word, DIGIT, Asciinumeric);\n  tr(Word, ASCII_LETTER, Word);\n  tr(Asciinumeric, DIGIT, Asciinumeric);\n  tr(Asciinumeric, ASCII_LETTER, Asciinumeric);\n\n  // Same as previous, but specific to non-fsm.ascii alphabet words\n  const UWord = tr(Start, LETTER, UWORD, {\n    [alpha]: true\n  });\n  tr(UWord, ASCII_LETTER); // Non-accepting\n  tr(UWord, DIGIT, Alphanumeric);\n  tr(UWord, LETTER, UWord);\n  tr(Alphanumeric, DIGIT, Alphanumeric);\n  tr(Alphanumeric, ASCII_LETTER); // Non-accepting\n  tr(Alphanumeric, LETTER, Alphanumeric); // Non-accepting\n\n  // Whitespace jumps\n  // Tokens of only non-newline whitespace are arbitrarily long\n  // If any whitespace except newline, more whitespace!\n  const Nl = tt(Start, LF, NL, {\n    [whitespace]: true\n  });\n  const Cr = tt(Start, CR, WS, {\n    [whitespace]: true\n  });\n  const Ws = tr(Start, SPACE, WS, {\n    [whitespace]: true\n  });\n  tt(Start, OBJECT_REPLACEMENT, Ws);\n  tt(Cr, LF, Nl); // \\r\\n\n  tt(Cr, OBJECT_REPLACEMENT, Ws);\n  tr(Cr, SPACE, Ws);\n  tt(Ws, CR); // non-accepting state to avoid mixing whitespaces\n  tt(Ws, LF); // non-accepting state to avoid mixing whitespaces\n  tr(Ws, SPACE, Ws);\n  tt(Ws, OBJECT_REPLACEMENT, Ws);\n\n  // Emoji tokens. They are not grouped by the scanner except in cases where a\n  // zero-width joiner is present\n  const Emoji = tr(Start, EMOJI, EMOJI$1, {\n    [emoji]: true\n  });\n  tt(Emoji, '#'); // no transition, emoji regex seems to match #\n  tr(Emoji, EMOJI, Emoji);\n  tt(Emoji, EMOJI_VARIATION, Emoji);\n  // tt(Start, EMOJI_VARIATION, Emoji); // This one is sketchy\n\n  const EmojiJoiner = tt(Emoji, EMOJI_JOINER);\n  tt(EmojiJoiner, '#');\n  tr(EmojiJoiner, EMOJI, Emoji);\n  // tt(EmojiJoiner, EMOJI_VARIATION, Emoji); // also sketchy\n\n  // Generates states for top-level domains\n  // Note that this is most accurate when tlds are in alphabetical order\n  const wordjr = [[ASCII_LETTER, Word], [DIGIT, Asciinumeric]];\n  const uwordjr = [[ASCII_LETTER, null], [LETTER, UWord], [DIGIT, Alphanumeric]];\n  for (let i = 0; i < tlds.length; i++) {\n    fastts(Start, tlds[i], TLD, WORD, wordjr);\n  }\n  for (let i = 0; i < utlds.length; i++) {\n    fastts(Start, utlds[i], UTLD, UWORD, uwordjr);\n  }\n  addToGroups(TLD, {\n    tld: true,\n    ascii: true\n  }, groups);\n  addToGroups(UTLD, {\n    utld: true,\n    alpha: true\n  }, groups);\n\n  // Collect the states generated by different protocols. NOTE: If any new TLDs\n  // get added that are also protocols, set the token to be the same as the\n  // protocol to ensure parsing works as expected.\n  fastts(Start, 'file', SCHEME, WORD, wordjr);\n  fastts(Start, 'mailto', SCHEME, WORD, wordjr);\n  fastts(Start, 'http', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'https', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'ftp', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'ftps', SLASH_SCHEME, WORD, wordjr);\n  addToGroups(SCHEME, {\n    scheme: true,\n    ascii: true\n  }, groups);\n  addToGroups(SLASH_SCHEME, {\n    slashscheme: true,\n    ascii: true\n  }, groups);\n\n  // Register custom schemes. Assumes each scheme is asciinumeric with hyphens\n  customSchemes = customSchemes.sort((a, b) => a[0] > b[0] ? 1 : -1);\n  for (let i = 0; i < customSchemes.length; i++) {\n    const sch = customSchemes[i][0];\n    const optionalSlashSlash = customSchemes[i][1];\n    const flags = optionalSlashSlash ? {\n      [scheme]: true\n    } : {\n      [slashscheme]: true\n    };\n    if (sch.indexOf('-') >= 0) {\n      flags[domain] = true;\n    } else if (!ASCII_LETTER.test(sch)) {\n      flags[numeric] = true; // numbers only\n    } else if (DIGIT.test(sch)) {\n      flags[asciinumeric] = true;\n    } else {\n      flags[ascii] = true;\n    }\n    ts(Start, sch, sch, flags);\n  }\n\n  // Localhost token\n  ts(Start, 'localhost', LOCALHOST, {\n    ascii: true\n  });\n\n  // Set default transition for start state (some symbol)\n  Start.jd = new State(SYM);\n  return {\n    start: Start,\n    tokens: assign({\n      groups\n    }, tk)\n  };\n}\n\n/**\n\tGiven a string, returns an array of TOKEN instances representing the\n\tcomposition of that string.\n\n\t@method run\n\t@param {State<string>} start scanner starting state\n\t@param {string} str input string to scan\n\t@return {Token[]} list of tokens, each with a type and value\n*/\nfunction run$1(start, str) {\n  // State machine is not case sensitive, so input is tokenized in lowercased\n  // form (still returns regular case). Uses selective `toLowerCase` because\n  // lowercasing the entire string causes the length and character position to\n  // vary in some non-English strings with V8-based runtimes.\n  const iterable = stringToArray(str.replace(/[A-Z]/g, c => c.toLowerCase()));\n  const charCount = iterable.length; // <= len if there are emojis, etc\n  const tokens = []; // return value\n\n  // cursor through the string itself, accounting for characters that have\n  // width with length 2 such as emojis\n  let cursor = 0;\n\n  // Cursor through the array-representation of the string\n  let charCursor = 0;\n\n  // Tokenize the string\n  while (charCursor < charCount) {\n    let state = start;\n    let nextState = null;\n    let tokenLength = 0;\n    let latestAccepting = null;\n    let sinceAccepts = -1;\n    let charsSinceAccepts = -1;\n    while (charCursor < charCount && (nextState = state.go(iterable[charCursor]))) {\n      state = nextState;\n\n      // Keep track of the latest accepting state\n      if (state.accepts()) {\n        sinceAccepts = 0;\n        charsSinceAccepts = 0;\n        latestAccepting = state;\n      } else if (sinceAccepts >= 0) {\n        sinceAccepts += iterable[charCursor].length;\n        charsSinceAccepts++;\n      }\n      tokenLength += iterable[charCursor].length;\n      cursor += iterable[charCursor].length;\n      charCursor++;\n    }\n\n    // Roll back to the latest accepting state\n    cursor -= sinceAccepts;\n    charCursor -= charsSinceAccepts;\n    tokenLength -= sinceAccepts;\n\n    // No more jumps, just make a new token from the last accepting one\n    tokens.push({\n      t: latestAccepting.t,\n      // token type/name\n      v: str.slice(cursor - tokenLength, cursor),\n      // string value\n      s: cursor - tokenLength,\n      // start index\n      e: cursor // end index (excluding)\n    });\n  }\n  return tokens;\n}\n\n/**\n * Convert a String to an Array of characters, taking into account that some\n * characters like emojis take up two string indexes.\n *\n * Adapted from core-js (MIT license)\n * https://github.com/zloirock/core-js/blob/2d69cf5f99ab3ea3463c395df81e5a15b68f49d9/packages/core-js/internals/string-multibyte.js\n *\n * @function stringToArray\n * @param {string} str\n * @returns {string[]}\n */\nfunction stringToArray(str) {\n  const result = [];\n  const len = str.length;\n  let index = 0;\n  while (index < len) {\n    let first = str.charCodeAt(index);\n    let second;\n    let char = first < 0xd800 || first > 0xdbff || index + 1 === len || (second = str.charCodeAt(index + 1)) < 0xdc00 || second > 0xdfff ? str[index] // single character\n    : str.slice(index, index + 2); // two-index characters\n    result.push(char);\n    index += char.length;\n  }\n  return result;\n}\n\n/**\n * Fast version of ts function for when transition defaults are well known\n * @param {State<string>} state\n * @param {string} input\n * @param {string} t\n * @param {string} defaultt\n * @param {[RegExp, State<string>][]} jr\n * @returns {State<string>}\n */\nfunction fastts(state, input, t, defaultt, jr) {\n  let next;\n  const len = input.length;\n  for (let i = 0; i < len - 1; i++) {\n    const char = input[i];\n    if (state.j[char]) {\n      next = state.j[char];\n    } else {\n      next = new State(defaultt);\n      next.jr = jr.slice();\n      state.j[char] = next;\n    }\n    state = next;\n  }\n  next = new State(t);\n  next.jr = jr.slice();\n  state.j[input[len - 1]] = next;\n  return next;\n}\n\n/**\n * Converts a string of Top-Level Domain names encoded in update-tlds.js back\n * into a list of strings.\n * @param {str} encoded encoded TLDs string\n * @returns {str[]} original TLDs list\n */\nfunction decodeTlds(encoded) {\n  const words = [];\n  const stack = [];\n  let i = 0;\n  let digits = '0123456789';\n  while (i < encoded.length) {\n    let popDigitCount = 0;\n    while (digits.indexOf(encoded[i + popDigitCount]) >= 0) {\n      popDigitCount++; // encountered some digits, have to pop to go one level up trie\n    }\n    if (popDigitCount > 0) {\n      words.push(stack.join('')); // whatever preceded the pop digits must be a word\n      for (let popCount = parseInt(encoded.substring(i, i + popDigitCount), 10); popCount > 0; popCount--) {\n        stack.pop();\n      }\n      i += popDigitCount;\n    } else {\n      stack.push(encoded[i]); // drop down a level into the trie\n      i++;\n    }\n  }\n  return words;\n}\n\n/**\n * An object where each key is a valid DOM Event Name such as `click` or `focus`\n * and each value is an event handler function.\n *\n * https://developer.mozilla.org/en-US/docs/Web/API/Element#events\n * @typedef {?{ [event: string]: Function }} EventListeners\n */\n\n/**\n * All formatted properties required to render a link, including `tagName`,\n * `attributes`, `content` and `eventListeners`.\n * @typedef {{ tagName: any, attributes: {[attr: string]: any}, content: string,\n * eventListeners: EventListeners }} IntermediateRepresentation\n */\n\n/**\n * Specify either an object described by the template type `O` or a function.\n *\n * The function takes a string value (usually the link's href attribute), the\n * link type (`'url'`, `'hashtag`', etc.) and an internal token representation\n * of the link. It should return an object of the template type `O`\n * @template O\n * @typedef {O | ((value: string, type: string, token: MultiToken) => O)} OptObj\n */\n\n/**\n * Specify either a function described by template type `F` or an object.\n *\n * Each key in the object should be a link type (`'url'`, `'hashtag`', etc.). Each\n * value should be a function with template type `F` that is called when the\n * corresponding link type is encountered.\n * @template F\n * @typedef {F | { [type: string]: F}} OptFn\n */\n\n/**\n * Specify either a value with template type `V`, a function that returns `V` or\n * an object where each value resolves to `V`.\n *\n * The function takes a string value (usually the link's href attribute), the\n * link type (`'url'`, `'hashtag`', etc.) and an internal token representation\n * of the link. It should return an object of the template type `V`\n *\n * For the object, each key should be a link type (`'url'`, `'hashtag`', etc.).\n * Each value should either have type `V` or a function that returns V. This\n * function similarly takes a string value and a token.\n *\n * Example valid types for `Opt<string>`:\n *\n * ```js\n * 'hello'\n * (value, type, token) => 'world'\n * { url: 'hello', email: (value, token) => 'world'}\n * ```\n * @template V\n * @typedef {V | ((value: string, type: string, token: MultiToken) => V) | { [type: string]: V | ((value: string, token: MultiToken) => V) }} Opt\n */\n\n/**\n * See available options: https://linkify.js.org/docs/options.html\n * @typedef {{\n * \tdefaultProtocol?: string,\n *  events?: OptObj<EventListeners>,\n * \tformat?: Opt<string>,\n * \tformatHref?: Opt<string>,\n * \tnl2br?: boolean,\n * \ttagName?: Opt<any>,\n * \ttarget?: Opt<string>,\n * \trel?: Opt<string>,\n * \tvalidate?: Opt<boolean>,\n * \ttruncate?: Opt<number>,\n * \tclassName?: Opt<string>,\n * \tattributes?: OptObj<({ [attr: string]: any })>,\n *  ignoreTags?: string[],\n * \trender?: OptFn<((ir: IntermediateRepresentation) => any)>\n * }} Opts\n */\n\n/**\n * @type Required<Opts>\n */\nconst defaults = {\n  defaultProtocol: 'http',\n  events: null,\n  format: noop,\n  formatHref: noop,\n  nl2br: false,\n  tagName: 'a',\n  target: null,\n  rel: null,\n  validate: true,\n  truncate: Infinity,\n  className: null,\n  attributes: null,\n  ignoreTags: [],\n  render: null\n};\n\n/**\n * Utility class for linkify interfaces to apply specified\n * {@link Opts formatting and rendering options}.\n *\n * @param {Opts | Options} [opts] Option value overrides.\n * @param {(ir: IntermediateRepresentation) => any} [defaultRender] (For\n *   internal use) default render function that determines how to generate an\n *   HTML element based on a link token's derived tagName, attributes and HTML.\n *   Similar to render option\n */\nfunction Options(opts, defaultRender = null) {\n  let o = assign({}, defaults);\n  if (opts) {\n    o = assign(o, opts instanceof Options ? opts.o : opts);\n  }\n\n  // Ensure all ignored tags are uppercase\n  const ignoredTags = o.ignoreTags;\n  const uppercaseIgnoredTags = [];\n  for (let i = 0; i < ignoredTags.length; i++) {\n    uppercaseIgnoredTags.push(ignoredTags[i].toUpperCase());\n  }\n  /** @protected */\n  this.o = o;\n  if (defaultRender) {\n    this.defaultRender = defaultRender;\n  }\n  this.ignoreTags = uppercaseIgnoredTags;\n}\nOptions.prototype = {\n  o: defaults,\n  /**\n   * @type string[]\n   */\n  ignoreTags: [],\n  /**\n   * @param {IntermediateRepresentation} ir\n   * @returns {any}\n   */\n  defaultRender(ir) {\n    return ir;\n  },\n  /**\n   * Returns true or false based on whether a token should be displayed as a\n   * link based on the user options.\n   * @param {MultiToken} token\n   * @returns {boolean}\n   */\n  check(token) {\n    return this.get('validate', token.toString(), token);\n  },\n  // Private methods\n\n  /**\n   * Resolve an option's value based on the value of the option and the given\n   * params. If operator and token are specified and the target option is\n   * callable, automatically calls the function with the given argument.\n   * @template {keyof Opts} K\n   * @param {K} key Name of option to use\n   * @param {string} [operator] will be passed to the target option if it's a\n   * function. If not specified, RAW function value gets returned\n   * @param {MultiToken} [token] The token from linkify.tokenize\n   * @returns {Opts[K] | any}\n   */\n  get(key, operator, token) {\n    const isCallable = operator != null;\n    let option = this.o[key];\n    if (!option) {\n      return option;\n    }\n    if (typeof option === 'object') {\n      option = token.t in option ? option[token.t] : defaults[key];\n      if (typeof option === 'function' && isCallable) {\n        option = option(operator, token);\n      }\n    } else if (typeof option === 'function' && isCallable) {\n      option = option(operator, token.t, token);\n    }\n    return option;\n  },\n  /**\n   * @template {keyof Opts} L\n   * @param {L} key Name of options object to use\n   * @param {string} [operator]\n   * @param {MultiToken} [token]\n   * @returns {Opts[L] | any}\n   */\n  getObj(key, operator, token) {\n    let obj = this.o[key];\n    if (typeof obj === 'function' && operator != null) {\n      obj = obj(operator, token.t, token);\n    }\n    return obj;\n  },\n  /**\n   * Convert the given token to a rendered element that may be added to the\n   * calling-interface's DOM\n   * @param {MultiToken} token Token to render to an HTML element\n   * @returns {any} Render result; e.g., HTML string, DOM element, React\n   *   Component, etc.\n   */\n  render(token) {\n    const ir = token.render(this); // intermediate representation\n    const renderFn = this.get('render', null, token) || this.defaultRender;\n    return renderFn(ir, token.t, token);\n  }\n};\nfunction noop(val) {\n  return val;\n}\n\nvar options = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tdefaults: defaults,\n\tOptions: Options,\n\tassign: assign\n});\n\n/******************************************************************************\n\tMulti-Tokens\n\tTokens composed of arrays of TextTokens\n******************************************************************************/\n\n/**\n * @param {string} value\n * @param {Token[]} tokens\n */\nfunction MultiToken(value, tokens) {\n  this.t = 'token';\n  this.v = value;\n  this.tk = tokens;\n}\n\n/**\n * Abstract class used for manufacturing tokens of text tokens. That is rather\n * than the value for a token being a small string of text, it's value an array\n * of text tokens.\n *\n * Used for grouping together URLs, emails, hashtags, and other potential\n * creations.\n * @class MultiToken\n * @property {string} t\n * @property {string} v\n * @property {Token[]} tk\n * @abstract\n */\nMultiToken.prototype = {\n  isLink: false,\n  /**\n   * Return the string this token represents.\n   * @return {string}\n   */\n  toString() {\n    return this.v;\n  },\n  /**\n   * What should the value for this token be in the `href` HTML attribute?\n   * Returns the `.toString` value by default.\n   * @param {string} [scheme]\n   * @return {string}\n   */\n  toHref(scheme) {\n    return this.toString();\n  },\n  /**\n   * @param {Options} options Formatting options\n   * @returns {string}\n   */\n  toFormattedString(options) {\n    const val = this.toString();\n    const truncate = options.get('truncate', val, this);\n    const formatted = options.get('format', val, this);\n    return truncate && formatted.length > truncate ? formatted.substring(0, truncate) + '…' : formatted;\n  },\n  /**\n   *\n   * @param {Options} options\n   * @returns {string}\n   */\n  toFormattedHref(options) {\n    return options.get('formatHref', this.toHref(options.get('defaultProtocol')), this);\n  },\n  /**\n   * The start index of this token in the original input string\n   * @returns {number}\n   */\n  startIndex() {\n    return this.tk[0].s;\n  },\n  /**\n   * The end index of this token in the original input string (up to this\n   * index but not including it)\n   * @returns {number}\n   */\n  endIndex() {\n    return this.tk[this.tk.length - 1].e;\n  },\n  /**\n  \tReturns an object  of relevant values for this token, which includes keys\n  \t* type - Kind of token ('url', 'email', etc.)\n  \t* value - Original text\n  \t* href - The value that should be added to the anchor tag's href\n  \t\tattribute\n  \t\t@method toObject\n  \t@param {string} [protocol] `'http'` by default\n  */\n  toObject(protocol = defaults.defaultProtocol) {\n    return {\n      type: this.t,\n      value: this.toString(),\n      isLink: this.isLink,\n      href: this.toHref(protocol),\n      start: this.startIndex(),\n      end: this.endIndex()\n    };\n  },\n  /**\n   *\n   * @param {Options} options Formatting option\n   */\n  toFormattedObject(options) {\n    return {\n      type: this.t,\n      value: this.toFormattedString(options),\n      isLink: this.isLink,\n      href: this.toFormattedHref(options),\n      start: this.startIndex(),\n      end: this.endIndex()\n    };\n  },\n  /**\n   * Whether this token should be rendered as a link according to the given options\n   * @param {Options} options\n   * @returns {boolean}\n   */\n  validate(options) {\n    return options.get('validate', this.toString(), this);\n  },\n  /**\n   * Return an object that represents how this link should be rendered.\n   * @param {Options} options Formattinng options\n   */\n  render(options) {\n    const token = this;\n    const href = this.toHref(options.get('defaultProtocol'));\n    const formattedHref = options.get('formatHref', href, this);\n    const tagName = options.get('tagName', href, token);\n    const content = this.toFormattedString(options);\n    const attributes = {};\n    const className = options.get('className', href, token);\n    const target = options.get('target', href, token);\n    const rel = options.get('rel', href, token);\n    const attrs = options.getObj('attributes', href, token);\n    const eventListeners = options.getObj('events', href, token);\n    attributes.href = formattedHref;\n    if (className) {\n      attributes.class = className;\n    }\n    if (target) {\n      attributes.target = target;\n    }\n    if (rel) {\n      attributes.rel = rel;\n    }\n    if (attrs) {\n      assign(attributes, attrs);\n    }\n    return {\n      tagName,\n      attributes,\n      content,\n      eventListeners\n    };\n  }\n};\n\n/**\n * Create a new token that can be emitted by the parser state machine\n * @param {string} type readable type of the token\n * @param {object} props properties to assign or override, including isLink = true or false\n * @returns {new (value: string, tokens: Token[]) => MultiToken} new token class\n */\nfunction createTokenClass(type, props) {\n  class Token extends MultiToken {\n    constructor(value, tokens) {\n      super(value, tokens);\n      this.t = type;\n    }\n  }\n  for (const p in props) {\n    Token.prototype[p] = props[p];\n  }\n  Token.t = type;\n  return Token;\n}\n\n/**\n\tRepresents a list of tokens making up a valid email address\n*/\nconst Email = createTokenClass('email', {\n  isLink: true,\n  toHref() {\n    return 'mailto:' + this.toString();\n  }\n});\n\n/**\n\tRepresents some plain text\n*/\nconst Text = createTokenClass('text');\n\n/**\n\tMulti-linebreak token - represents a line break\n\t@class Nl\n*/\nconst Nl = createTokenClass('nl');\n\n/**\n\tRepresents a list of text tokens making up a valid URL\n\t@class Url\n*/\nconst Url = createTokenClass('url', {\n  isLink: true,\n  /**\n  \tLowercases relevant parts of the domain and adds the protocol if\n  \trequired. Note that this will not escape unsafe HTML characters in the\n  \tURL.\n  \t\t@param {string} [scheme] default scheme (e.g., 'https')\n  \t@return {string} the full href\n  */\n  toHref(scheme = defaults.defaultProtocol) {\n    // Check if already has a prefix scheme\n    return this.hasProtocol() ? this.v : `${scheme}://${this.v}`;\n  },\n  /**\n   * Check whether this URL token has a protocol\n   * @return {boolean}\n   */\n  hasProtocol() {\n    const tokens = this.tk;\n    return tokens.length >= 2 && tokens[0].t !== LOCALHOST && tokens[1].t === COLON;\n  }\n});\n\nvar multi = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tMultiToken: MultiToken,\n\tBase: MultiToken,\n\tcreateTokenClass: createTokenClass,\n\tEmail: Email,\n\tText: Text,\n\tNl: Nl,\n\tUrl: Url\n});\n\n/**\n\tNot exactly parser, more like the second-stage scanner (although we can\n\ttheoretically hotswap the code here with a real parser in the future... but\n\tfor a little URL-finding utility abstract syntax trees may be a little\n\toverkill).\n\n\tURL format: http://en.wikipedia.org/wiki/URI_scheme\n\tEmail format: http://en.wikipedia.org/wiki/EmailAddress (links to RFC in\n\treference)\n\n\t@module linkify\n\t@submodule parser\n\t@main run\n*/\nconst makeState = arg => new State(arg);\n\n/**\n * Generate the parser multi token-based state machine\n * @param {{ groups: Collections<string> }} tokens\n */\nfunction init$1({\n  groups\n}) {\n  // Types of characters the URL can definitely end in\n  const qsAccepting = groups.domain.concat([AMPERSAND, ASTERISK, AT, BACKSLASH, BACKTICK, CARET, DOLLAR, EQUALS, HYPHEN, NUM, PERCENT, PIPE, PLUS, POUND, SLASH, SYM, TILDE, UNDERSCORE]);\n\n  // Types of tokens that can follow a URL and be part of the query string\n  // but cannot be the very last characters\n  // Characters that cannot appear in the URL at all should be excluded\n  const qsNonAccepting = [COLON, COMMA, DOT, EXCLAMATION, PERCENT, QUERY, QUOTE, SEMI, OPENANGLEBRACKET, CLOSEANGLEBRACKET, OPENBRACE, CLOSEBRACE, CLOSEBRACKET, OPENBRACKET, OPENPAREN, CLOSEPAREN, FULLWIDTHLEFTPAREN, FULLWIDTHRIGHTPAREN, LEFTCORNERBRACKET, RIGHTCORNERBRACKET, LEFTWHITECORNERBRACKET, RIGHTWHITECORNERBRACKET, FULLWIDTHLESSTHAN, FULLWIDTHGREATERTHAN];\n\n  // For addresses without the mailto prefix\n  // Tokens allowed in the localpart of the email\n  const localpartAccepting = [AMPERSAND, APOSTROPHE, ASTERISK, BACKSLASH, BACKTICK, CARET, DOLLAR, EQUALS, HYPHEN, OPENBRACE, CLOSEBRACE, PERCENT, PIPE, PLUS, POUND, QUERY, SLASH, SYM, TILDE, UNDERSCORE];\n\n  // The universal starting state.\n  /**\n   * @type State<Token>\n   */\n  const Start = makeState();\n  const Localpart = tt(Start, TILDE); // Local part of the email address\n  ta(Localpart, localpartAccepting, Localpart);\n  ta(Localpart, groups.domain, Localpart);\n  const Domain = makeState(),\n    Scheme = makeState(),\n    SlashScheme = makeState();\n  ta(Start, groups.domain, Domain); // parsed string ends with a potential domain name (A)\n  ta(Start, groups.scheme, Scheme); // e.g., 'mailto'\n  ta(Start, groups.slashscheme, SlashScheme); // e.g., 'http'\n\n  ta(Domain, localpartAccepting, Localpart);\n  ta(Domain, groups.domain, Domain);\n  const LocalpartAt = tt(Domain, AT); // Local part of the email address plus @\n\n  tt(Localpart, AT, LocalpartAt); // close to an email address now\n\n  // Local part of an email address can be e.g. 'http' or 'mailto'\n  tt(Scheme, AT, LocalpartAt);\n  tt(SlashScheme, AT, LocalpartAt);\n  const LocalpartDot = tt(Localpart, DOT); // Local part of the email address plus '.' (localpart cannot end in .)\n  ta(LocalpartDot, localpartAccepting, Localpart);\n  ta(LocalpartDot, groups.domain, Localpart);\n  const EmailDomain = makeState();\n  ta(LocalpartAt, groups.domain, EmailDomain); // parsed string starts with local email info + @ with a potential domain name\n  ta(EmailDomain, groups.domain, EmailDomain);\n  const EmailDomainDot = tt(EmailDomain, DOT); // domain followed by DOT\n  ta(EmailDomainDot, groups.domain, EmailDomain);\n  const Email$1 = makeState(Email); // Possible email address (could have more tlds)\n  ta(EmailDomainDot, groups.tld, Email$1);\n  ta(EmailDomainDot, groups.utld, Email$1);\n  tt(LocalpartAt, LOCALHOST, Email$1);\n\n  // Hyphen can jump back to a domain name\n  const EmailDomainHyphen = tt(EmailDomain, HYPHEN); // parsed string starts with local email info + @ with a potential domain name\n  tt(EmailDomainHyphen, HYPHEN, EmailDomainHyphen);\n  ta(EmailDomainHyphen, groups.domain, EmailDomain);\n  ta(Email$1, groups.domain, EmailDomain);\n  tt(Email$1, DOT, EmailDomainDot);\n  tt(Email$1, HYPHEN, EmailDomainHyphen);\n\n  // Final possible email states\n  const EmailColon = tt(Email$1, COLON); // URL followed by colon (potential port number here)\n  /*const EmailColonPort = */\n  ta(EmailColon, groups.numeric, Email); // URL followed by colon and port number\n\n  // Account for dots and hyphens. Hyphens are usually parts of domain names\n  // (but not TLDs)\n  const DomainHyphen = tt(Domain, HYPHEN); // domain followed by hyphen\n  const DomainDot = tt(Domain, DOT); // domain followed by DOT\n  tt(DomainHyphen, HYPHEN, DomainHyphen);\n  ta(DomainHyphen, groups.domain, Domain);\n  ta(DomainDot, localpartAccepting, Localpart);\n  ta(DomainDot, groups.domain, Domain);\n  const DomainDotTld = makeState(Url); // Simplest possible URL with no query string\n  ta(DomainDot, groups.tld, DomainDotTld);\n  ta(DomainDot, groups.utld, DomainDotTld);\n  ta(DomainDotTld, groups.domain, Domain);\n  ta(DomainDotTld, localpartAccepting, Localpart);\n  tt(DomainDotTld, DOT, DomainDot);\n  tt(DomainDotTld, HYPHEN, DomainHyphen);\n  tt(DomainDotTld, AT, LocalpartAt);\n  const DomainDotTldColon = tt(DomainDotTld, COLON); // URL followed by colon (potential port number here)\n  const DomainDotTldColonPort = makeState(Url); // TLD followed by a port number\n  ta(DomainDotTldColon, groups.numeric, DomainDotTldColonPort);\n\n  // Long URL with optional port and maybe query string\n  const Url$1 = makeState(Url);\n\n  // URL with extra symbols at the end, followed by an opening bracket\n  const UrlNonaccept = makeState(); // URL followed by some symbols (will not be part of the final URL)\n\n  // Query strings\n  ta(Url$1, qsAccepting, Url$1);\n  ta(Url$1, qsNonAccepting, UrlNonaccept);\n  ta(UrlNonaccept, qsAccepting, Url$1);\n  ta(UrlNonaccept, qsNonAccepting, UrlNonaccept);\n\n  // Become real URLs after `SLASH` or `COLON NUM SLASH`\n  // Here works with or without scheme:// prefix\n  tt(DomainDotTld, SLASH, Url$1);\n  tt(DomainDotTldColonPort, SLASH, Url$1);\n\n  // Note that domains that begin with schemes are treated slighly differently\n  const SchemeColon = tt(Scheme, COLON); // e.g., 'mailto:'\n  const SlashSchemeColon = tt(SlashScheme, COLON); // e.g., 'http:'\n  const SlashSchemeColonSlash = tt(SlashSchemeColon, SLASH); // e.g., 'http:/'\n\n  const UriPrefix = tt(SlashSchemeColonSlash, SLASH); // e.g., 'http://'\n\n  // Scheme states can transition to domain states\n  ta(Scheme, groups.domain, Domain);\n  tt(Scheme, DOT, DomainDot);\n  tt(Scheme, HYPHEN, DomainHyphen);\n  ta(SlashScheme, groups.domain, Domain);\n  tt(SlashScheme, DOT, DomainDot);\n  tt(SlashScheme, HYPHEN, DomainHyphen);\n\n  // Force URL with scheme prefix followed by anything sane\n  ta(SchemeColon, groups.domain, Url$1);\n  tt(SchemeColon, SLASH, Url$1);\n  tt(SchemeColon, QUERY, Url$1);\n  ta(UriPrefix, groups.domain, Url$1);\n  ta(UriPrefix, qsAccepting, Url$1);\n  tt(UriPrefix, SLASH, Url$1);\n  const bracketPairs = [[OPENBRACE, CLOSEBRACE],\n  // {}\n  [OPENBRACKET, CLOSEBRACKET],\n  // []\n  [OPENPAREN, CLOSEPAREN],\n  // ()\n  [OPENANGLEBRACKET, CLOSEANGLEBRACKET],\n  // <>\n  [FULLWIDTHLEFTPAREN, FULLWIDTHRIGHTPAREN],\n  // （）\n  [LEFTCORNERBRACKET, RIGHTCORNERBRACKET],\n  // 「」\n  [LEFTWHITECORNERBRACKET, RIGHTWHITECORNERBRACKET],\n  // 『』\n  [FULLWIDTHLESSTHAN, FULLWIDTHGREATERTHAN] // ＜＞\n  ];\n  for (let i = 0; i < bracketPairs.length; i++) {\n    const [OPEN, CLOSE] = bracketPairs[i];\n    const UrlOpen = tt(Url$1, OPEN); // URL followed by open bracket\n\n    // Continue not accepting for open brackets\n    tt(UrlNonaccept, OPEN, UrlOpen);\n\n    // Closing bracket component. This character WILL be included in the URL\n    tt(UrlOpen, CLOSE, Url$1);\n\n    // URL that beings with an opening bracket, followed by a symbols.\n    // Note that the final state can still be `UrlOpen` (if the URL has a\n    // single opening bracket for some reason).\n    const UrlOpenQ = makeState(Url);\n    ta(UrlOpen, qsAccepting, UrlOpenQ);\n    const UrlOpenSyms = makeState(); // UrlOpen followed by some symbols it cannot end it\n    ta(UrlOpen, qsNonAccepting);\n\n    // URL that begins with an opening bracket, followed by some symbols\n    ta(UrlOpenQ, qsAccepting, UrlOpenQ);\n    ta(UrlOpenQ, qsNonAccepting, UrlOpenSyms);\n    ta(UrlOpenSyms, qsAccepting, UrlOpenQ);\n    ta(UrlOpenSyms, qsNonAccepting, UrlOpenSyms);\n\n    // Close brace/bracket to become regular URL\n    tt(UrlOpenQ, CLOSE, Url$1);\n    tt(UrlOpenSyms, CLOSE, Url$1);\n  }\n  tt(Start, LOCALHOST, DomainDotTld); // localhost is a valid URL state\n  tt(Start, NL, Nl); // single new line\n\n  return {\n    start: Start,\n    tokens: tk\n  };\n}\n\n/**\n * Run the parser state machine on a list of scanned string-based tokens to\n * create a list of multi tokens, each of which represents a URL, email address,\n * plain text, etc.\n *\n * @param {State<MultiToken>} start parser start state\n * @param {string} input the original input used to generate the given tokens\n * @param {Token[]} tokens list of scanned tokens\n * @returns {MultiToken[]}\n */\nfunction run(start, input, tokens) {\n  let len = tokens.length;\n  let cursor = 0;\n  let multis = [];\n  let textTokens = [];\n  while (cursor < len) {\n    let state = start;\n    let secondState = null;\n    let nextState = null;\n    let multiLength = 0;\n    let latestAccepting = null;\n    let sinceAccepts = -1;\n    while (cursor < len && !(secondState = state.go(tokens[cursor].t))) {\n      // Starting tokens with nowhere to jump to.\n      // Consider these to be just plain text\n      textTokens.push(tokens[cursor++]);\n    }\n    while (cursor < len && (nextState = secondState || state.go(tokens[cursor].t))) {\n      // Get the next state\n      secondState = null;\n      state = nextState;\n\n      // Keep track of the latest accepting state\n      if (state.accepts()) {\n        sinceAccepts = 0;\n        latestAccepting = state;\n      } else if (sinceAccepts >= 0) {\n        sinceAccepts++;\n      }\n      cursor++;\n      multiLength++;\n    }\n    if (sinceAccepts < 0) {\n      // No accepting state was found, part of a regular text token add\n      // the first text token to the text tokens array and try again from\n      // the next\n      cursor -= multiLength;\n      if (cursor < len) {\n        textTokens.push(tokens[cursor]);\n        cursor++;\n      }\n    } else {\n      // Accepting state!\n      // First close off the textTokens (if available)\n      if (textTokens.length > 0) {\n        multis.push(initMultiToken(Text, input, textTokens));\n        textTokens = [];\n      }\n\n      // Roll back to the latest accepting state\n      cursor -= sinceAccepts;\n      multiLength -= sinceAccepts;\n\n      // Create a new multitoken\n      const Multi = latestAccepting.t;\n      const subtokens = tokens.slice(cursor - multiLength, cursor);\n      multis.push(initMultiToken(Multi, input, subtokens));\n    }\n  }\n\n  // Finally close off the textTokens (if available)\n  if (textTokens.length > 0) {\n    multis.push(initMultiToken(Text, input, textTokens));\n  }\n  return multis;\n}\n\n/**\n * Utility function for instantiating a new multitoken with all the relevant\n * fields during parsing.\n * @param {new (value: string, tokens: Token[]) => MultiToken} Multi class to instantiate\n * @param {string} input original input string\n * @param {Token[]} tokens consecutive tokens scanned from input string\n * @returns {MultiToken}\n */\nfunction initMultiToken(Multi, input, tokens) {\n  const startIdx = tokens[0].s;\n  const endIdx = tokens[tokens.length - 1].e;\n  const value = input.slice(startIdx, endIdx);\n  return new Multi(value, tokens);\n}\n\nconst warn = typeof console !== 'undefined' && console && console.warn || (() => {});\nconst warnAdvice = 'until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.';\n\n// Side-effect initialization state\nconst INIT = {\n  scanner: null,\n  parser: null,\n  tokenQueue: [],\n  pluginQueue: [],\n  customSchemes: [],\n  initialized: false\n};\n\n/**\n * @typedef {{\n * \tstart: State<string>,\n * \ttokens: { groups: Collections<string> } & typeof tk\n * }} ScannerInit\n */\n\n/**\n * @typedef {{\n * \tstart: State<MultiToken>,\n * \ttokens: typeof multi\n * }} ParserInit\n */\n\n/**\n * @typedef {(arg: { scanner: ScannerInit }) => void} TokenPlugin\n */\n\n/**\n * @typedef {(arg: { scanner: ScannerInit, parser: ParserInit }) => void} Plugin\n */\n\n/**\n * De-register all plugins and reset the internal state-machine. Used for\n * testing; not required in practice.\n * @private\n */\nfunction reset() {\n  State.groups = {};\n  INIT.scanner = null;\n  INIT.parser = null;\n  INIT.tokenQueue = [];\n  INIT.pluginQueue = [];\n  INIT.customSchemes = [];\n  INIT.initialized = false;\n  return INIT;\n}\n\n/**\n * Register a token plugin to allow the scanner to recognize additional token\n * types before the parser state machine is constructed from the results.\n * @param {string} name of plugin to register\n * @param {TokenPlugin} plugin function that accepts the scanner state machine\n * and available scanner tokens and collections and extends the state machine to\n * recognize additional tokens or groups.\n */\nfunction registerTokenPlugin(name, plugin) {\n  if (typeof plugin !== 'function') {\n    throw new Error(`linkifyjs: Invalid token plugin ${plugin} (expects function)`);\n  }\n  for (let i = 0; i < INIT.tokenQueue.length; i++) {\n    if (name === INIT.tokenQueue[i][0]) {\n      warn(`linkifyjs: token plugin \"${name}\" already registered - will be overwritten`);\n      INIT.tokenQueue[i] = [name, plugin];\n      return;\n    }\n  }\n  INIT.tokenQueue.push([name, plugin]);\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register token plugin \"${name}\" ${warnAdvice}`);\n  }\n}\n\n/**\n * Register a linkify plugin\n * @param {string} name of plugin to register\n * @param {Plugin} plugin function that accepts the parser state machine and\n * extends the parser to recognize additional link types\n */\nfunction registerPlugin(name, plugin) {\n  if (typeof plugin !== 'function') {\n    throw new Error(`linkifyjs: Invalid plugin ${plugin} (expects function)`);\n  }\n  for (let i = 0; i < INIT.pluginQueue.length; i++) {\n    if (name === INIT.pluginQueue[i][0]) {\n      warn(`linkifyjs: plugin \"${name}\" already registered - will be overwritten`);\n      INIT.pluginQueue[i] = [name, plugin];\n      return;\n    }\n  }\n  INIT.pluginQueue.push([name, plugin]);\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register plugin \"${name}\" ${warnAdvice}`);\n  }\n}\n\n/**\n * Detect URLs with the following additional protocol. Anything with format\n * \"protocol://...\" will be considered a link. If `optionalSlashSlash` is set to\n * `true`, anything with format \"protocol:...\" will be considered a link.\n * @param {string} scheme\n * @param {boolean} [optionalSlashSlash]\n */\nfunction registerCustomProtocol(scheme, optionalSlashSlash = false) {\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register custom scheme \"${scheme}\" ${warnAdvice}`);\n  }\n  if (!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(scheme)) {\n    throw new Error(`linkifyjs: incorrect scheme format.\n1. Must only contain digits, lowercase ASCII letters or \"-\"\n2. Cannot start or end with \"-\"\n3. \"-\" cannot repeat`);\n  }\n  INIT.customSchemes.push([scheme, optionalSlashSlash]);\n}\n\n/**\n * Initialize the linkify state machine. Called automatically the first time\n * linkify is called on a string, but may be called manually as well.\n */\nfunction init() {\n  // Initialize scanner state machine and plugins\n  INIT.scanner = init$2(INIT.customSchemes);\n  for (let i = 0; i < INIT.tokenQueue.length; i++) {\n    INIT.tokenQueue[i][1]({\n      scanner: INIT.scanner\n    });\n  }\n\n  // Initialize parser state machine and plugins\n  INIT.parser = init$1(INIT.scanner.tokens);\n  for (let i = 0; i < INIT.pluginQueue.length; i++) {\n    INIT.pluginQueue[i][1]({\n      scanner: INIT.scanner,\n      parser: INIT.parser\n    });\n  }\n  INIT.initialized = true;\n  return INIT;\n}\n\n/**\n * Parse a string into tokens that represent linkable and non-linkable sub-components\n * @param {string} str\n * @return {MultiToken[]} tokens\n */\nfunction tokenize(str) {\n  if (!INIT.initialized) {\n    init();\n  }\n  return run(INIT.parser.start, str, run$1(INIT.scanner.start, str));\n}\ntokenize.scan = run$1; // for testing\n\n/**\n * Find a list of linkable items in the given string.\n * @param {string} str string to find links in\n * @param {string | Opts} [type] either formatting options or specific type of\n * links to find, e.g., 'url' or 'email'\n * @param {Opts} [opts] formatting options for final output. Cannot be specified\n * if opts already provided in `type` argument\n */\nfunction find(str, type = null, opts = null) {\n  if (type && typeof type === 'object') {\n    if (opts) {\n      throw Error(`linkifyjs: Invalid link type ${type}; must be a string`);\n    }\n    opts = type;\n    type = null;\n  }\n  const options = new Options(opts);\n  const tokens = tokenize(str);\n  const filtered = [];\n  for (let i = 0; i < tokens.length; i++) {\n    const token = tokens[i];\n    if (token.isLink && (!type || token.t === type) && options.check(token)) {\n      filtered.push(token.toFormattedObject(options));\n    }\n  }\n  return filtered;\n}\n\n/**\n * Is the given string valid linkable text of some sort. Note that this does not\n * trim the text for you.\n *\n * Optionally pass in a second `type` param, which is the type of link to test\n * for.\n *\n * For example,\n *\n *     linkify.test(str, 'email');\n *\n * Returns `true` if str is a valid email.\n * @param {string} str string to test for links\n * @param {string} [type] optional specific link type to look for\n * @returns boolean true/false\n */\nfunction test(str, type = null) {\n  const tokens = tokenize(str);\n  return tokens.length === 1 && tokens[0].isLink && (!type || tokens[0].t === type);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/linkifyjs/dist/linkify.es.js\n");

/***/ })

};
;