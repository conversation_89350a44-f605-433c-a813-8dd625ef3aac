import { useCallback } from 'react';
import useSWR from 'swr';
import { endpoints, fetcher } from 'src/lib/axios';
import { PLT1RoadOrderDetailsDto } from '../types/plt1-details.types';

/**
 * Custom hook for fetching and managing T1 order details
 * @param orderId The ID of the T1 order to fetch
 */
export const usePLT1RoadOrderDetails = (orderId: string) => {

  // Fetch order details using SWR
  const {
    data: response,
    error,
    isLoading,
    mutate
  } = useSWR<{ isSuccessful: boolean; errorMessages?: string[]; data?: PLT1RoadOrderDetailsDto }>(
    orderId ? `${endpoints.plt1.roadDetails}/${orderId}` : undefined,
    fetcher
  );

  // Extract the order from the response
  const order: PLT1RoadOrderDetailsDto | undefined = response?.data;

  // Reload the order data
  const reload = useCallback(async () => {
    return await mutate();
  }, [mutate]);

  return {
    order,
    isLoading,
    error,
    reload
  };
};
