'use client';

import { useState, useRef } from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import CardHeader from '@mui/material/CardHeader';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Tooltip from '@mui/material/Tooltip';
import Grid from '@mui/material/Grid2';

import { Iconify } from 'src/components/iconify';
import { Field } from 'src/components/hook-form';
import { useTranslate } from 'src/locales';
import { ConfirmDialog } from 'src/components/custom-dialog';

// ----------------------------------------------------------------------

export interface PLT1MerchandisePositionData {
  id?: string;
  invoicePositionNumber: number;
  goodsDescription: string;
  translatedGoodsDescription: string;
  packageMarks: string;
  numberOfPackages: number;
  packageType: string;
  packageTypeDescription: string;
  commodityCode: string;
  fullTariffCode: string;
  grossMass: number;
  grossMassUnit: string;
  netMass: number;
  netMassUnit: string;
  positionValue: number;
  positionValueCurrency: string;
  containerNumbers: string[];
  previousDocuments: string[];
  merchandisePositionsId: string;
}

const DEFAULT_POSITION = {
  id: null,
  invoicePositionNumber: 0,
  goodsDescription: '',
  translatedGoodsDescription: '',
  packageMarks: '',
  numberOfPackages: 0,
  packageType: '',
  packageTypeDescription: '',
  commodityCode: '',
  fullTariffCode: '',
  grossMass: 0,
  grossMassUnit: 'kg',
  netMass: 0,
  netMassUnit: 'kg',
  positionValue: 0,
  positionValueCurrency: 'USD',
  containerNumbers: [],
  previousDocuments: [],
  merchandisePositionsId: '',
};

// ----------------------------------------------------------------------

interface PLT1MerchandisePositionsFormProps {
  t1OrderId: string;
  readOnly?: boolean;
}

export default function PLT1MerchandisePositionsForm({
  t1OrderId,
  readOnly = false,
}: PLT1MerchandisePositionsFormProps) {
  const { t } = useTranslate();
  const { control, watch, getValues } = useFormContext();

  const fieldArray = useFieldArray({
    control,
    name: 'merchandisePositions.positions',
  });

  const merchandisePositions = watch('merchandisePositions');

  // State for position dialog
  const [openPositionDialog, setOpenPositionDialog] = useState(false);
  const [currentPositionIndex, setCurrentPositionIndex] = useState<number | null>(null);
  const [deletePositionIndex, setDeletePositionIndex] = useState<number | null>(null);
  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const deleteButtonRef = useRef<HTMLButtonElement>(null);

  // Handle opening the position dialog for adding a new position
  const handleAddPosition = () => {
    setCurrentPositionIndex(null);
    // Add a new position with default values
    const newPosition = {
      ...DEFAULT_POSITION,
      merchandisePositionsId: merchandisePositions?.id || '',
    };
    fieldArray.append(newPosition);
    setCurrentPositionIndex(fieldArray.fields.length); // Set to the new index
    setOpenPositionDialog(true);
  };

  // Handle opening the position dialog for editing an existing position
  const handleEditPosition = (index: number) => {
    setCurrentPositionIndex(index);
    setOpenPositionDialog(true);
  };

  // Handle closing the position dialog
  const handleClosePositionDialog = () => {
    setOpenPositionDialog(false);
    // If we were adding a new position and user cancels, remove the empty position
    if (currentPositionIndex !== null && currentPositionIndex === fieldArray.fields.length - 1) {
      const position = getValues(`merchandisePositions.positions.${currentPositionIndex}`);
      // Check if it's an empty position (all fields are default values)
      const isEmpty = !position.goodsDescription && !position.translatedGoodsDescription &&
                     !position.packageMarks && !position.commodityCode && !position.fullTariffCode &&
                     position.numberOfPackages === 0 && position.grossMass === 0 && position.netMass === 0;
      if (isEmpty) {
        fieldArray.remove(currentPositionIndex);
      }
    }
    setCurrentPositionIndex(null);
  };

  // Handle opening the delete confirmation dialog
  const handleOpenDeleteConfirm = (index: number) => {
    setDeletePositionIndex(index);
    setOpenDeleteConfirm(true);
  };

  // Handle closing the delete confirmation dialog
  const handleCloseDeleteConfirm = () => {
    setOpenDeleteConfirm(false);
    if (deletePositionIndex !== null) {
      deleteButtonRef.current?.focus();
    }
  };

  // Handle deleting a position
  const handleDeletePosition = () => {
    if (deletePositionIndex !== null) {
      fieldArray.remove(deletePositionIndex);
    }
    handleCloseDeleteConfirm();
  };

  // Handle saving a position (just close the dialog since form is already updated)
  const handleSavePosition = () => {
    handleClosePositionDialog();
  };

  // Render the position form in the dialog - using React Hook Form fields
  const renderPositionForm = () => {
    if (currentPositionIndex === null) return null;

    return (
      <Stack spacing={3}>
        {/* Invoice Position Number */}
        <Field.Text
          name={`merchandisePositions.positions.${currentPositionIndex}.invoicePositionNumber`}
          label={t('plt1.details.documents.merchandisePositions.form.invoicePositionNumber')}
          type="number"
          disabled={readOnly}
        />

        {/* Goods Description Section */}
        <Box
          sx={{
            rowGap: 3,
            pt: 2,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' },
          }}
        >
          <Field.Text
            name={`merchandisePositions.positions.${currentPositionIndex}.goodsDescription`}
            label={t('plt1.details.documents.merchandisePositions.form.goodsDescription')}
            multiline
            rows={2}
            disabled={readOnly}
          />
          <Field.Text
            name={`merchandisePositions.positions.${currentPositionIndex}.translatedGoodsDescription`}
            label={t('plt1.details.documents.merchandisePositions.form.translatedGoodsDescription')}
            multiline
            rows={2}
            disabled={readOnly}
          />
        </Box>

        {/* Package Information */}
        <Box
          sx={{
            rowGap: 2.5,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(3, 1fr)' },
          }}
        >
          <Field.Text
            name={`merchandisePositions.positions.${currentPositionIndex}.numberOfPackages`}
            label={t('plt1.details.documents.merchandisePositions.form.numberOfPackages')}
            type="number"
            disabled={readOnly}
          />
          <Field.Text
            name={`merchandisePositions.positions.${currentPositionIndex}.packageType`}
            label={t('plt1.details.documents.merchandisePositions.form.packageType')}

            disabled={readOnly}
          />
          <Field.Text
            name={`merchandisePositions.positions.${currentPositionIndex}.packageTypeDescription`}
            label={t('plt1.details.documents.merchandisePositions.form.packageTypeDescription')}
            disabled={readOnly}
          />
        </Box>

        <Field.Text
          name={`merchandisePositions.positions.${currentPositionIndex}.packageMarks`}
          label={t('plt1.details.documents.merchandisePositions.form.packageMarks')}
          disabled={readOnly}
        />

        {/* Codes */}
        <Box
          sx={{
            rowGap: 3,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' },
          }}
        >
          <Field.Text
            name={`merchandisePositions.positions.${currentPositionIndex}.commodityCode`}
            label={t('plt1.details.documents.merchandisePositions.form.commodityCode')}
            disabled={readOnly}
          />
          <Field.Text
            name={`merchandisePositions.positions.${currentPositionIndex}.fullTariffCode`}
            label={t('plt1.details.documents.merchandisePositions.form.fullTariffCode')}
            disabled={readOnly}
          />
        </Box>

        {/* Weight Information */}
        <Box
          sx={{
            rowGap: 3,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(4, 1fr)' },
          }}
        >
          <Field.Text
            name={`merchandisePositions.positions.${currentPositionIndex}.grossMass`}
            label={t('plt1.details.documents.merchandisePositions.form.grossMass')}
            type="number"
            disabled={readOnly}
          />
          <Field.Text
            name={`merchandisePositions.positions.${currentPositionIndex}.grossMassUnit`}
            label={t('plt1.details.documents.merchandisePositions.form.grossMassUnit')}
            disabled={readOnly}
          />
          <Field.Text
            name={`merchandisePositions.positions.${currentPositionIndex}.netMass`}
            label={t('plt1.details.documents.merchandisePositions.form.netMass')}
            type="number"
            disabled={readOnly}
          />
          <Field.Text
            name={`merchandisePositions.positions.${currentPositionIndex}.netMassUnit`}
            label={t('plt1.details.documents.merchandisePositions.form.netMassUnit')}
            disabled={readOnly}
          />
        </Box>

        {/* Position Value */}
        <Box
          sx={{
            rowGap: 2.5,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' },
          }}
        >
          <Field.Text
            name={`merchandisePositions.positions.${currentPositionIndex}.positionValue`}
            label={t('plt1.details.documents.merchandisePositions.form.positionValue')}
            type="number"
            disabled={readOnly}
          />
          <Field.Text
            name={`merchandisePositions.positions.${currentPositionIndex}.positionValueCurrency`}
            label={t('plt1.details.documents.merchandisePositions.form.positionValueCurrency')}
            disabled={readOnly}
          />
        </Box>
      </Stack>
    );
  };

  // Render the positions table
  const renderPositionsTable = () => (
    <TableContainer component={Paper}>
      <Table sx={{ minWidth: 650 }} aria-label="merchandise positions table">
        <TableHead>
          <TableRow>
            <TableCell>{t('plt1.details.documents.merchandisePositions.form.invoicePositionNumber')}</TableCell>
            <TableCell>{t('plt1.details.documents.merchandisePositions.form.goodsDescription')}</TableCell>
            <TableCell>{t('plt1.details.documents.merchandisePositions.form.numberOfPackages')}</TableCell>
            <TableCell>{t('plt1.details.documents.merchandisePositions.form.packageType')}</TableCell>
            <TableCell>{t('plt1.details.documents.merchandisePositions.form.grossMass')}</TableCell>
            <TableCell>{t('plt1.details.documents.merchandisePositions.form.netMass')}</TableCell>
            <TableCell>{t('plt1.details.documents.merchandisePositions.form.positionValue')}</TableCell>
            <TableCell align="right">{t('common.actions')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {fieldArray.fields.length === 0 ? (
            <TableRow>
              <TableCell colSpan={8} align="center">
                <Typography variant="body2" color="text.secondary">
                  {t('plt1.details.documents.merchandisePositions.noPositions')}
                </Typography>
              </TableCell>
            </TableRow>
          ) : (
            fieldArray.fields.map((field, index) => {
              const position = getValues(`merchandisePositions.positions.${index}`) || {};
              return (
                <TableRow key={field.id}>
                  <TableCell>
                    {position.invoicePositionNumber ?? '-'}
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ maxWidth: 200 }}>
                      {position.goodsDescription || '-'}
                    </Typography>
                    {position.translatedGoodsDescription && (
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                        {position.translatedGoodsDescription}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    {position.numberOfPackages ?? '-'}
                  </TableCell>
                  <TableCell>
                    {position.packageTypeDescription || position.packageType || '-'}
                  </TableCell>
                  <TableCell>
                    {position.grossMass ?? '-'} {position.grossMassUnit || ''}
                  </TableCell>
                  <TableCell>
                    {position.netMass ?? '-'} {position.netMassUnit || ''}
                  </TableCell>
                  <TableCell>
                    {position.positionValue ?? '-'} {position.positionValueCurrency || ''}
                  </TableCell>
                  <TableCell align="right">
                    <Stack direction="row" spacing={1} justifyContent="flex-end">
                      <Tooltip title={t('common.edit')}>
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleEditPosition(index)}
                          disabled={readOnly}
                        >
                          <Iconify icon="eva:edit-fill" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={t('common.delete')}>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleOpenDeleteConfirm(index)}
                          ref={deletePositionIndex === index ? deleteButtonRef : null}
                          disabled={readOnly}
                        >
                          <Iconify icon="eva:trash-2-outline" />
                        </IconButton>
                      </Tooltip>
                    </Stack>
                  </TableCell>
                </TableRow>
              );
            })
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );

  // Render the totals summary
  const renderTotalsSummary = () => {
    const totals = merchandisePositions?.totals;

    if (!totals) {
      return null;
    }

    return (
      <Card sx={{ boxShadow: 'none', mb: 3 }}>
        <CardHeader
          title={t('plt1.details.documents.merchandisePositions.form.totalsTitle')}
        />
        <Divider sx={{ borderStyle: 'dashed' }} />
        <Box sx={{ p: 3 }}>
          <Grid container spacing={3}>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                  {t('plt1.details.documents.merchandisePositions.form.totalNumberOfPositions')}
                </Typography>
                <Typography variant="h6">
                  {totals.totalNumberOfPositions || 0}
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                  {t('plt1.details.documents.merchandisePositions.form.totalNumberOfPackages')}
                </Typography>
                <Typography variant="h6">
                  {totals.totalNumberOfPackages || 0}
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                  {t('plt1.details.documents.merchandisePositions.form.totalGrossMass')}
                </Typography>
                <Typography variant="h6">
                  {totals.totalGrossMass || 0} {totals.totalGrossMassUnit || ''}
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                  {t('plt1.details.documents.merchandisePositions.form.totalNetMass')}
                </Typography>
                <Typography variant="h6">
                  {totals.totalNetMass || 0} {totals.totalNetMassUnit || ''}
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                  {t('plt1.details.documents.merchandisePositions.form.totalPositionsValue')}
                </Typography>
                <Typography variant="h6">
                  {totals.totalPositionsValue || 0} {totals.totalPositionsValueCurrency || ''}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Card>
    );
  };

  return (
    <Stack spacing={3}>
      {/* Totals Summary */}
      {renderTotalsSummary()}

      {/* Positions Table */}
      <Card sx={{ boxShadow: 'none' }}>
        <CardHeader
          title={t('plt1.details.documents.merchandisePositions.form.positionsTitle')}
          action={
            <Button
              variant="contained"
              size="small"
              startIcon={<Iconify icon="eva:plus-fill" />}
              onClick={handleAddPosition}
              sx={{ mb: 2 }}
              disabled={readOnly}
            >
              {t('plt1.details.documents.merchandisePositions.addPosition')}
            </Button>
          }
        />
        <Divider sx={{ borderStyle: 'dashed' }} />
        <Box sx={{ p: 3 }}>
          {renderPositionsTable()}

          {fieldArray.fields.length > 0 && (
            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
              <Button
                variant="soft"
                color="primary"
                startIcon={<Iconify icon="eva:plus-fill" />}
                onClick={handleAddPosition}
                disabled={readOnly}
              >
                {t('plt1.details.documents.merchandisePositions.addPosition')}
              </Button>
            </Box>
          )}
        </Box>
      </Card>

      {/* Position Dialog */}
      <Dialog
        open={openPositionDialog}
        onClose={handleClosePositionDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {currentPositionIndex === null
            ? t('plt1.details.documents.merchandisePositions.addPosition')
            : t('plt1.details.documents.merchandisePositions.editPosition')}
        </DialogTitle>
        <DialogContent>
          {renderPositionForm()}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePositionDialog}>
            {t('common.cancel')}
          </Button>
          <Button variant="contained" onClick={handleSavePosition}>
            {t('common.save')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={openDeleteConfirm}
        onClose={handleCloseDeleteConfirm}
        title={t('plt1.details.documents.merchandisePositions.confirmDeleteDialog.title')}
        content={t('plt1.details.documents.merchandisePositions.confirmDeleteDialog.content')}
        action={
          <Button variant="contained" color="error" onClick={handleDeletePosition}>
            {t('common.delete')}
          </Button>
        }
      />
    </Stack>
  );
}
