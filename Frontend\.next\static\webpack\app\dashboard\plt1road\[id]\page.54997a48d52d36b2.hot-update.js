"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1road/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/plt1-road-document-tabs.tsx":
/*!**************************************************************!*\
  !*** ./src/sections/orders/plt1/plt1-road-document-tabs.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1RoadDocumentsTabs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material/Box */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _mui_material_Tab__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/Tab */ \"(app-pages-browser)/./node_modules/@mui/material/Tab/Tab.js\");\n/* harmony import */ var _mui_material_Tabs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/Tabs */ \"(app-pages-browser)/./node_modules/@mui/material/Tabs/Tabs.js\");\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material/Card */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material/Divider */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/CardHeader */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var _tabs_road_plt1_cmr_tab__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tabs/road/plt1-cmr-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/road/plt1-cmr-tab.tsx\");\n/* harmony import */ var _tabs_plt1_commercial_invoice_tab__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tabs/plt1-commercial-invoice-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-commercial-invoice-tab.tsx\");\n/* harmony import */ var _tabs_plt1_packing_list_tab__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tabs/plt1-packing-list-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-packing-list-tab.tsx\");\n/* harmony import */ var _tabs_plt1_notifications_arrival_tab__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./tabs/plt1-notifications-arrival-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-notifications-arrival-tab.tsx\");\n/* harmony import */ var _tabs_plt1_transit_tab__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./tabs/plt1-transit-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-transit-tab.tsx\");\n/* harmony import */ var _tabs_plt1_merchandise_positions_tab__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./tabs/plt1-merchandise-positions-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-merchandise-positions-tab.tsx\");\n// src/sections/plt1-road-order-details/plt1-road-document-tabs.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// ----------------------------------------------------------------------\nvar TabNames = /*#__PURE__*/ function(TabNames) {\n    TabNames[\"CMR\"] = \"cmr\";\n    TabNames[\"COMMERCIAL_INVOICES\"] = \"commercialInvoices\";\n    TabNames[\"PACKING_LISTS\"] = \"packingLists\";\n    TabNames[\"NOTIFICATIONS\"] = \"notifications\";\n    TabNames[\"TRANSIT_DOCUMENTS\"] = \"transitDocuments\";\n    TabNames[\"MERCHANDISE_POSITIONS\"] = \"merchandisePositions\";\n    return TabNames;\n}(TabNames || {});\nfunction PLT1RoadDocumentsTabs(param) {\n    let { t1OrderId, order, readOnly = false, reload } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    // Try to restore the expanded state from sessionStorage\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"PLT1RoadDocumentsTabs.useState\": ()=>{\n            const savedTab = sessionStorage.getItem(\"plt1-road-current-tab-\".concat(t1OrderId));\n            return savedTab && Object.values(TabNames).includes(savedTab) ? savedTab : \"cmr\";\n        }\n    }[\"PLT1RoadDocumentsTabs.useState\"]);\n    // Save tab selection to sessionStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1RoadDocumentsTabs.useEffect\": ()=>{\n            sessionStorage.setItem(\"plt1-road-current-tab-\".concat(t1OrderId), currentTab);\n        }\n    }[\"PLT1RoadDocumentsTabs.useEffect\"], [\n        currentTab,\n        t1OrderId\n    ]);\n    const handleChangeTab = (event, newValue)=>{\n        setCurrentTab(newValue);\n    };\n    const TABS = [\n        {\n            value: \"cmr\",\n            label: t('plt1.details.documents.tabs.cmr'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_road_plt1_cmr_tab__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 64,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"commercialInvoices\",\n            label: t('plt1.details.documents.tabs.commercialInvoices'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_commercial_invoice_tab__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 69,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"packingLists\",\n            label: t('plt1.details.documents.tabs.packingLists'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_packing_list_tab__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 74,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"notifications\",\n            label: t('plt1.details.documents.tabs.notificationsOfArrival'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_notifications_arrival_tab__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 79,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"transitDocuments\",\n            label: t('plt1.details.documents.tabs.transitDocuments'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_transit_tab__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 84,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"merchandisePositions\",\n            label: t('plt1.details.documents.tabs.merchandisePositions'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_merchandise_positions_tab__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 89,\n                columnNumber: 24\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                title: t('plt1.details.documents.heading'),\n                sx: {\n                    mb: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 95,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                sx: {\n                    borderStyle: 'dashed'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 99,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tabs__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                value: currentTab,\n                onChange: handleChangeTab,\n                sx: {\n                    px: 2.5,\n                    boxShadow: (theme)=>\"inset 0 -2px 0 0 \".concat(theme.palette.divider)\n                },\n                children: TABS.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tab__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        value: tab.value,\n                        label: tab.label\n                    }, tab.value, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 21\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 100,\n                columnNumber: 13\n            }, this),\n            TABS.map((tab)=>tab.value === currentTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: tab.component\n                }, tab.value, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 25\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n        lineNumber: 94,\n        columnNumber: 9\n    }, this);\n}\n_s(PLT1RoadDocumentsTabs, \"xZNw/UDA+Vc50l0DEB7kcPEGJZo=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate\n    ];\n});\n_c = PLT1RoadDocumentsTabs;\nvar _c;\n$RefreshReg$(_c, \"PLT1RoadDocumentsTabs\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZWN0aW9ucy9vcmRlcnMvcGx0MS9wbHQxLXJvYWQtZG9jdW1lbnQtdGFicy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxtRUFBbUU7OztBQUNQO0FBRXhCO0FBQ0E7QUFDRTtBQUNBO0FBQ007QUFDTTtBQUVQO0FBQ087QUFDeUI7QUFDWjtBQUNtQjtBQUM3QjtBQUMyQjtBQUdoRix5RUFBeUU7QUFFekUsc0NBQUtlOzs7Ozs7O1dBQUFBO0VBQUFBO0FBa0JVLFNBQVNDLHNCQUFzQixLQUEwRTtRQUExRSxFQUFFQyxTQUFTLEVBQUVDLEtBQUssRUFBRUMsV0FBVyxLQUFLLEVBQUVDLE1BQU0sRUFBOEIsR0FBMUU7O0lBQzFDLE1BQU0sRUFBRUMsQ0FBQyxFQUFFLEdBQUdiLHlEQUFZQTtJQUUxQix3REFBd0Q7SUFDeEQsTUFBTSxDQUFDYyxZQUFZQyxjQUFjLEdBQUd2QiwrQ0FBUUE7MENBQVc7WUFDbkQsTUFBTXdCLFdBQVdDLGVBQWVDLE9BQU8sQ0FBQyx5QkFBbUMsT0FBVlQ7WUFDakUsT0FBT08sWUFBWUcsT0FBT0MsTUFBTSxDQUFDYixVQUFVYyxRQUFRLENBQUNMLFlBQzdDQTtRQUVYOztJQUVBLHVDQUF1QztJQUN2Q3ZCLGdEQUFTQTsyQ0FBQztZQUNOd0IsZUFBZUssT0FBTyxDQUFDLHlCQUFtQyxPQUFWYixZQUFhSztRQUNqRTswQ0FBRztRQUFDQTtRQUFZTDtLQUFVO0lBRTFCLE1BQU1jLGtCQUFrQixDQUFDQyxPQUF1QkM7UUFDNUNWLGNBQWNVO0lBQ2xCO0lBRUEsTUFBTUMsT0FBTztRQUNUO1lBQ0lDLEtBQUs7WUFDTEMsT0FBT2YsRUFBRTtZQUNUZ0IseUJBQVcsOERBQUM1QiwrREFBVUE7Z0JBQUNRLFdBQVdBO2dCQUFXQyxPQUFPQTtnQkFBT0MsVUFBVUE7Ozs7OztRQUN6RTtRQUNBO1lBQ0lnQixLQUFLO1lBQ0xDLE9BQU9mLEVBQUU7WUFDVGdCLHlCQUFXLDhEQUFDM0IseUVBQXlCQTtnQkFBQ08sV0FBV0E7Z0JBQVdDLE9BQU9BO2dCQUFPQyxVQUFVQTs7Ozs7O1FBQ3hGO1FBQ0E7WUFDSWdCLEtBQUs7WUFDTEMsT0FBT2YsRUFBRTtZQUNUZ0IseUJBQVcsOERBQUMxQixtRUFBbUJBO2dCQUFDTSxXQUFXQTtnQkFBV0MsT0FBT0E7Z0JBQU9DLFVBQVVBOzs7Ozs7UUFDbEY7UUFDQTtZQUNJZ0IsS0FBSztZQUNMQyxPQUFPZixFQUFFO1lBQ1RnQix5QkFBVyw4REFBQ3pCLDRFQUE2QkE7Z0JBQUNLLFdBQVdBO2dCQUFXQyxPQUFPQTtnQkFBT0MsVUFBVUE7Ozs7OztRQUM1RjtRQUNBO1lBQ0lnQixLQUFLO1lBQ0xDLE9BQU9mLEVBQUU7WUFDVGdCLHlCQUFXLDhEQUFDeEIsOERBQWNBO2dCQUFDSSxXQUFXQTtnQkFBV0MsT0FBT0E7Z0JBQU9DLFVBQVVBOzs7Ozs7UUFDN0U7UUFDQTtZQUNJZ0IsS0FBSztZQUNMQyxPQUFPZixFQUFFO1lBQ1RnQix5QkFBVyw4REFBQ3ZCLDRFQUEyQkE7Z0JBQUNHLFdBQVdBO2dCQUFXQyxPQUFPQTtnQkFBT0MsVUFBVUE7Ozs7OztRQUMxRjtLQUNIO0lBRUQscUJBQ0ksOERBQUNkLDBEQUFJQTs7MEJBQ0QsOERBQUNFLGlFQUFVQTtnQkFDUCtCLE9BQU9qQixFQUFFO2dCQUNUa0IsSUFBSTtvQkFBRUMsSUFBSTtnQkFBRTs7Ozs7OzBCQUVoQiw4REFBQ2xDLDhEQUFPQTtnQkFBQ2lDLElBQUk7b0JBQUVFLGFBQWE7Z0JBQVM7Ozs7OzswQkFDckMsOERBQUNyQywyREFBSUE7Z0JBQ0QrQixPQUFPYjtnQkFDUG9CLFVBQVVYO2dCQUNWUSxJQUFJO29CQUNBSSxJQUFJO29CQUNKQyxXQUFXLENBQUNDLFFBQVUsb0JBQTBDLE9BQXRCQSxNQUFNQyxPQUFPLENBQUNDLE9BQU87Z0JBQ25FOzBCQUVDYixLQUFLYyxHQUFHLENBQUMsQ0FBQ0Msb0JBQ1AsOERBQUM5QywwREFBR0E7d0JBRUFnQyxPQUFPYyxJQUFJZCxLQUFLO3dCQUNoQkMsT0FBT2EsSUFBSWIsS0FBSzt1QkFGWGEsSUFBSWQsS0FBSzs7Ozs7Ozs7OztZQU96QkQsS0FBS2MsR0FBRyxDQUNMLENBQUNDLE1BQ0dBLElBQUlkLEtBQUssS0FBS2IsNEJBQ1YsOERBQUNwQiwwREFBR0E7b0JBQWlCcUMsSUFBSTt3QkFBRVcsR0FBRztvQkFBRTs4QkFDM0JELElBQUlaLFNBQVM7bUJBRFJZLElBQUlkLEtBQUs7Ozs7Ozs7Ozs7O0FBTzNDO0dBdkZ3Qm5COztRQUNOUixxREFBWUE7OztLQUROUSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxtLm1hbGlrXFxzb3VyY2VcXHJlcG9zXFxSb3NzZXRhXFxGcm9udGVuZFxcc3JjXFxzZWN0aW9uc1xcb3JkZXJzXFxwbHQxXFxwbHQxLXJvYWQtZG9jdW1lbnQtdGFicy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3NlY3Rpb25zL3BsdDEtcm9hZC1vcmRlci1kZXRhaWxzL3BsdDEtcm9hZC1kb2N1bWVudC10YWJzLnRzeFxyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgU3ludGhldGljRXZlbnQsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmltcG9ydCBCb3ggZnJvbSAnQG11aS9tYXRlcmlhbC9Cb3gnO1xyXG5pbXBvcnQgVGFiIGZyb20gJ0BtdWkvbWF0ZXJpYWwvVGFiJztcclxuaW1wb3J0IFRhYnMgZnJvbSAnQG11aS9tYXRlcmlhbC9UYWJzJztcclxuaW1wb3J0IENhcmQgZnJvbSAnQG11aS9tYXRlcmlhbC9DYXJkJztcclxuaW1wb3J0IERpdmlkZXIgZnJvbSAnQG11aS9tYXRlcmlhbC9EaXZpZGVyJztcclxuaW1wb3J0IENhcmRIZWFkZXIgZnJvbSAnQG11aS9tYXRlcmlhbC9DYXJkSGVhZGVyJztcclxuXHJcbmltcG9ydCB7IHVzZVRyYW5zbGF0ZSB9IGZyb20gJ3NyYy9sb2NhbGVzJztcclxuaW1wb3J0IFBMVDFDTVJUYWIgZnJvbSAnLi90YWJzL3JvYWQvcGx0MS1jbXItdGFiJztcclxuaW1wb3J0IFBMVDFDb21tZXJjaWFsSW52b2ljZXNUYWIgZnJvbSAnLi90YWJzL3BsdDEtY29tbWVyY2lhbC1pbnZvaWNlLXRhYic7XHJcbmltcG9ydCBQTFQxUGFja2luZ0xpc3RzVGFiIGZyb20gJy4vdGFicy9wbHQxLXBhY2tpbmctbGlzdC10YWInO1xyXG5pbXBvcnQgUExUMU5vdGlmaWNhdGlvbnNPZkFycml2YWxUYWIgZnJvbSAnLi90YWJzL3BsdDEtbm90aWZpY2F0aW9ucy1hcnJpdmFsLXRhYic7XHJcbmltcG9ydCBQTFQxVHJhbnNpdFRhYiBmcm9tICcuL3RhYnMvcGx0MS10cmFuc2l0LXRhYic7XHJcbmltcG9ydCBQTFQxTWVyY2hhbmRpc2VQb3NpdGlvbnNUYWIgZnJvbSAnLi90YWJzL3BsdDEtbWVyY2hhbmRpc2UtcG9zaXRpb25zLXRhYic7XHJcblxyXG5cclxuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG5cclxuZW51bSBUYWJOYW1lcyB7XHJcbiAgICBDTVIgPSAnY21yJyxcclxuICAgIENPTU1FUkNJQUxfSU5WT0lDRVMgPSAnY29tbWVyY2lhbEludm9pY2VzJyxcclxuICAgIFBBQ0tJTkdfTElTVFMgPSAncGFja2luZ0xpc3RzJyxcclxuICAgIE5PVElGSUNBVElPTlMgPSAnbm90aWZpY2F0aW9ucycsXHJcbiAgICBUUkFOU0lUX0RPQ1VNRU5UUyA9ICd0cmFuc2l0RG9jdW1lbnRzJyxcclxuICAgIE1FUkNIQU5ESVNFX1BPU0lUSU9OUyA9ICdtZXJjaGFuZGlzZVBvc2l0aW9ucydcclxufVxyXG5cclxuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBQTFQxUm9hZERvY3VtZW50c1RhYnNQcm9wcyB7XHJcbiAgICB0MU9yZGVySWQ6IHN0cmluZztcclxuICAgIG9yZGVyPzogYW55O1xyXG4gICAgcmVhZE9ubHk/OiBib29sZWFuO1xyXG4gICAgcmVsb2FkPzogKCkgPT4gUHJvbWlzZTxhbnk+IHwgUHJvbWlzZTx2b2lkPiB8IHZvaWQ7XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBMVDFSb2FkRG9jdW1lbnRzVGFicyh7IHQxT3JkZXJJZCwgb3JkZXIsIHJlYWRPbmx5ID0gZmFsc2UsIHJlbG9hZCB9OiBQTFQxUm9hZERvY3VtZW50c1RhYnNQcm9wcykge1xyXG4gICAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGUoKTtcclxuICAgIFxyXG4gICAgLy8gVHJ5IHRvIHJlc3RvcmUgdGhlIGV4cGFuZGVkIHN0YXRlIGZyb20gc2Vzc2lvblN0b3JhZ2VcclxuICAgIGNvbnN0IFtjdXJyZW50VGFiLCBzZXRDdXJyZW50VGFiXSA9IHVzZVN0YXRlPFRhYk5hbWVzPigoKSA9PiB7XHJcbiAgICAgICAgY29uc3Qgc2F2ZWRUYWIgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKGBwbHQxLXJvYWQtY3VycmVudC10YWItJHt0MU9yZGVySWR9YCk7XHJcbiAgICAgICAgcmV0dXJuIHNhdmVkVGFiICYmIE9iamVjdC52YWx1ZXMoVGFiTmFtZXMpLmluY2x1ZGVzKHNhdmVkVGFiIGFzIFRhYk5hbWVzKSBcclxuICAgICAgICAgICAgPyAoc2F2ZWRUYWIgYXMgVGFiTmFtZXMpIFxyXG4gICAgICAgICAgICA6IFRhYk5hbWVzLkNNUjtcclxuICAgIH0pO1xyXG5cclxuICAgIC8vIFNhdmUgdGFiIHNlbGVjdGlvbiB0byBzZXNzaW9uU3RvcmFnZVxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBzZXNzaW9uU3RvcmFnZS5zZXRJdGVtKGBwbHQxLXJvYWQtY3VycmVudC10YWItJHt0MU9yZGVySWR9YCwgY3VycmVudFRhYik7XHJcbiAgICB9LCBbY3VycmVudFRhYiwgdDFPcmRlcklkXSk7XHJcblxyXG4gICAgY29uc3QgaGFuZGxlQ2hhbmdlVGFiID0gKGV2ZW50OiBTeW50aGV0aWNFdmVudCwgbmV3VmFsdWU6IFRhYk5hbWVzKSA9PiB7XHJcbiAgICAgICAgc2V0Q3VycmVudFRhYihuZXdWYWx1ZSk7XHJcbiAgICB9O1xyXG5cclxuICAgIGNvbnN0IFRBQlMgPSBbXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICB2YWx1ZTogVGFiTmFtZXMuQ01SLFxyXG4gICAgICAgICAgICBsYWJlbDogdCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy50YWJzLmNtcicpLFxyXG4gICAgICAgICAgICBjb21wb25lbnQ6IDxQTFQxQ01SVGFiIHQxT3JkZXJJZD17dDFPcmRlcklkfSBvcmRlcj17b3JkZXJ9IHJlYWRPbmx5PXtyZWFkT25seX0gLz5cclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgdmFsdWU6IFRhYk5hbWVzLkNPTU1FUkNJQUxfSU5WT0lDRVMsXHJcbiAgICAgICAgICAgIGxhYmVsOiB0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnRhYnMuY29tbWVyY2lhbEludm9pY2VzJyksXHJcbiAgICAgICAgICAgIGNvbXBvbmVudDogPFBMVDFDb21tZXJjaWFsSW52b2ljZXNUYWIgdDFPcmRlcklkPXt0MU9yZGVySWR9IG9yZGVyPXtvcmRlcn0gcmVhZE9ubHk9e3JlYWRPbmx5fSAvPlxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICB2YWx1ZTogVGFiTmFtZXMuUEFDS0lOR19MSVNUUyxcclxuICAgICAgICAgICAgbGFiZWw6IHQoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMudGFicy5wYWNraW5nTGlzdHMnKSxcclxuICAgICAgICAgICAgY29tcG9uZW50OiA8UExUMVBhY2tpbmdMaXN0c1RhYiB0MU9yZGVySWQ9e3QxT3JkZXJJZH0gb3JkZXI9e29yZGVyfSByZWFkT25seT17cmVhZE9ubHl9IC8+XHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIHZhbHVlOiBUYWJOYW1lcy5OT1RJRklDQVRJT05TLFxyXG4gICAgICAgICAgICBsYWJlbDogdCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy50YWJzLm5vdGlmaWNhdGlvbnNPZkFycml2YWwnKSxcclxuICAgICAgICAgICAgY29tcG9uZW50OiA8UExUMU5vdGlmaWNhdGlvbnNPZkFycml2YWxUYWIgdDFPcmRlcklkPXt0MU9yZGVySWR9IG9yZGVyPXtvcmRlcn0gcmVhZE9ubHk9e3JlYWRPbmx5fSAvPlxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICB2YWx1ZTogVGFiTmFtZXMuVFJBTlNJVF9ET0NVTUVOVFMsXHJcbiAgICAgICAgICAgIGxhYmVsOiB0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnRhYnMudHJhbnNpdERvY3VtZW50cycpLFxyXG4gICAgICAgICAgICBjb21wb25lbnQ6IDxQTFQxVHJhbnNpdFRhYiB0MU9yZGVySWQ9e3QxT3JkZXJJZH0gb3JkZXI9e29yZGVyfSByZWFkT25seT17cmVhZE9ubHl9IC8+XHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIHZhbHVlOiBUYWJOYW1lcy5NRVJDSEFORElTRV9QT1NJVElPTlMsXHJcbiAgICAgICAgICAgIGxhYmVsOiB0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnRhYnMubWVyY2hhbmRpc2VQb3NpdGlvbnMnKSxcclxuICAgICAgICAgICAgY29tcG9uZW50OiA8UExUMU1lcmNoYW5kaXNlUG9zaXRpb25zVGFiIHQxT3JkZXJJZD17dDFPcmRlcklkfSBvcmRlcj17b3JkZXJ9IHJlYWRPbmx5PXtyZWFkT25seX0gLz5cclxuICAgICAgICB9XHJcbiAgICBdO1xyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPENhcmQ+XHJcbiAgICAgICAgICAgIDxDYXJkSGVhZGVyXHJcbiAgICAgICAgICAgICAgICB0aXRsZT17dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5oZWFkaW5nJyl9XHJcbiAgICAgICAgICAgICAgICBzeD17eyBtYjogMiB9fVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8RGl2aWRlciBzeD17eyBib3JkZXJTdHlsZTogJ2Rhc2hlZCcgfX0gLz5cclxuICAgICAgICAgICAgPFRhYnNcclxuICAgICAgICAgICAgICAgIHZhbHVlPXtjdXJyZW50VGFifVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNoYW5nZVRhYn1cclxuICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgcHg6IDIuNSxcclxuICAgICAgICAgICAgICAgICAgICBib3hTaGFkb3c6ICh0aGVtZSkgPT4gYGluc2V0IDAgLTJweCAwIDAgJHt0aGVtZS5wYWxldHRlLmRpdmlkZXJ9YCxcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtUQUJTLm1hcCgodGFiKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3RhYi52YWx1ZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3RhYi52YWx1ZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9e3RhYi5sYWJlbH1cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgIDwvVGFicz5cclxuXHJcbiAgICAgICAgICAgIHtUQUJTLm1hcChcclxuICAgICAgICAgICAgICAgICh0YWIpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgdGFiLnZhbHVlID09PSBjdXJyZW50VGFiICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPEJveCBrZXk9e3RhYi52YWx1ZX0gc3g9e3sgcDogMyB9fT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0YWIuY29tcG9uZW50fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgPC9DYXJkPlxyXG4gICAgKTtcclxufSJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkJveCIsIlRhYiIsIlRhYnMiLCJDYXJkIiwiRGl2aWRlciIsIkNhcmRIZWFkZXIiLCJ1c2VUcmFuc2xhdGUiLCJQTFQxQ01SVGFiIiwiUExUMUNvbW1lcmNpYWxJbnZvaWNlc1RhYiIsIlBMVDFQYWNraW5nTGlzdHNUYWIiLCJQTFQxTm90aWZpY2F0aW9uc09mQXJyaXZhbFRhYiIsIlBMVDFUcmFuc2l0VGFiIiwiUExUMU1lcmNoYW5kaXNlUG9zaXRpb25zVGFiIiwiVGFiTmFtZXMiLCJQTFQxUm9hZERvY3VtZW50c1RhYnMiLCJ0MU9yZGVySWQiLCJvcmRlciIsInJlYWRPbmx5IiwicmVsb2FkIiwidCIsImN1cnJlbnRUYWIiLCJzZXRDdXJyZW50VGFiIiwic2F2ZWRUYWIiLCJzZXNzaW9uU3RvcmFnZSIsImdldEl0ZW0iLCJPYmplY3QiLCJ2YWx1ZXMiLCJpbmNsdWRlcyIsInNldEl0ZW0iLCJoYW5kbGVDaGFuZ2VUYWIiLCJldmVudCIsIm5ld1ZhbHVlIiwiVEFCUyIsInZhbHVlIiwibGFiZWwiLCJjb21wb25lbnQiLCJ0aXRsZSIsInN4IiwibWIiLCJib3JkZXJTdHlsZSIsIm9uQ2hhbmdlIiwicHgiLCJib3hTaGFkb3ciLCJ0aGVtZSIsInBhbGV0dGUiLCJkaXZpZGVyIiwibWFwIiwidGFiIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/plt1-road-document-tabs.tsx\n"));

/***/ })

});