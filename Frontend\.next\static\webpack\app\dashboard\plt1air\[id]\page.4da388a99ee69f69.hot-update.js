"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-merchandise-positions-form.tsx":
/*!****************************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-merchandise-positions-form.tsx ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1MerchandisePositionsForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material/Box */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/Card */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material/Stack */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material_Button__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/material/Button */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/material/Divider */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/CardHeader */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/Typography */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/IconButton */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _mui_material_Table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material/Table */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _mui_material_TableBody__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mui/material/TableBody */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material/TableCell */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _mui_material_TableContainer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material/TableContainer */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _mui_material_TableHead__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/TableHead */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/TableRow */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _mui_material_Paper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/Paper */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _mui_material_Dialog__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/material/Dialog */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _mui_material_DialogTitle__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/material/DialogTitle */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _mui_material_DialogContent__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/material/DialogContent */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _mui_material_DialogActions__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/material/DialogActions */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/Tooltip */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/material/Grid2 */ \"(app-pages-browser)/./node_modules/@mui/material/Grid2/Grid2.js\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/custom-dialog */ \"(app-pages-browser)/./src/components/custom-dialog/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst DEFAULT_POSITION = {\n    id: null,\n    goodsDescription: '',\n    translatedGoodsDescription: '',\n    packageMarks: '',\n    numberOfPackages: 0,\n    packageType: '',\n    packageTypeDescription: '',\n    commodityCode: '',\n    fullTariffCode: '',\n    grossMass: 0,\n    grossMassUnit: 'kg',\n    netMass: 0,\n    netMassUnit: 'kg',\n    containerNumbers: [],\n    previousDocuments: [],\n    merchandisePositionsId: ''\n};\nfunction PLT1MerchandisePositionsForm(param) {\n    let { t1OrderId, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_4__.useTranslate)();\n    const { control, watch, getValues } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFormContext)();\n    const fieldArray = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFieldArray)({\n        control,\n        name: 'merchandisePositions.positions'\n    });\n    const merchandisePositions = watch('merchandisePositions');\n    // State for position dialog\n    const [openPositionDialog, setOpenPositionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPositionIndex, setCurrentPositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletePositionIndex, setDeletePositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Handle opening the position dialog for adding a new position\n    const handleAddPosition = ()=>{\n        setCurrentPositionIndex(null);\n        // Add a new position with default values\n        const newPosition = {\n            ...DEFAULT_POSITION,\n            merchandisePositionsId: (merchandisePositions === null || merchandisePositions === void 0 ? void 0 : merchandisePositions.id) || ''\n        };\n        fieldArray.append(newPosition);\n        setCurrentPositionIndex(fieldArray.fields.length); // Set to the new index\n        setOpenPositionDialog(true);\n    };\n    // Handle opening the position dialog for editing an existing position\n    const handleEditPosition = (index)=>{\n        setCurrentPositionIndex(index);\n        setOpenPositionDialog(true);\n    };\n    // Handle closing the position dialog\n    const handleClosePositionDialog = ()=>{\n        setOpenPositionDialog(false);\n        // If we were adding a new position and user cancels, remove the empty position\n        if (currentPositionIndex !== null && currentPositionIndex === fieldArray.fields.length - 1) {\n            const position = getValues(\"merchandisePositions.positions.\".concat(currentPositionIndex));\n            // Check if it's an empty position (all fields are default values)\n            const isEmpty = !position.goodsDescription && !position.translatedGoodsDescription && !position.packageMarks && !position.commodityCode && !position.fullTariffCode && position.numberOfPackages === 0 && position.grossMass === 0 && position.netMass === 0;\n            if (isEmpty) {\n                fieldArray.remove(currentPositionIndex);\n            }\n        }\n        setCurrentPositionIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeletePositionIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deletePositionIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting a position\n    const handleDeletePosition = ()=>{\n        if (deletePositionIndex !== null) {\n            fieldArray.remove(deletePositionIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving a position (just close the dialog since form is already updated)\n    const handleSavePosition = ()=>{\n        handleClosePositionDialog();\n    };\n    // Render the position form in the dialog - using React Hook Form fields\n    const renderPositionForm = ()=>{\n        if (currentPositionIndex === null) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 3,\n                        pt: 2,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(2, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".goodsDescription\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.goodsDescription'),\n                            multiline: true,\n                            rows: 2,\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".translatedGoodsDescription\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.translatedGoodsDescription'),\n                            multiline: true,\n                            rows: 2,\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 2.5,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(3, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".numberOfPackages\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.numberOfPackages'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".packageType\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.packageType'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".packageTypeDescription\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.packageTypeDescription'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".packageMarks\"),\n                    label: t('plt1.details.documents.merchandisePositions.form.packageMarks'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 3,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(2, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".commodityCode\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.commodityCode'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".fullTariffCode\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.fullTariffCode'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 3,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(4, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".grossMass\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.grossMass'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".grossMassUnit\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.grossMassUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".netMass\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.netMass'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".netMassUnit\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.netMassUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the positions table\n    const renderPositionsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableContainer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            component: _mui_material_Paper__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Table__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"merchandise positions table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableHead__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.goodsDescription')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.numberOfPackages')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.packageType')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.grossMass')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.netMass')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableBody__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        children: fieldArray.fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                colSpan: 6,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.merchandisePositions.noPositions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, this) : fieldArray.fields.map((field, index)=>{\n                            const position = getValues(\"merchandisePositions.positions.\".concat(index)) || {};\n                            var _position_numberOfPackages, _position_grossMass, _position_netMass;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                variant: \"body2\",\n                                                sx: {\n                                                    maxWidth: 200\n                                                },\n                                                children: position.goodsDescription || '-'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 21\n                                            }, this),\n                                            position.translatedGoodsDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                variant: \"caption\",\n                                                color: \"text.secondary\",\n                                                sx: {\n                                                    display: 'block',\n                                                    mt: 0.5\n                                                },\n                                                children: position.translatedGoodsDescription\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: (_position_numberOfPackages = position.numberOfPackages) !== null && _position_numberOfPackages !== void 0 ? _position_numberOfPackages : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: position.packageTypeDescription || position.packageType || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_position_grossMass = position.grossMass) !== null && _position_grossMass !== void 0 ? _position_grossMass : '-',\n                                            \" \",\n                                            position.grossMassUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_position_netMass = position.netMass) !== null && _position_netMass !== void 0 ? _position_netMass : '-',\n                                            \" \",\n                                            position.netMassUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditPosition(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deletePositionIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n            lineNumber: 292,\n            columnNumber: 5\n        }, this);\n    // Render the totals summary\n    const renderTotalsSummary = ()=>{\n        const totals = merchandisePositions === null || merchandisePositions === void 0 ? void 0 : merchandisePositions.totals;\n        if (!totals) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            sx: {\n                boxShadow: 'none',\n                mb: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    title: t('plt1.details.documents.merchandisePositions.form.totalsTitle')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    sx: {\n                        borderStyle: 'dashed'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalNumberOfPositions')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: totals.totalNumberOfPositions || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalNumberOfPackages')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: totals.totalNumberOfPackages || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalGrossMass')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: [\n                                                totals.totalGrossMass || 0,\n                                                \" \",\n                                                totals.totalGrossMassUnit || ''\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalNetMass')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: [\n                                                totals.totalNetMass || 0,\n                                                \" \",\n                                                totals.totalNetMassUnit || ''\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n            lineNumber: 383,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        spacing: 3,\n        children: [\n            renderTotalsSummary(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        title: t('plt1.details.documents.merchandisePositions.form.positionsTitle'),\n                        action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            variant: \"contained\",\n                            size: \"small\",\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                icon: \"eva:plus-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: handleAddPosition,\n                            sx: {\n                                mb: 2\n                            },\n                            disabled: readOnly,\n                            children: t('plt1.details.documents.merchandisePositions.addPosition')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: [\n                            renderPositionsTable(),\n                            fieldArray.fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                sx: {\n                                    mt: 2,\n                                    display: 'flex',\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    variant: \"soft\",\n                                    color: \"primary\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                        icon: \"eva:plus-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    onClick: handleAddPosition,\n                                    disabled: readOnly,\n                                    children: t('plt1.details.documents.merchandisePositions.addPosition')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 442,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Dialog__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                open: openPositionDialog,\n                onClose: handleClosePositionDialog,\n                maxWidth: \"md\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_DialogTitle__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        children: currentPositionIndex === null ? t('plt1.details.documents.merchandisePositions.addPosition') : t('plt1.details.documents.merchandisePositions.editPosition')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 485,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_DialogContent__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        children: renderPositionForm()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_DialogActions__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                onClick: handleClosePositionDialog,\n                                children: t('common.cancel')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                variant: \"contained\",\n                                onClick: handleSavePosition,\n                                children: t('common.save')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 479,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__.ConfirmDialog, {\n                open: openDeleteConfirm,\n                onClose: handleCloseDeleteConfirm,\n                title: t('plt1.details.documents.merchandisePositions.confirmDeleteDialog.title'),\n                content: t('plt1.details.documents.merchandisePositions.confirmDeleteDialog.content'),\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                    variant: \"contained\",\n                    color: \"error\",\n                    onClick: handleDeletePosition,\n                    children: t('common.delete')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 510,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 504,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n        lineNumber: 437,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1MerchandisePositionsForm, \"rzlgvldrAdFsKfES/DPx4X+02i8=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_4__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFormContext,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFieldArray\n    ];\n});\n_c = PLT1MerchandisePositionsForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1MerchandisePositionsForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-merchandise-positions-form.tsx\n"));

/***/ })

});