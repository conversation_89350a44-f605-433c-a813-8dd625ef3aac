'use client';

import React from 'react';
import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import CardContent from '@mui/material/CardContent';
import Divider from '@mui/material/Divider';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';

import { useTranslate } from 'src/locales';
import { Iconify } from 'src/components/iconify';

interface PartyAddress {
  name?: string;
  address?: Address;
}

export interface Address {
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  postCode?: string;
  country?: string;
  countryCode?: string;
}

interface PartyAddressViewProps {
  partyAddress?: PartyAddress | null;
  title: string;
  icon?: string;
  emptyText?: string;
  compact?: boolean;
}

export default function PartyAddressView({
  partyAddress,
  title,
  icon = 'eva:person-outline',
  emptyText,
  compact = false,
}: PartyAddressViewProps) {
  const { t } = useTranslate();

  // Handle the case when no party address is provided
  if (!partyAddress || Object.keys(partyAddress).length === 0) {
    return compact ? (
      <Typography variant="body2" color="text.secondary">
        {emptyText || t('common.none')}
      </Typography>
    ) : (
      <Card>
        <CardHeader
          title={
            <Stack direction="row" alignItems="center" spacing={1}>
              <Iconify icon={icon} />
              <Typography variant="subtitle1">{title}</Typography>
            </Stack>
          }
        />
        <CardContent>
          <Typography variant="body2" color="text.secondary">
            {emptyText || t('common.noData')}
          </Typography>
        </CardContent>
      </Card>
    );
  }

  // Format the full address for display
  const formatFullAddress = () => {
    const addressParts = [
      partyAddress.address?.addressLine1,
      partyAddress.address?.addressLine2,
      partyAddress.address?.city && partyAddress.address?.state
        ? `${partyAddress.address?.city}, ${partyAddress.address?.state}`
        : partyAddress.address?.city || partyAddress.address?.state,
      partyAddress.address?.postCode,
      partyAddress.address?.country
    ];

    return addressParts.filter(Boolean).join(', ');
  };

  // Compact view for inline or small spaces
  if (compact) {
    return (
      <Box>
        <Typography variant="subtitle2">{partyAddress.name}</Typography>
        <Typography variant="body2" color="text.secondary">
          {formatFullAddress()}
        </Typography>
        {partyAddress.address?.countryCode && (
          <Chip
            size="small"
            label={partyAddress.address?.countryCode}
            variant="outlined"
            sx={{ mt: 0.5 }}
          />
        )}
      </Box>
    );
  }

  // Full card view
  return (
    <Card>
      <CardHeader
        title={
          <Stack direction="row" alignItems="center" spacing={1}>
            <Iconify icon={icon} />
            <Typography variant="subtitle1">{title}</Typography>
          </Stack>
        }
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <CardContent>
        <Stack spacing={2}>
          <Typography variant="subtitle2">{partyAddress.name || '-'}</Typography>

          <Stack spacing={1}>
            {partyAddress.address?.addressLine1 && (
              <Stack direction="row" justifyContent="space-between">
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {t('components.address.addressLine1')}
                </Typography>
                <Typography variant="body2">{partyAddress.address?.addressLine1}</Typography>
              </Stack>
            )}

            {partyAddress.address?.addressLine2 && (
              <Stack direction="row" justifyContent="space-between">
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {t('components.address.addressLine2')}
                </Typography>
                <Typography variant="body2">{partyAddress.address?.addressLine2}</Typography>
              </Stack>
            )}

            {partyAddress.address?.city && (
              <Stack direction="row" justifyContent="space-between">
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {t('components.address.city')}
                </Typography>
                <Typography variant="body2">{partyAddress.address?.city}</Typography>
              </Stack>
            )}

            {partyAddress.address?.state && (
              <Stack direction="row" justifyContent="space-between">
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {t('components.address.state')}
                </Typography>
                <Typography variant="body2">{partyAddress.address?.state}</Typography>
              </Stack>
            )}

            {partyAddress.address?.postCode && (
              <Stack direction="row" justifyContent="space-between">
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {t('components.address.postalCode')}
                </Typography>
                <Typography variant="body2">{partyAddress.address?.postCode}</Typography>
              </Stack>
            )}

            {partyAddress.address?.country && (
              <Stack direction="row" justifyContent="space-between">
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {t('components.address.country')}
                </Typography>
                <Typography variant="body2">
                  {partyAddress.address?.country}
                  {partyAddress.address?.countryCode && ` (${partyAddress.address?.countryCode})`}
                </Typography>
              </Stack>
            )}
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  );
}
