import { GridColumnVisibilityModel, GridDensity, GridFilterModel, GridPaginationModel, GridSortModel } from "@mui/x-data-grid";

export interface GridSettings {
  paginationModel?: GridPaginationModel;
  sortModel?: GridSortModel;
  filterModel?: GridFilterModel;
  columnVisibilityModel?: GridColumnVisibilityModel;
  density?: GridDensity;
}

const STORAGE_PREFIX = 'app-data-grid-settings-';

/**
 * Utility service to save and load grid settings from local storage
 */
export const gridSettingsStorage = {
  /**
   * Save grid settings to local storage
   * @param gridId Unique identifier for the grid
   * @param settings Grid settings to save
   */
  saveSettings(gridId: string, settings: GridSettings): void {
    try {
      if (typeof window === 'undefined') return;

      localStorage.setItem(
        `${STORAGE_PREFIX}${gridId}`,
        JSON.stringify(settings)
      );
    } catch (error) {
      console.error('Error saving grid settings to local storage:', error);
    }
  },

  /**
   * Load grid settings from local storage
   * @param gridId Unique identifier for the grid
   * @returns Grid settings or null if not found
   */
  loadSettings(gridId: string): GridSettings | null {
    try {
      if (typeof window === 'undefined') return null;

      const storedSettings = localStorage.getItem(`${STORAGE_PREFIX}${gridId}`);

      if (!storedSettings) return null;

      return JSON.parse(storedSettings) as GridSettings;
    } catch (error) {
      console.error('Error loading grid settings from local storage:', error);
      return null;
    }
  },

  /**
   * Clear grid settings from local storage
   * @param gridId Unique identifier for the grid
   */
  clearSettings(gridId: string): void {
    try {
      if (typeof window === 'undefined') return;

      localStorage.removeItem(`${STORAGE_PREFIX}${gridId}`);
    } catch (error) {
      console.error('Error clearing grid settings from local storage:', error);
    }
  }
};
