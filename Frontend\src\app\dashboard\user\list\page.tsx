import type { Metada<PERSON> } from 'next';

import { CONFIG } from 'src/global-config';
import { getServerTranslations } from 'src/locales/server';
import { UserListView } from 'src/sections/user/view/user-list-view';


// ----------------------------------------------------------------------
export async function generateMetadata(): Promise<Metadata> {
  const { t } = await getServerTranslations();
  return {
    title: `${t('user.list.pageTitle')} - ${CONFIG.appName}`
  };
}

export default function Page() {
  return <UserListView />;
}
