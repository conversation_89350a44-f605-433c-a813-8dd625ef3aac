"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/details/plt1-order-details-base.tsx":
/*!**********************************************************************!*\
  !*** ./src/sections/orders/plt1/details/plt1-order-details-base.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLT1OrderDetailsBase: () => (/* binding */ PLT1OrderDetailsBase)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/Box */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/material/Card */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/material/Stack */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material_Button__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/material/Button */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/material/Divider */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _mui_material_Container__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/Container */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/material/Typography */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _mui_material_CircularProgress__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/material/CircularProgress */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _mui_material_Grid2__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/material/Grid2 */ \"(app-pages-browser)/./node_modules/@mui/material/Grid2/Grid2.js\");\n/* harmony import */ var _mui_lab_LoadingButton__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/lab/LoadingButton */ \"(app-pages-browser)/./node_modules/@mui/lab/LoadingButton/LoadingButton.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/material/IconButton */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/material/Tooltip */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var src_routes_paths__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/routes/paths */ \"(app-pages-browser)/./src/routes/paths.ts\");\n/* harmony import */ var src_routes_hooks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/routes/hooks */ \"(app-pages-browser)/./src/routes/hooks/index.ts\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_layouts_dashboard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/layouts/dashboard */ \"(app-pages-browser)/./src/layouts/dashboard/index.ts\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_layouts_components_app_data_grid_components_enum_tag_EnumTag__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/layouts/components/app-data-grid/components/enum-tag/EnumTag */ \"(app-pages-browser)/./src/layouts/components/app-data-grid/components/enum-tag/EnumTag.tsx\");\n/* harmony import */ var src_lib_formatters__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! src/lib/formatters */ \"(app-pages-browser)/./src/lib/formatters.ts\");\n/* harmony import */ var _components_plt1_file_management__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/plt1-file-management */ \"(app-pages-browser)/./src/sections/orders/plt1/components/plt1-file-management.tsx\");\n/* harmony import */ var _components_plt1_file_content_preview__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/plt1-file-content-preview */ \"(app-pages-browser)/./src/sections/orders/plt1/components/plt1-file-content-preview.tsx\");\n/* harmony import */ var _components_plt1_file_list_sidebar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/plt1-file-list-sidebar */ \"(app-pages-browser)/./src/sections/orders/plt1/components/plt1-file-list-sidebar.tsx\");\n/* harmony import */ var _components_plt1_file_cache__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../components/plt1-file-cache */ \"(app-pages-browser)/./src/sections/orders/plt1/components/plt1-file-cache.ts\");\n/* harmony import */ var _plt1_status__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../plt1-status */ \"(app-pages-browser)/./src/sections/orders/plt1/plt1-status.tsx\");\n/* harmony import */ var src_sections_plt1_other_data_tabs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! src/sections/plt1-other-data-tabs */ \"(app-pages-browser)/./src/sections/plt1-other-data-tabs.tsx\");\n/* harmony import */ var src_lib_axios__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! src/lib/axios */ \"(app-pages-browser)/./src/lib/axios.ts\");\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! swr */ \"(app-pages-browser)/./node_modules/swr/dist/index/index.mjs\");\n/* harmony import */ var _plt1_order_type__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../plt1-order-type */ \"(app-pages-browser)/./src/sections/orders/plt1/plt1-order-type.tsx\");\n// src/sections/plt1-order-details-base/PLT1OrderDetailsBase.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction PLT1OrderDetailsBase(param) {\n    let { orderId, readOnly: propReadOnly = false, isLoading, error, order, orderStatus, orderNumber, statusError, onSaveOrder, documentTabs, formChanged, isSaving, onCancel, isValid } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_4__.useTranslate)();\n    const router = (0,src_routes_hooks__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleBack = ()=>{\n        router.push(src_routes_paths__WEBPACK_IMPORTED_MODULE_2__.paths.dashboard.plt1.list);\n    };\n    // State management\n    const [splitViewActive, setSplitViewActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orderFiles, setOrderFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileListRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const methods = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useFormContext)();\n    // Determine if the form should be read-only\n    // Form is read-only if explicitly set to read-only or if the order status is \"Scanning\" or \"MerchandisePositionGeneration\"\n    const isScanning = orderStatus === _plt1_status__WEBPACK_IMPORTED_MODULE_13__.PLT1_ORDER_STATUS.Scanning;\n    const isMerchandisePositionGeneration = orderStatus === _plt1_status__WEBPACK_IMPORTED_MODULE_13__.PLT1_ORDER_STATUS.MerchandisePositionGeneration;\n    const readOnly = propReadOnly || isScanning || isMerchandisePositionGeneration;\n    // Handle save order\n    const handleSaveOrderSubmit = async (formData)=>{\n        await onSaveOrder(formData);\n    };\n    // State to track if files are being prefetched\n    const [isPrefetching, setIsPrefetching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // SWR fetcher function\n    const filesFetcher = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PLT1OrderDetailsBase.useCallback[filesFetcher]\": async (url)=>{\n            const response = await src_lib_axios__WEBPACK_IMPORTED_MODULE_15__.axiosInstance.get(url);\n            return response.data.data || [];\n        }\n    }[\"PLT1OrderDetailsBase.useCallback[filesFetcher]\"], []);\n    // Use SWR to fetch and refresh order files every 10 seconds\n    const { data: files, mutate: refreshFiles, isValidating } = (0,swr__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(\"\".concat(src_lib_axios__WEBPACK_IMPORTED_MODULE_15__.endpoints.plt1.filePreviews, \"/\").concat(orderId), filesFetcher, {\n        refreshInterval: 10000,\n        revalidateOnFocus: true,\n        dedupingInterval: 5000,\n        onSuccess: {\n            \"PLT1OrderDetailsBase.useSWR\": (data)=>{\n                setOrderFiles(data);\n                // Only prefetch if there are files\n                if (data && data.length > 0) {\n                    prefetchFiles(data);\n                } else {\n                    // Reset prefetching state if there are no files\n                    setIsPrefetching(false);\n                }\n            }\n        }[\"PLT1OrderDetailsBase.useSWR\"]\n    });\n    // Update isPrefetching state based on SWR validation state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            if (isValidating && !isPrefetching) {\n                setIsPrefetching(true);\n            } else if (!isValidating && isPrefetching && (!files || files.length === 0)) {\n                // Reset prefetching state when validation completes and there are no files\n                setIsPrefetching(false);\n            }\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], [\n        isValidating,\n        isPrefetching,\n        files\n    ]);\n    // Prefetch files with different priorities\n    const prefetchFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PLT1OrderDetailsBase.useCallback[prefetchFiles]\": async (files)=>{\n            // Early return with state reset if no files\n            if (!files || files.length === 0) {\n                setIsPrefetching(false);\n                return;\n            }\n            setIsPrefetching(true);\n            try {\n                // Try to get the last viewed file ID from localStorage\n                const lastViewedFileId = localStorage.getItem(\"plt1-last-viewed-file-\".concat(orderId));\n                if (lastViewedFileId) {\n                    // Find the last viewed file and prefetch it with highest priority\n                    const lastViewedFile = files.find({\n                        \"PLT1OrderDetailsBase.useCallback[prefetchFiles].lastViewedFile\": (file)=>file.id === lastViewedFileId\n                    }[\"PLT1OrderDetailsBase.useCallback[prefetchFiles].lastViewedFile\"]);\n                    if (lastViewedFile) {\n                        // Prefetch the last viewed file with highest priority (10)\n                        await _components_plt1_file_cache__WEBPACK_IMPORTED_MODULE_12__.fileCache.prefetchFile(lastViewedFile, 10);\n                    }\n                }\n                // Prefetch the first few files with high priority (5)\n                const highPriorityFiles = files.slice(0, Math.min(5, files.length));\n                if (highPriorityFiles.length > 0) {\n                    await _components_plt1_file_cache__WEBPACK_IMPORTED_MODULE_12__.fileCache.prefetchFiles(highPriorityFiles, 5);\n                }\n                // Prefetch all remaining files with normal priority (1)\n                const remainingFiles = files.slice(Math.min(5, files.length));\n                if (remainingFiles.length > 0) {\n                    await _components_plt1_file_cache__WEBPACK_IMPORTED_MODULE_12__.fileCache.prefetchFiles(remainingFiles, 1);\n                }\n            } catch (error) {\n                console.error('Error prefetching files:', error);\n            } finally{\n                setIsPrefetching(false);\n            }\n        }\n    }[\"PLT1OrderDetailsBase.useCallback[prefetchFiles]\"], [\n        orderId\n    ]);\n    // Set initial files when SWR loads them\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            if (files) {\n                setOrderFiles(files);\n            }\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], [\n        files\n    ]);\n    // Function to manually refresh files\n    const handleRefreshFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PLT1OrderDetailsBase.useCallback[handleRefreshFiles]\": ()=>{\n            refreshFiles();\n        }\n    }[\"PLT1OrderDetailsBase.useCallback[handleRefreshFiles]\"], [\n        refreshFiles\n    ]);\n    // Clean up cache when component unmounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            return ({\n                \"PLT1OrderDetailsBase.useEffect\": ()=>{\n                    _components_plt1_file_cache__WEBPACK_IMPORTED_MODULE_12__.fileCache.clearCache();\n                }\n            })[\"PLT1OrderDetailsBase.useEffect\"];\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], []);\n    // Select the first file or the last viewed file\n    const selectFirstOrLastViewedFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PLT1OrderDetailsBase.useCallback[selectFirstOrLastViewedFile]\": ()=>{\n            if (orderFiles.length === 0) return;\n            // Try to get the last viewed file ID from localStorage\n            const lastViewedFileId = localStorage.getItem(\"plt1-last-viewed-file-\".concat(orderId));\n            if (lastViewedFileId) {\n                // Find the file with the saved ID\n                const lastFile = orderFiles.find({\n                    \"PLT1OrderDetailsBase.useCallback[selectFirstOrLastViewedFile].lastFile\": (file)=>file.id === lastViewedFileId\n                }[\"PLT1OrderDetailsBase.useCallback[selectFirstOrLastViewedFile].lastFile\"]);\n                if (lastFile) {\n                    setSelectedFile(lastFile);\n                    return;\n                }\n            }\n            // If no last viewed file or it's not found, select the first file\n            setSelectedFile(orderFiles[0]);\n        }\n    }[\"PLT1OrderDetailsBase.useCallback[selectFirstOrLastViewedFile]\"], [\n        orderId,\n        orderFiles\n    ]);\n    // Select first file or last viewed file when files are loaded and no file is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            if (orderFiles.length > 0 && !selectedFile && splitViewActive) {\n                selectFirstOrLastViewedFile();\n            }\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], [\n        orderFiles,\n        selectedFile,\n        splitViewActive,\n        selectFirstOrLastViewedFile\n    ]);\n    // Focus the file list and prefetch files when the split view is activated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            if (splitViewActive) {\n                // If there are no files, close the split view\n                if (orderFiles.length === 0) {\n                    setSplitViewActive(false);\n                    setIsPrefetching(false);\n                    return;\n                }\n                // Focus the file list\n                if (fileListRef.current) {\n                    // Use a small timeout to ensure the DOM is fully rendered\n                    const timeoutId = setTimeout({\n                        \"PLT1OrderDetailsBase.useEffect.timeoutId\": ()=>{\n                            var _fileListRef_current;\n                            (_fileListRef_current = fileListRef.current) === null || _fileListRef_current === void 0 ? void 0 : _fileListRef_current.focus();\n                        }\n                    }[\"PLT1OrderDetailsBase.useEffect.timeoutId\"], 100);\n                    return ({\n                        \"PLT1OrderDetailsBase.useEffect\": ()=>clearTimeout(timeoutId)\n                    })[\"PLT1OrderDetailsBase.useEffect\"];\n                }\n                // Prefetch all files in the background\n                prefetchFiles(orderFiles);\n            }\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], [\n        splitViewActive,\n        orderFiles,\n        prefetchFiles\n    ]);\n    // Handle closing file preview split view\n    const handleCloseFilePreview = ()=>{\n        setSplitViewActive(false);\n        setSelectedFile(null);\n    };\n    // Handle keyboard shortcut (Ctrl+Q) to toggle file preview split view\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"PLT1OrderDetailsBase.useEffect.handleKeyDown\": (event)=>{\n                    // Check if Ctrl+Q is pressed\n                    if ((event.ctrlKey || event.metaKey) && event.key === 'q') {\n                        event.preventDefault(); // Prevent any default browser action\n                        // If split view is active, always allow closing it\n                        if (splitViewActive) {\n                            setSplitViewActive(false);\n                            setSelectedFile(null);\n                        } else if (orderFiles.length > 0) {\n                            setSplitViewActive(true);\n                            // If no file is selected, select the first file or last viewed file\n                            if (!selectedFile) {\n                                selectFirstOrLastViewedFile();\n                            }\n                        }\n                    }\n                }\n            }[\"PLT1OrderDetailsBase.useEffect.handleKeyDown\"];\n            // Add event listener\n            window.addEventListener('keydown', handleKeyDown);\n            // Remove event listener on cleanup\n            return ({\n                \"PLT1OrderDetailsBase.useEffect\": ()=>{\n                    window.removeEventListener('keydown', handleKeyDown);\n                }\n            })[\"PLT1OrderDetailsBase.useEffect\"];\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], [\n        splitViewActive,\n        orderFiles,\n        orderId,\n        selectedFile,\n        selectFirstOrLastViewedFile\n    ]);\n    // Handle selecting a file to preview\n    const handleSelectFile = (file)=>{\n        setSelectedFile(file);\n        setSplitViewActive(true);\n        // Save the selected file ID to localStorage for future reference\n        localStorage.setItem(\"plt1-last-viewed-file-\".concat(orderId), file.id);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_layouts_dashboard__WEBPACK_IMPORTED_MODULE_5__.DashboardContent, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Container__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    sx: {\n                        display: 'flex',\n                        justifyContent: 'center',\n                        alignItems: 'center',\n                        height: '60vh'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CircularProgress__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                lineNumber: 308,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n            lineNumber: 307,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !order) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_layouts_dashboard__WEBPACK_IMPORTED_MODULE_5__.DashboardContent, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Container__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    sx: {\n                        textAlign: 'center',\n                        py: 5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"error\",\n                            sx: {\n                                mb: 2\n                            },\n                            children: t('plt1.details.notFound')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            variant: \"contained\",\n                            onClick: handleBack,\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                icon: \"eva:arrow-back-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 26\n                            }, void 0),\n                            children: t('common.back')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                lineNumber: 322,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n            lineNumber: 321,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_layouts_dashboard__WEBPACK_IMPORTED_MODULE_5__.DashboardContent, {\n        fullWidth: splitViewActive,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Container__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            maxWidth: splitViewActive ? false : 'lg',\n            disableGutters: splitViewActive,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    sx: {\n                        mb: 3,\n                        mt: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            direction: \"row\",\n                            alignItems: \"center\",\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                    onClick: handleBack,\n                                    sx: {\n                                        border: '1px solid',\n                                        borderColor: 'divider',\n                                        borderRadius: 1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                        icon: \"eva:arrow-back-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_layouts_components_app_data_grid_components_enum_tag_EnumTag__WEBPACK_IMPORTED_MODULE_7__.EnumTag, {\n                                    value: order === null || order === void 0 ? void 0 : order.orderType,\n                                    config: _plt1_order_type__WEBPACK_IMPORTED_MODULE_16__.PLT1_ORDER_TYPE_CONFIG,\n                                    translationPrefix: \"plt1.type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    variant: \"h4\",\n                                    children: orderNumber || (order === null || order === void 0 ? void 0 : order.number) || t('plt1.details.orderDetails')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            direction: \"row\",\n                            spacing: 2,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                sx: {\n                                    display: {\n                                        xs: 'none',\n                                        sm: 'flex'\n                                    },\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    title: orderFiles.length > 0 ? isPrefetching ? t('plt1.details.filePreview.prefetchingFiles', {\n                                        defaultValue: 'Prefetching files...'\n                                    }) : t('plt1.details.filePreview.shortcutHint', {\n                                        defaultValue: 'Press Ctrl+Q to toggle preview'\n                                    }) : t('plt1.details.filePreview.noFiles', {\n                                        defaultValue: 'No files available to preview'\n                                    }),\n                                    arrow: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: isPrefetching ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            variant: \"outlined\",\n                                            color: \"primary\",\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CircularProgress__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 34\n                                            }, void 0),\n                                            disabled: true,\n                                            children: t('plt1.details.filePreview.prefetchingFiles', {\n                                                defaultValue: 'Prefetching files...'\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            variant: \"outlined\",\n                                            color: \"primary\",\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                                icon: \"eva:eye-fill\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 34\n                                            }, void 0),\n                                            onClick: ()=>{\n                                                if (splitViewActive) {\n                                                    setSplitViewActive(false);\n                                                    setSelectedFile(null);\n                                                } else if (orderFiles.length > 0) {\n                                                    setSplitViewActive(true);\n                                                    if (!selectedFile) {\n                                                        selectFirstOrLastViewedFile();\n                                                    }\n                                                }\n                                            },\n                                            disabled: !splitViewActive && orderFiles.length === 0,\n                                            children: splitViewActive ? t('plt1.details.filePreview.closePreview') : t('plt1.details.filePreview.openPreview')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this),\n                (isScanning || isMerchandisePositionGeneration) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    sx: {\n                        mb: 3,\n                        p: 2,\n                        bgcolor: 'warning.lighter',\n                        borderRadius: 1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        variant: \"subtitle1\",\n                        color: \"warning.darker\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                icon: \"eva:alert-triangle-fill\",\n                                sx: {\n                                    mr: 1,\n                                    verticalAlign: 'middle'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 15\n                            }, this),\n                            isScanning ? t('plt1.details.scanningInProgress') : t('plt1.details.merchandisePositionGenerationInProgress')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                    lineNumber: 430,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: methods.handleSubmit(handleSaveOrderSubmit),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            children: [\n                                !splitViewActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            size: {\n                                                xs: 12,\n                                                md: 5\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                sx: {\n                                                    mb: 3\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    sx: {\n                                                        p: 3,\n                                                        pb: 2\n                                                    },\n                                                    spacing: 2,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            direction: \"row\",\n                                                            justifyContent: \"space-between\",\n                                                            alignItems: \"center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    variant: \"h6\",\n                                                                    children: t('plt1.details.orderInfo.heading')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_layouts_components_app_data_grid_components_enum_tag_EnumTag__WEBPACK_IMPORTED_MODULE_7__.EnumTag, {\n                                                                    value: orderStatus || (order === null || order === void 0 ? void 0 : order.status),\n                                                                    config: _plt1_status__WEBPACK_IMPORTED_MODULE_13__.PLT1_ORDER_STATUS_CONFIG,\n                                                                    translationPrefix: \"plt1.status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                            sx: {\n                                                                borderStyle: 'dashed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            spacing: 1.5,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    direction: \"row\",\n                                                                    justifyContent: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                color: 'text.secondary'\n                                                                            },\n                                                                            children: t('plt1.details.orderInfo.orderingPartyEmail')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 462,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            children: order.orderingPartyEmail\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 465,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    direction: \"row\",\n                                                                    justifyContent: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                color: 'text.secondary'\n                                                                            },\n                                                                            children: t('plt1.details.orderInfo.orderDate')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 469,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            children: order.orderDate ? src_lib_formatters__WEBPACK_IMPORTED_MODULE_8__.formatters.dateTimeOffset(order.orderDate) : '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 472,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    direction: \"row\",\n                                                                    justifyContent: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                color: 'text.secondary'\n                                                                            },\n                                                                            children: t('plt1.details.orderInfo.confirmationDate')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 478,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            children: order.confirmationDate ? src_lib_formatters__WEBPACK_IMPORTED_MODULE_8__.formatters.dateTimeOffset(order.confirmationDate) : '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 481,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    direction: \"row\",\n                                                                    justifyContent: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                color: 'text.secondary'\n                                                                            },\n                                                                            children: t('plt1.details.orderInfo.forwardedDate')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 489,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            children: order.forwardedToTheCustomsOfficeDate ? src_lib_formatters__WEBPACK_IMPORTED_MODULE_8__.formatters.dateTimeOffset(order.forwardedToTheCustomsOfficeDate) : '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 492,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    direction: \"row\",\n                                                                    justifyContent: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                color: 'text.secondary'\n                                                                            },\n                                                                            children: t('plt1.details.orderInfo.completionDate')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 500,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            children: order.completionDate ? src_lib_formatters__WEBPACK_IMPORTED_MODULE_8__.formatters.dateTimeOffset(order.completionDate) : '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 503,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 499,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            size: {\n                                                xs: 12,\n                                                md: 7\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                spacing: 2,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_plt1_file_management__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    orderId: orderId,\n                                                    orderType: order === null || order === void 0 ? void 0 : order.orderType,\n                                                    hasPendingChanges: formChanged,\n                                                    isScanning: isScanning || isMerchandisePositionGeneration\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        md: 12\n                                    },\n                                    children: splitViewActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        sx: {\n                                            display: 'flex',\n                                            flexDirection: 'row',\n                                            height: 'calc(100vh - 150px)',\n                                            mb: 3,\n                                            mt: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                sx: {\n                                                    width: '40%',\n                                                    pr: 2,\n                                                    overflow: 'auto'\n                                                },\n                                                children: documentTabs\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                sx: {\n                                                    width: '60%',\n                                                    display: 'flex',\n                                                    flexDirection: 'column',\n                                                    border: '1px solid',\n                                                    borderColor: 'divider',\n                                                    borderRadius: 1,\n                                                    overflow: 'hidden'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        sx: {\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'space-between',\n                                                            p: 2,\n                                                            borderBottom: '1px solid',\n                                                            borderColor: 'divider'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        variant: \"h6\",\n                                                                        component: \"div\",\n                                                                        children: t('plt1.details.filePreview.title')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        variant: \"caption\",\n                                                                        color: \"text.secondary\",\n                                                                        children: t('plt1.details.filePreview.shortcutHint', {\n                                                                            defaultValue: 'Press Ctrl+Q to toggle preview'\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                direction: \"row\",\n                                                                spacing: 1,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    edge: \"end\",\n                                                                    color: \"inherit\",\n                                                                    onClick: handleCloseFilePreview,\n                                                                    \"aria-label\": \"close\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                                                        icon: \"eva:close-fill\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                        lineNumber: 583,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 577,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        sx: {\n                                                            display: 'flex',\n                                                            flexDirection: {\n                                                                xs: 'column',\n                                                                md: 'row'\n                                                            },\n                                                            flexGrow: 1,\n                                                            overflow: 'hidden'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                sx: {\n                                                                    flexGrow: 1,\n                                                                    display: 'flex',\n                                                                    alignItems: 'stretch',\n                                                                    justifyContent: 'stretch',\n                                                                    p: 0,\n                                                                    overflow: 'hidden',\n                                                                    width: '100%'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_plt1_file_content_preview__WEBPACK_IMPORTED_MODULE_10__.PLT1FileContentPreview, {\n                                                                    file: selectedFile\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 609,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                lineNumber: 598,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_plt1_file_list_sidebar__WEBPACK_IMPORTED_MODULE_11__.PLT1FileListSidebar, {\n                                                                ref: fileListRef,\n                                                                files: orderFiles,\n                                                                selectedFile: selectedFile,\n                                                                onSelectFile: handleSelectFile,\n                                                                autoFocus: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                lineNumber: 612,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            documentTabs,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                sx: {\n                                                    mt: 3\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_sections_plt1_other_data_tabs__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    readOnly: readOnly\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 11\n                        }, this),\n                        !readOnly && formChanged && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            sx: {\n                                position: 'fixed',\n                                bottom: 0,\n                                left: 0,\n                                right: 0,\n                                py: 2,\n                                px: 3,\n                                bgcolor: 'background.paper',\n                                borderTop: (theme)=>\"1px solid \".concat(theme.palette.divider),\n                                zIndex: (theme)=>theme.zIndex.appBar - 1,\n                                boxShadow: (theme)=>theme.shadows[3],\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'flex-end'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    sx: {\n                                        mr: 2\n                                    },\n                                    children: t('plt1.details.unsavedChanges')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 651,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    direction: \"row\",\n                                    spacing: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            color: \"inherit\",\n                                            variant: \"outlined\",\n                                            onClick: onCancel,\n                                            disabled: isSaving,\n                                            children: t('common.cancel')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_lab_LoadingButton__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                            variant: \"contained\",\n                                            color: \"primary\",\n                                            loading: isSaving,\n                                            type: \"submit\",\n                                            disabled: !isValid,\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                                icon: \"eva:save-fill\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            children: t('common.save')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 634,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                    lineNumber: 441,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n            lineNumber: 342,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n        lineNumber: 341,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1OrderDetailsBase, \"7OIb69be8/4PrdyfVTyY0TC99JE=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_4__.useTranslate,\n        src_routes_hooks__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useFormContext,\n        swr__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n    ];\n});\n_c = PLT1OrderDetailsBase;\nvar _c;\n$RefreshReg$(_c, \"PLT1OrderDetailsBase\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/details/plt1-order-details-base.tsx\n"));

/***/ })

});