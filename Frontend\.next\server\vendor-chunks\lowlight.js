"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lowlight";
exports.ids = ["vendor-chunks/lowlight"];
exports.modules = {

/***/ "(ssr)/./node_modules/lowlight/lib/common.js":
/*!*********************************************!*\
  !*** ./node_modules/lowlight/lib/common.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   grammars: () => (/* binding */ grammars)\n/* harmony export */ });\n/* harmony import */ var highlight_js_lib_languages_arduino__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! highlight.js/lib/languages/arduino */ \"(ssr)/./node_modules/highlight.js/es/languages/arduino.js\");\n/* harmony import */ var highlight_js_lib_languages_bash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! highlight.js/lib/languages/bash */ \"(ssr)/./node_modules/highlight.js/es/languages/bash.js\");\n/* harmony import */ var highlight_js_lib_languages_c__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! highlight.js/lib/languages/c */ \"(ssr)/./node_modules/highlight.js/es/languages/c.js\");\n/* harmony import */ var highlight_js_lib_languages_cpp__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! highlight.js/lib/languages/cpp */ \"(ssr)/./node_modules/highlight.js/es/languages/cpp.js\");\n/* harmony import */ var highlight_js_lib_languages_csharp__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! highlight.js/lib/languages/csharp */ \"(ssr)/./node_modules/highlight.js/es/languages/csharp.js\");\n/* harmony import */ var highlight_js_lib_languages_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! highlight.js/lib/languages/css */ \"(ssr)/./node_modules/highlight.js/es/languages/css.js\");\n/* harmony import */ var highlight_js_lib_languages_diff__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! highlight.js/lib/languages/diff */ \"(ssr)/./node_modules/highlight.js/es/languages/diff.js\");\n/* harmony import */ var highlight_js_lib_languages_go__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! highlight.js/lib/languages/go */ \"(ssr)/./node_modules/highlight.js/es/languages/go.js\");\n/* harmony import */ var highlight_js_lib_languages_graphql__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! highlight.js/lib/languages/graphql */ \"(ssr)/./node_modules/highlight.js/es/languages/graphql.js\");\n/* harmony import */ var highlight_js_lib_languages_ini__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! highlight.js/lib/languages/ini */ \"(ssr)/./node_modules/highlight.js/es/languages/ini.js\");\n/* harmony import */ var highlight_js_lib_languages_java__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! highlight.js/lib/languages/java */ \"(ssr)/./node_modules/highlight.js/es/languages/java.js\");\n/* harmony import */ var highlight_js_lib_languages_javascript__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! highlight.js/lib/languages/javascript */ \"(ssr)/./node_modules/highlight.js/es/languages/javascript.js\");\n/* harmony import */ var highlight_js_lib_languages_json__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! highlight.js/lib/languages/json */ \"(ssr)/./node_modules/highlight.js/es/languages/json.js\");\n/* harmony import */ var highlight_js_lib_languages_kotlin__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! highlight.js/lib/languages/kotlin */ \"(ssr)/./node_modules/highlight.js/es/languages/kotlin.js\");\n/* harmony import */ var highlight_js_lib_languages_less__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! highlight.js/lib/languages/less */ \"(ssr)/./node_modules/highlight.js/es/languages/less.js\");\n/* harmony import */ var highlight_js_lib_languages_lua__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! highlight.js/lib/languages/lua */ \"(ssr)/./node_modules/highlight.js/es/languages/lua.js\");\n/* harmony import */ var highlight_js_lib_languages_makefile__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! highlight.js/lib/languages/makefile */ \"(ssr)/./node_modules/highlight.js/es/languages/makefile.js\");\n/* harmony import */ var highlight_js_lib_languages_markdown__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! highlight.js/lib/languages/markdown */ \"(ssr)/./node_modules/highlight.js/es/languages/markdown.js\");\n/* harmony import */ var highlight_js_lib_languages_objectivec__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! highlight.js/lib/languages/objectivec */ \"(ssr)/./node_modules/highlight.js/es/languages/objectivec.js\");\n/* harmony import */ var highlight_js_lib_languages_perl__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! highlight.js/lib/languages/perl */ \"(ssr)/./node_modules/highlight.js/es/languages/perl.js\");\n/* harmony import */ var highlight_js_lib_languages_php__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! highlight.js/lib/languages/php */ \"(ssr)/./node_modules/highlight.js/es/languages/php.js\");\n/* harmony import */ var highlight_js_lib_languages_php_template__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! highlight.js/lib/languages/php-template */ \"(ssr)/./node_modules/highlight.js/es/languages/php-template.js\");\n/* harmony import */ var highlight_js_lib_languages_plaintext__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! highlight.js/lib/languages/plaintext */ \"(ssr)/./node_modules/highlight.js/es/languages/plaintext.js\");\n/* harmony import */ var highlight_js_lib_languages_python__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! highlight.js/lib/languages/python */ \"(ssr)/./node_modules/highlight.js/es/languages/python.js\");\n/* harmony import */ var highlight_js_lib_languages_python_repl__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! highlight.js/lib/languages/python-repl */ \"(ssr)/./node_modules/highlight.js/es/languages/python-repl.js\");\n/* harmony import */ var highlight_js_lib_languages_r__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! highlight.js/lib/languages/r */ \"(ssr)/./node_modules/highlight.js/es/languages/r.js\");\n/* harmony import */ var highlight_js_lib_languages_ruby__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! highlight.js/lib/languages/ruby */ \"(ssr)/./node_modules/highlight.js/es/languages/ruby.js\");\n/* harmony import */ var highlight_js_lib_languages_rust__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! highlight.js/lib/languages/rust */ \"(ssr)/./node_modules/highlight.js/es/languages/rust.js\");\n/* harmony import */ var highlight_js_lib_languages_scss__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! highlight.js/lib/languages/scss */ \"(ssr)/./node_modules/highlight.js/es/languages/scss.js\");\n/* harmony import */ var highlight_js_lib_languages_shell__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! highlight.js/lib/languages/shell */ \"(ssr)/./node_modules/highlight.js/es/languages/shell.js\");\n/* harmony import */ var highlight_js_lib_languages_sql__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! highlight.js/lib/languages/sql */ \"(ssr)/./node_modules/highlight.js/es/languages/sql.js\");\n/* harmony import */ var highlight_js_lib_languages_swift__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! highlight.js/lib/languages/swift */ \"(ssr)/./node_modules/highlight.js/es/languages/swift.js\");\n/* harmony import */ var highlight_js_lib_languages_typescript__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! highlight.js/lib/languages/typescript */ \"(ssr)/./node_modules/highlight.js/es/languages/typescript.js\");\n/* harmony import */ var highlight_js_lib_languages_vbnet__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! highlight.js/lib/languages/vbnet */ \"(ssr)/./node_modules/highlight.js/es/languages/vbnet.js\");\n/* harmony import */ var highlight_js_lib_languages_wasm__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! highlight.js/lib/languages/wasm */ \"(ssr)/./node_modules/highlight.js/es/languages/wasm.js\");\n/* harmony import */ var highlight_js_lib_languages_xml__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! highlight.js/lib/languages/xml */ \"(ssr)/./node_modules/highlight.js/es/languages/xml.js\");\n/* harmony import */ var highlight_js_lib_languages_yaml__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! highlight.js/lib/languages/yaml */ \"(ssr)/./node_modules/highlight.js/es/languages/yaml.js\");\n/**\n * @import {LanguageFn} from 'highlight.js'\n */\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Map of grammars.\n *\n * @type {Record<string, LanguageFn>}\n */\nconst grammars = {\n  arduino: highlight_js_lib_languages_arduino__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n  bash: highlight_js_lib_languages_bash__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  c: highlight_js_lib_languages_c__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n  cpp: highlight_js_lib_languages_cpp__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n  csharp: highlight_js_lib_languages_csharp__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n  css: highlight_js_lib_languages_css__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n  diff: highlight_js_lib_languages_diff__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n  go: highlight_js_lib_languages_go__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n  graphql: highlight_js_lib_languages_graphql__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n  ini: highlight_js_lib_languages_ini__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n  java: highlight_js_lib_languages_java__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n  javascript: highlight_js_lib_languages_javascript__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n  json: highlight_js_lib_languages_json__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n  kotlin: highlight_js_lib_languages_kotlin__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n  less: highlight_js_lib_languages_less__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n  lua: highlight_js_lib_languages_lua__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n  makefile: highlight_js_lib_languages_makefile__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n  markdown: highlight_js_lib_languages_markdown__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n  objectivec: highlight_js_lib_languages_objectivec__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n  perl: highlight_js_lib_languages_perl__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n  php: highlight_js_lib_languages_php__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n  'php-template': highlight_js_lib_languages_php_template__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n  plaintext: highlight_js_lib_languages_plaintext__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n  python: highlight_js_lib_languages_python__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n  'python-repl': highlight_js_lib_languages_python_repl__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n  r: highlight_js_lib_languages_r__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n  ruby: highlight_js_lib_languages_ruby__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n  rust: highlight_js_lib_languages_rust__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n  scss: highlight_js_lib_languages_scss__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n  shell: highlight_js_lib_languages_shell__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n  sql: highlight_js_lib_languages_sql__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n  swift: highlight_js_lib_languages_swift__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n  typescript: highlight_js_lib_languages_typescript__WEBPACK_IMPORTED_MODULE_32__[\"default\"],\n  vbnet: highlight_js_lib_languages_vbnet__WEBPACK_IMPORTED_MODULE_33__[\"default\"],\n  wasm: highlight_js_lib_languages_wasm__WEBPACK_IMPORTED_MODULE_34__[\"default\"],\n  xml: highlight_js_lib_languages_xml__WEBPACK_IMPORTED_MODULE_35__[\"default\"],\n  yaml: highlight_js_lib_languages_yaml__WEBPACK_IMPORTED_MODULE_36__[\"default\"]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lowlight/lib/common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lowlight/lib/index.js":
/*!********************************************!*\
  !*** ./node_modules/lowlight/lib/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createLowlight: () => (/* binding */ createLowlight)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var highlight_js_lib_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! highlight.js/lib/core */ \"(ssr)/./node_modules/highlight.js/es/core.js\");\n/**\n * @import {ElementContent, Element, RootData, Root} from 'hast'\n * @import {Emitter, HLJSOptions as HljsOptions, HighlightResult, LanguageFn} from 'highlight.js'\n */\n\n/**\n * @typedef {Object} ExtraOptions\n *   Extra fields.\n * @property {ReadonlyArray<string> | null | undefined} [subset]\n *   List of allowed languages (default: all registered languages).\n *\n * @typedef Options\n *   Configuration for `highlight`.\n * @property {string | null | undefined} [prefix='hljs-']\n *   Class prefix (default: `'hljs-'`).\n *\n * @typedef {Options & ExtraOptions} AutoOptions\n *   Configuration for `highlightAuto`.\n */\n\n\n\n\n/** @type {AutoOptions} */\nconst emptyOptions = {}\n\nconst defaultPrefix = 'hljs-'\n\n/**\n * Create a `lowlight` instance.\n *\n * @param {Readonly<Record<string, LanguageFn>> | null | undefined} [grammars]\n *   Grammars to add (optional).\n * @returns\n *   Lowlight.\n */\nfunction createLowlight(grammars) {\n  const high = highlight_js_lib_core__WEBPACK_IMPORTED_MODULE_0__[\"default\"].newInstance()\n\n  if (grammars) {\n    register(grammars)\n  }\n\n  return {\n    highlight,\n    highlightAuto,\n    listLanguages,\n    register,\n    registerAlias,\n    registered\n  }\n\n  /**\n   * Highlight `value` (code) as `language` (name).\n   *\n   * @example\n   *   ```js\n   *   import {common, createLowlight} from 'lowlight'\n   *\n   *   const lowlight = createLowlight(common)\n   *\n   *   console.log(lowlight.highlight('css', 'em { color: red }'))\n   *   ```\n   *\n   *   Yields:\n   *\n   *   ```js\n   *   {type: 'root', children: [Array], data: {language: 'css', relevance: 3}}\n   *   ```\n   *\n   * @param {string} language\n   *   Programming language name.\n   * @param {string} value\n   *   Code to highlight.\n   * @param {Readonly<Options> | null | undefined} [options={}]\n   *   Configuration (optional).\n   * @returns {Root}\n   *   Tree; with the following `data` fields: `language` (`string`), detected\n   *   programming language name; `relevance` (`number`), how sure lowlight is\n   *   that the given code is in the language.\n   */\n  function highlight(language, value, options) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof language === 'string', 'expected `string` as `name`')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof value === 'string', 'expected `string` as `value`')\n    const settings = options || emptyOptions\n    const prefix =\n      typeof settings.prefix === 'string' ? settings.prefix : defaultPrefix\n\n    if (!high.getLanguage(language)) {\n      throw new Error('Unknown language: `' + language + '` is not registered')\n    }\n\n    // See: <https://github.com/highlightjs/highlight.js/issues/3621#issuecomment-1528841888>\n    high.configure({__emitter: HastEmitter, classPrefix: prefix})\n\n    const result = /** @type {HighlightResult & {_emitter: HastEmitter}} */ (\n      high.highlight(value, {ignoreIllegals: true, language})\n    )\n\n    // `highlight.js` seems to use this (currently) for broken grammars, so let’s\n    // keep it in there just to be sure.\n    /* c8 ignore next 5 */\n    if (result.errorRaised) {\n      throw new Error('Could not highlight with `Highlight.js`', {\n        cause: result.errorRaised\n      })\n    }\n\n    const root = result._emitter.root\n\n    // Cast because it is always defined.\n    const data = /** @type {RootData} */ (root.data)\n\n    data.language = result.language\n    data.relevance = result.relevance\n\n    return root\n  }\n\n  /**\n   * Highlight `value` (code) and guess its programming language.\n   *\n   * @example\n   *   ```js\n   *   import {common, createLowlight} from 'lowlight'\n   *\n   *   const lowlight = createLowlight(common)\n   *\n   *   console.log(lowlight.highlightAuto('\"hello, \" + name + \"!\"'))\n   *   ```\n   *\n   *   Yields:\n   *\n   *   ```js\n   *   {type: 'root', children: [Array], data: {language: 'arduino', relevance: 2}}\n   *   ```\n   *\n   * @param {string} value\n   *   Code to highlight.\n   * @param {Readonly<AutoOptions> | null | undefined} [options={}]\n   *   Configuration (optional).\n   * @returns {Root}\n   *   Tree; with the following `data` fields: `language` (`string`), detected\n   *   programming language name; `relevance` (`number`), how sure lowlight is\n   *   that the given code is in the language.\n   */\n  function highlightAuto(value, options) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof value === 'string', 'expected `string` as `value`')\n    const settings = options || emptyOptions\n    const subset = settings.subset || listLanguages()\n\n    let index = -1\n    let relevance = 0\n    /** @type {Root | undefined} */\n    let result\n\n    while (++index < subset.length) {\n      const name = subset[index]\n\n      if (!high.getLanguage(name)) continue\n\n      const current = highlight(name, value, options)\n\n      if (\n        current.data &&\n        current.data.relevance !== undefined &&\n        current.data.relevance > relevance\n      ) {\n        relevance = current.data.relevance\n        result = current\n      }\n    }\n\n    return (\n      result || {\n        type: 'root',\n        children: [],\n        data: {language: undefined, relevance}\n      }\n    )\n  }\n\n  /**\n   * List registered languages.\n   *\n   * @example\n   *   ```js\n   *   import {createLowlight} from 'lowlight'\n   *   import markdown from 'highlight.js/lib/languages/markdown'\n   *\n   *   const lowlight = createLowlight()\n   *\n   *   console.log(lowlight.listLanguages()) // => []\n   *\n   *   lowlight.register({markdown})\n   *\n   *   console.log(lowlight.listLanguages()) // => ['markdown']\n   *   ```\n   *\n   * @returns {Array<string>}\n   *   Names of registered language.\n   */\n  function listLanguages() {\n    return high.listLanguages()\n  }\n\n  /**\n   * Register languages.\n   *\n   * @example\n   *   ```js\n   *   import {createLowlight} from 'lowlight'\n   *   import xml from 'highlight.js/lib/languages/xml'\n   *\n   *   const lowlight = createLowlight()\n   *\n   *   lowlight.register({xml})\n   *\n   *   // Note: `html` is an alias for `xml`.\n   *   console.log(lowlight.highlight('html', '<em>Emphasis</em>'))\n   *   ```\n   *\n   *   Yields:\n   *\n   *   ```js\n   *   {type: 'root', children: [Array], data: {language: 'html', relevance: 2}}\n   *   ```\n   *\n   * @overload\n   * @param {Readonly<Record<string, LanguageFn>>} grammars\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {string} name\n   * @param {LanguageFn} grammar\n   * @returns {undefined}\n   *\n   * @param {Readonly<Record<string, LanguageFn>> | string} grammarsOrName\n   *   Grammars or programming language name.\n   * @param {LanguageFn | undefined} [grammar]\n   *   Grammar, if with name.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function register(grammarsOrName, grammar) {\n    if (typeof grammarsOrName === 'string') {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(grammar !== undefined, 'expected `grammar`')\n      high.registerLanguage(grammarsOrName, grammar)\n    } else {\n      /** @type {string} */\n      let name\n\n      for (name in grammarsOrName) {\n        if (Object.hasOwn(grammarsOrName, name)) {\n          high.registerLanguage(name, grammarsOrName[name])\n        }\n      }\n    }\n  }\n\n  /**\n   * Register aliases.\n   *\n   * @example\n   *   ```js\n   *   import {createLowlight} from 'lowlight'\n   *   import markdown from 'highlight.js/lib/languages/markdown'\n   *\n   *   const lowlight = createLowlight()\n   *\n   *   lowlight.register({markdown})\n   *\n   *   // lowlight.highlight('mdown', '<em>Emphasis</em>')\n   *   // ^ would throw: Error: Unknown language: `mdown` is not registered\n   *\n   *   lowlight.registerAlias({markdown: ['mdown', 'mkdn', 'mdwn', 'ron']})\n   *   lowlight.highlight('mdown', '<em>Emphasis</em>')\n   *   // ^ Works!\n   *   ```\n   *\n   * @overload\n   * @param {Readonly<Record<string, ReadonlyArray<string> | string>>} aliases\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {string} language\n   * @param {ReadonlyArray<string> | string} alias\n   * @returns {undefined}\n   *\n   * @param {Readonly<Record<string, ReadonlyArray<string> | string>> | string} aliasesOrName\n   *   Map of programming language names to one or more aliases, or programming\n   *   language name.\n   * @param {ReadonlyArray<string> | string | undefined} [alias]\n   *   One or more aliases for the programming language, if with `name`.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function registerAlias(aliasesOrName, alias) {\n    if (typeof aliasesOrName === 'string') {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(alias !== undefined)\n      high.registerAliases(\n        // Note: copy needed because hljs doesn’t accept readonly arrays yet.\n        typeof alias === 'string' ? alias : [...alias],\n        {languageName: aliasesOrName}\n      )\n    } else {\n      /** @type {string} */\n      let key\n\n      for (key in aliasesOrName) {\n        if (Object.hasOwn(aliasesOrName, key)) {\n          const aliases = aliasesOrName[key]\n          high.registerAliases(\n            // Note: copy needed because hljs doesn’t accept readonly arrays yet.\n            typeof aliases === 'string' ? aliases : [...aliases],\n            {languageName: key}\n          )\n        }\n      }\n    }\n  }\n\n  /**\n   * Check whether an alias or name is registered.\n   *\n   * @example\n   *   ```js\n   *   import {createLowlight} from 'lowlight'\n   *   import javascript from 'highlight.js/lib/languages/javascript'\n   *\n   *   const lowlight = createLowlight({javascript})\n   *\n   *   console.log(lowlight.registered('funkyscript')) // => `false`\n   *\n   *   lowlight.registerAlias({javascript: 'funkyscript'})\n   *   console.log(lowlight.registered('funkyscript')) // => `true`\n   *   ```\n   *\n   * @param {string} aliasOrName\n   *   Name of a language or alias for one.\n   * @returns {boolean}\n   *   Whether `aliasOrName` is registered.\n   */\n  function registered(aliasOrName) {\n    return Boolean(high.getLanguage(aliasOrName))\n  }\n}\n\n/** @type {Emitter} */\nclass HastEmitter {\n  /**\n   * @param {Readonly<HljsOptions>} options\n   *   Configuration.\n   * @returns\n   *   Instance.\n   */\n  constructor(options) {\n    /** @type {HljsOptions} */\n    this.options = options\n    /** @type {Root} */\n    this.root = {\n      type: 'root',\n      children: [],\n      data: {language: undefined, relevance: 0}\n    }\n    /** @type {[Root, ...Array<Element>]} */\n    this.stack = [this.root]\n  }\n\n  /**\n   * @param {string} value\n   *   Text to add.\n   * @returns {undefined}\n   *   Nothing.\n   *\n   */\n  addText(value) {\n    if (value === '') return\n\n    const current = this.stack[this.stack.length - 1]\n    const tail = current.children[current.children.length - 1]\n\n    if (tail && tail.type === 'text') {\n      tail.value += value\n    } else {\n      current.children.push({type: 'text', value})\n    }\n  }\n\n  /**\n   *\n   * @param {unknown} rawName\n   *   Name to add.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  startScope(rawName) {\n    this.openNode(String(rawName))\n  }\n\n  /**\n   * @returns {undefined}\n   *   Nothing.\n   */\n  endScope() {\n    this.closeNode()\n  }\n\n  /**\n   * @param {HastEmitter} other\n   *   Other emitter.\n   * @param {string} name\n   *   Name of the sublanguage.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  __addSublanguage(other, name) {\n    const current = this.stack[this.stack.length - 1]\n    // Assume only element content.\n    const results = /** @type {Array<ElementContent>} */ (other.root.children)\n\n    if (name) {\n      current.children.push({\n        type: 'element',\n        tagName: 'span',\n        properties: {className: [name]},\n        children: results\n      })\n    } else {\n      current.children.push(...results)\n    }\n  }\n\n  /**\n   * @param {string} name\n   *   Name to add.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  openNode(name) {\n    const self = this\n    // First “class” gets the prefix. Rest gets a repeated underscore suffix.\n    // See: <https://github.com/highlightjs/highlight.js/commit/51806aa>\n    // See: <https://github.com/wooorm/lowlight/issues/43>\n    const className = name.split('.').map(function (d, i) {\n      return i ? d + '_'.repeat(i) : self.options.classPrefix + d\n    })\n    const current = this.stack[this.stack.length - 1]\n    /** @type {Element} */\n    const child = {\n      type: 'element',\n      tagName: 'span',\n      properties: {className},\n      children: []\n    }\n\n    current.children.push(child)\n    this.stack.push(child)\n  }\n\n  /**\n   * @returns {undefined}\n   *   Nothing.\n   */\n  closeNode() {\n    this.stack.pop()\n  }\n\n  /**\n   * @returns {undefined}\n   *   Nothing.\n   */\n  finalize() {}\n\n  /**\n   * @returns {string}\n   *   Nothing.\n   */\n  toHTML() {\n    return ''\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lowlight/lib/index.js\n");

/***/ })

};
;