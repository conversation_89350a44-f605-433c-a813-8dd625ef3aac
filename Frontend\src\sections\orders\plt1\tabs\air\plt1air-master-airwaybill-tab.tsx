'use client';

import { useRef, useState, useEffect } from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';

import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { useTranslate } from 'src/locales';
import PLT1DocumentTabBase from '../plt1-document-tab-base';
import PLT1AirMasterAirWaybillForm, {
  PLT1AirMasterAirWaybillData as PLT1AirMasterAirWaybillData,
} from '../../forms/air/plt1air-master-airwaybill-form';
import { PLT1Order } from '../../types/plt1-details.types';

// ----------------------------------------------------------------------

interface PLT1AirMasterAirWaybillsTabProps {
  t1OrderId: string;
  order?: PLT1Order;
  readOnly?: boolean;
}

interface FormValues {
  masterAirWaybills: PLT1AirMasterAirWaybillData[];
}

export default function PLT1AirMasterAirWaybillsTab({
  t1OrderId,
  order,
  readOnly = false,
}: PLT1AirMasterAirWaybillsTabProps) {
  const { t } = useTranslate();
  const { control } = useFormContext<FormValues>();

  // Try to restore the expanded state from sessionStorage
  const [expandedMawb, setExpandedMawb] = useState<number | null>(() => {
    const saved = sessionStorage.getItem(`plt1-expanded-mawb-${t1OrderId}`);
    return saved ? parseInt(saved, 10) : null;
  });

  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const deleteButtonRef = useRef<HTMLButtonElement>(null);

  const fieldArray = useFieldArray({
    control,
    name: 'masterAirWaybills',
  });

  // Save expanded state to sessionStorage whenever it changes
  useEffect(() => {
    if (expandedMawb !== null) {
      sessionStorage.setItem(`plt1-expanded-mawb-${t1OrderId}`, expandedMawb.toString());
    } else {
      sessionStorage.removeItem(`plt1-expanded-mawb-${t1OrderId}`);
    }
  }, [expandedMawb, t1OrderId]);

  const handleAddMawb = (): void => {
    const newMawb: PLT1AirMasterAirWaybillData = {
      id: undefined,
      mawbNumber: '',
      grossWeight: 0,
      grossWeightUnit: 'kg',
      numberOfPieces: 0,
      t1OrderId,
    };

    fieldArray.append(newMawb);
    setExpandedMawb(fieldArray.fields.length);
  };

  const handleToggleExpand = (index: number): void => {
    setExpandedMawb(expandedMawb === index ? null : index);
  };

  const handleOpenConfirm = (index: number): void => {
    setDeleteIndex(index);
    setOpenConfirm(true);
  };

  const handleCloseConfirm = (): void => {
    setOpenConfirm(false);
    if (deleteIndex !== null) {
      deleteButtonRef.current?.focus();
    }
  };

  const handleDeleteMawb = (): void => {
    if (deleteIndex !== null) {
      fieldArray.remove(deleteIndex);
      if (expandedMawb === deleteIndex) {
        setExpandedMawb(null);
      }
    }
    handleCloseConfirm();
  };

  // Render preview of the MAWB item when collapsed
  const renderPreview = (mawb: any, _index: number) => (
    <Stack
      direction="row"
      spacing={2}
      sx={{
        px: 3,
        pb: 2,
        display: 'flex',
        flexWrap: 'wrap',
        '& > *': { mr: 3, mb: 1 },
      }}
    >
      {mawb.numberOfPieces !== undefined &&
        mawb.numberOfPieces !== null &&
        mawb.numberOfPieces > 0 && (
          <Typography variant="body2" color="text.secondary">
            {t('plt1.details.documents.masterAirWaybill.preview.numberOfPieces')}:{' '}
            {mawb.numberOfPieces}
          </Typography>
        )}

      {mawb.grossWeight !== undefined && mawb.grossWeight !== null && mawb.grossWeight > 0 && (
        <Typography variant="body2" color="text.secondary">
          {t('plt1.details.documents.masterAirWaybill.preview.grossWeight')}: {mawb.grossWeight}{' '}
          {mawb.grossWeightUnit}
        </Typography>
      )}
    </Stack>
  );

  // Render the form when expanded
  const renderForm = (index: number) => (
    <PLT1AirMasterAirWaybillForm formPath="masterAirWaybills" index={index} readOnly={readOnly} />
  );

  // Render the title of each item
  const getItemTitle = (mawb: any) => (
    <Typography variant="subtitle1">
      {mawb.mawbNumber || t('plt1.details.documents.masterAirWaybill.preview.newMawb')}
    </Typography>
  );

  return (
    <PLT1DocumentTabBase
      t1OrderId={t1OrderId}
      order={order}
      readOnly={readOnly}
      title={t('plt1.details.documents.masterAirWaybill.heading')}
      emptyTitle={t('plt1.details.documents.masterAirWaybill.noData')}
      emptyDescription={t('plt1.details.documents.masterAirWaybill.addYourFirst')}
      expandedIndex={expandedMawb}
      deleteIndex={deleteIndex}
      openConfirm={openConfirm}
      fieldArray={fieldArray}
      fieldArrayName="masterAirWaybills"
      onToggleExpand={handleToggleExpand}
      onOpenConfirm={handleOpenConfirm}
      onCloseConfirm={handleCloseConfirm}
      onDelete={handleDeleteMawb}
      onAdd={handleAddMawb}
      renderPreview={renderPreview}
      renderForm={renderForm}
      getItemTitle={getItemTitle}
    />
  );
}
