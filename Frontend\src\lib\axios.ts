import type { AxiosRequestConfig } from 'axios';
import axios from 'axios';
import { endSession, JWT_STORAGE_KEY, REFRESH_TOKEN_STORAGE_KEY, setSession } from 'src/auth/context/jwt';
import { CONFIG } from 'src/global-config';
import { paths } from 'src/routes/paths';
import { handleApiError } from './errorHandler';
import { fallbackLng, LANGUAGE_LOCAL_STORAGE_KEY } from 'src/locales';

// Create axios instance with default config
export const axiosInstance = axios.create({
  baseURL: CONFIG.serverUrl,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to handle auth tokens
axiosInstance.interceptors.request.use(
  (config) => {
    const accessToken = sessionStorage.getItem(JWT_STORAGE_KEY);

    if (accessToken && config.headers) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }

    if (config.headers) {
      const currentLang = localStorage.getItem(LANGUAGE_LOCAL_STORAGE_KEY) || fallbackLng;
      config.headers['X-Requested-Language'] = currentLang;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor to handle token refresh and errors
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    const status = error.response?.status;

    if (status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = sessionStorage.getItem(REFRESH_TOKEN_STORAGE_KEY);
        const token = sessionStorage.getItem(JWT_STORAGE_KEY);

        if (!refreshToken || !token) {
          throw new Error('No refresh token available');
        }

        const response = await axiosInstance.post(endpoints.auth.refreshToken, {
          token: token,
          refreshToken: refreshToken
        });

        const { token: newAccessToken, refreshToken: newRefreshToken } = response.data;

        setSession(newAccessToken, newRefreshToken);
        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;

        const currentLang = localStorage.getItem(LANGUAGE_LOCAL_STORAGE_KEY) || fallbackLng;
        originalRequest.headers['X-Requested-Language'] = currentLang;

        return axios(originalRequest);
      } catch (refreshError) {
        endSession();
        window.location.href = paths.auth.auth0.signIn;
        return Promise.reject(refreshError);
      }
    }

    handleApiError(error);

    if (!status || status >= 500) {
      return Promise.reject(error.response?.data || 'Something went wrong!');
    }

    return { data: error.response?.data, isSuccess: false };
  }
);

// SWR fetcher for GET requests
export const fetcher = async (args: string | [string, AxiosRequestConfig]) => {
  try {
    const [url, config] = Array.isArray(args) ? args : [args];
    const res = await axiosInstance.get(url, { ...config });
    return res.data;
  } catch (error) {
    handleApiError(error);
    throw error;
  }
};

export const endpoints = {
  auth: {
    exchangeToken: '/auth/exchange-token',
    refreshToken: '/auth/refresh-token',
    onboard: '/auth/onboard',
  },
  identity: {
    list: '/odata/identities',
    delete: '/identity/delete',
    invite: '/identity/invite'
  },
  plt1: {
    list: '/odata/PLT1Orders',

    airBeginOrder: '/plt1air/begin-order',
    airDetails: '/plt1air',
    airUpdate: '/plt1air/update',
    airDelete: '/plt1air/delete',
    airUploadFiles: '/plt1air/files',
    airExportXlsx: '/plt1air',
    airExportSadecTxt: '/plt1air',

    roadDetails: '/plt1road',
    roadUpdate: '/plt1road/update',
    roadDelete: '/plt1road/delete',
    roadUploadFiles: '/plt1road/files',
    roadExportSadecTxt: '/plt1road',

    seaBeginOrder: '/plt1sea/begin-order',
    seaDetails: '/plt1sea',
    seaUpdate: '/plt1sea/update',
    seaDelete: '/plt1sea/delete',
    seaUploadFiles: '/plt1sea/files',
    seaExportSadecTxt: '/plt1sea',

    listFiles: '/plt1/files',
    filePreviews: '/plt1/file-previews',
    deleteFile: '/plt1/files',
    downloadFile: '/plt1/files/download',
    status: '/plt1/status',
    generateMerchandisePositions: '/plt1/orders/generate-merchandise-positions'
  },
  partyAddress: {
    getAll: '/party-address',
    getById: '/party-address',
    create: '/party-address',
    update: '/party-address',
    delete: '/party-address'
  }
};