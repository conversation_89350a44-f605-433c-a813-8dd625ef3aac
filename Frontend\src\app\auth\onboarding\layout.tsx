import { CONFIG } from 'src/global-config';

import { AuthGuard } from 'src/auth/guard';
import { DashboardLayout } from 'src/layouts/dashboard';

// ----------------------------------------------------------------------

type Props = {
  children: React.ReactNode;
};

export default function Layout({ children }: Props) {
  if (CONFIG.auth.skip) {
    return <DashboardLayout  disableNavigation = {true} >{children}</DashboardLayout>;
  }

  return (
    <AuthGuard>
      <DashboardLayout disableNavigation = {true} >{children}</DashboardLayout>
    </AuthGuard>
  );
}
