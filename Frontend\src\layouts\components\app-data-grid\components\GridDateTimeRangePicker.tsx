import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { Stack, Typography } from '@mui/material';
import dayjs from 'dayjs';
import { useTranslate } from 'src/locales';

interface GridDateTimeRangePickerProps {
    item: {
        value?: [Date | null, Date | null];
    };
    applyValue: (value: { value: [Date | null, Date | null] }) => void;
    [key: string]: any;
}

export const GridDateRangePicker = ({ item, applyValue, ...other }: GridDateTimeRangePickerProps) => {
    const { t, currentLang } = useTranslate();
    const timeString = dayjs().locale(currentLang.value).format('LT');
    const useAMPM = timeString.includes('AM') || timeString.includes('PM');

    const [startDate, endDate] = item.value || [null, null];

    return (
        <Stack spacing={2}>
            <div>
                <Typography variant="caption" mb={1}>
                    {t('components.gridDateRangePicker.from')}
                </Typography>
                <DateTimePicker
                    {...other}
                    value={startDate ? dayjs(startDate) : null}
                    onChange={(newValue) => {
                        applyValue({
                            ...item,
                            value: [newValue ? dayjs(newValue).toDate() : null, endDate]
                        });
                    }}
                    format="L LT"
                    ampm={useAMPM}
                    slotProps={{
                        textField: {
                            variant: 'outlined',
                            size: 'small',
                            fullWidth: true,
                            placeholder: t('components.gridDatePicker.selectDateTime')
                        }
                    }}
                    localeText={{
                        fieldDayPlaceholder: () => t('components.gridDatePicker.dd'),
                        fieldMonthPlaceholder: () => t('components.gridDatePicker.mm'),
                        fieldYearPlaceholder: () => t('components.gridDatePicker.yyyy'),
                        fieldHoursPlaceholder: () => t('components.gridDatePicker.hh'),
                        fieldMinutesPlaceholder: () => t('components.gridDatePicker.mm'),
                        toolbarTitle: t('components.gridDatePicker.selectDateTime'),
                    }}
                />
            </div>
            <div>
                <Typography variant="caption" mb={1}>
                    {t('components.gridDateRangePicker.to')}
                </Typography>
                <DateTimePicker
                    {...other}
                    value={endDate ? dayjs(endDate) : null}
                    onChange={(newValue) => {
                        applyValue({
                            ...item,
                            value: [startDate, newValue ? dayjs(newValue).toDate() : null]
                        });
                    }}
                    format="L LT"
                    ampm={useAMPM}
                    slotProps={{
                        textField: {
                            variant: 'outlined',
                            size: 'small',
                            fullWidth: true,
                            placeholder: t('components.gridDatePicker.selectDateTime')
                        }
                    }}
                    localeText={{
                        fieldDayPlaceholder: () => t('components.gridDatePicker.dd'),
                        fieldMonthPlaceholder: () => t('components.gridDatePicker.mm'),
                        fieldYearPlaceholder: () => t('components.gridDatePicker.yyyy'),
                        fieldHoursPlaceholder: () => t('components.gridDatePicker.hh'),
                        fieldMinutesPlaceholder: () => t('components.gridDatePicker.mm'),
                        toolbarTitle: t('components.gridDatePicker.selectDateTime'),
                    }}
                />
            </div>
        </Stack>
    );
};
