'use server';

import type { Metada<PERSON> } from 'next';

import { CONFIG } from 'src/global-config';

import { Auth0SignInView } from 'src/auth/view/auth0';
import { getServerTranslations } from 'src/locales/server';

// ----------------------------------------------------------------------

export async function generateMetadata(): Promise<Metadata> {
  const { t } = await getServerTranslations();
  return {
    title: `${t('signIn.pageTitle')} - ${CONFIG.appName}`
  };
}

export default async function Page() {
  return <Auth0SignInView />;
}
