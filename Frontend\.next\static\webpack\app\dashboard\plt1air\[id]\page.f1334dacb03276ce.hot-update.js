"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx":
/*!*******************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1PackingListForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/material/Grid2 */ \"(app-pages-browser)/./node_modules/@mui/material/Grid2/Grid2.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/custom-dialog */ \"(app-pages-browser)/./src/components/custom-dialog/index.ts\");\n/* harmony import */ var src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/components/party-address/hooks/usePartyAddressForm */ \"(app-pages-browser)/./src/components/party-address/hooks/usePartyAddressForm.tsx\");\n/* harmony import */ var src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/components/party-address/party-address-dialog */ \"(app-pages-browser)/./src/components/party-address/party-address-dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst DEFAULT_PACKED_ITEM = {\n    id: '',\n    name: '',\n    hsCode: '',\n    modelNumber: '',\n    quantity: 0,\n    itemNetWeight: 0,\n    itemNetWeightUnit: 'KGM',\n    itemGrossWeight: 0,\n    hsCodeHints: '',\n    packingListPositionId: ''\n};\nconst DEFAULT_POSITION = {\n    id: '',\n    positionNumber: 1,\n    commercialInvoiceNumber: '',\n    packageNetWeight: 0,\n    packageNetWeightUnit: 'KGM',\n    packageGrossWeight: 0,\n    packageGrossWeightUnit: 'KGM',\n    packagesNetWeight: 0,\n    packagesNetWeightUnit: 'KGM',\n    packagesGrossWeight: 0,\n    packagesGrossWeightUnit: 'KGM',\n    packageUnit: 'CTNS',\n    packageSize: '',\n    packageVolume: 0,\n    packageVolumeUnit: 'CBM',\n    numberOfPackages: 0,\n    packedItems: [],\n    packingListId: ''\n};\nconst DEFAULT_TOTAL = {\n    id: '',\n    shipmentOrPackingId: '',\n    totalQuantity: 0,\n    totalPackagesUnit: 'CTNS',\n    totalNumberOfPackages: 0,\n    totalNumberOfPallets: 0,\n    totalNetWeight: 0,\n    totalNetWeightUnit: 'KGM',\n    totalGrossWeight: 0,\n    totalGrossWeightUnit: 'KGM',\n    totalVolume: 0,\n    totalVolumeMeasurementUnit: 'CBM',\n    packingListId: ''\n};\nfunction PLT1PackingListForm(param) {\n    let { formPath, index, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    const { control, getValues, watch, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext)();\n    const fieldPrefix = index !== undefined ? \"\".concat(formPath, \".\").concat(index) : formPath;\n    const positionsFieldName = \"\".concat(fieldPrefix, \".packingListPositions\");\n    const totalFieldName = \"\".concat(fieldPrefix, \".listTotal\");\n    // Use the party address form hook\n    const { openPartyDialog, currentPartyType, handleClosePartyDialog, handleUpdateParty } = (0,src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm)({\n        fieldPrefix\n    });\n    // State for position dialog\n    const [openPositionDialog, setOpenPositionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPositionIndex, setCurrentPositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletePositionIndex, setDeletePositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize listTotal if it doesn't exist\n    const listTotal = watch(totalFieldName);\n    if (!listTotal) {\n        setValue(totalFieldName, DEFAULT_TOTAL);\n    }\n    // UseFieldArray hook to manage the positions array\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray)({\n        control,\n        name: positionsFieldName\n    });\n    // UseFieldArray for packed items within the current position\n    const packedItemsFieldName = currentPositionIndex !== null ? \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packedItems\") : '';\n    const packedItemsFieldArray = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray)({\n        control,\n        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packedItems\")\n    });\n    // Handle opening the position dialog for adding a new position\n    const handleAddPosition = ()=>{\n        setCurrentPositionIndex(null);\n        // Add a new position with default values\n        const newPosition = {\n            ...DEFAULT_POSITION,\n            positionNumber: fields.length + 1\n        };\n        append(newPosition);\n        setCurrentPositionIndex(fields.length); // Set to the new index\n        setOpenPositionDialog(true);\n    };\n    // Handle opening the position dialog for editing an existing position\n    const handleEditPosition = (index)=>{\n        setCurrentPositionIndex(index);\n        setOpenPositionDialog(true);\n    };\n    // Handle closing the position dialog\n    const handleClosePositionDialog = ()=>{\n        setOpenPositionDialog(false);\n        // If we were adding a new position and user cancels, remove the empty position\n        if (currentPositionIndex !== null && currentPositionIndex === fields.length - 1) {\n            const position = getValues(\"\".concat(positionsFieldName, \".\").concat(currentPositionIndex));\n            // Check if it's an empty position (all fields are default values)\n            const isEmpty = !position.commercialInvoiceNumber && position.numberOfPackages === 0 && position.packageNetWeight === 0 && position.packageGrossWeight === 0 && position.packageVolume === 0 && (!position.packedItems || position.packedItems.length === 0);\n            if (isEmpty) {\n                remove(currentPositionIndex);\n            }\n        }\n        setCurrentPositionIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeletePositionIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deletePositionIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting a position\n    const handleDeletePosition = ()=>{\n        if (deletePositionIndex !== null) {\n            remove(deletePositionIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving a position (just close the dialog since form is already updated)\n    const handleSavePosition = ()=>{\n        handleClosePositionDialog();\n    };\n    // Handle adding a new packed item\n    const handleAddPackedItem = ()=>{\n        if (currentPositionIndex !== null) {\n            const newPackedItem = {\n                ...DEFAULT_PACKED_ITEM\n            };\n            packedItemsFieldArray.append(newPackedItem);\n        }\n    };\n    // Handle removing a packed item\n    const handleRemovePackedItem = (itemIndex)=>{\n        if (currentPositionIndex !== null) {\n            packedItemsFieldArray.remove(itemIndex);\n        }\n    };\n    // Render the position form in the dialog\n    const renderPositionForm = ()=>{\n        if (currentPositionIndex === null) return null;\n        // Render inline editable table for packed items\n        const renderPackedItemsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        direction: \"row\",\n                        justifyContent: \"space-between\",\n                        alignItems: \"center\",\n                        sx: {\n                            mb: 2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                variant: \"h6\",\n                                children: t('plt1.details.documents.packingList.packedItem.title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                variant: \"outlined\",\n                                size: \"small\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                    icon: \"eva:plus-fill\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 24\n                                }, void 0),\n                                onClick: handleAddPackedItem,\n                                disabled: readOnly,\n                                children: t('plt1.details.documents.packingList.packedItem.addNew')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        component: _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                        sx: {\n                            maxHeight: 400\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            size: \"small\",\n                            stickyHeader: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.name')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.hsCode')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.modelNumber')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.quantity')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.itemNetWeight')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                align: \"right\",\n                                                children: t('common.actions')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    children: packedItemsFieldArray.fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            colSpan: 6,\n                                            align: \"center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"body2\",\n                                                color: \"text.secondary\",\n                                                children: t('plt1.details.documents.packingList.packedItem.noItems')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 17\n                                    }, this) : packedItemsFieldArray.fields.map((field, itemIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 150\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".name\"),\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 120\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".hsCode\"),\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 120\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".modelNumber\"),\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 100\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".quantity\"),\n                                                        type: \"number\",\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 120\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".itemNetWeight\"),\n                                                        type: \"number\",\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    align: \"right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        title: t('common.delete'),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            size: \"small\",\n                                                            color: \"error\",\n                                                            onClick: ()=>handleRemovePackedItem(itemIndex),\n                                                            disabled: readOnly,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                                icon: \"eva:trash-2-outline\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, field.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            variant: \"h6\",\n                            sx: {\n                                mb: 2\n                            },\n                            children: t('plt1.details.documents.packingList.position.details')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".positionNumber\"),\n                                        label: t('plt1.details.documents.packingList.position.positionNumber'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".commercialInvoiceNumber\"),\n                                        label: t('plt1.details.documents.packingList.position.commercialInvoiceNumber'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".numberOfPackages\"),\n                                        label: t('plt1.details.documents.packingList.position.numberOfPackages'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageUnit\"),\n                                        label: t('plt1.details.documents.packingList.position.packageUnit'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageSize\"),\n                                        label: t('plt1.details.documents.packingList.position.packageSize'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageVolume\"),\n                                        label: t('plt1.details.documents.packingList.position.packageVolume'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.position.packageNetWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.position.packageGrossWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packagesNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.position.packagesNetWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packagesGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.position.packagesGrossWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, this),\n                renderPackedItemsTable()\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 377,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the positions table\n    const renderPositionsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            component: _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"packing list positions table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.positionNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.commercialInvoiceNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.numberOfPackages')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.packageNetWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.packageGrossWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        children: fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                colSpan: 6,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.packingList.position.noPositions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 13\n                        }, this) : fields.map((field, index)=>{\n                            const position = getValues(\"\".concat(positionsFieldName, \".\").concat(index)) || {};\n                            console.log(position);\n                            var _position_numberOfPackages, _position_packageNetWeight, _position_packageGrossWeight;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: position.positionNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: position.commercialInvoiceNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: (_position_numberOfPackages = position.numberOfPackages) !== null && _position_numberOfPackages !== void 0 ? _position_numberOfPackages : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: [\n                                            (_position_packageNetWeight = position.packageNetWeight) !== null && _position_packageNetWeight !== void 0 ? _position_packageNetWeight : '-',\n                                            \" \",\n                                            position.packageNetWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: [\n                                            (_position_packageGrossWeight = position.packageGrossWeight) !== null && _position_packageGrossWeight !== void 0 ? _position_packageGrossWeight : '-',\n                                            \" \",\n                                            position.packageGrossWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditPosition(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deletePositionIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 488,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 487,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        spacing: 3,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        title: t('plt1.details.documents.packingList.form.positionsTitle'),\n                        action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"contained\",\n                            size: \"small\",\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                icon: \"eva:plus-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: handleAddPosition,\n                            sx: {\n                                mb: 2\n                            },\n                            disabled: readOnly,\n                            children: t('plt1.details.documents.packingList.position.addNew')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: [\n                            renderPositionsTable(),\n                            fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    mt: 2,\n                                    display: 'flex',\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    variant: \"soft\",\n                                    color: \"primary\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                        icon: \"eva:plus-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    onClick: handleAddPosition,\n                                    disabled: readOnly,\n                                    children: t('plt1.details.documents.packingList.position.addNew')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 577,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 560,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        title: t('plt1.details.documents.packingList.form.totalsTitle')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 598,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 599,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalQuantity\"),\n                                        label: t('plt1.details.documents.packingList.total.totalQuantity'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPackages\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPackages'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalPackagesUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalPackagesUnit'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolume\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolume'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 601,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 600,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 597,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                open: openPositionDialog,\n                onClose: handleClosePositionDialog,\n                fullWidth: true,\n                maxWidth: \"lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        children: currentPositionIndex === null ? t('plt1.details.documents.packingList.position.addNew') : t('plt1.details.documents.packingList.position.edit')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            sx: {\n                                pt: 3\n                            },\n                            children: renderPositionForm()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                onClick: handleClosePositionDialog,\n                                color: \"inherit\",\n                                children: t('common.cancel')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 664,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                onClick: handleSavePosition,\n                                variant: \"contained\",\n                                children: t('common.save')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 667,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 663,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 654,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__.ConfirmDialog, {\n                open: openDeleteConfirm,\n                onClose: handleCloseDeleteConfirm,\n                title: t('plt1.details.documents.packingList.position.confirmDeleteDialog.title'),\n                content: t('plt1.details.documents.packingList.position.confirmDeleteDialog.content'),\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    variant: \"contained\",\n                    color: \"error\",\n                    onClick: handleDeletePosition,\n                    children: t('common.delete')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 680,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 674,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                open: openPartyDialog,\n                onClose: handleClosePartyDialog,\n                onSave: handleUpdateParty,\n                formPath: fieldPrefix,\n                currentPartyType: currentPartyType,\n                readOnly: readOnly,\n                titlePrefix: \"plt1.details.documents.packingList.partyAddress\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 687,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n        lineNumber: 558,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1PackingListForm, \"jbyTJ/5RFz4AqmM1furakeA1SSM=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext,\n        src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray\n    ];\n});\n_c = PLT1PackingListForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1PackingListForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx\n"));

/***/ })

});