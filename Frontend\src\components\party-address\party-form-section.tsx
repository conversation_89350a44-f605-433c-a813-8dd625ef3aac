import { <PERSON>, Button, Stack, Typography } from '@mui/material';
import { PartyAddressData } from 'src/components/party-address/party-address-form';
import { Iconify } from 'src/components/iconify';
import PartyAddressView from 'src/components/party-address/party-address-view';
import { useTranslate } from 'src/locales';
import { PartyType } from 'src/types/parties';

// Helper function to check if a party is effectively empty (all values are empty or null)
const isPartyEffectivelyEmpty = (party: PartyAddressData | null): boolean => {
  if (!party) return true;
  if (Object.keys(party).length === 0) return true;

  // Check if name is empty
  if (!party.name || party.name.trim() === '') {
    // If address is also empty or null, consider the party effectively empty
    if (!party.address) return true;

    // Check if all address fields are empty
    const addressFields = ['addressLine1', 'addressLine2', 'city', 'state', 'postalCode', 'country'];
    const hasNonEmptyAddressField = addressFields.some(
      (field) => party.address[field as keyof typeof party.address] &&
                String(party.address[field as keyof typeof party.address]).trim() !== ''
    );

    return !hasNonEmptyAddressField;
  }

  return false;
};

interface PartyFormSectionProps {
  partyType: PartyType;
  labelKey: string;
  party: PartyAddressData | null;
  onOpenPartyDialog: (partyType: PartyType) => void;
  readOnly?: boolean;
}

export default function PartyFormSection({
  partyType,
  labelKey,
  party,
  onOpenPartyDialog,
  readOnly = false,
}: PartyFormSectionProps) {
  const { t } = useTranslate();

  const isEmpty = isPartyEffectivelyEmpty(party);

  return (
    <Stack spacing={2}>
      <Typography variant="subtitle2">{t(labelKey)}</Typography>
      {!isEmpty && (
        <Box sx={{ mt: 2 }}>
          <PartyAddressView partyAddress={party} title="" compact />
        </Box>
      )}
      <Stack direction="row" spacing={2}>
        <Button
          variant="contained"
          color="primary"
          onClick={() => onOpenPartyDialog(partyType)}
          disabled={readOnly}
          startIcon={
            <Iconify
              icon={isEmpty ? 'eva:plus-fill' : 'eva:edit-fill'}
            />
          }
        >
          {isEmpty ? t('common.addNew') : t('common.edit')}
        </Button>
      </Stack>
    </Stack>
  );
}
