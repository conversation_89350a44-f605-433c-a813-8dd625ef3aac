{"name": "@minimal-kit/starter-next-ts", "author": "Minimals", "version": "6.2.0", "description": "Next Starter & TypeScript", "private": true, "scripts": {"dev": "next dev -p 8083", "start": "next start -p 8083", "build": "next build", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "lint:print": "npx eslint --print-config eslint.config.mjs > eslint-show-config.json", "fm:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "fm:fix": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "fix:all": "npm run lint:fix && npm run fm:fix", "clean": "rm -rf node_modules .next out dist build", "re:dev": "yarn clean && yarn install && yarn dev", "re:build": "yarn clean && yarn install && yarn build", "re:build-npm": "npm run clean && npm install && npm run build", "tsc:dev": "yarn dev & yarn tsc:watch", "tsc:watch": "tsc --noEmit --watch", "tsc:print": "npx tsc --showConfig"}, "engines": {"node": "20.x"}, "packageManager": "yarn@1.22.22", "dependencies": {"@auth0/auth0-react": "^2.2.4", "@canvas-fonts/helvetica": "^1.0.4", "@emotion/cache": "^11.13.5", "@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@fontsource-variable/dm-sans": "^5.1.0", "@fontsource-variable/inter": "^5.1.0", "@fontsource-variable/nunito-sans": "^5.1.0", "@fontsource-variable/public-sans": "^5.1.1", "@fontsource/barlow": "^5.1.0", "@hookform/resolvers": "^3.9.1", "@iconify/react": "^5.0.2", "@mui/lab": "^6.0.0-beta.18", "@mui/material": "^6.1.10", "@mui/material-nextjs": "^6.1.9", "@mui/x-data-grid": "^7.23.1", "@mui/x-date-pickers": "^7.23.1", "@mui/x-tree-view": "^7.23.0", "@tiptap/extension-code-block-lowlight": "^2.11.2", "@tiptap/extension-image": "^2.11.2", "@tiptap/extension-link": "^2.11.2", "@tiptap/extension-placeholder": "^2.11.2", "@tiptap/extension-text-align": "^2.11.2", "@tiptap/extension-underline": "^2.11.2", "@tiptap/react": "^2.11.2", "@tiptap/starter-kit": "^2.11.2", "autosuggest-highlight": "^3.3.4", "axios": "^1.7.9", "dayjs": "^1.11.13", "es-toolkit": "^1.29.0", "framer-motion": "^11.13.1", "i18next": "^24.0.5", "i18next-browser-languagedetector": "^8.0.0", "i18next-resources-to-backend": "^1.2.1", "lowlight": "^3.3.0", "minimal-shared": "^1.0.5", "mui-one-time-password-input": "^3.0.2", "next": "^15.1.2", "nprogress": "^0.2.0", "react": "^18.2.0", "react-dom": "^18.3.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.2", "react-i18next": "^15.1.3", "react-phone-number-input": "^3.4.11", "simplebar-react": "^3.2.6", "sonner": "^1.7.1", "stylis": "^4.3.4", "stylis-plugin-rtl": "^2.1.1", "swr": "^2.3.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.16.0", "@svgr/webpack": "^8.1.0", "@types/autosuggest-highlight": "^3.2.3", "@types/node": "^22.10.1", "@types/nprogress": "^0.2.3", "@types/react": "^18.3.13", "@types/react-dom": "^18.3.1", "@types/react-phone-number-input": "^3.0.17", "@types/stylis": "^4.2.7", "@typescript-eslint/parser": "^8.17.0", "eslint": "^9.16.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-perfectionist": "^4.2.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.13.0", "prettier": "^3.4.2", "typescript": "^5.7.2", "typescript-eslint": "^8.17.0"}}