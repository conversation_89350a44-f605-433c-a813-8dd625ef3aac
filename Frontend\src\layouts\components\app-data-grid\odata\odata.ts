import { GridColDef, GridFilterModel, GridPaginationModel, GridSortModel } from "@mui/x-data-grid";

export type ODataFieldType = 'string' | 'date' | 'enum';

export interface ODataQueryParams {
    pagination?: GridPaginationModel;
    sortModel?: GridSortModel;
    filterModel?: GridFilterModel;
    columns?: readonly GridColDef[];
  }
  
  export interface ODataFilter {
    field: string;
    operator: string;
    value: any;
    fieldType: ODataFieldType;
  }
  