// forms/sea/plt1sea-master-bill-of-lading-form.tsx
'use client';

import { useFormContext } from 'react-hook-form';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Divider from '@mui/material/Divider';
import CardHeader from '@mui/material/CardHeader';

import { Field } from 'src/components/hook-form';
import { useTranslate } from 'src/locales';
import { PartyAddressData } from 'src/components/party-address/party-address-form';
import PartyAddressDialog from 'src/components/party-address/party-address-dialog';
import { usePartyAddressForm } from 'src/components/party-address/hooks/usePartyAddressForm';
import PartyFormSection from '../../../../../components/party-address/party-form-section';
import { PartyType } from 'src/types/parties';

// ----------------------------------------------------------------------

export interface PLT1SeaWaybillLadingData {
  id?: string;
  seaWaybillNumber: string;
  containerNumber: string;
  containerType: string;
  grossWeight: number;
  grossWeightUnit: string;
  numberOfPieces: number;
  volumeMeasurement: number;
  shipper: PartyAddressData;
  consignee: PartyAddressData;
  plT1SeaOrderId: string;
}

// ----------------------------------------------------------------------

interface PLT1SeaWaybillOfLadingFormProps {
  formPath: string;
  index?: number;
  readOnly?: boolean;
}

export default function PLT1SeaWaybillOfLadingForm({
  formPath,
  index,
  readOnly = false,
}: PLT1SeaWaybillOfLadingFormProps) {
  const { t } = useTranslate();
  const { watch } = useFormContext();

  // If index is provided, this form is part of a field array
  const fieldPrefix = index !== undefined ? `${formPath}[${index}]` : formPath;

  const {
    openPartyDialog,
    currentPartyType,
    handleOpenPartyDialog,
    handleClosePartyDialog,
    handleUpdateParty,
  } = usePartyAddressForm({ fieldPrefix });

  // Watch shipper and consignee to display their info
  const shipper = watch(`${fieldPrefix}.shipper`);
  const consignee = watch(`${fieldPrefix}.consignee`);

  const renderMainInfo = () => (
    <Card sx={{ boxShadow: 'none' }}>
      <CardHeader
        title={t('plt1.details.documents.seaWaybill.form.mainInfoTitle')}
        sx={{ mb: 2 }}
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Field.Text
          name={`${fieldPrefix}.seaWaybillNumber`}
          label={t('plt1.details.documents.seaWaybill.form.swbNumber')}
          disabled={readOnly}
        />

        <Box
          sx={{
            rowGap: 3,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' },
          }}
        >
          <Field.Text
            name={`${fieldPrefix}.containerNumber`}
            label={t('plt1.details.documents.seaWaybill.form.containerNumber')}
            disabled={readOnly}
          />
          <Field.Text
            name={`${fieldPrefix}.containerType`}
            label={t('plt1.details.documents.seaWaybill.form.containerType')}
            disabled={readOnly}
          />
        </Box>
      </Stack>
    </Card>
  );

  const renderShipmentInfo = () => (
    <Card sx={{ mt: 3, boxShadow: 'none' }}>
      <CardHeader
        title={t('plt1.details.documents.seaWaybill.form.shipmentInfoTitle')}
        sx={{ mb: 2 }}
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Box
          sx={{
            rowGap: 3,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(3, 1fr)' },
          }}
        >
          <Field.Text
            name={`${fieldPrefix}.numberOfPieces`}
            label={t('plt1.details.documents.seaWaybill.form.numberOfPieces')}
            type="number"
            disabled={readOnly}
          />
          <Field.Text
            name={`${fieldPrefix}.grossWeight`}
            label={t('plt1.details.documents.seaWaybill.form.grossWeight')}
            type="number"
            disabled={readOnly}
          />
          <Field.Text
            name={`${fieldPrefix}.grossWeightUnit`}
            label={t('plt1.details.documents.seaWaybill.form.grossWeightUnit')}
            disabled={readOnly}
          />
        </Box>
        <Field.Text
          name={`${fieldPrefix}.volumeMeasurement`}
          label={t('plt1.details.documents.seaWaybill.form.volumeMeasurement')}
          type="number"
          disabled={readOnly}
        />
      </Stack>
    </Card>
  );

  const renderParties = () => (
    <Card sx={{ mt: 3, boxShadow: 'none' }}>
      <CardHeader
        title={t('plt1.details.documents.seaWaybill.form.partiesTitle')}
        sx={{ mb: 2 }}
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Box
          sx={{
            rowGap: 3,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' },
          }}
        >
          <PartyFormSection
            partyType={PartyType.SHIPPER}
            labelKey="plt1.details.documents.seaWaybill.form.shipper"
            party={shipper}
            onOpenPartyDialog={handleOpenPartyDialog}
            readOnly={readOnly}
          />
          <PartyFormSection
            partyType={PartyType.CONSIGNEE}
            labelKey="plt1.details.documents.seaWaybill.form.consignee"
            party={consignee}
            onOpenPartyDialog={handleOpenPartyDialog}
            readOnly={readOnly}
          />
        </Box>
      </Stack>
    </Card>
  );

  const renderPartyDialog = () => (
    <PartyAddressDialog
      open={openPartyDialog}
      onClose={handleClosePartyDialog}
      onSave={handleUpdateParty}
      formPath={fieldPrefix}
      currentPartyType={currentPartyType}
      readOnly={readOnly}
      titlePrefix="plt1.details.documents.seaWaybill.partyAddress"
    />
  );

  return (
    <>
      <Stack spacing={3}>
        {renderMainInfo()}
        {renderShipmentInfo()}
        {renderParties()}
      </Stack>

      {renderPartyDialog()}
    </>
  );
}