'use client';

import { getGridDateOperators, getGridStringOperators, GridFilterOperator } from '@mui/x-data-grid';
import { GridDateTimePicker } from '../components/GridDateTimePicker';
import { GridDateRangePicker } from '../components/GridDateTimeRangePicker';
import { GridEnumFilter } from '../components/GridEnumFilter';
import { useTranslate } from 'src/locales';

export const gridOperators = {
    getEnumOperators: (options?: Array<{ value: string; label: string }>) =>
        getGridStringOperators()
            .filter((operator) => [
                'isAnyOf'
            ].includes(operator.value))
            .map((operator) => ({
                ...operator,
                InputComponent: GridEnumFilter
            })),

    getDateOperators: () => {
        const { t } = useTranslate();

        const defaultOperators = getGridDateOperators()
            .filter((operator) => ['after', 'before'].includes(operator.value))
            .map((operator) => ({
                ...operator,
                InputComponent: GridDateTimePicker
            }));

        const betweenOperator: GridFilterOperator = {
            value: 'between',
            getApplyFilterFn: (filterItem) => {
                if (!filterItem.value || !Array.isArray(filterItem.value)) {
                    return null;
                }

                const [start, end] = filterItem.value;
                return (value: any) => {
                    if (!value) return false;
                    const date = new Date(value);
                    if (start && end) {
                        return date >= start && date <= end;
                    }
                    if (start) {
                        return date >= start;
                    }
                    if (end) {
                        return date <= end;
                    }
                    return false;
                };
            },
            InputComponent: GridDateRangePicker,
            label: t('dataGrid.filterOperator.between')
        };

        return [...defaultOperators, betweenOperator];
    },

    getStringOperators: () =>
        getGridStringOperators()
            .filter((operator) => [
                'contains',
                'isEmpty',
                'isNotEmpty',
                'isAnyOf'
            ].includes(operator.value))
};