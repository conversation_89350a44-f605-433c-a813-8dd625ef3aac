import { ODataFieldType } from "./odata";

export const formatODataValue = (value: any, fieldType: ODataFieldType): string => {
  if (value === undefined || value === null) return "null";

  // Handle date range array
  if (Array.isArray(value) && fieldType === 'date') {
    const [start, end] = value;
    return "null";
  }

  switch (fieldType) {
    case 'date':
      try {
        const dateValue = value instanceof Date ? value : new Date(value);
        if (isNaN(dateValue.getTime())) return "null";
        return dateValue.toISOString();
      } catch {
        return "null";
      }
    
    case 'enum':
      return `'${value}'`;

    default:
      return typeof value === 'string' ? `'${value}'` : String(value);
  }
};