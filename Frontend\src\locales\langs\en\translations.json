{"navbar": {"processes": {"transits": "T1", "exports": "Exports", "imports": "<PERSON><PERSON><PERSON>", "subheader": "Customs", "group": "Processes"}, "management": {"subheader": "Management", "usersGroup": "Users", "users": {"list": "List"}}}, "settingsDrawer": {"title": "Settings", "toolTip": {"resetAll": "Reset all", "close": "Close"}, "darkMode": {"title": "Dark mode"}, "compact": {"title": "Compact", "toolTip": "Dashboard only and available at large resolutions > 1600px (xl)"}, "navigation": {"titleTag": "nav", "titleTagToolTip": "Dashboard only", "subheader": "Layout"}, "color": {"title": "Color"}, "font": {"title": "Font", "subheader": "Font Size"}}, "fullScreenButton": {"fullScreen": "Full Screen", "exit": "Exit"}, "navColorOptions": {"integrate": "Integrate", "apparent": "Apparent"}, "signIn": {"pageTitle": "Sign in", "authSplitLayout": {"title": "Hi, Welcome back!", "subtitle": "Get advantage of best optimized processes."}, "signInView": {"formTitle": "Sign in to your account", "singUpDescription": "Don’t have an account?"}}, "navUpgreade": {"title": "Up to 50% faster", "subTitleFirstLine": "We automate your customs", "subTitleSecondLine": "declarations"}, "notFound": {"pageTitle": "404 page not found!", "title": "Sorry, page not found!", "description": "Sorry, we couldn’t find the page you’re looking for. Perhaps you’ve mistyped the URL? Be sure to check your spelling.", "back": "Go to home"}, "components": {"signOutButton": {"label": "Logout"}, "signInButton": {"label": "Sign in"}, "signUpButton": {"label": "Sign up"}, "signUpLink": {"label": "Get started"}, "confirmDialog": {"cancel": "Cancel"}, "filtersResult": {"resultsFound": "Results count:", "clear": "Clear"}, "tablePaginationCustom": {"denseSwich": "<PERSON><PERSON>", "labelRowsPerPage": "Rows per page:"}, "emptyContent": {"title": "No data"}, "gridDatePicker": {"selectDateTime": "Select date and time", "dd": "DD", "mm": "MM", "yyyy": "YYYY", "hh": "HH", "min": "MM"}, "gridDateRangePicker": {"from": "From", "to": "To"}, "upload": {"placeholder": {"title": "Drop or select files", "description1": "Drop files here or click to", "description2": "browse", "description3": "through your machine."}}, "address": {"addressLine1": "Address first line", "addressLine2": "Address second line", "country": "Country", "city": "City", "postalCode": "Postal code", "state": "State"}, "partyAddress": {"name": "Company name", "taxNumber": "Tax number", "form": {"name": "Nazwa firmy", "taxNumber": "Numer NIP", "eoriNumber": "Numer EORI"}, "savedSuccessfully": "Party saved successfully.", "errorSaving": "Error saving Party."}}, "onboarding": {"pageTitle": "Onboarding", "welcomeTitle": "Welcome 👋 \n {{name}}", "welcomeDescription": "We are glad you use our system. We will make sure you get the best advantage out of it.", "formValidation": {"requiredCompanyName": "Company name is required.", "requiredTaxNumber": "Tax number is required.", "requiredStreet1": "Address first line is required.", "requiredStreet2": "Address second line is required.", "requiredCountry": "Country is required.", "requiredCity": "City is required.", "requiredPostalCode": "Postal code is required."}, "form": {"formTitle": "Customs agency details", "formSubheader": "Please provide your company details", "addressSectionTitle": "Address details", "submit": "Onboard"}, "success": "Onboarding completed!"}, "useLocales": {"languageChangedMessage": "Language changed to English"}, "plt1": {"type": {"air": "Air", "sea": "Sea", "road": "Road"}, "details": {"pageTitle": "T1 Order Details", "heading": "T1 Order Details", "unsavedChanges": "You have unsaved changes", "notFound": "Order not found", "statusChanged": "Order status has changed.", "scanningInProgress": "This order is currently being scanned. Editing is disabled until scanning is complete.", "merchandisePositionGenerationInProgress": "Merchandise positions are being generated for this order. Editing is disabled until the process is complete.", "orderInfo": {"heading": "T1 Order Details:", "number": "Number:", "orderingPartyEmail": "Ordering party email:", "orderDate": "Order date:", "confirmationDate": "Confirmation date:", "forwardedDate": "Forwarded to customs office date:", "completionDate": "Completion date:"}, "fileManagement": {"title": "Files", "uploadFile": "Upload File", "dragAndDrop": "Drag and drop or click to upload", "recentFiles": "Recent Files", "noFiles": "No files uploaded yet", "loading": "Loading files...", "uploading": "Uploading...", "fetchError": "Error loading files", "uploadError": "Error uploading files", "deleteError": "Error deleting file", "uploadSuccess": "File uploaded successfully", "deleteSuccess": "File deleted successfully", "savePendingChanges": "Save pending changes before uploading files", "scanningInProgress": "File upload is disabled during scanning", "deleteConfirmation": {"title": "Delete File", "confirmation": "Are you sure you want to delete file '{{fileName}}'?", "typeToConfirm": "Type 'delete' to confirm:", "confirmPlaceholder": "delete", "error": "Error deleting file"}}, "filePreview": {"title": "Document Preview", "preview": "Preview", "openPreview": "Preview Documents", "closePreview": "General View", "download": "Download", "fileList": "Files", "selectFile": "Select a file to preview", "loadError": "Error loading file", "excelParseError": "Error parsing Excel file. The file may be corrupted or in an unsupported format.", "cannotPreview": "This file type cannot be previewed directly. You can download it instead.", "officePreviewNote": "Office documents may not display correctly in the preview. If you have trouble viewing the document, please download it.", "shortcutHint": "Press Ctrl+Q to toggle preview", "arrowNavHint": "Use ↑↓ arrows, Home, End to navigate", "prefetchingFiles": "Prefetching files...", "noFiles": "No files available to preview"}, "otherData": {"heading": "Other data"}, "documents": {"heading": "Documents", "masterAirWaybill": {"heading": "MAWB - Master Air Waybills", "noData": "No Master Air Waybills", "addYourFirst": "Add your first Master Air Waybill", "preview": {"newMawb": "New Master Air Waybill", "numberOfPieces": "Number of pieces", "grossWeight": "Gross weight"}, "form": {"mawbNumber": "MAWB Number", "grossWeight": "Gross Weight", "grossWeightUnit": "Weight Unit", "numberOfPieces": "Number of Pieces"}, "confirmDeleteDialog": {"title": "Delete Master Air Waybill", "content": "Are you sure you want to delete this Master Air Waybill?"}}, "tabs": {"houseAirWaybills": "HAWB", "masterAirWaybills": "MAWB", "billsOfLading": "BOL", "seaWaybills": "SWB", "commercialInvoices": "CIV", "packingLists": "PKL", "notificationsOfArrival": "DSK", "transitDocuments": "T1", "customsOffice": "Customs Office", "vehicleRegistration": "Vehicle Registration", "cmr": "CMR", "merchandisePositions": "CIV + PKL"}, "houseAirWaybill": {"heading": "HAWB - House Air Waybills", "noData": "No House Air Waybill", "addYourFirst": "Add your first House Air Waybill", "confirmDeleteDialog": {"title": "Delete HAWB", "content": "Are you sure you want to delete this HAWB?"}, "preview": {"newHawb": "New HAWB", "grossWeight": "Gross weight", "numberOfPieces": "Number of pieces"}, "form": {"mainInfoTitle": "Main information", "hawbNumber": "HAWB number", "mawbNumber": "MAWB number", "shipmentInfoTitle": "Shipment information", "numberOfPieces": "Number of pieces", "grossWeight": "Gross weight", "grossWeightUnit": "Gross weight unit", "partiesTitle": "Parties", "selectShipper": "Select shipper", "selectConsignee": "Select consignee", "shipper": "Shipper", "consignee": "Consignee"}, "partyAddress": {"shipperDetails": "Shipper details", "consigneeDetails": "Consignee details", "editShipper": "Edit shipper", "editConsignee": "Edit consignee"}}, "commercialInvoice": {"heading": "CIV - Commercial Invoices", "noData": "No Commercial Invoices", "addYourFirst": "Add your first Commercial Invoice", "preview": {"newInvoice": "New Commercial Invoice", "numberOfPieces": "Number of pieces", "grossWeight": "Gross weight", "totalAmount": "Total amount"}, "item": {"name": "Item Name", "translatedName": "Translated Name", "modelNumber": "Model number", "hsCode": "HS Code", "quantity": "Quantity", "unit": "Unit", "positionValue": "Position Value", "itemValue": "Item Value", "currency": "<PERSON><PERSON><PERSON><PERSON>", "noItems": "No items added", "addNew": "Add Item", "edit": "<PERSON>em", "confirmDeleteDialog": {"title": "Delete Item", "content": "Are you sure you want to delete this item?"}}, "form": {"mainInfoTitle": "Main Information", "shipmentInfoTitle": "Shipment Information", "partiesTitle": "Parties", "invoiceNumber": "Invoice Number", "invoiceDate": "Invoice Date", "grossWeight": "Gross Weight", "grossWeightUnit": "Weight Unit", "numberOfPieces": "Number of Pieces", "totalAmount": "Total Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "shipper": "Shipper", "consignee": "Consignee", "selectShipper": "Select Shipper", "selectConsignee": "Select Consignee", "itemsTitle": "Items", "incoterms": "Incoterms", "countryOfOrigin": "Country of origin"}, "partyAddress": {"shipperDetails": "Shipper Details", "consigneeDetails": "Consignee <PERSON>", "editShipper": "Edit shipper", "editConsignee": "Edit consignee"}, "confirmDeleteDialog": {"title": "Delete Commercial Invoice", "content": "Are you sure you want to delete this Commercial Invoice?"}}, "packingList": {"heading": "PKL - Packing Lists", "noData": "No Packing Lists", "addYourFirst": "Add your first Packing List", "preview": {"newPackingList": "Packing List", "totalItems": "Total Items"}, "form": {"packingListNumber": "Packing List Number", "packingListDate": "Packing List Date", "grossWeight": "Gross Weight", "grossWeightUnit": "Weight Unit", "numberOfPieces": "Number of Pieces", "positionsTitle": "Packing List Positions", "totalsTitle": "Totals"}, "item": {"name": "Item Name", "modelNumber": "Model Number", "purchaseOrderNumber": "PO Number", "commercialInvoiceNumber": "Invoice Number", "packageNetWeight": "Goods Net Weight", "packageNetWeightUnit": "Goods Net Weight Unit", "packageGrossWeight": "Goods Gross Weight", "packageGrossWeightUnit": "Goods Gross Weight Unit", "quantity": "Quantity", "stockUnit": "Packages unit", "volume": "Volume", "noItems": "No items added", "addNew": "Add Item", "edit": "<PERSON>em", "confirmDeleteDialog": {"title": "Delete Item", "content": "Are you sure you want to delete this item?"}}, "position": {"positionNumber": "Position Number", "commercialInvoiceNumber": "Commercial Invoice Number", "packageNetWeight": "Package Net Weight", "packageNetWeightUnit": "Package Net Weight Unit", "packageGrossWeight": "Package Gross Weight", "packageGrossWeightUnit": "Package Gross Weight Unit", "packagesNetWeight": "Packages Net Weight", "packagesNetWeightUnit": "Packages Net Weight Unit", "packagesGrossWeight": "Packages Gross Weight", "packagesGrossWeightUnit": "Packages Gross Weight Unit", "packageUnit": "Package Unit", "packageSize": "Package Size", "packageVolume": "Package Volume", "packagesVolume": "Packages Volume", "packageVolumeUnit": "Package Volume Unit", "numberOfPackages": "Number of Packages", "details": "Position Details", "noPositions": "No positions added yet", "addNew": "Add New Position", "edit": "Edit Position", "confirmDeleteDialog": {"title": "Delete Position", "content": "Are you sure you want to delete this position?"}}, "packedItem": {"title": "Packed Items", "name": "Item Name", "hsCode": "HS Code", "modelNumber": "Model Number", "quantity": "Quantity", "itemNetWeight": "Item Net Weight", "itemNetWeightUnit": "Item Net Weight Unit", "itemGrossWeight": "<PERSON><PERSON> Weight", "hsCodeHints": "HS Code Hints", "noItems": "No items added yet", "addNew": "Add Item"}, "total": {"totalQuantity": "Total Quantity", "totalStockUnit": "Total Stock Unit", "totalNumberOfPackages": "Total Number of Packages", "totalPackagesUnit": "Total Packages Unit", "totalNumberOfPallets": "Total Number of Pallets", "totalNetWeight": "Total Net Weight", "totalNetWeightUnit": "Total Net Weight Unit", "totalGrossWeight": "Total Gross Weight", "totalGrossWeightUnit": "Total Gross Weight Unit", "totalVolume": "Total Volume", "totalVolumeMeasurementUnit": "Total Volume Measurement Unit"}, "packagesAdditionalWeight": "Packages Additional Weight", "numberOfBulkPackages": "Number of Bulk Packages", "bulkPackagesUnit": "Bulk Packages Unit", "confirmDeleteDialog": {"title": "Delete Packing List", "content": "Are you sure you want to delete this Packing List?"}}, "notificationOfArrival": {"heading": "DSK - Notifications of Arrival", "noData": "No Notifications of Arrival", "addYourFirst": "Add your first Notification of Arrival", "preview": {"newNotification": "New Notification of Arrival", "flight": "Flight", "pieces": "Pieces", "weight": "Weight", "arrivalDate": "Arrival Date"}, "form": {"shipmentInfoTitle": "Shipment Information", "weightInfoTitle": "Weight Information", "customsInfoTitle": "Customs Information", "identificationNumber": "Identification Number", "flightNumber": "Flight Number", "arrivalDate": "Arrival Date", "customsOrigin": "Customs Origin", "shipper": "Shipper", "description": "Description", "pieces": "Number of Pieces", "weight": "Weight", "chargedWeight": "Charged Weight", "unitOfWeight": "Weight Unit", "storageLocationCode": "Storage Location Code", "dskTemporaryStorageNumber": "DSK Temporary Storage Number", "referenceNumber": "Reference Number", "remarks": "Remarks"}, "confirmDeleteDialog": {"title": "Delete Notification of Arrival", "content": "Are you sure you want to delete this Notification of Arrival?"}, "partyAddress": {"shipperDetails": "Shipper Details", "consigneeDetails": "Consignee <PERSON>", "editShipper": "<PERSON>", "editConsignee": "Edit Consignee"}}, "transit": {"heading": "T1 - Transit Documents", "noData": "No transit documents found", "addYourFirst": "Add your first transit document", "preview": {"newDocument": "New Transit Document", "totalPackages": "Total Packages", "totalWeight": "Total Weight", "items": "Items"}, "form": {"mainInfoTitle": "Transit Document Information", "mrnNumber": "MRN Number", "totalWeight": "Total Weight", "weightUnit": "Weight Unit", "totalPackages": "Total Packages", "itemsTitle": "Transit Items", "additionalInfoTitle": "Additional Information", "lrnNumber": "LRN Number", "previousDocument": "Previous Document", "storageLocationCode": "Storage Location Code", "transportDocument": "Transport Document", "attachedDocument": "Attached Document", "dispatchCountry": "Dispatch Country", "destinationCustomsOffice": "Destination Customs Office", "transportMeansAtExit": "Transport Means at Exit", "partyInfoTitle": "Party Information", "shipper": "Shipper", "consignee": "Consignee", "validityEndDate": "Validity End Date"}, "partyAddress": {"shipperDetails": "Shipper Details", "consigneeDetails": "Consignee <PERSON>", "editShipper": "<PERSON>", "editConsignee": "Edit Consignee"}, "item": {"addNew": "Add New Transit Item", "edit": "Edit Transit Item", "itemNumber": "Item Number", "packagingType": "Packaging Type", "description": "Description", "grossWeight": "Gross Weight", "netWeight": "Net Weight", "hsCode": "HS Code", "noItems": "No items added yet", "confirmDeleteDialog": {"title": "Delete Transit Item", "content": "Are you sure you want to delete this transit item?"}}, "confirmDeleteDialog": {"title": "Delete Transit Document", "content": "Are you sure you want to delete this transit document?"}}, "cmr": {"heading": "CMR - Road Consignment Notes", "noData": "No CMR documents found", "addYourFirst": "Add your first CMR document", "preview": {"newCMR": "New CMR Document", "vehicleNumber": "Vehicle Number", "numberOfPieces": "Number of Pieces", "grossWeight": "Gross Weight", "volume": "Volume"}, "form": {"mainInfoTitle": "Main Information", "cmrNumber": "CMR Number", "vehicleNumber": "Vehicle Number", "shipmentInfoTitle": "Shipment Information", "numberOfPieces": "Number of Pieces", "grossWeight": "Gross Weight", "grossWeightUnit": "Weight Unit", "volume": "Volume", "partiesTitle": "Parties", "shipper": "Shipper", "consignee": "Consignee"}, "partyAddress": {"shipperDetails": "Shipper Details", "consigneeDetails": "Consignee <PERSON>", "editShipper": "<PERSON>", "editConsignee": "Edit Consignee"}, "confirmDeleteDialog": {"title": "Delete CMR Document", "content": "Are you sure you want to delete this CMR document?"}}, "seaWaybill": {"heading": "SWB - Sea Waybills", "noData": "No Sea Waybills", "addYourFirst": "Add your first Sea Waybill", "preview": {"newSWB": "New Sea Waybill", "containerNumber": "Container number", "numberOfPieces": "Number of pieces", "grossWeight": "Gross weight"}, "form": {"mainInfoTitle": "Main Information", "swbNumber": "SWB Number", "containerNumber": "Container Number", "containerType": "Container Type", "shipmentInfoTitle": "Shipment Information", "grossWeight": "Gross Weight", "grossWeightUnit": "Weight Unit", "numberOfPieces": "Number of Pieces", "volumeMeasurement": "Volume Measurement", "partiesTitle": "Parties", "shipper": "Shipper", "consignee": "Consignee"}, "partyAddress": {"shipperDetails": "Shipper Details", "consigneeDetails": "Consignee <PERSON>", "editShipper": "<PERSON>", "editConsignee": "Edit Consignee"}, "confirmDeleteDialog": {"title": "Delete Sea Waybill", "content": "Are you sure you want to delete this Sea Waybill?"}}, "billOfLading": {"heading": "BOL - Bills of Lading", "noData": "No Bills of Lading", "addYourFirst": "Add your first Bill of Lading", "preview": {"newBOL": "New Bill of Lading", "containerNumber": "Container number", "numberOfPieces": "Number of pieces", "grossWeight": "Gross weight", "container": "Container"}, "form": {"mainInfoTitle": "Main Information", "bolNumber": "BOL Number", "containerNumber": "Container Number", "containerType": "Container Type", "shipmentInfoTitle": "Shipment Information", "numberOfPieces": "Number of Pieces", "grossWeight": "Gross Weight", "grossWeightUnit": "Weight Unit", "volumeMeasurement": "Volume Measurement", "partiesTitle": "Parties", "shipper": "Shipper", "consignee": "Consignee"}, "partyAddress": {"shipperDetails": "Shipper Details", "consigneeDetails": "Consignee <PERSON>", "editShipper": "<PERSON>", "editConsignee": "Edit Consignee"}, "confirmDeleteDialog": {"title": "Delete Bill of Lading", "content": "Are you sure you want to delete this Bill of Lading?"}}, "merchandisePositions": {"heading": "CIV + PKL - Merchandise Positions", "noData": "No Merchandise Positions", "addYourFirst": "Add your first Merchandise Position", "noPositions": "No positions added yet", "position": "Position", "addPosition": "Add Position", "editPosition": "Edit Position", "regeneratePositions": "Regenerate Positions", "regenerateSuccess": "Merchandise positions regenerated successfully!", "regenerateError": "Error regenerating merchandise positions", "form": {"positionsTitle": "Positions", "totalsTitle": "Totals Summary", "totalNumberOfPositions": "Total Number of Positions", "totalNumberOfPackages": "Total Number of Packages", "totalGrossMass": "Total Gross Mass", "totalNetMass": "Total Net Mass", "goodsDescription": "Goods Description", "translatedGoodsDescription": "Translated Goods Description", "packageMarks": "Package Marks", "numberOfPackages": "Number of Packages", "packageType": "Package Type", "packageTypeDescription": "Package Type Description", "commodityCode": "Commodity Code", "fullTariffCode": "Full Tariff Code", "grossMass": "Gross Mass", "grossMassUnit": "Gross Mass Unit", "netMass": "Net Mass", "netMassUnit": "Net Mass Unit"}, "confirmDeleteDialog": {"title": "Delete Position", "content": "Are you sure you want to delete this position?"}}}, "customsOffice": {"title": "Customs Office", "editTitle": "Edit Customs Office", "code": "Customs Office Code"}, "vehicleRegistration": {"title": "Vehicle Registration", "editTitle": "Edit Vehicle Registration", "vehicleRegistrationNumber": "Vehicle Registration Number", "vehicleCountryCode": "Vehicle Country", "trailerRegistrationNumber": "Trailer Registration Number", "trailerCountryCode": "Trailer Country"}}, "list": {"heading": "T1 Orders", "actions": {"create": {"label": "Begin an order"}}, "headers": {"number": "Number", "transportDocumentNumber": "Transport Document Number", "consigneeName": "Consignee", "createdDate": "Created on", "orderingPartyEmail": "Ordering party email", "orderDate": "Order date", "status": "Status", "confirmationDate": "Confirmation date", "forwardedDate": "Forwarded to customs office date", "completionDate": "Completion date", "orderType": "Order Type"}}, "beginningDialog": {"orderType": "Order Type", "header": "Begin new T1 order", "upload": "Upload", "removeAll": "Remove all", "uploadHelperAir": "Supported documents are: Master Air Waybill, House Air Waybill, Commercial Invoice, Packing List, Transit Document, Notification of Arrival", "uploadHelperSea": "Supported documents are: Master Bill of Lading, House Bill of Lading, Commercial Invoice, Packing List, Transit Document, Notification of Arrival", "uploadHelperRoad": "Supported documents are: CMR, Commercial Invoice, Packing List, Transit Document, Notification of Arrival", "uploadSuccess": "T1 Order begun successfully!"}, "status": {"created": "Created", "draft": "Draft", "confirmed": "Confirmed", "forwardedToTheCustomsOffice": "Submitted to the Customs Office", "completed": "Completed", "scanned": "Scanned", "scanning": "Scanning", "ordered": "Ordered", "scanFailed": "<PERSON>an failed", "merchandisePositionGeneration": "Generating Merchandise Positions", "hsCodeRecognition": "Recognizing HS Codes"}, "saveSuccess": "T1 Order saved successfully!", "saveError": "Error saving T1 Order", "delete": {"title": "Delete T1 Order", "confirmation": "Are you sure you want to delete T1 Order #{{orderNumber}}?", "typeToConfirm": "Type 'delete' to confirm:", "confirmPlaceholder": "delete", "error": "Error deleting T1 Order"}, "validation": {"requiredOrderId": "Order ID is required"}, "houseAirWaybill": {"formValidation": {"requiredHawbNumber": "HAWB number is required", "requiredMawbNumber": "MAWB number is required", "invalidGrossWeight": "Invalid gross weight", "positiveGrossWeight": "Gross weight must be positive", "invalidNumberOfPieces": "Invalid number of pieces", "intNumberOfPieces": "Number of pieces must be an integer", "positiveNumberOfPieces": "Number of pieces must be positive", "requiredOrderId": "Order ID is required", "invalidMasterAirWaybills": "Invalid Master Air Waybills", "invalidCommercialInvoices": "Invalid Commercial Invoices", "invalidPackingLists": "Invalid Packing Lists", "invalidNotificationsOfArrival": "Invalid Notifications of Arrival", "invalidTransitDocuments": "Invalid Transit Documents"}}}, "user": {"list": {"pageTitle": "Users", "heading": "Users acccount list", "userTableToolbar": {"searchPlaceholder": "Search..."}, "userTableFiltersResult": {"keyword": "Keyword:"}, "actions": {"deleteAccount": {"label": "Delete account", "confirmation": "Are you sure want to delete {{name}}'s account?", "deleteSuccess": "Account deleted successfully!"}, "invite": {"label": "Invite new user"}}, "headers": {"name": "Name", "role": "Role", "email": "Email", "status": "Status"}}, "invite": {"title": "Invite new user", "description": "Please provide the email address of the user you want to invite.", "emailPlaceholder": "Email", "inviteLabel": "Invite", "success": "Invitation sent to {{email}}!", "errorInvalid": "Invalid email address", "errorEmpty": "Email address is required"}}, "invitation": {"pageTitle": "Invitation", "welcomeTitle": "Welcome!", "welcomeDescription": "Please sign up to start using our system."}, "dataGrid": {"noRows": "No rows", "noResults": "No results found", "toolbar": {"density": "Density", "densityLabel": "Row density", "densityCompact": "Compact", "densityStandard": "Standard", "densityComfortable": "Comfortable", "columns": "Columns", "columnsLabel": "Select columns", "filters": "Filters", "filtersLabel": "Show filters", "filtersTooltipHide": "Hide filters", "filtersTooltipShow": "Show filters", "filtersTooltipActive": "{{count}} active filters", "quickFilterPlaceholder": "Search...", "quickFilterLabel": "Search", "quickFilterDeleteIconLabel": "Clear", "export": "Export", "exportLabel": "Export", "exportCSV": "Download CSV", "exportPrint": "Print", "exportExcel": "Download Excel"}, "columnsManagement": {"searchTitle": "Search columns", "noColumns": "No columns available", "showHideAll": "Show/Hide all", "reset": "Reset", "deleteIconLabel": "Delete"}, "filterPanel": {"addFilter": "Add filter", "removeAll": "Remove all", "deleteIconLabel": "Delete", "logicOperator": "Logic operator", "operator": "Operator", "operatorAnd": "AND", "operatorOr": "OR", "columns": "Columns", "inputLabel": "Value", "inputPlaceholder": "Filter value"}, "filterOperator": {"contains": "contains", "doesNotContain": "doesn't contain", "equals": "equals", "doesNotEqual": "not equal", "startsWith": "starts with", "endsWith": "ends with", "is": "is", "not": "is not", "after": "is after", "onOrAfter": "is on or after", "before": "is before", "onOrBefore": "is on or before", "isEmpty": "is empty", "isNotEmpty": "is not empty", "isAnyOf": "is any of", "notEquals": "≠", "greaterThan": ">", "greaterThanOrEqual": "≥", "lessThan": "<", "lessThanOrEqual": "≤", "between": "between"}, "headerFilterOperator": {"contains": "Contains", "doesNotContain": "Doesn't contain", "equals": "Equals", "doesNotEqual": "Not equal", "startsWith": "Starts with", "endsWith": "Ends with", "is": "Is", "not": "Is not", "after": "After", "onOrAfter": "On or after", "before": "Before", "onOrBefore": "On or before", "isEmpty": "Is empty", "isNotEmpty": "Is not empty", "isAnyOf": "Is any of"}, "filterValue": {"any": "any", "true": "true", "false": "false"}, "columnMenu": {"label": "<PERSON><PERSON>", "showColumns": "Show columns", "manageColumns": "Manage columns", "filter": "Filter", "hideColumn": "Hide column", "unsort": "Unsort", "sortAsc": "Sort ascending", "sortDesc": "Sort descending"}, "columnHeader": {"filtersTooltipActive": "{{count}} active filters", "filtersLabel": "Show filters", "sortIconLabel": "Sort"}, "footer": {"rowSelected": "{{count}} rows selected", "totalRows": "Total rows:", "totalVisibleRows": "{{visibleCount}} of {{totalCount}}"}, "checkboxSelection": {"headerName": "Checkbox selection", "selectAllRows": "Select all rows", "unselectAllRows": "Unselect all rows", "selectRow": "Select row", "unselectRow": "Unselect row"}, "booleanCell": {"trueLabel": "Yes", "falseLabel": "No"}, "actions": {"cellMore": "More", "pinToLeft": "<PERSON>n to left", "pinToRight": "Pin to right", "unpin": "Unpin"}, "treeData": {"groupingHeaderName": "Group", "expand": "Expand", "collapse": "Collapse"}, "grouping": {"columnHeaderName": "Group", "groupColumn": "Group by {{name}}", "unGroupColumn": "Stop grouping by {{name}}"}, "detailPanel": {"toggle": "Toggle detail panel", "expand": "Expand", "collapse": "Collapse"}, "rowReordering": {"headerName": "Row reordering"}, "aggregation": {"menuItemHeader": "Aggregation", "sum": "Sum", "avg": "Average", "min": "Min", "max": "Max", "size": "Size"}, "pagination": {"rowsPerPage": "Rows per page:", "displayedRows": "{{from}}-{{to}} of {{count}}"}}, "common": {"addItem": "Add new item", "addNew": "Add", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "none": "None", "back": "Back", "expand": "Expand", "collapse": "Collapse", "noData": "No data", "view": "View", "actions": "Actions", "copied": "Copied!", "itemsCount": "Items", "retry": "Retry", "deleteConfirmation": "Confirm Delete", "deleteConfirmationMessage": "Are you sure you want to delete this?"}, "weightUnits": {"kg": "Kilograms (kg)", "lb": "Pounds (lb)"}, "fileManager": {"dialog": {"uploadFiles": "Upload files", "folderName": "Folder name", "upload": "Upload", "uploading": "Uploading...", "removeAll": "Remove all", "save": "Save", "create": "Create", "noFilesSelected": "No files selected", "uploadSuccess": "Files uploaded successfully", "uploadError": "Error uploading files", "deleteConfirmation": {"title": "Delete File", "confirmation": "Are you sure you want to delete file '{{fileName}}'?", "typeToConfirm": "Type 'delete' to confirm:", "confirmPlaceholder": "delete", "error": "Error deleting file"}}, "fileItem": {"copyLink": "Copy Link", "view": "View", "delete": "Delete", "download": "Download", "downloading": "Downloading...", "downloadSuccess": "File downloaded successfully", "downloadError": "Error downloading file"}}, "transit": {"formValidation": {"requiredOrderId": "Order ID is required", "requiredMrnNumber": "MRN Number is required", "invalidTotalWeight": "Total weight must be a number", "nonNegativeTotalWeight": "Total weight cannot be negative", "invalidTotalPackages": "Total packages must be a number", "intTotalPackages": "Total packages must be a whole number", "nonNegativeTotalPackages": "Total packages cannot be negative", "invalidItemNumber": "Item number must be a number", "intItemNumber": "Item number must be a whole number", "positiveItemNumber": "Item number must be positive", "invalidGrossWeight": "Gross weight must be a number", "nonNegativeGrossWeight": "Gross weight cannot be negative", "invalidNetWeight": "Net weight must be a number", "nonNegativeNetWeight": "Net weight cannot be negative"}}}