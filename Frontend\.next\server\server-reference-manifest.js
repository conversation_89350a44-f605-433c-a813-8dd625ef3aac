self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"00049515d7d528586b80a7a58d8274362b065299b3\": {\n      \"workers\": {\n        \"app/auth/sign-in/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Cm.malik%5C%5Csource%5C%5Crepos%5C%5CRosseta%5C%5CFrontend%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Csign-in%5C%5Cpage.tsx%22%2C%5B%5B%2200049515d7d528586b80a7a58d8274362b065299b3%22%2C%22default%22%5D%2C%5B%2200bcae7f0b85b9c55e6827e87d762517f6217c9d14%22%2C%22generateMetadata%22%5D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/auth/sign-in/page\": \"rsc\"\n      }\n    },\n    \"00bcae7f0b85b9c55e6827e87d762517f6217c9d14\": {\n      \"workers\": {\n        \"app/auth/sign-in/page\": {\n          \"moduleId\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Cm.malik%5C%5Csource%5C%5Crepos%5C%5CRosseta%5C%5CFrontend%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Csign-in%5C%5Cpage.tsx%22%2C%5B%5B%2200049515d7d528586b80a7a58d8274362b065299b3%22%2C%22default%22%5D%2C%5B%2200bcae7f0b85b9c55e6827e87d762517f6217c9d14%22%2C%22generateMetadata%22%5D%5D%5D%5D&__client_imported__=!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/auth/sign-in/page\": \"rsc\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"