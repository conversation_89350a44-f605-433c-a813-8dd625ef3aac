'use client';

import type { GridColDef } from '@mui/x-data-grid';
import type { IUserItem } from 'src/types/user';

import useSWR from 'swr';
import { useBoolean } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';

import { DashboardContent } from 'src/layouts/dashboard';
import { _roles } from 'src/_mock';
import { useTranslate } from 'src/locales';
import { axiosInstance, endpoints, fetcher } from 'src/lib/axios';
import { toast } from 'src/components/snackbar';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';
import { Iconify } from 'src/components/iconify';
import { InviteUserDialog } from '../invite-user-dialog';
import { useAuthContext } from 'src/auth/hooks';
import { ConfirmDialog } from 'src/components/custom-dialog';
import { useState } from 'react';
import { ODataResponse } from 'src/types/baseResponse';
import { AppDataGrid } from 'src/layouts/components/app-data-grid/app-data-grid';

// ----------------------------------------------------------------------

export function UserListView() {
  const { t } = useTranslate();
  const { user } = useAuthContext();
  const inviteDialog = useBoolean();

  const [odataParams, setODataParams] = useState('');

  const { data, isLoading, mutate } = useSWR<ODataResponse<IUserItem>>(
    `${endpoints.identity.list}?${odataParams}`,
    fetcher
  );

  const handleDeleteRow = async (id: string) => {
    const result = await axiosInstance.post(endpoints.identity.delete, {
      identityId: id
    });

    await mutate();

    if (result.data.isSuccess) {
      toast.success(t('user.list.actions.deleteAccount.deleteSuccess'));
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'fullName',
      headerName: t('user.list.headers.name'),
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar src={params.row.Picture} alt={params.row.givenName} />
          {params.row.fullName}
        </Box>
      )
    },
    {
      field: 'email',
      headerName: t('user.list.headers.email'),
      flex: 1
    },
    {
      field: 'role',
      headerName: t('user.list.headers.role'),
      flex: 1
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: '',
      width: 80,
      getActions: (params) => {
        if (user?.email !== params.row.email) {
          return [
            <RowActions
              row={params.row}
              onDelete={() => handleDeleteRow(params.row.id)}
            />
          ];
        }
        return [];
      }
    }
  ];

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading={t('user.list.heading')}
        action={
          <Button
            variant="contained"
            onClick={inviteDialog.onTrue}
            startIcon={<Iconify icon="mingcute:add-line" />}
          >
            {t('user.list.actions.invite.label')}
          </Button>
        }
        sx={{ mb: 3, mt: 3 }}
      />

      <Card sx={{ height: '50vh', width: '100%' }}>
        <AppDataGrid
          rows={data?.value || []}
          columns={columns}
          totalCount={data?.['@odata.count'] || 0}
          loading={isLoading}
          onODataChange={setODataParams}
          disableRowSelectionOnClick
          getRowId={(row) => row.id}
          gridId="user-list"
        />
      </Card>

      <InviteUserDialog
        open={inviteDialog.value}
        onClose={inviteDialog.onFalse}
        onSuccess={() => mutate()}
      />
    </DashboardContent>
  );
}

function RowActions({ row, onDelete }: { row: IUserItem; onDelete: () => void }) {
  const [openConfirm, setOpenConfirm] = useState(false);
  const { t } = useTranslate();

  return (
    <>
      <IconButton onClick={() => setOpenConfirm(true)}>
        <Iconify icon="eva:more-vertical-fill" />
      </IconButton>

      <ConfirmDialog
        open={openConfirm}
        onClose={() => setOpenConfirm(false)}
        title={t('user.list.actions.deleteAccount.label')}
        content={t('user.list.actions.deleteAccount.confirmation', {
          name: `${row.givenName} ${row.surname}`
        })}
        action={
          <Button variant="contained" color="error" onClick={onDelete}>
            {t('user.list.actions.deleteAccount.label')}
          </Button>
        }
      />
    </>
  );
}