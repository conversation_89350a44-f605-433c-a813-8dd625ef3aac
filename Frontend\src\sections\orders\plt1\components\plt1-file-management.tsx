import { useState, useCallback, useEffect } from 'react';
import { useBoolean } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import CircularProgress from '@mui/material/CircularProgress';

import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { useTranslate } from 'src/locales';
import { PLT1OrderFileItem } from './plt1-file-item';
import { axiosInstance, endpoints } from 'src/lib/axios';
import { toast } from 'src/components/snackbar';
import { FileManagerNewFolderDialog } from 'src/components/file-manager/file-manager-new-folder-dialog';
import { PLT1FileDeleteConfirmationDialog } from './plt1-file-delete-confirmation-dialog';
import { PLT1OrderType } from '../plt1-order-type';

// Define types for PLT1 order files
export interface PLT1OrderFile {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  dateModified: string;
  isFavorited: boolean;
  t1OrderId: string;
  blobName?: string;
  uploadBatchId?: string;
}

export interface PLT1OrderFilesResponse {
  data: PLT1OrderFile[];
  statusCode: number;
  messages: string[];
  isSuccess: boolean;
}

// ----------------------------------------------------------------------

interface PLT1OrderFileManagementProps {
  orderId: string;
  orderType?: PLT1OrderType; // Add this new prop to determine the order type
  hasPendingChanges?: boolean;
  isScanning?: boolean;
}

export default function PLT1OrderFileManagement({ 
  orderId, 
  orderType,
  hasPendingChanges = false, 
  isScanning = false 
}: PLT1OrderFileManagementProps) {
  const { t } = useTranslate();
  const [orderFiles, setOrderFiles] = useState<PLT1OrderFile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const newFileDialog = useBoolean();
  const [fileToDelete, setFileToDelete] = useState<PLT1OrderFile | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Determine the upload endpoint based on order type
  const getUploadEndpoint = useCallback(() => {
    switch (orderType?.toLowerCase()) {
      case 'air':
        return `${endpoints.plt1.airUploadFiles}/${orderId}`;
      case 'road':
        return `${endpoints.plt1.roadUploadFiles}/${orderId}`;
      case 'sea':
        return `${endpoints.plt1.seaUploadFiles}/${orderId}`;
      default:
        return `${endpoints.plt1.airUploadFiles}/${orderId}`;
    }
  }, [orderId, orderType]);

  // Function to fetch order files from the API
  const fetchOrderFiles = useCallback(async () => {
    if (!orderId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await axiosInstance.get<PLT1OrderFilesResponse>(
        `${endpoints.plt1.listFiles}/${orderId}`
      );

      if (response.data.isSuccess) {
        setOrderFiles(response.data.data || []);
      } else {
        setError(response.data.messages?.[0] || t('plt1.details.fileManagement.fetchError'));
        toast.error(response.data.messages?.[0] || t('plt1.details.fileManagement.fetchError'));
      }
    } catch (err: any) {
      console.error('Error fetching order files:', err);
      setError(err.message || t('plt1.details.fileManagement.fetchError'));
      toast.error(err.message || t('plt1.details.fileManagement.fetchError'));
    } finally {
      setIsLoading(false);
    }
  }, [orderId, t]);

  // Fetch order files on component mount
  useEffect(() => {
    fetchOrderFiles();
  }, [fetchOrderFiles]);

  // Handle file drop
  const handleDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (!orderId || acceptedFiles.length === 0) return;

      setIsLoading(true);
      setError(null);

      try {
        // Create form data for file upload
        const formData = new FormData();
        formData.append('OrderId', orderId);

        // Append each file to the form data
        acceptedFiles.forEach((file) => {
          formData.append('Files', file);
        });

        // Get the correct upload endpoint based on order type
        const uploadEndpoint = getUploadEndpoint();

        // Upload files to the API using the correct endpoint
        const response = await axiosInstance.post<PLT1OrderFilesResponse>(
          uploadEndpoint,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }
        );

        if (response.data.isSuccess) {
          toast.success(t('plt1.details.fileManagement.uploadSuccess'));
          // Refresh the files list to get the latest files
          await fetchOrderFiles();
        } else {
          setError(response.data.messages?.[0] || t('plt1.details.fileManagement.uploadError'));
          toast.error(response.data.messages?.[0] || t('plt1.details.fileManagement.uploadError'));
        }
      } catch (err: any) {
        console.error('Error uploading files:', err);
        setError(err.message || t('plt1.details.fileManagement.uploadError'));
        toast.error(err.message || t('plt1.details.fileManagement.uploadError'));
      } finally {
        setIsLoading(false);
      }
    },
    [orderId, t, fetchOrderFiles, getUploadEndpoint]
  );

  // Handle file delete confirmation
  const handleDeleteConfirmation = (file: PLT1OrderFile) => {
    setFileToDelete(file);
    setDeleteDialogOpen(true);
  };

  // Handle file delete success
  const handleDeleteSuccess = () => {
    if (fileToDelete) {
      // Remove the file from the list
      setOrderFiles(prevFiles => prevFiles.filter(file => file.id !== fileToDelete.id));
      toast.success(t('plt1.details.fileManagement.deleteSuccess'));
      setFileToDelete(null);
    }
  };

  // Handle close delete dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  return (
    <Card>
      <CardHeader
        title={t('plt1.details.fileManagement.title')}
        action={
          <Tooltip
            title={
              hasPendingChanges
                ? t('plt1.details.fileManagement.savePendingChanges')
                : isScanning
                  ? t('plt1.details.fileManagement.scanningInProgress')
                  : ''
            }
            arrow
          >
            <span> {/* Wrapper needed for disabled button tooltip */}
              <Button
                size="small"
                color="primary"
                startIcon={<Iconify icon="eva:cloud-upload-fill" />}
                onClick={newFileDialog.onTrue}
                disabled={isLoading || hasPendingChanges || isScanning}
              >
                {t('plt1.details.fileManagement.uploadFile')}
              </Button>
            </span>
          </Tooltip>
        }
      />

      <Divider sx={{ borderStyle: 'dashed' }} />

      <Stack spacing={3} sx={{ p: 3 }}>
        {/* Error message */}
        {error && (
          <Typography color="error" variant="body2">
            {error}
          </Typography>
        )}

        {/* Loading indicator */}
        {isLoading && (
          <Box sx={{ display: 'flex', justifyContent: 'center' }}>
            <CircularProgress size={24} />
            <Typography variant="body2" sx={{ ml: 1 }}>
              {t('plt1.details.fileManagement.loading')}
            </Typography>
          </Box>
        )}

        {/* File list */}
        {orderFiles.length > 0 ? (
          <Box sx={{ mt: 2 }}>
            <Scrollbar sx={{ maxHeight: 320 }}>
              <Box sx={{ gap: 2, display: 'flex', flexDirection: 'column' }}>
                {orderFiles.map((file) => (
                  <PLT1OrderFileItem
                    key={file.id}
                    file={file}
                    onDelete={() => handleDeleteConfirmation(file)}
                    displayFavorite={false} />
                ))}
              </Box>
            </Scrollbar>
          </Box>
        ) : !isLoading && (
          <Typography variant="body2" sx={{ textAlign: 'center', color: 'text.secondary', py: 2 }}>
            {t('plt1.details.fileManagement.noFiles')}
          </Typography>
        )}
      </Stack>

      {/* Only render the dialogs if there are no pending changes and not scanning */}
      {!hasPendingChanges && !isScanning && (
        <>
          <FileManagerNewFolderDialog
            open={newFileDialog.value}
            onClose={newFileDialog.onFalse}
            title={t('plt1.details.fileManagement.uploadFile')}
            onUploadFiles={handleDrop}
            orderId={orderId}
            orderType={orderType} // Pass order type to dialog
          />

          {fileToDelete && (
            <PLT1FileDeleteConfirmationDialog
              open={deleteDialogOpen}
              onClose={handleCloseDeleteDialog}
              fileId={fileToDelete.id}
              fileName={fileToDelete.name}
              onSuccess={handleDeleteSuccess}
            />
          )}
        </>
      )}
    </Card >
  );
}