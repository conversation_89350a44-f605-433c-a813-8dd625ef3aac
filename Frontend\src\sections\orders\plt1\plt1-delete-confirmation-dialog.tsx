'use client';

import { useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import LoadingButton from '@mui/lab/LoadingButton';
import TextField from '@mui/material/TextField';
import Box from '@mui/material/Box';
import { useTranslate } from 'src/locales';
import { axiosInstance, endpoints } from 'src/lib/axios';

interface PLT1DeleteConfirmationDialogProps {
  open: boolean;
  onClose: () => void;
  orderId: string;
  orderNumber: string;
  orderType: string;
  onSuccess: () => void;
}

export function PLT1DeleteConfirmationDialog({
  open,
  onClose,
  orderId,
  orderNumber,
  orderType,
  onSuccess,
}: PLT1DeleteConfirmationDialogProps) {
  const { t } = useTranslate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [confirmText, setConfirmText] = useState('');

  const getDeleteEndpoint = (type: string) => {
    switch (type.toLowerCase()) {
      case 'air':
        return endpoints.plt1.airDelete;
      case 'road':
        return endpoints.plt1.roadDelete;
      case 'sea':
        return endpoints.plt1.seaDelete;
      default:
        return endpoints.plt1.airDelete;
    }
  };

  const handleDelete = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      const deleteEndpoint = getDeleteEndpoint(orderType);
      await axiosInstance.delete(`${deleteEndpoint}/${orderId}`);
      onSuccess();
      onClose();
      setConfirmText(''); // Reset the confirmation text on success
    } catch (error) {
      console.error(`Error deleting PLT1 ${orderType} order:`, error);
      setError(t('plt1.delete.error'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    onClose();
    setConfirmText('');
    setError(null);
  };

  // Check if the confirmation text matches the required text (case insensitive)
  const isDeleteEnabled = confirmText.toLowerCase() === 'delete';

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>{t('plt1.delete.title')}</DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 1, typography: 'body2' }}>
          {t('plt1.delete.confirmation', { orderNumber })}
        </Box>
        <Box sx={{ mb: 1 }}>
          <Box sx={{ mb: 3, typography: 'body2' }}>{t('plt1.delete.typeToConfirm')}</Box>

          <TextField
            fullWidth
            value={confirmText}
            onChange={(e) => setConfirmText(e.target.value)}
            placeholder={t('plt1.delete.confirmPlaceholder')}
            variant="outlined"
            size="small"
            autoComplete="off"
            disabled={isSubmitting}
          />
        </Box>

        {error && (
          <DialogContentText color="error" sx={{ mt: 2 }}>
            {error}
          </DialogContentText>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={isSubmitting}>
          {t('common.cancel')}
        </Button>
        <LoadingButton
          onClick={handleDelete}
          loading={isSubmitting}
          color="error"
          variant="contained"
          disabled={!isDeleteEnabled || isSubmitting}
        >
          {t('common.delete')}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
}