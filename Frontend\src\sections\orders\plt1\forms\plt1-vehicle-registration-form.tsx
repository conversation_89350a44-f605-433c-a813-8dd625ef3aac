'use client';

import { useFormContext } from 'react-hook-form';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Divider from '@mui/material/Divider';
import CardHeader from '@mui/material/CardHeader';
import { Field } from 'src/components/hook-form';
import { useTranslate } from 'src/locales';
import { PLT1VehicleRegistration } from '../types/plt1-details.types';
import { Box } from '@mui/material';

// ----------------------------------------------------------------------

interface PLT1VehicleRegistrationFormProps {
  formPath: string;
  readOnly?: boolean;
}

export default function PLT1VehicleRegistrationForm({
  formPath,
  readOnly = false,
}: PLT1VehicleRegistrationFormProps) {
  const { t } = useTranslate();

  const renderVehicleInfo = () => (
    <Card sx={{ boxShadow: 'none' }}>
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
          <Box
            sx={{
              rowGap: 3,
              columnGap: 2,
              display: 'grid',
              gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' },
            }}
          >
            <Field.Text
              name={`${formPath}.vehicleRegistrationNumber`}
              label={t('plt1.details.vehicleRegistration.vehicleRegistrationNumber')}
              disabled={readOnly}
            />
            <Field.CountrySelect
              name={`${formPath}.vehicleCountryCode`}
              label={t('plt1.details.vehicleRegistration.vehicleCountryCode')}
              disabled={readOnly}
            />
          </Box>
          <Box
            sx={{
              rowGap: 3,
              columnGap: 2,
              display: 'grid',
              gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' },
            }}
          >
          <Field.Text
            name={`${formPath}.trailerRegistrationNumber`}
            label={t('plt1.details.vehicleRegistration.trailerRegistrationNumber')}
            disabled={readOnly}
          />
          <Field.CountrySelect
            name={`${formPath}.trailerCountryCode`}
            label={t('plt1.details.vehicleRegistration.trailerCountryCode')}
            disabled={readOnly}
          />
        </Box>
      </Stack>
    </Card>
  );

  const renderTrailerInfo = () => (
    <Card sx={{ mt: 3, boxShadow: 'none' }}>
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}></Stack>
    </Card>
  );

  return (
    <Stack spacing={3}>
      {renderVehicleInfo()}
      {renderTrailerInfo()}
    </Stack>
  );
}
