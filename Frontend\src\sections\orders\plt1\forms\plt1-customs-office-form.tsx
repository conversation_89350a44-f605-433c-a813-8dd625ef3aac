'use client';

import { useFormContext } from 'react-hook-form';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Divider from '@mui/material/Divider';
import CardHeader from '@mui/material/CardHeader';
import { Field } from 'src/components/hook-form';
import { useTranslate } from 'src/locales';
import { PLT1CustomsOffice } from '../types/plt1-details.types';

// ----------------------------------------------------------------------

interface PLT1CustomsOfficeFormProps {
  formPath: string;
  readOnly?: boolean;
}

export default function PLT1CustomsOfficeForm({
  formPath,
  readOnly = false,
}: PLT1CustomsOfficeFormProps) {
  const { t } = useTranslate();

  const renderCustomsOfficeInfo = () => (
    <Card sx={{ boxShadow: 'none' }}>
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Field.Text
          name={`${formPath}.customsOfficeCode`}
          label={t('plt1.details.customsOffice.code')}
          disabled={readOnly}
        />
      </Stack>
    </Card>
  );

  return (
    <Stack spacing={3}>
      {renderCustomsOfficeInfo()}
    </Stack>
  );
}
