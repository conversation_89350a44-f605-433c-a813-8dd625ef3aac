// utils/grid/componentsents/GridDatePicker.tsx
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import dayjs from 'dayjs';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import { useTranslate } from 'src/locales';

dayjs.extend(localizedFormat);

interface GridDateTimePickerProps {
  item: {
    value: Date | null;
  };
  applyValue: (value: { value: Date | null }) => void;
  [key: string]: any;
}

export const GridDateTimePicker = ({ item, applyValue, ...other }: GridDateTimePickerProps) => {
  const { t, currentLang } = useTranslate();

  const timeString = dayjs().locale(currentLang.value).format('LT');
  const useAMPM = timeString.includes('AM') || timeString.includes('PM');

  return (
    <DateTimePicker
      {...other}
      value={item.value ? dayjs(item.value) : null}
      onChange={(newValue) => {
        applyValue({ 
          ...item, 
          value: newValue ? dayjs(newValue).toDate() : null 
        });
      }}
      format="L LT"
      ampm={useAMPM}
      slotProps={{
        textField: {
          variant: 'outlined',
          size: 'small',
          fullWidth: true,
          placeholder: t('components.gridDatePicker.selectDateTime')
        },
        mobilePaper: {
          sx: {
            '& .MuiPickersToolbar-title': {
              display: 'none'
            }
          }
        }
      }}
      localeText={{
        fieldDayPlaceholder: () => t('components.gridDatePicker.dd'),
        fieldMonthPlaceholder: () => t('components.gridDatePicker.mm'),
        fieldYearPlaceholder: () => t('components.gridDatePicker.yyyy'),
        fieldHoursPlaceholder: () => t('components.gridDatePicker.hh'),
        fieldMinutesPlaceholder: () => t('components.gridDatePicker.mm'),
        toolbarTitle: t('components.gridDatePicker.selectDateTime'),
      }}
    />
  );
};