import { useState, useEffect, useRef } from 'react';
import { axiosInstance, endpoints } from 'src/lib/axios';
import { PLT1OrderStatus } from '../plt1-status';

interface PLT1OrderStatusResponse {
  isSuccess: boolean;
  messages: string[];
  data: {
    orderId: string;
    orderNumber: string;
    status: string;
  };
}

/**
 * Custom hook for fetching T1 order status
 * @param orderId The ID of the T1 order to fetch status for
 * @param refreshInterval Optional interval in milliseconds to refresh the status
 * @param onStatusChange Optional callback that is called when the status changes
 */
export const usePLT1OrderStatus = (
  orderId: string,
  refreshInterval?: number,
  onStatusChange?: (newStatus: PLT1OrderStatus, oldStatus: PLT1OrderStatus | null) => void
) => {
  const [status, setStatus] = useState<PLT1OrderStatus | null>(null);
  const [orderNumber, setOrderNumber] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use a ref to track the previous status for change detection
  const previousStatusRef = useRef<PLT1OrderStatus | null>(null);

  // Fetch order status
  const fetchStatus = async () => {
    if (!orderId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await axiosInstance.get<PLT1OrderStatusResponse>(`${endpoints.plt1.status}/${orderId}`);

      if (response.data.isSuccess && response.data.data) {
        const newStatus = response.data.data.status as PLT1OrderStatus;

        // Check if status has changed and we have a previous status
        if (previousStatusRef.current !== null && newStatus !== previousStatusRef.current) {
          console.log(`Order status changed from ${previousStatusRef.current} to ${newStatus}`);

          // Call the callback if provided
          if (onStatusChange) {
            onStatusChange(newStatus, previousStatusRef.current);
          }
        }

        // Update the status and store the previous value
        setStatus(newStatus);
        previousStatusRef.current = newStatus;

        setOrderNumber(response.data.data.orderNumber);
      } else {
        setError(response.data.messages?.[0] || 'Failed to fetch order status');
      }
    } catch (err: any) {
      console.error('Error fetching T1 order status:', err);
      setError(err.message || 'Failed to fetch order status');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchStatus();
  }, [orderId]);

  // Set up refresh interval if provided
  useEffect(() => {
    if (!refreshInterval) return;

    const intervalId = setInterval(fetchStatus, refreshInterval);

    return () => clearInterval(intervalId);
  }, [refreshInterval, orderId]);

  return {
    status,
    orderNumber,
    isLoading,
    error,
    refetch: fetchStatus
  };
};
