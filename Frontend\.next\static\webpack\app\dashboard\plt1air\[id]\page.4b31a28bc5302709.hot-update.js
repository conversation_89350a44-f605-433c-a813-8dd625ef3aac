"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-packing-list-tab.tsx":
/*!*****************************************************************!*\
  !*** ./src/sections/orders/plt1/tabs/plt1-packing-list-tab.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1PackingListsTab)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _mui_material_Stack__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material/Stack */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material/Typography */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var _plt1_document_tab_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./plt1-document-tab-base */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-document-tab-base.tsx\");\n/* harmony import */ var _forms_plt1_packing_list_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../forms/plt1-packing-list-form */ \"(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction PLT1PackingListsTab(param) {\n    let { t1OrderId, order, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    const { control } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useFormContext)();\n    // Try to restore the expanded state from sessionStorage\n    const [expandedPackingList, setExpandedPackingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"PLT1PackingListsTab.useState\": ()=>{\n            const saved = sessionStorage.getItem(\"plt1-expanded-packing-list-\".concat(t1OrderId));\n            return saved ? parseInt(saved, 10) : null;\n        }\n    }[\"PLT1PackingListsTab.useState\"]);\n    const [deleteIndex, setDeleteIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openConfirm, setOpenConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fieldArray = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useFieldArray)({\n        control,\n        name: 'packingLists'\n    });\n    // Save expanded state to sessionStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1PackingListsTab.useEffect\": ()=>{\n            if (expandedPackingList !== null) {\n                sessionStorage.setItem(\"plt1-expanded-packing-list-\".concat(t1OrderId), expandedPackingList.toString());\n            } else {\n                sessionStorage.removeItem(\"plt1-expanded-packing-list-\".concat(t1OrderId));\n            }\n        }\n    }[\"PLT1PackingListsTab.useEffect\"], [\n        expandedPackingList,\n        t1OrderId\n    ]);\n    const handleAddPackingList = ()=>{\n        const newPackingList = {\n            id: undefined,\n            t1OrderId,\n            packingListPositions: [],\n            listTotal: {\n                id: undefined,\n                shipmentOrPackingId: '',\n                totalQuantity: 0,\n                totalPackagesUnit: 'CTNS',\n                totalNumberOfPackages: 0,\n                totalNumberOfPallets: 0,\n                totalNetWeight: 0,\n                totalNetWeightUnit: 'KGM',\n                totalGrossWeight: 0,\n                totalGrossWeightUnit: 'KGM',\n                totalVolume: 0,\n                totalVolumeMeasurementUnit: 'CBM',\n                packingListId: undefined\n            }\n        };\n        fieldArray.append(newPackingList);\n        setExpandedPackingList(fieldArray.fields.length);\n    };\n    const handleToggleExpand = (index)=>{\n        setExpandedPackingList(expandedPackingList === index ? null : index);\n    };\n    const handleOpenConfirm = (index)=>{\n        setDeleteIndex(index);\n        setOpenConfirm(true);\n    };\n    const handleCloseConfirm = ()=>{\n        setOpenConfirm(false);\n        if (deleteIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    const handleDeletePackingList = ()=>{\n        if (deleteIndex !== null) {\n            fieldArray.remove(deleteIndex);\n            if (expandedPackingList === deleteIndex) {\n                setExpandedPackingList(null);\n            }\n        }\n        handleCloseConfirm();\n    };\n    // Render preview of the packing list item when collapsed\n    const renderPreview = (packingList, _index)=>{\n        var _packingList_packingListPositions, _packingList_listTotal, _packingList_listTotal1, _packingList_listTotal2, _packingList_listTotal3, _packingList_listTotal4, _packingList_listTotal5, _packingList_listTotal6, _packingList_listTotal7, _packingList_listTotal8, _packingList_listTotal9, _packingList_listTotal10, _packingList_listTotal11, _packingList_listTotal12;\n        // Calculate total packed items across all positions\n        const totalPackedItems = ((_packingList_packingListPositions = packingList.packingListPositions) === null || _packingList_packingListPositions === void 0 ? void 0 : _packingList_packingListPositions.reduce((total, position)=>{\n            var _position_packedItems;\n            return total + (((_position_packedItems = position.packedItems) === null || _position_packedItems === void 0 ? void 0 : _position_packedItems.length) || 0);\n        }, 0)) || 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            direction: \"row\",\n            spacing: 2,\n            sx: {\n                px: 3,\n                pb: 2,\n                display: 'flex',\n                flexWrap: 'wrap',\n                '& > *': {\n                    mr: 3,\n                    mb: 1\n                }\n            },\n            children: [\n                packingList.packingListPositions && packingList.packingListPositions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\n                        t('plt1.details.documents.packingList.position.positionNumber'),\n                        \": \",\n                        packingList.packingListPositions.length\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\tabs\\\\plt1-packing-list-tab.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 11\n                }, this),\n                totalPackedItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\n                        t('common.itemsCount'),\n                        \": \",\n                        totalPackedItems\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\tabs\\\\plt1-packing-list-tab.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this),\n                ((_packingList_listTotal = packingList.listTotal) === null || _packingList_listTotal === void 0 ? void 0 : _packingList_listTotal.totalNumberOfPackages) !== undefined && ((_packingList_listTotal1 = packingList.listTotal) === null || _packingList_listTotal1 === void 0 ? void 0 : _packingList_listTotal1.totalNumberOfPackages) !== null && ((_packingList_listTotal2 = packingList.listTotal) === null || _packingList_listTotal2 === void 0 ? void 0 : _packingList_listTotal2.totalNumberOfPackages) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\n                        t('plt1.details.documents.packingList.total.totalNumberOfPackages'),\n                        \":\",\n                        ' ',\n                        packingList.listTotal.totalNumberOfPackages,\n                        ' ',\n                        packingList.listTotal.totalPackagesUnit || ''\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\tabs\\\\plt1-packing-list-tab.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 13\n                }, this),\n                ((_packingList_listTotal3 = packingList.listTotal) === null || _packingList_listTotal3 === void 0 ? void 0 : _packingList_listTotal3.totalNumberOfPallets) !== undefined && ((_packingList_listTotal4 = packingList.listTotal) === null || _packingList_listTotal4 === void 0 ? void 0 : _packingList_listTotal4.totalNumberOfPallets) !== null && ((_packingList_listTotal5 = packingList.listTotal) === null || _packingList_listTotal5 === void 0 ? void 0 : _packingList_listTotal5.totalNumberOfPallets) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\n                        t('plt1.details.documents.packingList.total.totalNumberOfPallets'),\n                        \":\",\n                        ' ',\n                        packingList.listTotal.totalNumberOfPallets\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\tabs\\\\plt1-packing-list-tab.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 13\n                }, this),\n                ((_packingList_listTotal6 = packingList.listTotal) === null || _packingList_listTotal6 === void 0 ? void 0 : _packingList_listTotal6.totalGrossWeight) !== undefined && ((_packingList_listTotal7 = packingList.listTotal) === null || _packingList_listTotal7 === void 0 ? void 0 : _packingList_listTotal7.totalGrossWeight) !== null && ((_packingList_listTotal8 = packingList.listTotal) === null || _packingList_listTotal8 === void 0 ? void 0 : _packingList_listTotal8.totalGrossWeight) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\n                        t('plt1.details.documents.packingList.total.totalGrossWeight'),\n                        \":\",\n                        ' ',\n                        packingList.listTotal.totalGrossWeight,\n                        ' ',\n                        packingList.listTotal.totalGrossWeightUnit || 'KGM'\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\tabs\\\\plt1-packing-list-tab.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 13\n                }, this),\n                ((_packingList_listTotal9 = packingList.listTotal) === null || _packingList_listTotal9 === void 0 ? void 0 : _packingList_listTotal9.totalNetWeight) !== undefined && ((_packingList_listTotal10 = packingList.listTotal) === null || _packingList_listTotal10 === void 0 ? void 0 : _packingList_listTotal10.totalNetWeight) !== null && ((_packingList_listTotal11 = packingList.listTotal) === null || _packingList_listTotal11 === void 0 ? void 0 : _packingList_listTotal11.totalNetWeight) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\n                        t('plt1.details.documents.packingList.total.totalNetWeight'),\n                        \":\",\n                        ' ',\n                        packingList.listTotal.totalNetWeight,\n                        ' ',\n                        packingList.listTotal.totalNetWeightUnit || 'KGM'\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\tabs\\\\plt1-packing-list-tab.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 13\n                }, this),\n                ((_packingList_listTotal12 = packingList.listTotal) === null || _packingList_listTotal12 === void 0 ? void 0 : _packingList_listTotal12.shipmentOrPackingId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\n                        \"ID: \",\n                        packingList.listTotal.shipmentOrPackingId\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\tabs\\\\plt1-packing-list-tab.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\tabs\\\\plt1-packing-list-tab.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the form when expanded\n    const renderForm = (index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_plt1_packing_list_form__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            formPath: \"packingLists\",\n            index: index,\n            readOnly: readOnly\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\tabs\\\\plt1-packing-list-tab.tsx\",\n            lineNumber: 202,\n            columnNumber: 5\n        }, this);\n    // Render the title of each item\n    const getItemTitle = (packingList)=>{\n        var _packingList_listSummary_, _packingList_listTotal;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            variant: \"subtitle1\",\n            children: ((_packingList_listSummary_ = packingList.listSummary[0]) === null || _packingList_listSummary_ === void 0 ? void 0 : _packingList_listSummary_.name) || (((_packingList_listTotal = packingList.listTotal) === null || _packingList_listTotal === void 0 ? void 0 : _packingList_listTotal.totalQuantity) ? \"\".concat(t('plt1.details.documents.packingList.preview.totalItems'), \": \").concat(packingList.listTotal.totalQuantity, \" \").concat(packingList.listTotal.totalPackagesUnit || '') : t('plt1.details.documents.packingList.preview.newPackingList'))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\tabs\\\\plt1-packing-list-tab.tsx\",\n            lineNumber: 207,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plt1_document_tab_base__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        t1OrderId: t1OrderId,\n        order: order,\n        readOnly: readOnly,\n        title: t('plt1.details.documents.packingList.heading'),\n        emptyTitle: t('plt1.details.documents.packingList.noData'),\n        emptyDescription: t('plt1.details.documents.packingList.addYourFirst'),\n        expandedIndex: expandedPackingList,\n        deleteIndex: deleteIndex,\n        openConfirm: openConfirm,\n        fieldArray: fieldArray,\n        fieldArrayName: \"packingLists\",\n        onToggleExpand: handleToggleExpand,\n        onOpenConfirm: handleOpenConfirm,\n        onCloseConfirm: handleCloseConfirm,\n        onDelete: handleDeletePackingList,\n        onAdd: handleAddPackingList,\n        renderPreview: renderPreview,\n        renderForm: renderForm,\n        getItemTitle: getItemTitle\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\tabs\\\\plt1-packing-list-tab.tsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1PackingListsTab, \"XqMsZcEpu/21u/SGYYII3s3zSuE=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useFormContext,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_5__.useFieldArray\n    ];\n});\n_c = PLT1PackingListsTab;\nvar _c;\n$RefreshReg$(_c, \"PLT1PackingListsTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZWN0aW9ucy9vcmRlcnMvcGx0MS90YWJzL3BsdDEtcGFja2luZy1saXN0LXRhYi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRW9EO0FBQ1k7QUFFeEI7QUFDVTtBQUVQO0FBQ2dCO0FBQ2dDO0FBZTVFLFNBQVNVLG9CQUFvQixLQUlqQjtRQUppQixFQUMxQ0MsU0FBUyxFQUNUQyxLQUFLLEVBQ0xDLFdBQVcsS0FBSyxFQUNTLEdBSmlCOztJQUsxQyxNQUFNLEVBQUVDLENBQUMsRUFBRSxHQUFHUCx5REFBWUE7SUFDMUIsTUFBTSxFQUFFUSxPQUFPLEVBQUUsR0FBR1osK0RBQWNBO0lBRWxDLHdEQUF3RDtJQUN4RCxNQUFNLENBQUNhLHFCQUFxQkMsdUJBQXVCLEdBQUdoQiwrQ0FBUUE7d0NBQWdCO1lBQzVFLE1BQU1pQixRQUFRQyxlQUFlQyxPQUFPLENBQUMsOEJBQXdDLE9BQVZUO1lBQ25FLE9BQU9PLFFBQVFHLFNBQVNILE9BQU8sTUFBTTtRQUN2Qzs7SUFFQSxNQUFNLENBQUNJLGFBQWFDLGVBQWUsR0FBR3RCLCtDQUFRQSxDQUFnQjtJQUM5RCxNQUFNLENBQUN1QixhQUFhQyxlQUFlLEdBQUd4QiwrQ0FBUUEsQ0FBVTtJQUN4RCxNQUFNeUIsa0JBQWtCMUIsNkNBQU1BLENBQW9CO0lBRWxELE1BQU0yQixhQUFhdkIsOERBQWFBLENBQUM7UUFDL0JXO1FBQ0FhLE1BQU07SUFDUjtJQUVBLDREQUE0RDtJQUM1RDFCLGdEQUFTQTt5Q0FBQztZQUNSLElBQUljLHdCQUF3QixNQUFNO2dCQUNoQ0csZUFBZVUsT0FBTyxDQUNwQiw4QkFBd0MsT0FBVmxCLFlBQzlCSyxvQkFBb0JjLFFBQVE7WUFFaEMsT0FBTztnQkFDTFgsZUFBZVksVUFBVSxDQUFDLDhCQUF3QyxPQUFWcEI7WUFDMUQ7UUFDRjt3Q0FBRztRQUFDSztRQUFxQkw7S0FBVTtJQUVuQyxNQUFNcUIsdUJBQXVCO1FBQzNCLE1BQU1DLGlCQUFzQztZQUMxQ0MsSUFBSUM7WUFDSnhCO1lBQ0F5QixzQkFBc0IsRUFBRTtZQUN4QkMsV0FBVztnQkFDVEgsSUFBSUM7Z0JBQ0pHLHFCQUFxQjtnQkFDckJDLGVBQWU7Z0JBQ2ZDLG1CQUFtQjtnQkFDbkJDLHVCQUF1QjtnQkFDdkJDLHNCQUFzQjtnQkFDdEJDLGdCQUFnQjtnQkFDaEJDLG9CQUFvQjtnQkFDcEJDLGtCQUFrQjtnQkFDbEJDLHNCQUFzQjtnQkFDdEJDLGFBQWE7Z0JBQ2JDLDRCQUE0QjtnQkFDNUJDLGVBQWVkO1lBQ2pCO1FBQ0Y7UUFFQVIsV0FBV3VCLE1BQU0sQ0FBQ2pCO1FBQ2xCaEIsdUJBQXVCVSxXQUFXd0IsTUFBTSxDQUFDQyxNQUFNO0lBQ2pEO0lBRUEsTUFBTUMscUJBQXFCLENBQUNDO1FBQzFCckMsdUJBQXVCRCx3QkFBd0JzQyxRQUFRLE9BQU9BO0lBQ2hFO0lBRUEsTUFBTUMsb0JBQW9CLENBQUNEO1FBQ3pCL0IsZUFBZStCO1FBQ2Y3QixlQUFlO0lBQ2pCO0lBRUEsTUFBTStCLHFCQUFxQjtRQUN6Qi9CLGVBQWU7UUFDZixJQUFJSCxnQkFBZ0IsTUFBTTtnQkFDeEJJO2FBQUFBLDJCQUFBQSxnQkFBZ0IrQixPQUFPLGNBQXZCL0IsK0NBQUFBLHlCQUF5QmdDLEtBQUs7UUFDaEM7SUFDRjtJQUVBLE1BQU1DLDBCQUEwQjtRQUM5QixJQUFJckMsZ0JBQWdCLE1BQU07WUFDeEJLLFdBQVdpQyxNQUFNLENBQUN0QztZQUNsQixJQUFJTix3QkFBd0JNLGFBQWE7Z0JBQ3ZDTCx1QkFBdUI7WUFDekI7UUFDRjtRQUNBdUM7SUFDRjtJQUVBLHlEQUF5RDtJQUN6RCxNQUFNSyxnQkFBZ0IsQ0FBQ0MsYUFBa0JDO1lBRWRELG1DQWdDcEJBLHdCQUNDQSx5QkFDQUEseUJBU0RBLHlCQUNDQSx5QkFDQUEseUJBUURBLHlCQUNDQSx5QkFDQUEseUJBU0RBLHlCQUNDQSwwQkFDQUEsMEJBU0RBO1FBNUVMLG9EQUFvRDtRQUNwRCxNQUFNRSxtQkFBbUJGLEVBQUFBLG9DQUFBQSxZQUFZMUIsb0JBQW9CLGNBQWhDMEIsd0RBQUFBLGtDQUFrQ0csTUFBTSxDQUMvRCxDQUFDQyxPQUFlQztnQkFBMkJBO21CQUFURCxRQUFTQyxDQUFBQSxFQUFBQSx3QkFBQUEsU0FBU0MsV0FBVyxjQUFwQkQsNENBQUFBLHNCQUFzQmYsTUFBTSxLQUFJO1dBQzNFLE9BQ0c7UUFFTCxxQkFDRSw4REFBQy9DLDJEQUFLQTtZQUNKZ0UsV0FBVTtZQUNWQyxTQUFTO1lBQ1RDLElBQUk7Z0JBQ0ZDLElBQUk7Z0JBQ0pDLElBQUk7Z0JBQ0pDLFNBQVM7Z0JBQ1RDLFVBQVU7Z0JBQ1YsU0FBUztvQkFBRUMsSUFBSTtvQkFBR0MsSUFBSTtnQkFBRTtZQUMxQjs7Z0JBR0NmLFlBQVkxQixvQkFBb0IsSUFBSTBCLFlBQVkxQixvQkFBb0IsQ0FBQ2dCLE1BQU0sR0FBRyxtQkFDN0UsOERBQUM5QyxnRUFBVUE7b0JBQUN3RSxTQUFRO29CQUFRQyxPQUFNOzt3QkFDL0JqRSxFQUFFO3dCQUE4RDt3QkFBR2dELFlBQVkxQixvQkFBb0IsQ0FBQ2dCLE1BQU07Ozs7Ozs7Z0JBSzlHWSxtQkFBbUIsbUJBQ2xCLDhEQUFDMUQsZ0VBQVVBO29CQUFDd0UsU0FBUTtvQkFBUUMsT0FBTTs7d0JBQy9CakUsRUFBRTt3QkFBcUI7d0JBQUdrRDs7Ozs7OztnQkFLOUJGLEVBQUFBLHlCQUFBQSxZQUFZekIsU0FBUyxjQUFyQnlCLDZDQUFBQSx1QkFBdUJyQixxQkFBcUIsTUFBS04sYUFDaEQyQixFQUFBQSwwQkFBQUEsWUFBWXpCLFNBQVMsY0FBckJ5Qiw4Q0FBQUEsd0JBQXVCckIscUJBQXFCLE1BQUssUUFDakRxQixFQUFBQSwwQkFBQUEsWUFBWXpCLFNBQVMsY0FBckJ5Qiw4Q0FBQUEsd0JBQXVCckIscUJBQXFCLElBQUcsbUJBQzdDLDhEQUFDbkMsZ0VBQVVBO29CQUFDd0UsU0FBUTtvQkFBUUMsT0FBTTs7d0JBQy9CakUsRUFBRTt3QkFBa0U7d0JBQUU7d0JBQ3RFZ0QsWUFBWXpCLFNBQVMsQ0FBQ0kscUJBQXFCO3dCQUFFO3dCQUM3Q3FCLFlBQVl6QixTQUFTLENBQUNHLGlCQUFpQixJQUFJOzs7Ozs7O2dCQUtqRHNCLEVBQUFBLDBCQUFBQSxZQUFZekIsU0FBUyxjQUFyQnlCLDhDQUFBQSx3QkFBdUJwQixvQkFBb0IsTUFBS1AsYUFDL0MyQixFQUFBQSwwQkFBQUEsWUFBWXpCLFNBQVMsY0FBckJ5Qiw4Q0FBQUEsd0JBQXVCcEIsb0JBQW9CLE1BQUssUUFDaERvQixFQUFBQSwwQkFBQUEsWUFBWXpCLFNBQVMsY0FBckJ5Qiw4Q0FBQUEsd0JBQXVCcEIsb0JBQW9CLElBQUcsbUJBQzVDLDhEQUFDcEMsZ0VBQVVBO29CQUFDd0UsU0FBUTtvQkFBUUMsT0FBTTs7d0JBQy9CakUsRUFBRTt3QkFBaUU7d0JBQUU7d0JBQ3JFZ0QsWUFBWXpCLFNBQVMsQ0FBQ0ssb0JBQW9COzs7Ozs7O2dCQUtoRG9CLEVBQUFBLDBCQUFBQSxZQUFZekIsU0FBUyxjQUFyQnlCLDhDQUFBQSx3QkFBdUJqQixnQkFBZ0IsTUFBS1YsYUFDM0MyQixFQUFBQSwwQkFBQUEsWUFBWXpCLFNBQVMsY0FBckJ5Qiw4Q0FBQUEsd0JBQXVCakIsZ0JBQWdCLE1BQUssUUFDNUNpQixFQUFBQSwwQkFBQUEsWUFBWXpCLFNBQVMsY0FBckJ5Qiw4Q0FBQUEsd0JBQXVCakIsZ0JBQWdCLElBQUcsbUJBQ3hDLDhEQUFDdkMsZ0VBQVVBO29CQUFDd0UsU0FBUTtvQkFBUUMsT0FBTTs7d0JBQy9CakUsRUFBRTt3QkFBNkQ7d0JBQUU7d0JBQ2pFZ0QsWUFBWXpCLFNBQVMsQ0FBQ1EsZ0JBQWdCO3dCQUFFO3dCQUN4Q2lCLFlBQVl6QixTQUFTLENBQUNTLG9CQUFvQixJQUFJOzs7Ozs7O2dCQUtwRGdCLEVBQUFBLDBCQUFBQSxZQUFZekIsU0FBUyxjQUFyQnlCLDhDQUFBQSx3QkFBdUJuQixjQUFjLE1BQUtSLGFBQ3pDMkIsRUFBQUEsMkJBQUFBLFlBQVl6QixTQUFTLGNBQXJCeUIsK0NBQUFBLHlCQUF1Qm5CLGNBQWMsTUFBSyxRQUMxQ21CLEVBQUFBLDJCQUFBQSxZQUFZekIsU0FBUyxjQUFyQnlCLCtDQUFBQSx5QkFBdUJuQixjQUFjLElBQUcsbUJBQ3RDLDhEQUFDckMsZ0VBQVVBO29CQUFDd0UsU0FBUTtvQkFBUUMsT0FBTTs7d0JBQy9CakUsRUFBRTt3QkFBMkQ7d0JBQUU7d0JBQy9EZ0QsWUFBWXpCLFNBQVMsQ0FBQ00sY0FBYzt3QkFBRTt3QkFDdENtQixZQUFZekIsU0FBUyxDQUFDTyxrQkFBa0IsSUFBSTs7Ozs7OztnQkFLbERrQixFQUFBQSwyQkFBQUEsWUFBWXpCLFNBQVMsY0FBckJ5QiwrQ0FBQUEseUJBQXVCeEIsbUJBQW1CLG1CQUN6Qyw4REFBQ2hDLGdFQUFVQTtvQkFBQ3dFLFNBQVE7b0JBQVFDLE9BQU07O3dCQUFpQjt3QkFDNUNqQixZQUFZekIsU0FBUyxDQUFDQyxtQkFBbUI7Ozs7Ozs7Ozs7Ozs7SUFLeEQ7SUFFQSxnQ0FBZ0M7SUFDaEMsTUFBTTBDLGFBQWEsQ0FBQzFCLHNCQUNsQiw4REFBQzdDLHFFQUFtQkE7WUFBQ3dFLFVBQVM7WUFBZTNCLE9BQU9BO1lBQU96QyxVQUFVQTs7Ozs7O0lBR3ZFLGdDQUFnQztJQUNoQyxNQUFNcUUsZUFBZSxDQUFDcEI7WUFFakJBLDJCQUNFQTs2QkFGTCw4REFBQ3hELGdFQUFVQTtZQUFDd0UsU0FBUTtzQkFDakJoQixFQUFBQSw0QkFBQUEsWUFBWXFCLFdBQVcsQ0FBQyxFQUFFLGNBQTFCckIsZ0RBQUFBLDBCQUE0QmxDLElBQUksS0FDOUJrQyxDQUFBQSxFQUFBQSx5QkFBQUEsWUFBWXpCLFNBQVMsY0FBckJ5Qiw2Q0FBQUEsdUJBQXVCdkIsYUFBYSxJQUNqQyxHQUFrRXVCLE9BQS9EaEQsRUFBRSwwREFBeUQsTUFBMkNnRCxPQUF2Q0EsWUFBWXpCLFNBQVMsQ0FBQ0UsYUFBYSxFQUFDLEtBQWlELE9BQTlDdUIsWUFBWXpCLFNBQVMsQ0FBQ0csaUJBQWlCLElBQUksTUFDcEoxQixFQUFFLDREQUEyRDs7Ozs7OztJQUl2RSxxQkFDRSw4REFBQ04sK0RBQW1CQTtRQUNsQkcsV0FBV0E7UUFDWEMsT0FBT0E7UUFDUEMsVUFBVUE7UUFDVnVFLE9BQU90RSxFQUFFO1FBQ1R1RSxZQUFZdkUsRUFBRTtRQUNkd0Usa0JBQWtCeEUsRUFBRTtRQUNwQnlFLGVBQWV2RTtRQUNmTSxhQUFhQTtRQUNiRSxhQUFhQTtRQUNiRyxZQUFZQTtRQUNaNkQsZ0JBQWU7UUFDZkMsZ0JBQWdCcEM7UUFDaEJxQyxlQUFlbkM7UUFDZm9DLGdCQUFnQm5DO1FBQ2hCb0MsVUFBVWpDO1FBQ1ZrQyxPQUFPN0Q7UUFDUDZCLGVBQWVBO1FBQ2ZtQixZQUFZQTtRQUNaRSxjQUFjQTs7Ozs7O0FBR3BCO0dBcE53QnhFOztRQUtSSCxxREFBWUE7UUFDTkosMkRBQWNBO1FBWWZDLDBEQUFhQTs7O0tBbEJWTSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxtLm1hbGlrXFxzb3VyY2VcXHJlcG9zXFxSb3NzZXRhXFxGcm9udGVuZFxcc3JjXFxzZWN0aW9uc1xcb3JkZXJzXFxwbHQxXFx0YWJzXFxwbHQxLXBhY2tpbmctbGlzdC10YWIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IHVzZVJlZiwgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgdXNlRm9ybUNvbnRleHQsIHVzZUZpZWxkQXJyYXkgfSBmcm9tICdyZWFjdC1ob29rLWZvcm0nO1xyXG5cclxuaW1wb3J0IFN0YWNrIGZyb20gJ0BtdWkvbWF0ZXJpYWwvU3RhY2snO1xyXG5pbXBvcnQgVHlwb2dyYXBoeSBmcm9tICdAbXVpL21hdGVyaWFsL1R5cG9ncmFwaHknO1xyXG5cclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRlIH0gZnJvbSAnc3JjL2xvY2FsZXMnO1xyXG5pbXBvcnQgUExUMURvY3VtZW50VGFiQmFzZSBmcm9tICcuL3BsdDEtZG9jdW1lbnQtdGFiLWJhc2UnO1xyXG5pbXBvcnQgUExUMVBhY2tpbmdMaXN0Rm9ybSwgeyBQTFQxUGFja2luZ0xpc3REYXRhIH0gZnJvbSAnLi4vZm9ybXMvcGx0MS1wYWNraW5nLWxpc3QtZm9ybSc7XHJcbmltcG9ydCB7IFBMVDFPcmRlciB9IGZyb20gJy4uL3R5cGVzL3BsdDEtZGV0YWlscy50eXBlcyc7XHJcblxyXG4vLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcblxyXG5pbnRlcmZhY2UgUExUMVBhY2tpbmdMaXN0c1RhYlByb3BzIHtcclxuICB0MU9yZGVySWQ6IHN0cmluZztcclxuICBvcmRlcj86IFBMVDFPcmRlcjtcclxuICByZWFkT25seT86IGJvb2xlYW47XHJcbn1cclxuXHJcbmludGVyZmFjZSBGb3JtVmFsdWVzIHtcclxuICBwYWNraW5nTGlzdHM6IFBMVDFQYWNraW5nTGlzdERhdGFbXTtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUExUMVBhY2tpbmdMaXN0c1RhYih7XHJcbiAgdDFPcmRlcklkLFxyXG4gIG9yZGVyLFxyXG4gIHJlYWRPbmx5ID0gZmFsc2UsXHJcbn06IFBMVDFQYWNraW5nTGlzdHNUYWJQcm9wcykge1xyXG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRlKCk7XHJcbiAgY29uc3QgeyBjb250cm9sIH0gPSB1c2VGb3JtQ29udGV4dDxGb3JtVmFsdWVzPigpO1xyXG5cclxuICAvLyBUcnkgdG8gcmVzdG9yZSB0aGUgZXhwYW5kZWQgc3RhdGUgZnJvbSBzZXNzaW9uU3RvcmFnZVxyXG4gIGNvbnN0IFtleHBhbmRlZFBhY2tpbmdMaXN0LCBzZXRFeHBhbmRlZFBhY2tpbmdMaXN0XSA9IHVzZVN0YXRlPG51bWJlciB8IG51bGw+KCgpID0+IHtcclxuICAgIGNvbnN0IHNhdmVkID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbShgcGx0MS1leHBhbmRlZC1wYWNraW5nLWxpc3QtJHt0MU9yZGVySWR9YCk7XHJcbiAgICByZXR1cm4gc2F2ZWQgPyBwYXJzZUludChzYXZlZCwgMTApIDogbnVsbDtcclxuICB9KTtcclxuXHJcbiAgY29uc3QgW2RlbGV0ZUluZGV4LCBzZXREZWxldGVJbmRleF0gPSB1c2VTdGF0ZTxudW1iZXIgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbb3BlbkNvbmZpcm0sIHNldE9wZW5Db25maXJtXSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKTtcclxuICBjb25zdCBkZWxldGVCdXR0b25SZWYgPSB1c2VSZWY8SFRNTEJ1dHRvbkVsZW1lbnQ+KG51bGwpO1xyXG5cclxuICBjb25zdCBmaWVsZEFycmF5ID0gdXNlRmllbGRBcnJheSh7XHJcbiAgICBjb250cm9sLFxyXG4gICAgbmFtZTogJ3BhY2tpbmdMaXN0cycsXHJcbiAgfSk7XHJcblxyXG4gIC8vIFNhdmUgZXhwYW5kZWQgc3RhdGUgdG8gc2Vzc2lvblN0b3JhZ2Ugd2hlbmV2ZXIgaXQgY2hhbmdlc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoZXhwYW5kZWRQYWNraW5nTGlzdCAhPT0gbnVsbCkge1xyXG4gICAgICBzZXNzaW9uU3RvcmFnZS5zZXRJdGVtKFxyXG4gICAgICAgIGBwbHQxLWV4cGFuZGVkLXBhY2tpbmctbGlzdC0ke3QxT3JkZXJJZH1gLFxyXG4gICAgICAgIGV4cGFuZGVkUGFja2luZ0xpc3QudG9TdHJpbmcoKVxyXG4gICAgICApO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2Vzc2lvblN0b3JhZ2UucmVtb3ZlSXRlbShgcGx0MS1leHBhbmRlZC1wYWNraW5nLWxpc3QtJHt0MU9yZGVySWR9YCk7XHJcbiAgICB9XHJcbiAgfSwgW2V4cGFuZGVkUGFja2luZ0xpc3QsIHQxT3JkZXJJZF0pO1xyXG5cclxuICBjb25zdCBoYW5kbGVBZGRQYWNraW5nTGlzdCA9ICgpOiB2b2lkID0+IHtcclxuICAgIGNvbnN0IG5ld1BhY2tpbmdMaXN0OiBQTFQxUGFja2luZ0xpc3REYXRhID0ge1xyXG4gICAgICBpZDogdW5kZWZpbmVkLFxyXG4gICAgICB0MU9yZGVySWQsXHJcbiAgICAgIHBhY2tpbmdMaXN0UG9zaXRpb25zOiBbXSxcclxuICAgICAgbGlzdFRvdGFsOiB7XHJcbiAgICAgICAgaWQ6IHVuZGVmaW5lZCxcclxuICAgICAgICBzaGlwbWVudE9yUGFja2luZ0lkOiAnJyxcclxuICAgICAgICB0b3RhbFF1YW50aXR5OiAwLFxyXG4gICAgICAgIHRvdGFsUGFja2FnZXNVbml0OiAnQ1ROUycsXHJcbiAgICAgICAgdG90YWxOdW1iZXJPZlBhY2thZ2VzOiAwLFxyXG4gICAgICAgIHRvdGFsTnVtYmVyT2ZQYWxsZXRzOiAwLFxyXG4gICAgICAgIHRvdGFsTmV0V2VpZ2h0OiAwLFxyXG4gICAgICAgIHRvdGFsTmV0V2VpZ2h0VW5pdDogJ0tHTScsXHJcbiAgICAgICAgdG90YWxHcm9zc1dlaWdodDogMCxcclxuICAgICAgICB0b3RhbEdyb3NzV2VpZ2h0VW5pdDogJ0tHTScsXHJcbiAgICAgICAgdG90YWxWb2x1bWU6IDAsXHJcbiAgICAgICAgdG90YWxWb2x1bWVNZWFzdXJlbWVudFVuaXQ6ICdDQk0nLFxyXG4gICAgICAgIHBhY2tpbmdMaXN0SWQ6IHVuZGVmaW5lZCxcclxuICAgICAgfSxcclxuICAgIH07XHJcblxyXG4gICAgZmllbGRBcnJheS5hcHBlbmQobmV3UGFja2luZ0xpc3QpO1xyXG4gICAgc2V0RXhwYW5kZWRQYWNraW5nTGlzdChmaWVsZEFycmF5LmZpZWxkcy5sZW5ndGgpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVRvZ2dsZUV4cGFuZCA9IChpbmRleDogbnVtYmVyKTogdm9pZCA9PiB7XHJcbiAgICBzZXRFeHBhbmRlZFBhY2tpbmdMaXN0KGV4cGFuZGVkUGFja2luZ0xpc3QgPT09IGluZGV4ID8gbnVsbCA6IGluZGV4KTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVPcGVuQ29uZmlybSA9IChpbmRleDogbnVtYmVyKTogdm9pZCA9PiB7XHJcbiAgICBzZXREZWxldGVJbmRleChpbmRleCk7XHJcbiAgICBzZXRPcGVuQ29uZmlybSh0cnVlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVDbG9zZUNvbmZpcm0gPSAoKTogdm9pZCA9PiB7XHJcbiAgICBzZXRPcGVuQ29uZmlybShmYWxzZSk7XHJcbiAgICBpZiAoZGVsZXRlSW5kZXggIT09IG51bGwpIHtcclxuICAgICAgZGVsZXRlQnV0dG9uUmVmLmN1cnJlbnQ/LmZvY3VzKCk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlRGVsZXRlUGFja2luZ0xpc3QgPSAoKTogdm9pZCA9PiB7XHJcbiAgICBpZiAoZGVsZXRlSW5kZXggIT09IG51bGwpIHtcclxuICAgICAgZmllbGRBcnJheS5yZW1vdmUoZGVsZXRlSW5kZXgpO1xyXG4gICAgICBpZiAoZXhwYW5kZWRQYWNraW5nTGlzdCA9PT0gZGVsZXRlSW5kZXgpIHtcclxuICAgICAgICBzZXRFeHBhbmRlZFBhY2tpbmdMaXN0KG51bGwpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICBoYW5kbGVDbG9zZUNvbmZpcm0oKTtcclxuICB9O1xyXG5cclxuICAvLyBSZW5kZXIgcHJldmlldyBvZiB0aGUgcGFja2luZyBsaXN0IGl0ZW0gd2hlbiBjb2xsYXBzZWRcclxuICBjb25zdCByZW5kZXJQcmV2aWV3ID0gKHBhY2tpbmdMaXN0OiBhbnksIF9pbmRleDogbnVtYmVyKSA9PiB7XHJcbiAgICAvLyBDYWxjdWxhdGUgdG90YWwgcGFja2VkIGl0ZW1zIGFjcm9zcyBhbGwgcG9zaXRpb25zXHJcbiAgICBjb25zdCB0b3RhbFBhY2tlZEl0ZW1zID0gcGFja2luZ0xpc3QucGFja2luZ0xpc3RQb3NpdGlvbnM/LnJlZHVjZShcclxuICAgICAgKHRvdGFsOiBudW1iZXIsIHBvc2l0aW9uOiBhbnkpID0+IHRvdGFsICsgKHBvc2l0aW9uLnBhY2tlZEl0ZW1zPy5sZW5ndGggfHwgMCksXHJcbiAgICAgIDBcclxuICAgICkgfHwgMDtcclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8U3RhY2tcclxuICAgICAgICBkaXJlY3Rpb249XCJyb3dcIlxyXG4gICAgICAgIHNwYWNpbmc9ezJ9XHJcbiAgICAgICAgc3g9e3tcclxuICAgICAgICAgIHB4OiAzLFxyXG4gICAgICAgICAgcGI6IDIsXHJcbiAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXHJcbiAgICAgICAgICBmbGV4V3JhcDogJ3dyYXAnLFxyXG4gICAgICAgICAgJyYgPiAqJzogeyBtcjogMywgbWI6IDEgfSxcclxuICAgICAgICB9fVxyXG4gICAgICA+XHJcbiAgICAgICAgey8qIFNob3cgbnVtYmVyIG9mIHBvc2l0aW9ucyAqL31cclxuICAgICAgICB7cGFja2luZ0xpc3QucGFja2luZ0xpc3RQb3NpdGlvbnMgJiYgcGFja2luZ0xpc3QucGFja2luZ0xpc3RQb3NpdGlvbnMubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBjb2xvcj1cInRleHQuc2Vjb25kYXJ5XCI+XHJcbiAgICAgICAgICAgIHt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnBhY2tpbmdMaXN0LnBvc2l0aW9uLnBvc2l0aW9uTnVtYmVyJyl9OiB7cGFja2luZ0xpc3QucGFja2luZ0xpc3RQb3NpdGlvbnMubGVuZ3RofVxyXG4gICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICl9XHJcblxyXG4gICAgICAgIHsvKiBTaG93IHRvdGFsIHBhY2tlZCBpdGVtcyAqL31cclxuICAgICAgICB7dG90YWxQYWNrZWRJdGVtcyA+IDAgJiYgKFxyXG4gICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgY29sb3I9XCJ0ZXh0LnNlY29uZGFyeVwiPlxyXG4gICAgICAgICAgICB7dCgnY29tbW9uLml0ZW1zQ291bnQnKX06IHt0b3RhbFBhY2tlZEl0ZW1zfVxyXG4gICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICl9XHJcblxyXG4gICAgICAgIHsvKiBTaG93IHRvdGFsIHBhY2thZ2VzIGlmIGF2YWlsYWJsZSAqL31cclxuICAgICAgICB7cGFja2luZ0xpc3QubGlzdFRvdGFsPy50b3RhbE51bWJlck9mUGFja2FnZXMgIT09IHVuZGVmaW5lZCAmJlxyXG4gICAgICAgICAgcGFja2luZ0xpc3QubGlzdFRvdGFsPy50b3RhbE51bWJlck9mUGFja2FnZXMgIT09IG51bGwgJiZcclxuICAgICAgICAgIHBhY2tpbmdMaXN0Lmxpc3RUb3RhbD8udG90YWxOdW1iZXJPZlBhY2thZ2VzID4gMCAmJiAoXHJcbiAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIGNvbG9yPVwidGV4dC5zZWNvbmRhcnlcIj5cclxuICAgICAgICAgICAgICB7dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5wYWNraW5nTGlzdC50b3RhbC50b3RhbE51bWJlck9mUGFja2FnZXMnKX06eycgJ31cclxuICAgICAgICAgICAgICB7cGFja2luZ0xpc3QubGlzdFRvdGFsLnRvdGFsTnVtYmVyT2ZQYWNrYWdlc317JyAnfVxyXG4gICAgICAgICAgICAgIHtwYWNraW5nTGlzdC5saXN0VG90YWwudG90YWxQYWNrYWdlc1VuaXQgfHwgJyd9XHJcbiAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICl9XHJcblxyXG4gICAgICAgIHsvKiBTaG93IHRvdGFsIHBhbGxldHMgaWYgYXZhaWxhYmxlICovfVxyXG4gICAgICAgIHtwYWNraW5nTGlzdC5saXN0VG90YWw/LnRvdGFsTnVtYmVyT2ZQYWxsZXRzICE9PSB1bmRlZmluZWQgJiZcclxuICAgICAgICAgIHBhY2tpbmdMaXN0Lmxpc3RUb3RhbD8udG90YWxOdW1iZXJPZlBhbGxldHMgIT09IG51bGwgJiZcclxuICAgICAgICAgIHBhY2tpbmdMaXN0Lmxpc3RUb3RhbD8udG90YWxOdW1iZXJPZlBhbGxldHMgPiAwICYmIChcclxuICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgY29sb3I9XCJ0ZXh0LnNlY29uZGFyeVwiPlxyXG4gICAgICAgICAgICAgIHt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnBhY2tpbmdMaXN0LnRvdGFsLnRvdGFsTnVtYmVyT2ZQYWxsZXRzJyl9OnsnICd9XHJcbiAgICAgICAgICAgICAge3BhY2tpbmdMaXN0Lmxpc3RUb3RhbC50b3RhbE51bWJlck9mUGFsbGV0c31cclxuICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgey8qIFNob3cgdG90YWwgZ3Jvc3Mgd2VpZ2h0IGlmIGF2YWlsYWJsZSAqL31cclxuICAgICAgICB7cGFja2luZ0xpc3QubGlzdFRvdGFsPy50b3RhbEdyb3NzV2VpZ2h0ICE9PSB1bmRlZmluZWQgJiZcclxuICAgICAgICAgIHBhY2tpbmdMaXN0Lmxpc3RUb3RhbD8udG90YWxHcm9zc1dlaWdodCAhPT0gbnVsbCAmJlxyXG4gICAgICAgICAgcGFja2luZ0xpc3QubGlzdFRvdGFsPy50b3RhbEdyb3NzV2VpZ2h0ID4gMCAmJiAoXHJcbiAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIGNvbG9yPVwidGV4dC5zZWNvbmRhcnlcIj5cclxuICAgICAgICAgICAgICB7dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5wYWNraW5nTGlzdC50b3RhbC50b3RhbEdyb3NzV2VpZ2h0Jyl9OnsnICd9XHJcbiAgICAgICAgICAgICAge3BhY2tpbmdMaXN0Lmxpc3RUb3RhbC50b3RhbEdyb3NzV2VpZ2h0fXsnICd9XHJcbiAgICAgICAgICAgICAge3BhY2tpbmdMaXN0Lmxpc3RUb3RhbC50b3RhbEdyb3NzV2VpZ2h0VW5pdCB8fCAnS0dNJ31cclxuICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgey8qIFNob3cgdG90YWwgbmV0IHdlaWdodCBpZiBhdmFpbGFibGUgKi99XHJcbiAgICAgICAge3BhY2tpbmdMaXN0Lmxpc3RUb3RhbD8udG90YWxOZXRXZWlnaHQgIT09IHVuZGVmaW5lZCAmJlxyXG4gICAgICAgICAgcGFja2luZ0xpc3QubGlzdFRvdGFsPy50b3RhbE5ldFdlaWdodCAhPT0gbnVsbCAmJlxyXG4gICAgICAgICAgcGFja2luZ0xpc3QubGlzdFRvdGFsPy50b3RhbE5ldFdlaWdodCA+IDAgJiYgKFxyXG4gICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBjb2xvcj1cInRleHQuc2Vjb25kYXJ5XCI+XHJcbiAgICAgICAgICAgICAge3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QudG90YWwudG90YWxOZXRXZWlnaHQnKX06eycgJ31cclxuICAgICAgICAgICAgICB7cGFja2luZ0xpc3QubGlzdFRvdGFsLnRvdGFsTmV0V2VpZ2h0fXsnICd9XHJcbiAgICAgICAgICAgICAge3BhY2tpbmdMaXN0Lmxpc3RUb3RhbC50b3RhbE5ldFdlaWdodFVuaXQgfHwgJ0tHTSd9XHJcbiAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICl9XHJcblxyXG4gICAgICAgIHsvKiBTaG93IHNoaXBtZW50L3BhY2tpbmcgSUQgaWYgYXZhaWxhYmxlICovfVxyXG4gICAgICAgIHtwYWNraW5nTGlzdC5saXN0VG90YWw/LnNoaXBtZW50T3JQYWNraW5nSWQgJiYgKFxyXG4gICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgY29sb3I9XCJ0ZXh0LnNlY29uZGFyeVwiPlxyXG4gICAgICAgICAgICBJRDoge3BhY2tpbmdMaXN0Lmxpc3RUb3RhbC5zaGlwbWVudE9yUGFja2luZ0lkfVxyXG4gICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICl9XHJcbiAgICAgIDwvU3RhY2s+XHJcbiAgICApO1xyXG4gIH07XHJcblxyXG4gIC8vIFJlbmRlciB0aGUgZm9ybSB3aGVuIGV4cGFuZGVkXHJcbiAgY29uc3QgcmVuZGVyRm9ybSA9IChpbmRleDogbnVtYmVyKSA9PiAoXHJcbiAgICA8UExUMVBhY2tpbmdMaXN0Rm9ybSBmb3JtUGF0aD1cInBhY2tpbmdMaXN0c1wiIGluZGV4PXtpbmRleH0gcmVhZE9ubHk9e3JlYWRPbmx5fSAvPlxyXG4gICk7XHJcblxyXG4gIC8vIFJlbmRlciB0aGUgdGl0bGUgb2YgZWFjaCBpdGVtXHJcbiAgY29uc3QgZ2V0SXRlbVRpdGxlID0gKHBhY2tpbmdMaXN0OiBhbnkpID0+IChcclxuICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJzdWJ0aXRsZTFcIj5cclxuICAgICAge3BhY2tpbmdMaXN0Lmxpc3RTdW1tYXJ5WzBdPy5uYW1lIHx8XHJcbiAgICAgICAgKHBhY2tpbmdMaXN0Lmxpc3RUb3RhbD8udG90YWxRdWFudGl0eVxyXG4gICAgICAgICAgPyBgJHt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnBhY2tpbmdMaXN0LnByZXZpZXcudG90YWxJdGVtcycpfTogJHtwYWNraW5nTGlzdC5saXN0VG90YWwudG90YWxRdWFudGl0eX0gJHtwYWNraW5nTGlzdC5saXN0VG90YWwudG90YWxQYWNrYWdlc1VuaXQgfHwgJyd9YFxyXG4gICAgICAgICAgOiB0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnBhY2tpbmdMaXN0LnByZXZpZXcubmV3UGFja2luZ0xpc3QnKSl9XHJcbiAgICA8L1R5cG9ncmFwaHk+XHJcbiAgKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxQTFQxRG9jdW1lbnRUYWJCYXNlXHJcbiAgICAgIHQxT3JkZXJJZD17dDFPcmRlcklkfVxyXG4gICAgICBvcmRlcj17b3JkZXJ9XHJcbiAgICAgIHJlYWRPbmx5PXtyZWFkT25seX1cclxuICAgICAgdGl0bGU9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMucGFja2luZ0xpc3QuaGVhZGluZycpfVxyXG4gICAgICBlbXB0eVRpdGxlPXt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnBhY2tpbmdMaXN0Lm5vRGF0YScpfVxyXG4gICAgICBlbXB0eURlc2NyaXB0aW9uPXt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLnBhY2tpbmdMaXN0LmFkZFlvdXJGaXJzdCcpfVxyXG4gICAgICBleHBhbmRlZEluZGV4PXtleHBhbmRlZFBhY2tpbmdMaXN0fVxyXG4gICAgICBkZWxldGVJbmRleD17ZGVsZXRlSW5kZXh9XHJcbiAgICAgIG9wZW5Db25maXJtPXtvcGVuQ29uZmlybX1cclxuICAgICAgZmllbGRBcnJheT17ZmllbGRBcnJheX1cclxuICAgICAgZmllbGRBcnJheU5hbWU9XCJwYWNraW5nTGlzdHNcIlxyXG4gICAgICBvblRvZ2dsZUV4cGFuZD17aGFuZGxlVG9nZ2xlRXhwYW5kfVxyXG4gICAgICBvbk9wZW5Db25maXJtPXtoYW5kbGVPcGVuQ29uZmlybX1cclxuICAgICAgb25DbG9zZUNvbmZpcm09e2hhbmRsZUNsb3NlQ29uZmlybX1cclxuICAgICAgb25EZWxldGU9e2hhbmRsZURlbGV0ZVBhY2tpbmdMaXN0fVxyXG4gICAgICBvbkFkZD17aGFuZGxlQWRkUGFja2luZ0xpc3R9XHJcbiAgICAgIHJlbmRlclByZXZpZXc9e3JlbmRlclByZXZpZXd9XHJcbiAgICAgIHJlbmRlckZvcm09e3JlbmRlckZvcm19XHJcbiAgICAgIGdldEl0ZW1UaXRsZT17Z2V0SXRlbVRpdGxlfVxyXG4gICAgLz5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VSZWYiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUZvcm1Db250ZXh0IiwidXNlRmllbGRBcnJheSIsIlN0YWNrIiwiVHlwb2dyYXBoeSIsInVzZVRyYW5zbGF0ZSIsIlBMVDFEb2N1bWVudFRhYkJhc2UiLCJQTFQxUGFja2luZ0xpc3RGb3JtIiwiUExUMVBhY2tpbmdMaXN0c1RhYiIsInQxT3JkZXJJZCIsIm9yZGVyIiwicmVhZE9ubHkiLCJ0IiwiY29udHJvbCIsImV4cGFuZGVkUGFja2luZ0xpc3QiLCJzZXRFeHBhbmRlZFBhY2tpbmdMaXN0Iiwic2F2ZWQiLCJzZXNzaW9uU3RvcmFnZSIsImdldEl0ZW0iLCJwYXJzZUludCIsImRlbGV0ZUluZGV4Iiwic2V0RGVsZXRlSW5kZXgiLCJvcGVuQ29uZmlybSIsInNldE9wZW5Db25maXJtIiwiZGVsZXRlQnV0dG9uUmVmIiwiZmllbGRBcnJheSIsIm5hbWUiLCJzZXRJdGVtIiwidG9TdHJpbmciLCJyZW1vdmVJdGVtIiwiaGFuZGxlQWRkUGFja2luZ0xpc3QiLCJuZXdQYWNraW5nTGlzdCIsImlkIiwidW5kZWZpbmVkIiwicGFja2luZ0xpc3RQb3NpdGlvbnMiLCJsaXN0VG90YWwiLCJzaGlwbWVudE9yUGFja2luZ0lkIiwidG90YWxRdWFudGl0eSIsInRvdGFsUGFja2FnZXNVbml0IiwidG90YWxOdW1iZXJPZlBhY2thZ2VzIiwidG90YWxOdW1iZXJPZlBhbGxldHMiLCJ0b3RhbE5ldFdlaWdodCIsInRvdGFsTmV0V2VpZ2h0VW5pdCIsInRvdGFsR3Jvc3NXZWlnaHQiLCJ0b3RhbEdyb3NzV2VpZ2h0VW5pdCIsInRvdGFsVm9sdW1lIiwidG90YWxWb2x1bWVNZWFzdXJlbWVudFVuaXQiLCJwYWNraW5nTGlzdElkIiwiYXBwZW5kIiwiZmllbGRzIiwibGVuZ3RoIiwiaGFuZGxlVG9nZ2xlRXhwYW5kIiwiaW5kZXgiLCJoYW5kbGVPcGVuQ29uZmlybSIsImhhbmRsZUNsb3NlQ29uZmlybSIsImN1cnJlbnQiLCJmb2N1cyIsImhhbmRsZURlbGV0ZVBhY2tpbmdMaXN0IiwicmVtb3ZlIiwicmVuZGVyUHJldmlldyIsInBhY2tpbmdMaXN0IiwiX2luZGV4IiwidG90YWxQYWNrZWRJdGVtcyIsInJlZHVjZSIsInRvdGFsIiwicG9zaXRpb24iLCJwYWNrZWRJdGVtcyIsImRpcmVjdGlvbiIsInNwYWNpbmciLCJzeCIsInB4IiwicGIiLCJkaXNwbGF5IiwiZmxleFdyYXAiLCJtciIsIm1iIiwidmFyaWFudCIsImNvbG9yIiwicmVuZGVyRm9ybSIsImZvcm1QYXRoIiwiZ2V0SXRlbVRpdGxlIiwibGlzdFN1bW1hcnkiLCJ0aXRsZSIsImVtcHR5VGl0bGUiLCJlbXB0eURlc2NyaXB0aW9uIiwiZXhwYW5kZWRJbmRleCIsImZpZWxkQXJyYXlOYW1lIiwib25Ub2dnbGVFeHBhbmQiLCJvbk9wZW5Db25maXJtIiwib25DbG9zZUNvbmZpcm0iLCJvbkRlbGV0ZSIsIm9uQWRkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-packing-list-tab.tsx\n"));

/***/ })

});