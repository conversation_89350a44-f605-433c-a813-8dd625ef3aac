"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1sea/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-merchandise-positions-form.tsx":
/*!****************************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-merchandise-positions-form.tsx ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1MerchandisePositionsForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material/Box */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/Card */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material/Stack */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material_Button__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/material/Button */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/material/Divider */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/CardHeader */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/Typography */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/IconButton */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _mui_material_Table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material/Table */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _mui_material_TableBody__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mui/material/TableBody */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material/TableCell */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _mui_material_TableContainer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material/TableContainer */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _mui_material_TableHead__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/TableHead */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/TableRow */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _mui_material_Paper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/Paper */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _mui_material_Dialog__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/material/Dialog */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _mui_material_DialogTitle__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/material/DialogTitle */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _mui_material_DialogContent__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/material/DialogContent */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _mui_material_DialogActions__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/material/DialogActions */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/Tooltip */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/material/Grid2 */ \"(app-pages-browser)/./node_modules/@mui/material/Grid2/Grid2.js\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/custom-dialog */ \"(app-pages-browser)/./src/components/custom-dialog/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst DEFAULT_POSITION = {\n    id: null,\n    invoicePositionNumber: 0,\n    goodsDescription: '',\n    translatedGoodsDescription: '',\n    packageMarks: '',\n    numberOfPackages: 0,\n    packageType: '',\n    packageTypeDescription: '',\n    commodityCode: '',\n    fullTariffCode: '',\n    grossMass: 0,\n    grossMassUnit: 'kg',\n    netMass: 0,\n    netMassUnit: 'kg',\n    positionValue: 0,\n    positionValueCurrency: 'USD',\n    containerNumbers: [],\n    previousDocuments: [],\n    merchandisePositionsId: ''\n};\nfunction PLT1MerchandisePositionsForm(param) {\n    let { t1OrderId, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_4__.useTranslate)();\n    const { control, watch, getValues } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFormContext)();\n    const fieldArray = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFieldArray)({\n        control,\n        name: 'merchandisePositions.positions'\n    });\n    const merchandisePositions = watch('merchandisePositions');\n    // State for position dialog\n    const [openPositionDialog, setOpenPositionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPositionIndex, setCurrentPositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletePositionIndex, setDeletePositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Handle opening the position dialog for adding a new position\n    const handleAddPosition = ()=>{\n        setCurrentPositionIndex(null);\n        // Add a new position with default values\n        const newPosition = {\n            ...DEFAULT_POSITION,\n            merchandisePositionsId: (merchandisePositions === null || merchandisePositions === void 0 ? void 0 : merchandisePositions.id) || ''\n        };\n        fieldArray.append(newPosition);\n        setCurrentPositionIndex(fieldArray.fields.length); // Set to the new index\n        setOpenPositionDialog(true);\n    };\n    // Handle opening the position dialog for editing an existing position\n    const handleEditPosition = (index)=>{\n        setCurrentPositionIndex(index);\n        setOpenPositionDialog(true);\n    };\n    // Handle closing the position dialog\n    const handleClosePositionDialog = ()=>{\n        setOpenPositionDialog(false);\n        // If we were adding a new position and user cancels, remove the empty position\n        if (currentPositionIndex !== null && currentPositionIndex === fieldArray.fields.length - 1) {\n            const position = getValues(\"merchandisePositions.positions.\".concat(currentPositionIndex));\n            // Check if it's an empty position (all fields are default values)\n            const isEmpty = !position.goodsDescription && !position.translatedGoodsDescription && !position.packageMarks && !position.commodityCode && !position.fullTariffCode && position.numberOfPackages === 0 && position.grossMass === 0 && position.netMass === 0;\n            if (isEmpty) {\n                fieldArray.remove(currentPositionIndex);\n            }\n        }\n        setCurrentPositionIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeletePositionIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deletePositionIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting a position\n    const handleDeletePosition = ()=>{\n        if (deletePositionIndex !== null) {\n            fieldArray.remove(deletePositionIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving a position (just close the dialog since form is already updated)\n    const handleSavePosition = ()=>{\n        handleClosePositionDialog();\n    };\n    // Render the position form in the dialog - using React Hook Form fields\n    const renderPositionForm = ()=>{\n        if (currentPositionIndex === null) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 3,\n                        pt: 2,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(2, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".goodsDescription\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.goodsDescription'),\n                            multiline: true,\n                            rows: 2,\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".translatedGoodsDescription\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.translatedGoodsDescription'),\n                            multiline: true,\n                            rows: 2,\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 2.5,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(3, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".numberOfPackages\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.numberOfPackages'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".packageType\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.packageType'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".packageTypeDescription\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.packageTypeDescription'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".packageMarks\"),\n                    label: t('plt1.details.documents.merchandisePositions.form.packageMarks'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 3,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(2, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".commodityCode\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.commodityCode'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".fullTariffCode\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.fullTariffCode'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 3,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(4, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".grossMass\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.grossMass'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".grossMassUnit\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.grossMassUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".netMass\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.netMass'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".netMassUnit\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.netMassUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the positions table\n    const renderPositionsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableContainer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            component: _mui_material_Paper__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Table__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"merchandise positions table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableHead__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.goodsDescription')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.numberOfPackages')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.packageType')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.grossMass')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.netMass')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableBody__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        children: fieldArray.fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                colSpan: 6,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.merchandisePositions.noPositions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, this) : fieldArray.fields.map((field, index)=>{\n                            const position = getValues(\"merchandisePositions.positions.\".concat(index)) || {};\n                            var _position_numberOfPackages, _position_grossMass, _position_netMass;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                variant: \"body2\",\n                                                sx: {\n                                                    maxWidth: 200\n                                                },\n                                                children: position.goodsDescription || '-'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 21\n                                            }, this),\n                                            position.translatedGoodsDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                variant: \"caption\",\n                                                color: \"text.secondary\",\n                                                sx: {\n                                                    display: 'block',\n                                                    mt: 0.5\n                                                },\n                                                children: position.translatedGoodsDescription\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: (_position_numberOfPackages = position.numberOfPackages) !== null && _position_numberOfPackages !== void 0 ? _position_numberOfPackages : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: position.packageTypeDescription || position.packageType || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_position_grossMass = position.grossMass) !== null && _position_grossMass !== void 0 ? _position_grossMass : '-',\n                                            \" \",\n                                            position.grossMassUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_position_netMass = position.netMass) !== null && _position_netMass !== void 0 ? _position_netMass : '-',\n                                            \" \",\n                                            position.netMassUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditPosition(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deletePositionIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n            lineNumber: 295,\n            columnNumber: 5\n        }, this);\n    // Render the totals summary\n    const renderTotalsSummary = ()=>{\n        const totals = merchandisePositions === null || merchandisePositions === void 0 ? void 0 : merchandisePositions.totals;\n        if (!totals) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            sx: {\n                boxShadow: 'none',\n                mb: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    title: t('plt1.details.documents.merchandisePositions.form.totalsTitle')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    sx: {\n                        borderStyle: 'dashed'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 390,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalNumberOfPositions')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: totals.totalNumberOfPositions || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalNumberOfPackages')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: totals.totalNumberOfPackages || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalGrossMass')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: [\n                                                totals.totalGrossMass || 0,\n                                                \" \",\n                                                totals.totalGrossMassUnit || ''\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalNetMass')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: [\n                                                totals.totalNetMass || 0,\n                                                \" \",\n                                                totals.totalNetMassUnit || ''\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n            lineNumber: 386,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        spacing: 3,\n        children: [\n            renderTotalsSummary(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        title: t('plt1.details.documents.merchandisePositions.form.positionsTitle'),\n                        action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            variant: \"contained\",\n                            size: \"small\",\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                icon: \"eva:plus-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: handleAddPosition,\n                            sx: {\n                                mb: 2\n                            },\n                            disabled: readOnly,\n                            children: t('plt1.details.documents.merchandisePositions.addPosition')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: [\n                            renderPositionsTable(),\n                            fieldArray.fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                sx: {\n                                    mt: 2,\n                                    display: 'flex',\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    variant: \"soft\",\n                                    color: \"primary\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                        icon: \"eva:plus-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    onClick: handleAddPosition,\n                                    disabled: readOnly,\n                                    children: t('plt1.details.documents.merchandisePositions.addPosition')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 445,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Dialog__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                open: openPositionDialog,\n                onClose: handleClosePositionDialog,\n                maxWidth: \"md\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_DialogTitle__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        children: currentPositionIndex === null ? t('plt1.details.documents.merchandisePositions.addPosition') : t('plt1.details.documents.merchandisePositions.editPosition')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_DialogContent__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        children: renderPositionForm()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_DialogActions__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                onClick: handleClosePositionDialog,\n                                children: t('common.cancel')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                variant: \"contained\",\n                                onClick: handleSavePosition,\n                                children: t('common.save')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 496,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 482,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__.ConfirmDialog, {\n                open: openDeleteConfirm,\n                onClose: handleCloseDeleteConfirm,\n                title: t('plt1.details.documents.merchandisePositions.confirmDeleteDialog.title'),\n                content: t('plt1.details.documents.merchandisePositions.confirmDeleteDialog.content'),\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                    variant: \"contained\",\n                    color: \"error\",\n                    onClick: handleDeletePosition,\n                    children: t('common.delete')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 513,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 507,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n        lineNumber: 440,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1MerchandisePositionsForm, \"rzlgvldrAdFsKfES/DPx4X+02i8=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_4__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFormContext,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFieldArray\n    ];\n});\n_c = PLT1MerchandisePositionsForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1MerchandisePositionsForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-merchandise-positions-form.tsx\n"));

/***/ })

});