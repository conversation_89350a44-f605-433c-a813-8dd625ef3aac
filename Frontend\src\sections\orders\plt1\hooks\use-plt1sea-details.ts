// hooks/use-plt1sea-details.ts
import { useCallback } from 'react';
import useSWR from 'swr';
import { endpoints, fetcher } from 'src/lib/axios';
import { PLT1SeaOrderDetailsDto } from '../types/plt1-details.types';

/**
 * Custom hook for fetching and managing PLT1 Sea order details
 * @param orderId The ID of the PLT1 Sea order to fetch
 */
export const usePLT1SeaOrderDetails = (orderId: string) => {

  // Fetch order details using SWR
  const {
    data: response,
    error,
    isLoading,
    mutate
  } = useSWR<{ isSuccessful: boolean; errorMessages?: string[]; data?: PLT1SeaOrderDetailsDto }>(
    orderId ? `${endpoints.plt1.seaDetails}/${orderId}` : undefined,
    fetcher
  );

  // Extract the order from the response
  const order: PLT1SeaOrderDetailsDto | undefined = response?.data;

  // Reload the order data
  const reload = useCallback(async () => {
    return await mutate();
  }, [mutate]);

  return {
    order,
    isLoading,
    error,
    reload
  };
};