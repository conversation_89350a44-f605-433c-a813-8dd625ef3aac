import { primary, secondary } from '../core/palette';

import type { PaletteColorNoChannels } from '../core';

// ----------------------------------------------------------------------

export const primaryColorPresets: Record<string, PaletteColorNoChannels> = {
  default: {
    lighter: primary.lighter,
    light: primary.light,
    main: primary.main,
    dark: primary.dark,
    darker: primary.darker,
    contrastText: primary.contrastText,
  }
};

export const secondaryColorPresets: Record<string, PaletteColorNoChannels> = {
  default: {
    lighter: secondary.lighter,
    light: secondary.light,
    main: secondary.main,
    dark: secondary.dark,
    darker: secondary.darker,
    contrastText: secondary.contrastText,
  }
};
