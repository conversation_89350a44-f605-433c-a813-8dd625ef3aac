function i(n,r=""){return n==null||typeof n=="number"&&Number.isNaN(n)?r:n.toString()}function o(n){let t=i(n).replace(/[^0-9.]/g,""),[e,...u]=t.split(".");return u.length>0?`${e}.${u.join("")}`:e}function s(n,r=""){if(n==null||typeof n=="number"&&Number.isNaN(n))return r;let t=parseFloat(n.toString());return Number.isNaN(t)?r:t}var a={onChange:o,onBlur:s,value:i};export{a as transformNumber,i as transformValue,s as transformValueOnBlur,o as transformValueOnChange};
