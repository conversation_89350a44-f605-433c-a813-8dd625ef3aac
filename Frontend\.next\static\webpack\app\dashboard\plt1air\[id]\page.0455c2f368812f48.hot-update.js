"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/components/hook-form/rhf-text-field.tsx":
/*!*****************************************************!*\
  !*** ./src/components/hook-form/rhf-text-field.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RHFTextField: () => (/* binding */ RHFTextField)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var minimal_shared_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! minimal-shared/utils */ \"(app-pages-browser)/./node_modules/minimal-shared/dist/utils/index.js\");\n/* harmony import */ var _mui_material_TextField__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/TextField */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n// ----------------------------------------------------------------------\n/**\n * Custom transform function for number fields on blur\n * Uses the package's transformValueOnBlur but ensures empty values become 0\n * to prevent backend validation errors\n */ function transformNumberOnBlur(value) {\n    const result = (0,minimal_shared_utils__WEBPACK_IMPORTED_MODULE_1__.transformValueOnBlur)(value, 0); // Use 0 as fallback instead of empty string\n    return typeof result === 'number' ? result : 0;\n}\nfunction RHFTextField(param) {\n    let { name, helperText, slotProps, type = 'text', ...other } = param;\n    _s();\n    const { control } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_2__.useFormContext)();\n    const isNumberType = type === 'number';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_2__.Controller, {\n        name: name,\n        control: control,\n        render: (param)=>{\n            let { field, fieldState: { error } } = param;\n            var _error_message;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TextField__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                ...field,\n                fullWidth: true,\n                value: isNumberType ? (0,minimal_shared_utils__WEBPACK_IMPORTED_MODULE_1__.transformValue)(field.value) : field.value,\n                onChange: (event)=>{\n                    const transformedValue = isNumberType ? (0,minimal_shared_utils__WEBPACK_IMPORTED_MODULE_1__.transformValueOnChange)(event.target.value) : event.target.value;\n                    field.onChange(transformedValue);\n                },\n                onBlur: (event)=>{\n                    const transformedValue = isNumberType ? (0,minimal_shared_utils__WEBPACK_IMPORTED_MODULE_1__.transformValueOnBlur)(event.target.value) : event.target.value;\n                    field.onChange(transformedValue);\n                },\n                type: isNumberType ? 'text' : type,\n                error: !!error,\n                helperText: (_error_message = error === null || error === void 0 ? void 0 : error.message) !== null && _error_message !== void 0 ? _error_message : helperText,\n                slotProps: {\n                    ...slotProps,\n                    htmlInput: {\n                        autoComplete: 'off',\n                        ...slotProps === null || slotProps === void 0 ? void 0 : slotProps.htmlInput,\n                        ...isNumberType && {\n                            inputMode: 'decimal',\n                            pattern: '[0-9]*\\\\.?[0-9]*'\n                        }\n                    }\n                },\n                ...other\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\components\\\\hook-form\\\\rhf-text-field.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, void 0);\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\components\\\\hook-form\\\\rhf-text-field.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(RHFTextField, \"zyAxkz+Wq3InUdCKNlVVi99oElQ=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_2__.useFormContext\n    ];\n});\n_c = RHFTextField;\nvar _c;\n$RefreshReg$(_c, \"RHFTextField\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/hook-form/rhf-text-field.tsx\n"));

/***/ })

});