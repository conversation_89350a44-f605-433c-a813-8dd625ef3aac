"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx":
/*!*******************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1PackingListForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/party-address/hooks/usePartyAddressForm */ \"(app-pages-browser)/./src/components/party-address/hooks/usePartyAddressForm.tsx\");\n/* harmony import */ var src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/components/party-address/party-address-dialog */ \"(app-pages-browser)/./src/components/party-address/party-address-dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst DEFAULT_ITEM = {\n    id: '',\n    name: '',\n    modelNumber: '',\n    purchaseOrderNumber: '',\n    commercialInvoiceNumber: '',\n    packageNetWeight: 0,\n    packageNetWeightUnit: 'kg',\n    packageGrossWeight: 0,\n    packageGrossWeightUnit: 'kg',\n    quantity: 0,\n    volume: 0,\n    packingListId: ''\n};\nconst DEFAULT_TOTAL = {\n    id: '',\n    totalQuantity: 0,\n    totalPackagesUnit: '',\n    totalNumberOfPackages: 0,\n    totalNumberOfPallets: 0,\n    totalNetWeight: 0,\n    totalNetWeightUnit: 'kg',\n    totalGrossWeight: 0,\n    totalGrossWeightUnit: 'kg',\n    totalVolume: 0,\n    totalVolumeMeasurementUnit: '',\n    packingListId: ''\n};\nfunction PLT1PackingListForm(param) {\n    let { formPath, index, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    const { control, getValues, watch, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useFormContext)();\n    const fieldPrefix = index !== undefined ? \"\".concat(formPath, \".\").concat(index) : formPath;\n    const itemsFieldName = \"\".concat(fieldPrefix, \".listSummary\");\n    const totalFieldName = \"\".concat(fieldPrefix, \".listTotal\");\n    // Use the party address form hook\n    const { openPartyDialog, currentPartyType, handleClosePartyDialog, handleUpdateParty } = (0,src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_5__.usePartyAddressForm)({\n        fieldPrefix\n    });\n    // State for item dialog\n    const [openItemDialog, setOpenItemDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteItemIndex, setDeleteItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize listTotal if it doesn't exist\n    const listTotal = watch(totalFieldName);\n    if (!listTotal) {\n        setValue(totalFieldName, DEFAULT_TOTAL);\n    }\n    // UseFieldArray hook to manage the items array\n    const { fields, append, remove, update } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useFieldArray)({\n        control,\n        name: itemsFieldName\n    });\n    // Handle opening the item dialog for adding a new item\n    const handleAddItem = ()=>{\n        setCurrentItemIndex(null);\n        // Add a new item with default values\n        const newItem = {\n            ...DEFAULT_ITEM\n        };\n        append(newItem);\n        setCurrentItemIndex(fields.length); // Set to the new index\n        setOpenItemDialog(true);\n    };\n    // Handle opening the item dialog for editing an existing item\n    const handleEditItem = (index)=>{\n        setCurrentItemIndex(index);\n        setOpenItemDialog(true);\n    };\n    // Handle closing the item dialog\n    const handleCloseItemDialog = ()=>{\n        setOpenItemDialog(false);\n        // If we were adding a new item and user cancels, remove the empty item\n        if (currentItemIndex !== null && currentItemIndex === fields.length - 1) {\n            const item = getValues(\"\".concat(itemsFieldName, \".\").concat(currentItemIndex));\n            // Check if it's an empty item (all fields are default values)\n            const isEmpty = !item.name && !item.modelNumber && !item.purchaseOrderNumber && !item.commercialInvoiceNumber && item.quantity === 0 && item.packageNetWeight === 0 && item.packageGrossWeight === 0 && item.volume === 0;\n            if (isEmpty) {\n                remove(currentItemIndex);\n            }\n        }\n        setCurrentItemIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeleteItemIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deleteItemIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting an item\n    const handleDeleteItem = ()=>{\n        if (deleteItemIndex !== null) {\n            remove(deleteItemIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving an item (just close the dialog since form is already updated)\n    const handleSaveItem = ()=>{\n        handleCloseItemDialog();\n    };\n    // Render the item form in the dialog - using React Hook Form fields\n    const renderItemForm = ()=>{\n        if (currentItemIndex === null) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".name\"),\n                    label: t('plt1.details.documents.packingList.item.name'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".modelNumber\"),\n                    label: t('plt1.details.documents.packingList.item.modelNumber'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".purchaseOrderNumber\"),\n                            label: t('plt1.details.documents.packingList.item.purchaseOrderNumber'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".commercialInvoiceNumber\"),\n                            label: t('plt1.details.documents.packingList.item.commercialInvoiceNumber'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageNetWeight\"),\n                            label: t('plt1.details.documents.packingList.item.packageNetWeight'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageNetWeightUnit\"),\n                            label: t('plt1.details.documents.packingList.item.packageNetWeightUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageGrossWeight\"),\n                            label: t('plt1.details.documents.packingList.item.packageGrossWeight'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageGrossWeightUnit\"),\n                            label: t('plt1.details.documents.packingList.item.packageGrossWeightUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".quantity\"),\n                            label: t('plt1.details.documents.packingList.item.quantity'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".volume\"),\n                            label: t('plt1.details.documents.packingList.item.volume'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the item table\n    const renderItemsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            component: _barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"packing list items table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.name')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.modelNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.quantity')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.packageNetWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.packageGrossWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        children: fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                colSpan: 6,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.packingList.item.noItems')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 13\n                        }, this) : fields.map((field, index)=>{\n                            const item = getValues(\"\".concat(itemsFieldName, \".\").concat(index)) || {};\n                            var _item_quantity, _item_packageNetWeight, _item_packageGrossWeight;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: item.name || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: item.modelNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: (_item_quantity = item.quantity) !== null && _item_quantity !== void 0 ? _item_quantity : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_item_packageNetWeight = item.packageNetWeight) !== null && _item_packageNetWeight !== void 0 ? _item_packageNetWeight : '-',\n                                            \" \",\n                                            item.packageNetWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_item_packageGrossWeight = item.packageGrossWeight) !== null && _item_packageGrossWeight !== void 0 ? _item_packageGrossWeight : '-',\n                                            \" \",\n                                            item.packageGrossWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditItem(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deleteItemIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 281,\n            columnNumber: 5\n        }, this);\n    const renderPartyDialog = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            open: openPartyDialog,\n            onClose: handleClosePartyDialog,\n            onSave: handleUpdateParty,\n            formPath: fieldPrefix,\n            currentPartyType: currentPartyType,\n            readOnly: readOnly,\n            titlePrefix: \"plt1.details.documents.packingList.partyAddress\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 355,\n            columnNumber: 5\n        }, this);\n    // Handle change for totals fields\n    const handleTotalChange = (field, value)=>{\n        setValue(\"\".concat(totalFieldName, \".\").concat(field), value);\n    };\n    // Render the totals section\n    const renderTotalsSection = ()=>{\n        const total = watch(totalFieldName) || DEFAULT_TOTAL;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            sx: {\n                boxShadow: 'none',\n                mt: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    title: t('plt1.details.documents.packingList.form.totalsTitle')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    sx: {\n                        borderStyle: 'dashed'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        spacing: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalQuantity\"),\n                                        label: t('plt1.details.documents.packingList.total.totalQuantity'),\n                                        type: \"number\",\n                                        value: total.totalQuantity,\n                                        onChange: (e)=>handleTotalChange('totalQuantity', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPallets\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPallets'),\n                                        type: \"number\",\n                                        value: total.totalNumberOfPallets,\n                                        onChange: (e)=>handleTotalChange('totalNumberOfPallets', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPackages\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPackages'),\n                                        type: \"number\",\n                                        value: total.totalNumberOfPackages,\n                                        onChange: (e)=>handleTotalChange('totalNumberOfPackages', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalPackagesUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalPackagesUnit'),\n                                        value: total.totalPackagesUnit,\n                                        onChange: (e)=>handleTotalChange('totalPackagesUnit', e.target.value),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeight'),\n                                        type: \"number\",\n                                        value: total.totalNetWeight,\n                                        onChange: (e)=>handleTotalChange('totalNetWeight', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeightUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeightUnit'),\n                                        value: total.totalNetWeightUnit,\n                                        onChange: (e)=>handleTotalChange('totalNetWeightUnit', e.target.value || 'kg'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeight'),\n                                        type: \"number\",\n                                        value: total.totalGrossWeight,\n                                        onChange: (e)=>handleTotalChange('totalGrossWeight', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeightUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeightUnit'),\n                                        value: total.totalGrossWeightUnit,\n                                        onChange: (e)=>handleTotalChange('totalGrossWeightUnit', e.target.value || 'kg'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolume\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolume'),\n                                        type: \"number\",\n                                        value: total.totalVolume,\n                                        onChange: (e)=>handleTotalChange('totalVolume', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolumeMeasurementUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolumeMeasurementUnit'),\n                                        value: total.totalVolumeMeasurementUnit,\n                                        onChange: (e)=>handleTotalChange('totalVolumeMeasurementUnit', e.target.value),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 376,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        spacing: 3,\n        children: [\n            renderTotalsSection(),\n            renderPartyDialog()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n        lineNumber: 488,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1PackingListForm, \"eC5q2XofSeE2jFgvJlQweZHYME0=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useFormContext,\n        src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_5__.usePartyAddressForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useFieldArray\n    ];\n});\n_c = PLT1PackingListForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1PackingListForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx\n"));

/***/ })

});