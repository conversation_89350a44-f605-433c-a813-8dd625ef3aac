'use client';

import dayjs from 'dayjs';
import { useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'src/routes/hooks';
import { allLangs } from './all-langs';
import { cookieName, fallbackLng, changeLangMessages as messages } from './locales-config';
import { toast } from 'src/components/snackbar';

import type { LanguageValue } from './locales-config';
import { useCookies } from 'minimal-shared/hooks';

// ----------------------------------------------------------------------

export const LANGUAGE_LOCAL_STORAGE_KEY = 'i18nextLng';

export function useTranslate(ns?: string) {
  const router = useRouter();

  const cookies = useCookies<string>(cookieName);

  const { t, i18n } = useTranslation(ns);

  useEffect(() => {
    const defaultBrowserLang = navigator.language.split('-')[0];

    const storedLang = localStorage.getItem(LANGUAGE_LOCAL_STORAGE_KEY) || cookies.state || defaultBrowserLang || fallbackLng;
    if (storedLang && allLangs.find(lang => lang.value === storedLang)) {
      i18n.changeLanguage(storedLang);
      dayjs.locale(allLangs.find(lang => lang.value === storedLang)?.adapterLocale || fallback.adapterLocale);
    }
  }, []);

  const fallback = allLangs.filter((lang) => lang.value === fallbackLng)[0];
  const currentLang = allLangs.find((lang) => lang.value === i18n.language) ?? fallback;

  const onChangeLang = useCallback(
    async (newLang: LanguageValue) => {
      try {
        await i18n.changeLanguage(newLang);
        localStorage.setItem(LANGUAGE_LOCAL_STORAGE_KEY, newLang);

        const newCurrentLang = allLangs.find((lang) => lang.value === newLang);
        if (newCurrentLang) {
          dayjs.locale(newCurrentLang.adapterLocale);
        }

        toast.success(t('useLocales.languageChangedMessage'), {
          position: "bottom-right"
        });

        router.refresh();
      } catch (error) {
        console.error(error);
      }
    },
    [i18n, router]
  );

  return {
    t,
    i18n,
    onChangeLang,
    currentLang: currentLang ?? fallback,
  };
}