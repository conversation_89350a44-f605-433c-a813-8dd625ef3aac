import { AuthSplitLayout } from 'src/layouts/auth-split';

import { GuestGuard } from 'src/auth/guard';
import { getServerTranslations } from 'src/locales/server';

// ----------------------------------------------------------------------

type Props = {
  children: React.ReactNode;
};

export default async function Layout({ children }: Props) {

  const { t } = await getServerTranslations();

  return (
    <GuestGuard>
      <AuthSplitLayout
        slotProps={{
          section: { title: t('signIn.authSplitLayout.title'), subtitle: t('signIn.authSplitLayout.subtitle') },
        }}
      >
        {children}
      </AuthSplitLayout>
    </GuestGuard>
  );
}
