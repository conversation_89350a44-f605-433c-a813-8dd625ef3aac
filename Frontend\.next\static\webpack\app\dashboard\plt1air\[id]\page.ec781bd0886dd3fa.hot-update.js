"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx":
/*!*******************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1PackingListForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/party-address/hooks/usePartyAddressForm */ \"(app-pages-browser)/./src/components/party-address/hooks/usePartyAddressForm.tsx\");\n/* harmony import */ var src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/components/party-address/party-address-dialog */ \"(app-pages-browser)/./src/components/party-address/party-address-dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst DEFAULT_ITEM = {\n    id: '',\n    name: '',\n    modelNumber: '',\n    purchaseOrderNumber: '',\n    commercialInvoiceNumber: '',\n    packageNetWeight: 0,\n    packageNetWeightUnit: 'kg',\n    packageGrossWeight: 0,\n    packageGrossWeightUnit: 'kg',\n    quantity: 0,\n    volume: 0,\n    packingListId: ''\n};\nconst DEFAULT_TOTAL = {\n    id: '',\n    totalQuantity: 0,\n    totalPackagesUnit: '',\n    totalNumberOfPackages: 0,\n    totalNumberOfPallets: 0,\n    totalNetWeight: 0,\n    totalNetWeightUnit: 'kg',\n    totalGrossWeight: 0,\n    totalGrossWeightUnit: 'kg',\n    totalVolume: 0,\n    totalVolumeMeasurementUnit: '',\n    packingListId: ''\n};\nfunction PLT1PackingListForm(param) {\n    let { formPath, index, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    const { control, getValues, watch, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useFormContext)();\n    const fieldPrefix = index !== undefined ? \"\".concat(formPath, \".\").concat(index) : formPath;\n    const itemsFieldName = \"\".concat(fieldPrefix, \".listSummary\");\n    const totalFieldName = \"\".concat(fieldPrefix, \".listTotal\");\n    // Use the party address form hook\n    const { openPartyDialog, currentPartyType, handleClosePartyDialog, handleUpdateParty } = (0,src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_5__.usePartyAddressForm)({\n        fieldPrefix\n    });\n    // State for item dialog\n    const [openItemDialog, setOpenItemDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteItemIndex, setDeleteItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize listTotal if it doesn't exist\n    const listTotal = watch(totalFieldName);\n    if (!listTotal) {\n        setValue(totalFieldName, DEFAULT_TOTAL);\n    }\n    // UseFieldArray hook to manage the items array\n    const { fields, append, remove, update } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useFieldArray)({\n        control,\n        name: itemsFieldName\n    });\n    // Handle opening the item dialog for adding a new item\n    const handleAddItem = ()=>{\n        setCurrentItemIndex(null);\n        // Add a new item with default values\n        const newItem = {\n            ...DEFAULT_ITEM\n        };\n        append(newItem);\n        setCurrentItemIndex(fields.length); // Set to the new index\n        setOpenItemDialog(true);\n    };\n    // Handle opening the item dialog for editing an existing item\n    const handleEditItem = (index)=>{\n        setCurrentItemIndex(index);\n        setOpenItemDialog(true);\n    };\n    // Handle closing the item dialog\n    const handleCloseItemDialog = ()=>{\n        setOpenItemDialog(false);\n        // If we were adding a new item and user cancels, remove the empty item\n        if (currentItemIndex !== null && currentItemIndex === fields.length - 1) {\n            const item = getValues(\"\".concat(itemsFieldName, \".\").concat(currentItemIndex));\n            // Check if it's an empty item (all fields are default values)\n            const isEmpty = !item.name && !item.modelNumber && !item.purchaseOrderNumber && !item.commercialInvoiceNumber && item.quantity === 0 && item.packageNetWeight === 0 && item.packageGrossWeight === 0 && item.volume === 0;\n            if (isEmpty) {\n                remove(currentItemIndex);\n            }\n        }\n        setCurrentItemIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeleteItemIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deleteItemIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting an item\n    const handleDeleteItem = ()=>{\n        if (deleteItemIndex !== null) {\n            remove(deleteItemIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving an item (just close the dialog since form is already updated)\n    const handleSaveItem = ()=>{\n        handleCloseItemDialog();\n    };\n    // Render the item form in the dialog - using React Hook Form fields\n    const renderItemForm = ()=>{\n        if (currentItemIndex === null) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".name\"),\n                    label: t('plt1.details.documents.packingList.item.name'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".modelNumber\"),\n                    label: t('plt1.details.documents.packingList.item.modelNumber'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".purchaseOrderNumber\"),\n                            label: t('plt1.details.documents.packingList.item.purchaseOrderNumber'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".commercialInvoiceNumber\"),\n                            label: t('plt1.details.documents.packingList.item.commercialInvoiceNumber'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageNetWeight\"),\n                            label: t('plt1.details.documents.packingList.item.packageNetWeight'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageNetWeightUnit\"),\n                            label: t('plt1.details.documents.packingList.item.packageNetWeightUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageGrossWeight\"),\n                            label: t('plt1.details.documents.packingList.item.packageGrossWeight'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageGrossWeightUnit\"),\n                            label: t('plt1.details.documents.packingList.item.packageGrossWeightUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".quantity\"),\n                            label: t('plt1.details.documents.packingList.item.quantity'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".volume\"),\n                            label: t('plt1.details.documents.packingList.item.volume'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the item table\n    const renderItemsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            component: _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"packing list items table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.name')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.modelNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.quantity')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.packageNetWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.packageGrossWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        children: fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                colSpan: 6,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.packingList.item.noItems')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 13\n                        }, this) : fields.map((field, index)=>{\n                            const item = getValues(\"\".concat(itemsFieldName, \".\").concat(index)) || {};\n                            var _item_quantity, _item_packageNetWeight, _item_packageGrossWeight;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: item.name || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: item.modelNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: (_item_quantity = item.quantity) !== null && _item_quantity !== void 0 ? _item_quantity : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_item_packageNetWeight = item.packageNetWeight) !== null && _item_packageNetWeight !== void 0 ? _item_packageNetWeight : '-',\n                                            \" \",\n                                            item.packageNetWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_item_packageGrossWeight = item.packageGrossWeight) !== null && _item_packageGrossWeight !== void 0 ? _item_packageGrossWeight : '-',\n                                            \" \",\n                                            item.packageGrossWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditItem(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deleteItemIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 281,\n            columnNumber: 5\n        }, this);\n    const renderPartyDialog = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            open: openPartyDialog,\n            onClose: handleClosePartyDialog,\n            onSave: handleUpdateParty,\n            formPath: fieldPrefix,\n            currentPartyType: currentPartyType,\n            readOnly: readOnly,\n            titlePrefix: \"plt1.details.documents.packingList.partyAddress\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 355,\n            columnNumber: 5\n        }, this);\n    // Handle change for totals fields\n    const handleTotalChange = (field, value)=>{\n        setValue(\"\".concat(totalFieldName, \".\").concat(field), value);\n    };\n    // Render the totals section\n    const renderTotalsSection = ()=>{\n        const total = watch(totalFieldName) || DEFAULT_TOTAL;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            sx: {\n                boxShadow: 'none',\n                mt: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    title: t('plt1.details.documents.packingList.form.totalsTitle')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    sx: {\n                        borderStyle: 'dashed'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        spacing: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalQuantity\"),\n                                        label: t('plt1.details.documents.packingList.total.totalQuantity'),\n                                        type: \"number\",\n                                        value: total.totalQuantity,\n                                        onChange: (e)=>handleTotalChange('totalQuantity', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPallets\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPallets'),\n                                        type: \"number\",\n                                        value: total.totalNumberOfPallets,\n                                        onChange: (e)=>handleTotalChange('totalNumberOfPallets', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPackages\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPackages'),\n                                        type: \"number\",\n                                        value: total.totalNumberOfPackages,\n                                        onChange: (e)=>handleTotalChange('totalNumberOfPackages', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalPackagesUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalPackagesUnit'),\n                                        value: total.totalPackagesUnit,\n                                        onChange: (e)=>handleTotalChange('totalPackagesUnit', e.target.value),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeight'),\n                                        type: \"number\",\n                                        value: total.totalNetWeight,\n                                        onChange: (e)=>handleTotalChange('totalNetWeight', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeightUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeightUnit'),\n                                        value: total.totalNetWeightUnit,\n                                        onChange: (e)=>handleTotalChange('totalNetWeightUnit', e.target.value || 'kg'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeight'),\n                                        type: \"number\",\n                                        value: total.totalGrossWeight,\n                                        onChange: (e)=>handleTotalChange('totalGrossWeight', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeightUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeightUnit'),\n                                        value: total.totalGrossWeightUnit,\n                                        onChange: (e)=>handleTotalChange('totalGrossWeightUnit', e.target.value || 'kg'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolume\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolume'),\n                                        type: \"number\",\n                                        value: total.totalVolume,\n                                        onChange: (e)=>handleTotalChange('totalVolume', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolumeMeasurementUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolumeMeasurementUnit'),\n                                        value: total.totalVolumeMeasurementUnit,\n                                        onChange: (e)=>handleTotalChange('totalVolumeMeasurementUnit', e.target.value),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 376,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        spacing: 3,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        title: t('plt1.details.documents.packingList.form.itemsTitle'),\n                        action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            variant: \"contained\",\n                            size: \"small\",\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                icon: \"eva:plus-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: handleAddItem,\n                            sx: {\n                                mb: 2\n                            },\n                            disabled: readOnly,\n                            children: t('common.addItem')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: [\n                            renderItemsTable(),\n                            fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                sx: {\n                                    mt: 2,\n                                    display: 'flex',\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    variant: \"soft\",\n                                    color: \"primary\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                        icon: \"eva:plus-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    onClick: handleAddItem,\n                                    disabled: readOnly,\n                                    children: t('common.addItem')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 490,\n                columnNumber: 7\n            }, this),\n            renderTotalsSection(),\n            renderPartyDialog()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n        lineNumber: 488,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1PackingListForm, \"eC5q2XofSeE2jFgvJlQweZHYME0=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useFormContext,\n        src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_5__.usePartyAddressForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useFieldArray\n    ];\n});\n_c = PLT1PackingListForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1PackingListForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx\n"));

/***/ })

});