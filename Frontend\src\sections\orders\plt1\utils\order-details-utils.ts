// src/utils/form-utils.ts
import { axiosInstance } from 'src/lib/axios';
import { toast } from 'src/components/snackbar';

/**
 * Normalizes form values for comparison by handling null/undefined values,
 * empty strings, NaN numbers, and nested objects/arrays consistently.
 * 
 * @param value - The value to normalize
 * @returns The normalized value
 */
export const normalizeValue = (value: any): any => {
  if (value === null || value === undefined) return '';
  if (typeof value === 'number' && isNaN(value)) return 0;
  if (typeof value === 'string' && value.trim() === '') return '';
  if (typeof value === 'object' && value !== null) {
    if (Array.isArray(value)) {
      return value.map(normalizeValue);
    } else {
      const normalized: Record<string, any> = {};
      Object.keys(value).forEach((key) => {
        normalized[key] = normalizeValue(value[key]);
      });
      return normalized;
    }
  }
  return value;
};

/**
 * Compares two form value objects by normalizing them first
 * 
 * @param currentValues - The current form values
 * @param initialValues - The initial form values to compare against
 * @returns true if the values are different, false if they're the same
 */
export const hasFormChanges = <T>(currentValues: T, initialValues: T): boolean => {
  const normalizedCurrent = normalizeValue(currentValues);
  const normalizedInitial = normalizeValue(initialValues);

  return JSON.stringify(normalizedCurrent) !== JSON.stringify(normalizedInitial);
};

/**
 * Configuration for saving PLT1 orders
 */
interface SaveOrderConfig {
  orderId: string;
  endpoint: string;
  idField: string; // The field name for the ID in the payload (e.g., 'PLT1AirId', 'pLT1RoadId')
  t: (key: string) => string; // Translation function
}

/**
 * Generic function to handle saving PLT1 orders
 * 
 * @param formData - The form data to save
 * @param config - Configuration object containing endpoint, ID field, etc.
 * @param setIsSaving - Function to update saving state
 * @param setFormChanged - Function to update form changed state
 * @param reload - Function to reload order data after successful save
 * @returns Promise that resolves when save is complete
 */
export const handlePLT1OrderSave = async <T>(
  formData: T,
  config: SaveOrderConfig,
  setIsSaving: (saving: boolean) => void,
  setFormChanged: (changed: boolean) => void,
  reload: () => Promise<any> | Promise<void> | void
): Promise<void> => {
  try {
    setIsSaving(true);

    const payload = {
      ...formData,
      [config.idField]: config.orderId,
    };

    // Debug: Log the payload being sent
    console.log('💾 PLT1 Order Save - Payload:', payload);
    console.log('💾 Endpoint:', `${config.endpoint}/${config.orderId}`);
    if ((payload as any).billsOfLading) {
      console.log('💾 Bills of Lading in Payload:', (payload as any).billsOfLading);
    }

    const response = await axiosInstance.put(
      `${config.endpoint}/${config.orderId}`,
      payload
    );

    if (response.status === 200) {
      toast.success(config.t('plt1.saveSuccess'));
      setFormChanged(false);
      const reloadResult = reload();
      // Handle case where reload might return undefined or a promise
      if (reloadResult && typeof reloadResult.then === 'function') {
        await reloadResult;
      }
    } else {
      toast.error(config.t('plt1.saveError'));
    }
  } catch (error: any) {
    console.error('Error saving order:', error);
    toast.error(error.response?.data?.errorMessages?.[0] || config.t('plt1.saveError'));
    throw error;
  } finally {
    setIsSaving(false);
  }
};