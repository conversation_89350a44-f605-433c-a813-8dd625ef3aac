import { useState, useRef } from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';
import {
  St<PERSON>,
  Card,
  CardHeader,
  Divider,
  Typography,
  Button,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import { useTranslate } from 'src/locales';
import { Field } from 'src/components/hook-form';
import { Iconify } from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';
import PLT1PackedItemsTable from './plt1-packed-items-table';
import { usePartyAddressForm } from 'src/components/party-address/hooks/usePartyAddressForm';
import PartyAddressDialog from 'src/components/party-address/party-address-dialog';

// Updated interfaces for new structure
export interface PLT1PackedItemData {
  id?: string;
  name: string;
  hsCode: string;
  modelNumber: string;
  quantity: number;
  itemNetWeight?: number;
  itemNetWeightUnit?: string;
  itemGrossWeight?: number;
  hsCodeHints?: string;
  packingListPositionId?: string;
}

export interface PLT1PackingListPositionData {
  id?: string;
  positionNumber: number;
  commercialInvoiceNumber: string;
  packageNetWeight: number;
  packageNetWeightUnit?: string;
  packageGrossWeight: number;
  packageGrossWeightUnit?: string;
  packagesNetWeight: number;
  packagesNetWeightUnit?: string;
  packagesGrossWeight: number;
  packagesGrossWeightUnit?: string;
  packageUnit: string;
  packageSize: string;
  packageVolume: number;
  packageVolumeUnit: string;
  numberOfPackages: number;
  packedItems: PLT1PackedItemData[];
  packingListId?: string;
}

export interface PLT1PackingListTotalData {
  id?: string;
  shipmentOrPackingId?: string;
  totalQuantity: number;
  totalPackagesUnit: string;
  totalNumberOfPackages: number;
  totalNumberOfPallets?: number;
  totalNetWeight: number;
  totalNetWeightUnit?: string;
  totalGrossWeight: number;
  totalGrossWeightUnit?: string;
  totalVolume: number;
  totalVolumeMeasurementUnit: string;
  packingListId?: string;
}

export interface PLT1PackingListData {
  id?: string;
  packingListPositions: PLT1PackingListPositionData[];
  listTotal?: PLT1PackingListTotalData;
  t1OrderId?: string;
}

const DEFAULT_POSITION = {
  id: null,
  positionNumber: 1,
  commercialInvoiceNumber: '',
  packageNetWeight: 0,
  packageNetWeightUnit: 'KGM',
  packageGrossWeight: 0,
  packageGrossWeightUnit: 'KGM',
  packagesNetWeight: 0,
  packagesNetWeightUnit: 'KGM',
  packagesGrossWeight: 0,
  packagesGrossWeightUnit: 'KGM',
  packageUnit: 'CTNS',
  packageSize: '',
  packageVolume: 0,
  packageVolumeUnit: 'CBM',
  numberOfPackages: 0,
  packedItems: [],
  packingListId: null,
};

const DEFAULT_TOTAL = {
  id: null,
  shipmentOrPackingId: null,
  totalQuantity: 0,
  totalPackagesUnit: 'CTNS',
  totalNumberOfPackages: 0,
  totalNumberOfPallets: 0,
  totalNetWeight: 0,
  totalNetWeightUnit: 'KGM',
  totalGrossWeight: 0,
  totalGrossWeightUnit: 'KGM',
  totalVolume: 0,
  totalVolumeMeasurementUnit: 'CBM',
  packingListId: null,
};

interface PLT1PackingListFormProps {
  formPath: string;
  index?: number;
  readOnly?: boolean;
}

export default function PLT1PackingListForm({
  formPath,
  index,
  readOnly = false,
}: PLT1PackingListFormProps) {
  const { t } = useTranslate();
  const { control, getValues, watch, setValue } = useFormContext();

  const fieldPrefix = index !== undefined ? `${formPath}.${index}` : formPath;
  const positionsFieldName = `${fieldPrefix}.packingListPositions`;
  const totalFieldName = `${fieldPrefix}.listTotal`;

  // Use the party address form hook
  const { openPartyDialog, currentPartyType, handleClosePartyDialog, handleUpdateParty } =
    usePartyAddressForm({ fieldPrefix });

  // State for position dialog
  const [openPositionDialog, setOpenPositionDialog] = useState(false);
  const [currentPositionIndex, setCurrentPositionIndex] = useState<number | null>(null);
  const [deletePositionIndex, setDeletePositionIndex] = useState<number | null>(null);
  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const deleteButtonRef = useRef<HTMLButtonElement>(null);

  // Initialize listTotal if it doesn't exist
  const listTotal = watch(totalFieldName);
  if (!listTotal) {
    setValue(totalFieldName, DEFAULT_TOTAL);
  }

  // UseFieldArray hook to manage the positions array
  const { fields, append, remove } = useFieldArray({
    control,
    name: positionsFieldName,
  });

  // Handle opening the position dialog for adding a new position
  const handleAddPosition = () => {
    setCurrentPositionIndex(null);
    // Add a new position with default values
    const newPosition = { ...DEFAULT_POSITION, positionNumber: fields.length + 1 };
    append(newPosition);
    setCurrentPositionIndex(fields.length); // Set to the new index
    setOpenPositionDialog(true);
  };

  // Handle opening the position dialog for editing an existing position
  const handleEditPosition = (index: number) => {
    setCurrentPositionIndex(index);
    setOpenPositionDialog(true);
  };

  // Handle closing the position dialog
  const handleClosePositionDialog = () => {
    setOpenPositionDialog(false);
    // If we were adding a new position and user cancels, remove the empty position
    if (currentPositionIndex !== null && currentPositionIndex === fields.length - 1) {
      const position = getValues(`${positionsFieldName}.${currentPositionIndex}`);
      // Check if it's an empty position (all fields are default values)
      const isEmpty =
        !position.commercialInvoiceNumber &&
        position.numberOfPackages === 0 &&
        position.packageNetWeight === 0 &&
        position.packageGrossWeight === 0 &&
        position.packageVolume === 0 &&
        (!position.packedItems || position.packedItems.length === 0);
      if (isEmpty) {
        remove(currentPositionIndex);
      }
    }
    setCurrentPositionIndex(null);
  };

  // Handle opening the delete confirmation dialog
  const handleOpenDeleteConfirm = (index: number) => {
    setDeletePositionIndex(index);
    setOpenDeleteConfirm(true);
  };

  // Handle closing the delete confirmation dialog
  const handleCloseDeleteConfirm = () => {
    setOpenDeleteConfirm(false);
    if (deletePositionIndex !== null) {
      deleteButtonRef.current?.focus();
    }
  };

  // Handle deleting a position
  const handleDeletePosition = () => {
    if (deletePositionIndex !== null) {
      remove(deletePositionIndex);
    }
    handleCloseDeleteConfirm();
  };

  // Handle saving a position (just close the dialog since form is already updated)
  const handleSavePosition = () => {
    handleClosePositionDialog();
  };

  // Render the position form in the dialog
  const renderPositionForm = () => {
    if (currentPositionIndex === null) return null;

    return (
      <Stack spacing={3}>
        {/* Position Details Section */}
        <Box>
          <Typography variant="h6" sx={{ mb: 2 }}>
            {t('plt1.details.documents.packingList.position.details')}
          </Typography>

          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Field.Text
                name={`${positionsFieldName}.${currentPositionIndex}.numberOfPackages`}
                label={t('plt1.details.documents.packingList.position.numberOfPackages')}
                type="number"
                disabled={readOnly}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Field.Text
                name={`${positionsFieldName}.${currentPositionIndex}.packageUnit`}
                label={t('plt1.details.documents.packingList.position.packageUnit')}
                disabled={readOnly}
              />
            </Grid>
          </Grid>

          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Field.Text
                name={`${positionsFieldName}.${currentPositionIndex}.packageSize`}
                label={t('plt1.details.documents.packingList.position.packageSize')}
                disabled={readOnly}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Field.Text
                name={`${positionsFieldName}.${currentPositionIndex}.packagesVolume`}
                label={t('plt1.details.documents.packingList.position.packagesVolume')}
                type="number"
                disabled={readOnly}
              />
            </Grid>
          </Grid>

          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Field.Text
                name={`${positionsFieldName}.${currentPositionIndex}.packagesNetWeight`}
                label={t('plt1.details.documents.packingList.position.packagesNetWeight')}
                type="number"
                disabled={readOnly}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Field.Text
                name={`${positionsFieldName}.${currentPositionIndex}.packagesGrossWeight`}
                label={t('plt1.details.documents.packingList.position.packagesGrossWeight')}
                type="number"
                disabled={readOnly}
              />
            </Grid>
          </Grid>

          <Grid container spacing={2}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Field.Text
                name={`${positionsFieldName}.${currentPositionIndex}.commercialInvoiceNumber`}
                label={t('plt1.details.documents.packingList.position.commercialInvoiceNumber')}
                disabled={readOnly}
              />
            </Grid>
          </Grid>
        </Box>

        <Divider />

        {/* Packed Items Section */}
        <PLT1PackedItemsTable
          fieldName={`${positionsFieldName}.${currentPositionIndex}.packedItems`}
          readOnly={readOnly}
        />
      </Stack>
    );
  };

  // Render the positions table
  const renderPositionsTable = () => (
    <TableContainer component={Paper}>
      <Table sx={{ minWidth: 650 }} aria-label="packing list positions table">
        <TableHead>
          <TableRow>
            <TableCell>
              {t('plt1.details.documents.packingList.position.commercialInvoiceNumber')}
            </TableCell>
            <TableCell>
              {t('plt1.details.documents.packingList.position.numberOfPackages')}
            </TableCell>
            <TableCell>
              {t('plt1.details.documents.packingList.position.packagesNetWeight')}
            </TableCell>
            <TableCell>
              {t('plt1.details.documents.packingList.position.packagesGrossWeight')}
            </TableCell>
            <TableCell align="right">{t('common.actions')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {fields.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} align="center">
                <Typography variant="body2" color="text.secondary">
                  {t('plt1.details.documents.packingList.position.noPositions')}
                </Typography>
              </TableCell>
            </TableRow>
          ) : (
            fields.map((field, index) => {
              const position = getValues(`${positionsFieldName}.${index}`) || {};
              return (
                <TableRow key={field.id}>
                  <TableCell>{position.commercialInvoiceNumber || '-'}</TableCell>
                  <TableCell>{position.numberOfPackages ?? '-'}</TableCell>
                  <TableCell>
                    {position.packagesNetWeight ?? '-'} {position.packagesNetWeightUnit || ''}
                  </TableCell>
                  <TableCell>
                    {position.packagesGrossWeight ?? '-'} {position.packagesGrossWeightUnit || ''}
                  </TableCell>
                  <TableCell align="right">
                    <Stack direction="row" spacing={1} justifyContent="flex-end">
                      <Tooltip title={t('common.edit')}>
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleEditPosition(index)}
                          disabled={readOnly}
                        >
                          <Iconify icon="eva:edit-fill" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={t('common.delete')}>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleOpenDeleteConfirm(index)}
                          ref={deletePositionIndex === index ? deleteButtonRef : null}
                          disabled={readOnly}
                        >
                          <Iconify icon="eva:trash-2-outline" />
                        </IconButton>
                      </Tooltip>
                    </Stack>
                  </TableCell>
                </TableRow>
              );
            })
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Stack spacing={3}>
      {/* Positions Section */}
      <Card sx={{ boxShadow: 'none' }}>
        <CardHeader
          title={t('plt1.details.documents.packingList.form.positionsTitle')}
          action={
            <Button
              variant="contained"
              size="small"
              startIcon={<Iconify icon="eva:plus-fill" />}
              onClick={handleAddPosition}
              sx={{ mb: 2 }}
              disabled={readOnly}
            >
              {t('plt1.details.documents.packingList.position.addNew')}
            </Button>
          }
        />
        <Divider sx={{ borderStyle: 'dashed' }} />
        <Box sx={{ p: 3 }}>
          {renderPositionsTable()}

          {fields.length > 0 && (
            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
              <Button
                variant="soft"
                color="primary"
                startIcon={<Iconify icon="eva:plus-fill" />}
                onClick={handleAddPosition}
                disabled={readOnly}
              >
                {t('plt1.details.documents.packingList.position.addNew')}
              </Button>
            </Box>
          )}
        </Box>
      </Card>

      {/* Totals Section */}
      <Card sx={{ boxShadow: 'none' }}>
        <CardHeader title={t('plt1.details.documents.packingList.form.totalsTitle')} />
        <Divider sx={{ borderStyle: 'dashed' }} />
        <Box sx={{ p: 3 }}>
          <Grid container spacing={3}>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
              <Field.Text
                name={`${totalFieldName}.totalQuantity`}
                label={t('plt1.details.documents.packingList.total.totalQuantity')}
                type="number"
                disabled={true}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
              <Field.Text
                name={`${totalFieldName}.totalNumberOfPackages`}
                label={t('plt1.details.documents.packingList.total.totalNumberOfPackages')}
                type="number"
                disabled={true}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
              <Field.Text
                name={`${totalFieldName}.totalPackagesUnit`}
                label={t('plt1.details.documents.packingList.total.totalPackagesUnit')}
                disabled={true}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
              <Field.Text
                name={`${totalFieldName}.totalNetWeight`}
                label={t('plt1.details.documents.packingList.total.totalNetWeight')}
                type="number"
                disabled={true}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
              <Field.Text
                name={`${fieldPrefix}.packagesAdditionalWeight`}
                label={t('plt1.details.documents.packingList.packagesAdditionalWeight')}
                type="number"
                disabled={true}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
              <Field.Text
                name={`${totalFieldName}.totalGrossWeight`}
                label={t('plt1.details.documents.packingList.total.totalGrossWeight')}
                type="number"
                disabled={true}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
              <Field.Text
                name={`${totalFieldName}.totalVolume`}
                label={t('plt1.details.documents.packingList.total.totalVolume')}
                type="number"
                disabled={true}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
              <Field.Text
                name={`${fieldPrefix}.numberOfBulkPackages`}
                label={t('plt1.details.documents.packingList.numberOfBulkPackages')}
                type="number"
                disabled={true}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
              <Field.Text
                name={`${fieldPrefix}.bulkPackagesUnit`}
                label={t('plt1.details.documents.packingList.bulkPackagesUnit')}
                type="number"
                disabled={true}
              />
            </Grid>
          </Grid>
        </Box>
      </Card>

      {/* Position Dialog */}
      <Dialog open={openPositionDialog} onClose={handleClosePositionDialog} fullWidth maxWidth="lg">
        <DialogTitle>
          {currentPositionIndex === null
            ? t('plt1.details.documents.packingList.position.addNew')
            : t('plt1.details.documents.packingList.position.edit')}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 3 }}>{renderPositionForm()}</Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePositionDialog} color="inherit">
            {t('common.cancel')}
          </Button>
          <Button onClick={handleSavePosition} variant="contained">
            {t('common.save')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={openDeleteConfirm}
        onClose={handleCloseDeleteConfirm}
        title={t('plt1.details.documents.packingList.position.confirmDeleteDialog.title')}
        content={t('plt1.details.documents.packingList.position.confirmDeleteDialog.content')}
        action={
          <Button variant="contained" color="error" onClick={handleDeletePosition}>
            {t('common.delete')}
          </Button>
        }
      />

      {/* Party Dialog */}
      <PartyAddressDialog
        open={openPartyDialog}
        onClose={handleClosePartyDialog}
        onSave={handleUpdateParty}
        formPath={fieldPrefix}
        currentPartyType={currentPartyType}
        readOnly={readOnly}
        titlePrefix="plt1.details.documents.packingList.partyAddress"
      />
    </Stack>
  );
}
