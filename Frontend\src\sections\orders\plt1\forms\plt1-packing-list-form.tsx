import { useFormContext } from 'react-hook-form';
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  Divider,
  Box
} from '@mui/material';
import { useTranslate } from 'src/locales';
import { Field } from 'src/components/hook-form';
import { usePartyAddressForm } from 'src/components/party-address/hooks/usePartyAddressForm';
import PartyAddressDialog from 'src/components/party-address/party-address-dialog';

// ----------------------------------------------------------------------

export interface PLT1PackingListItemData {
  id?: string;
  name: string;
  modelNumber: string;
  purchaseOrderNumber: string;
  commercialInvoiceNumber: string;
  packageNetWeight: number;
  packageNetWeightUnit: string;
  packageGrossWeightUnit: number;
  grossWeightUnit: string;
  quantity: number;
  volume: number;
  packingListId: string;
}

export interface PLT1PackingListTotalData {
  id?: string;
  totalQuantity: number;
  totalPackagesUnit: string;
  totalNumberOfPackages: number;
  totalNumberOfPallets: number;
  totalNetWeight: number;
  totalNetWeightUnit: string;
  totalGrossWeight: number;
  totalGrossWeightUnit: string;
  totalVolume: number;
  totalVolumeMeasurementUnit: string;
  packingListId?: string;
}

export interface PLT1PackingListData {
  id?: string;
  listSummary: PLT1PackingListItemData[];
  listTotal?: PLT1PackingListTotalData;
  t1OrderId?: string;
}

const DEFAULT_TOTAL = {
  id: '',
  totalQuantity: 0,
  totalPackagesUnit: '',
  totalNumberOfPackages: 0,
  totalNumberOfPallets: 0,
  totalNetWeight: 0,
  totalNetWeightUnit: 'kg',
  totalGrossWeight: 0,
  totalGrossWeightUnit: 'kg',
  totalVolume: 0,
  totalVolumeMeasurementUnit: '',
  packingListId: '',
};

interface PLT1PackingListFormProps {
  formPath: string;
  index?: number;
  readOnly?: boolean;
}

export default function PLT1PackingListForm({ formPath, index, readOnly = false }: PLT1PackingListFormProps) {
  const { t } = useTranslate();
  const { control, getValues, watch, setValue } = useFormContext();

  const fieldPrefix = index !== undefined ? `${formPath}.${index}` : formPath;
  const totalFieldName = `${fieldPrefix}.listTotal`;

  // Use the party address form hook
  const {
    openPartyDialog,
    currentPartyType,
    handleClosePartyDialog,
    handleUpdateParty
  } = usePartyAddressForm({ fieldPrefix });


  // Initialize listTotal if it doesn't exist
  const listTotal = watch(totalFieldName);
  if (!listTotal) {
    setValue(totalFieldName, DEFAULT_TOTAL);
  }

  const renderPartyDialog = () => (
    <PartyAddressDialog
      open={openPartyDialog}
      onClose={handleClosePartyDialog}
      onSave={handleUpdateParty}
      formPath={fieldPrefix}
      currentPartyType={currentPartyType}
      readOnly={readOnly}
      titlePrefix="plt1.details.documents.packingList.partyAddress"
    />
  );

  // Handle change for totals fields
  const handleTotalChange = (field: string, value: string | number) => {
    setValue(`${totalFieldName}.${field}`, value);
  };

  // Render the totals section
  const renderTotalsSection = () => {
    const total = watch(totalFieldName) || DEFAULT_TOTAL;
    return (
      <Card sx={{ boxShadow: 'none', mt: 3 }}>
        <CardHeader title={t('plt1.details.documents.packingList.form.totalsTitle')} />
        <Divider sx={{ borderStyle: 'dashed' }} />
        <Box sx={{ p: 3 }}>
          <Stack spacing={3}>
            <Stack direction="row" spacing={2}>
              <Field.Text
                name={`${totalFieldName}.totalQuantity`}
                label={t('plt1.details.documents.packingList.total.totalQuantity')}
                type="number"
                value={total.totalQuantity}
                onChange={(e) =>
                  handleTotalChange('totalQuantity', parseFloat(e.target.value) || 0)
                }
                disabled={readOnly}
              />
              <Field.Text
                name={`${totalFieldName}.totalNumberOfPallets`}
                label={t('plt1.details.documents.packingList.total.totalNumberOfPallets')}
                type="number"
                value={total.totalNumberOfPallets}
                onChange={(e) =>
                  handleTotalChange('totalNumberOfPallets', parseFloat(e.target.value) || 0)
                }
                disabled={readOnly}
              />
            </Stack>

            <Stack direction="row" spacing={2}>
              <Field.Text
                name={`${totalFieldName}.totalNumberOfPackages`}
                label={t('plt1.details.documents.packingList.total.totalNumberOfPackages')}
                type="number"
                value={total.totalNumberOfPackages}
                onChange={(e) =>
                  handleTotalChange('totalNumberOfPackages', parseFloat(e.target.value) || 0)
                }
                disabled={readOnly}
              />
              <Field.Text
                name={`${totalFieldName}.totalPackagesUnit`}
                label={t('plt1.details.documents.packingList.total.totalPackagesUnit')}
                value={total.totalPackagesUnit}
                onChange={(e) => handleTotalChange('totalPackagesUnit', e.target.value)}
                disabled={readOnly}
              />
            </Stack>

            <Stack direction="row" spacing={2}>
              <Field.Text
                name={`${totalFieldName}.totalNetWeight`}
                label={t('plt1.details.documents.packingList.total.totalNetWeight')}
                type="number"
                value={total.totalNetWeight}
                onChange={(e) =>
                  handleTotalChange('totalNetWeight', parseFloat(e.target.value) || 0)
                }
                disabled={readOnly}
              />
              <Field.Text
                name={`${totalFieldName}.totalNetWeightUnit`}
                label={t('plt1.details.documents.packingList.total.totalNetWeightUnit')}
                value={total.totalNetWeightUnit}
                onChange={(e) => handleTotalChange('totalNetWeightUnit', e.target.value || 'kg')}
                disabled={readOnly}
              />
            </Stack>

            <Stack direction="row" spacing={2}>
              <Field.Text
                name={`${totalFieldName}.totalGrossWeight`}
                label={t('plt1.details.documents.packingList.total.totalGrossWeight')}
                type="number"
                value={total.totalGrossWeight}
                onChange={(e) =>
                  handleTotalChange('totalGrossWeight', parseFloat(e.target.value) || 0)
                }
                disabled={readOnly}
              />
              <Field.Text
                name={`${totalFieldName}.totalGrossWeightUnit`}
                label={t('plt1.details.documents.packingList.total.totalGrossWeightUnit')}
                value={total.totalGrossWeightUnit}
                onChange={(e) => handleTotalChange('totalGrossWeightUnit', e.target.value || 'kg')}
                disabled={readOnly}
              />
            </Stack>

            <Stack direction="row" spacing={2}>
              <Field.Text
                name={`${totalFieldName}.totalVolume`}
                label={t('plt1.details.documents.packingList.total.totalVolume')}
                type="number"
                value={total.totalVolume}
                onChange={(e) => handleTotalChange('totalVolume', parseFloat(e.target.value) || 0)}
                disabled={readOnly}
              />
              <Field.Text
                name={`${totalFieldName}.totalVolumeMeasurementUnit`}
                label={t('plt1.details.documents.packingList.total.totalVolumeMeasurementUnit')}
                value={total.totalVolumeMeasurementUnit}
                onChange={(e) => handleTotalChange('totalVolumeMeasurementUnit', e.target.value)}
                disabled={readOnly}
              />
            </Stack>
          </Stack>
        </Box>
      </Card>
    );
  };

  return (
    <Stack spacing={3}>
      {/* Totals Section */}
      {renderTotalsSection()}

      {/* Party Dialog */}
      {renderPartyDialog()}
    </Stack>
  );
}
