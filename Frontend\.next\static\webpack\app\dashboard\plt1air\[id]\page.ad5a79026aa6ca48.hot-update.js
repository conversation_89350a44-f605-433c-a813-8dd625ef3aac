"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx":
/*!*******************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1PackingListForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/custom-dialog */ \"(app-pages-browser)/./src/components/custom-dialog/index.ts\");\n/* harmony import */ var src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/components/party-address/hooks/usePartyAddressForm */ \"(app-pages-browser)/./src/components/party-address/hooks/usePartyAddressForm.tsx\");\n/* harmony import */ var src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/components/party-address/party-address-dialog */ \"(app-pages-browser)/./src/components/party-address/party-address-dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst DEFAULT_PACKED_ITEM = {\n    id: '',\n    name: '',\n    hsCode: '',\n    modelNumber: '',\n    quantity: 0,\n    itemNetWeight: 0,\n    itemNetWeightUnit: 'KGM',\n    itemGrossWeight: 0,\n    hsCodeHints: '',\n    packingListPositionId: ''\n};\nconst DEFAULT_POSITION = {\n    id: '',\n    positionNumber: 1,\n    commercialInvoiceNumber: '',\n    packageNetWeight: 0,\n    packageNetWeightUnit: 'KGM',\n    packageGrossWeight: 0,\n    packageGrossWeightUnit: 'KGM',\n    packagesNetWeight: 0,\n    packagesNetWeightUnit: 'KGM',\n    packagesGrossWeight: 0,\n    packagesGrossWeightUnit: 'KGM',\n    packageUnit: 'CTNS',\n    packageSize: '',\n    packageVolume: 0,\n    packageVolumeUnit: 'CBM',\n    numberOfPackages: 0,\n    packedItems: [],\n    packingListId: ''\n};\nconst DEFAULT_TOTAL = {\n    id: '',\n    shipmentOrPackingId: '',\n    totalQuantity: 0,\n    totalPackagesUnit: 'CTNS',\n    totalNumberOfPackages: 0,\n    totalNumberOfPallets: 0,\n    totalNetWeight: 0,\n    totalNetWeightUnit: 'KGM',\n    totalGrossWeight: 0,\n    totalGrossWeightUnit: 'KGM',\n    totalVolume: 0,\n    totalVolumeMeasurementUnit: 'CBM',\n    packingListId: ''\n};\nfunction PLT1PackingListForm(param) {\n    let { formPath, index, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    const { control, getValues, watch, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext)();\n    const fieldPrefix = index !== undefined ? \"\".concat(formPath, \".\").concat(index) : formPath;\n    const positionsFieldName = \"\".concat(fieldPrefix, \".packingListPositions\");\n    const totalFieldName = \"\".concat(fieldPrefix, \".listTotal\");\n    // Use the party address form hook\n    const { openPartyDialog, currentPartyType, handleClosePartyDialog, handleUpdateParty } = (0,src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm)({\n        fieldPrefix\n    });\n    // State for item dialog\n    const [openItemDialog, setOpenItemDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteItemIndex, setDeleteItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize listTotal if it doesn't exist\n    const listTotal = watch(totalFieldName);\n    if (!listTotal) {\n        setValue(totalFieldName, DEFAULT_TOTAL);\n    }\n    // UseFieldArray hook to manage the positions array\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray)({\n        control,\n        name: positionsFieldName\n    });\n    // Handle opening the position dialog for adding a new position\n    const handleAddPosition = ()=>{\n        setCurrentItemIndex(null);\n        // Add a new position with default values\n        const newPosition = {\n            ...DEFAULT_POSITION\n        };\n        append(newPosition);\n        setCurrentItemIndex(fields.length); // Set to the new index\n        setOpenItemDialog(true);\n    };\n    // Handle opening the position dialog for editing an existing position\n    const handleEditPosition = (index)=>{\n        setCurrentItemIndex(index);\n        setOpenItemDialog(true);\n    };\n    // Handle closing the position dialog\n    const handleClosePositionDialog = ()=>{\n        setOpenItemDialog(false);\n        // If we were adding a new position and user cancels, remove the empty position\n        if (currentItemIndex !== null && currentItemIndex === fields.length - 1) {\n            const position = getValues(\"\".concat(positionsFieldName, \".\").concat(currentItemIndex));\n            // Check if it's an empty position (all fields are default values)\n            const isEmpty = !position.commercialInvoiceNumber && position.numberOfPackages === 0 && position.packageNetWeight === 0 && position.packageGrossWeight === 0 && position.packageVolume === 0 && (!position.packedItems || position.packedItems.length === 0);\n            if (isEmpty) {\n                remove(currentItemIndex);\n            }\n        }\n        setCurrentItemIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeleteItemIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deleteItemIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting an item\n    const handleDeleteItem = ()=>{\n        if (deleteItemIndex !== null) {\n            remove(deleteItemIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving a position (just close the dialog since form is already updated)\n    const handleSavePosition = ()=>{\n        handleClosePositionDialog();\n    };\n    // Render the item form in the dialog - using React Hook Form fields\n    const renderItemForm = ()=>{\n        if (currentItemIndex === null) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".name\"),\n                    label: t('plt1.details.documents.packingList.item.name'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".modelNumber\"),\n                    label: t('plt1.details.documents.packingList.item.modelNumber'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".purchaseOrderNumber\"),\n                            label: t('plt1.details.documents.packingList.item.purchaseOrderNumber'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".commercialInvoiceNumber\"),\n                            label: t('plt1.details.documents.packingList.item.commercialInvoiceNumber'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageNetWeight\"),\n                            label: t('plt1.details.documents.packingList.item.packageNetWeight'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageNetWeightUnit\"),\n                            label: t('plt1.details.documents.packingList.item.packageNetWeightUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageGrossWeight\"),\n                            label: t('plt1.details.documents.packingList.item.packageGrossWeight'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageGrossWeightUnit\"),\n                            label: t('plt1.details.documents.packingList.item.packageGrossWeightUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".quantity\"),\n                            label: t('plt1.details.documents.packingList.item.quantity'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".volume\"),\n                            label: t('plt1.details.documents.packingList.item.volume'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 244,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the item table\n    const renderItemsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            component: _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"packing list items table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.name')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.modelNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.quantity')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.packageNetWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.packageGrossWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                colSpan: 6,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.packingList.item.noItems')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 13\n                        }, this) : fields.map((field, index)=>{\n                            const item = getValues(\"\".concat(itemsFieldName, \".\").concat(index)) || {};\n                            var _item_quantity, _item_packageNetWeight, _item_packageGrossWeight;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: item.name || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: item.modelNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: (_item_quantity = item.quantity) !== null && _item_quantity !== void 0 ? _item_quantity : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: [\n                                            (_item_packageNetWeight = item.packageNetWeight) !== null && _item_packageNetWeight !== void 0 ? _item_packageNetWeight : '-',\n                                            \" \",\n                                            item.packageNetWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: [\n                                            (_item_packageGrossWeight = item.packageGrossWeight) !== null && _item_packageGrossWeight !== void 0 ? _item_packageGrossWeight : '-',\n                                            \" \",\n                                            item.packageGrossWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditItem(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deleteItemIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 318,\n            columnNumber: 5\n        }, this);\n    const renderPartyDialog = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            open: openPartyDialog,\n            onClose: handleClosePartyDialog,\n            onSave: handleUpdateParty,\n            formPath: fieldPrefix,\n            currentPartyType: currentPartyType,\n            readOnly: readOnly,\n            titlePrefix: \"plt1.details.documents.packingList.partyAddress\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 392,\n            columnNumber: 5\n        }, this);\n    // Handle change for totals fields\n    const handleTotalChange = (field, value)=>{\n        setValue(\"\".concat(totalFieldName, \".\").concat(field), value);\n    };\n    // Render the totals section\n    const renderTotalsSection = ()=>{\n        const total = watch(totalFieldName) || DEFAULT_TOTAL;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n            sx: {\n                boxShadow: 'none',\n                mt: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    title: t('plt1.details.documents.packingList.form.totalsTitle')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    sx: {\n                        borderStyle: 'dashed'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        spacing: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalQuantity\"),\n                                        label: t('plt1.details.documents.packingList.total.totalQuantity'),\n                                        type: \"number\",\n                                        value: total.totalQuantity,\n                                        onChange: (e)=>handleTotalChange('totalQuantity', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPallets\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPallets'),\n                                        type: \"number\",\n                                        value: total.totalNumberOfPallets,\n                                        onChange: (e)=>handleTotalChange('totalNumberOfPallets', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPackages\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPackages'),\n                                        type: \"number\",\n                                        value: total.totalNumberOfPackages,\n                                        onChange: (e)=>handleTotalChange('totalNumberOfPackages', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalPackagesUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalPackagesUnit'),\n                                        value: total.totalPackagesUnit,\n                                        onChange: (e)=>handleTotalChange('totalPackagesUnit', e.target.value),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeight'),\n                                        type: \"number\",\n                                        value: total.totalNetWeight,\n                                        onChange: (e)=>handleTotalChange('totalNetWeight', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeightUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeightUnit'),\n                                        value: total.totalNetWeightUnit,\n                                        onChange: (e)=>handleTotalChange('totalNetWeightUnit', e.target.value || 'kg'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeight'),\n                                        type: \"number\",\n                                        value: total.totalGrossWeight,\n                                        onChange: (e)=>handleTotalChange('totalGrossWeight', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeightUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeightUnit'),\n                                        value: total.totalGrossWeightUnit,\n                                        onChange: (e)=>handleTotalChange('totalGrossWeightUnit', e.target.value || 'kg'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolume\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolume'),\n                                        type: \"number\",\n                                        value: total.totalVolume,\n                                        onChange: (e)=>handleTotalChange('totalVolume', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolumeMeasurementUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolumeMeasurementUnit'),\n                                        value: total.totalVolumeMeasurementUnit,\n                                        onChange: (e)=>handleTotalChange('totalVolumeMeasurementUnit', e.target.value),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 414,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 411,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        spacing: 3,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        title: t('plt1.details.documents.packingList.form.itemsTitle'),\n                        action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            variant: \"contained\",\n                            size: \"small\",\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                icon: \"eva:plus-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: handleAddItem,\n                            sx: {\n                                mb: 2\n                            },\n                            disabled: readOnly,\n                            children: t('common.addItem')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 522,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: [\n                            renderItemsTable(),\n                            fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                sx: {\n                                    mt: 2,\n                                    display: 'flex',\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    variant: \"soft\",\n                                    color: \"primary\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                        icon: \"eva:plus-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    onClick: handleAddItem,\n                                    disabled: readOnly,\n                                    children: t('common.addItem')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 542,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 521,\n                columnNumber: 7\n            }, this),\n            renderTotalsSection(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                open: openItemDialog,\n                onClose: handleCloseItemDialog,\n                fullWidth: true,\n                maxWidth: \"md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        children: currentItemIndex === null ? t('plt1.details.documents.packingList.item.addNew') : t('plt1.details.documents.packingList.item.edit')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 562,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            sx: {\n                                pt: 3\n                            },\n                            children: renderItemForm()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 568,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 567,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                onClick: handleCloseItemDialog,\n                                color: \"inherit\",\n                                children: t('common.cancel')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                onClick: handleSaveItem,\n                                variant: \"contained\",\n                                children: t('common.save')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 561,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__.ConfirmDialog, {\n                open: openDeleteConfirm,\n                onClose: handleCloseDeleteConfirm,\n                title: t('plt1.details.documents.packingList.item.confirmDeleteDialog.title'),\n                content: t('plt1.details.documents.packingList.item.confirmDeleteDialog.content'),\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                    variant: \"contained\",\n                    color: \"error\",\n                    onClick: handleDeleteItem,\n                    children: t('common.delete')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 587,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 581,\n                columnNumber: 7\n            }, this),\n            renderPartyDialog()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n        lineNumber: 519,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1PackingListForm, \"rQqfRn3lyqnpgHcb7yCWKoSvnLA=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext,\n        src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray\n    ];\n});\n_c = PLT1PackingListForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1PackingListForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx\n"));

/***/ })

});