// src/sections/types/plt1-details.types.ts
import { PartyAddressData } from "src/components/party-address/party-address-form";
import { PLT1OrderStatus } from "../plt1-status";
import { PLT1OrderType } from "../plt1-order-type";

// Use PartyAddressData instead of redefining PartyAddress
export type PartyAddress = PartyAddressData;

// House Air Waybill Interface
export interface PLT1AirHouseAirWaybill {
  id: string | null;
  hawbNumber: string;
  mawbNumber: string;
  grossWeight?: number;
  grossWeightUnit?: string;
  numberOfPieces?: number;
  shipperId?: string;
  shipper?: PartyAddress;
  consigneeId?: string;
  consignee?: PartyAddress;
  t1Orderid: string | null;
}

// Master Air Waybill Interface
export interface PLT1AirMasterAirWaybill {
  id: string | null;
  mawbNumber: string;
  grossWeight?: number;
  grossWeightUnit?: string;
  numberOfPieces?: number;
  t1Orderid: string | null;
}

// CMR Document Interface
export interface PLT1RoadCMR {
  id: string | null;
  cmrNumber: string;
  vehicleNumber: string;
  grossWeight?: number;
  grossWeightUnit?: string;
  numberOfPieces?: number;
  volume?: number;
  shipper?: PartyAddress;
  consignee?: PartyAddress;
  t1Orderid: string | null;
}

// Commercial Invoice Item Interface
export interface PlT1CommercialInvoiceItem {
  id: string | null;
  name?: string;
  description?: string;
  quantity?: number;
  unit?: string;
  value?: number;
  currency?: string;
  hsCode?: string;
  commercialInvoiceid: string | null;
}

// Commercial Invoice Interface
export interface PLT1CommercialInvoice {
  id: string | null;
  invoiceNumber?: string;
  incoterms?: string;
  countryOfOrigin?: string;
  invoiceDate?: string;
  invoiceValue?: number;
  invoiceCurrency?: string;
  shipperId?: string;
  shipper?: PartyAddress;
  consigneeId?: string;
  consignee?: PartyAddress;
  items: PlT1CommercialInvoiceItem[];
  t1Orderid: string | null;
}

// Packing List Item Interface
export interface PLT1PackingListItem {
  id: string | null;
  name?: string;
  modelNumber?: string;
  purchaseOrderNumber?: string;
  commercialInvoiceNumber?: string;
  netWeight?: number;
  netWeightUnit?: string;
  grossWeight?: number;
  grossWeightUnit?: string;
  quantity?: number;
  stockUnit?: string;
  volume?: number;
  packingListId: string | null;
}

// Packing List Total Interface
export interface PLT1PackingListTotal {
  id: string | null;
  totalQuantity?: number;
  totalStockUnit?: string;
  totalNumberOfPackages?: number;
  totalNumberOfPallets?: number;
  totalNetWeight?: number;
  totalNetWeightUnit?: string;
  totalGrossWeight?: number;
  totalGrossWeightUnit?: string;
  totalVolume?: number;
  totalVolumeMeasurementUnit?: string;
  packingListId: string | null;
}

// Packing List Interface
export interface PLT1PackingList {
  id: string | null;
  listSummary: PLT1PackingListItem[];
  listTotal?: PLT1PackingListTotal;
  t1Orderid: string | null;
}

// Notification Of Arrival Interface
export interface PLT1NotificationOfArrival {
  id: string | null;
  identificationNumber?: string;
  flightNumber?: string;
  customsOrigin?: string;
  shipper?: string;
  description?: string;
  storageLocationCode?: string;
  dskTemporaryStorageNumber?: string;
  remarks?: string;
  referenceNumber?: string;
  pieces?: number;
  weight?: string;
  chargedWeight?: number;
  unitOfWeight?: string;
  arrivalDate?: string;
  t1Orderid: string | null;
}

// Transit Item Interface
export interface PLT1TransitItem {
  id: string | null;
  itemNumber?: number;
  packagingType?: string;
  grossWeight?: number;
  netWeight?: number;
  description?: string;
  hsCode?: string;
  t1TransitDocumentid: string | null;
}

// Transit Document Interface
export interface PLT1TransitDocument {
  id: string | null;
  mrnNumber?: string;
  totalWeight: number;
  weightUnit?: string;
  totalPackages: number;

  validityEndDate?: string;
  lrnNumber?: string;
  previousDocument?: string;
  storageLocationCode?: string;
  transportDocument?: string;
  attachedDocument?: string;
  dispatchCountry?: string;
  destinationCustomsOffice?: string;
  transportMeansAtExit?: string;

  shipper?: PartyAddress;
  consignee?: PartyAddress;

  items: PLT1TransitItem[];
  t1Orderid: string | null;
}

// Merchandise Position Interface
export interface PLT1MerchandisePosition {
  id: string | null;
  goodsDescription?: string;
  translatedGoodsDescription?: string;
  packageMarks?: string;
  numberOfPackages?: number;
  packageType?: string;
  packageTypeDescription?: string;
  commodityCode?: string;
  fullTariffCode?: string;
  grossMass?: number;
  grossMassUnit?: string;
  netMass?: number;
  netMassUnit?: string;
  containerNumbers?: string[];
  previousDocuments?: string[];
  merchandisePositionsId: string | null;
}

// Merchandise Positions Totals Interface
export interface PLT1MerchandisePositionsTotals {
  id: string | null;
  totalNumberOfPositions: number;
  totalNumberOfPackages: number;
  totalGrossMass: number;
  totalGrossMassUnit: string;
  totalNetMass: number;
  totalNetMassUnit: string;
  merchandisePositionsId: string | null;
}

// Merchandise Positions Interface (single object containing array of positions)
export interface PLT1MerchandisePositions {
  id: string | null;
  positions: PLT1MerchandisePosition[];
  t1OrderId: string | null;
  totals?: PLT1MerchandisePositionsTotals;
}

// CustomsOffice Interface
export interface PLT1CustomsOffice {
  customsOfficeCode: string;
}

// VehicleRegistration Interface
export interface PLT1VehicleRegistration {
  vehicleRegistrationNumber: string;
  vehicleCountryCode: string;
  trailerRegistrationNumber: string;
  trailerCountryCode: string;
}

// Base Order Details DTO Interface (common fields for all order types)
export interface PLT1OrderDetailsDto {
  id: string | null;
  number: string;
  orderingPartyEmail: string;
  orderDate: string;
  confirmationDate?: string;
  forwardedToTheCustomsOfficeDate?: string;
  completionDate?: string;
  status: PLT1OrderStatus;
  orderType: PLT1OrderType;
  customsOffice?: PLT1CustomsOffice;
  vehicleRegistration?: PLT1VehicleRegistration;
  commercialInvoices: PLT1CommercialInvoice[];
  packingLists: PLT1PackingList[];
  notificationsOfArrivals: PLT1NotificationOfArrival[];
  transitDocuments: PLT1TransitDocument[];
  merchandisePositions?: PLT1MerchandisePositions;
}

// Air Order Details Interface
export interface PLT1AirOrderDetailsDto extends PLT1OrderDetailsDto {
  houseAirWaybills: PLT1AirHouseAirWaybill[];
  masterAirWaybills: PLT1AirMasterAirWaybill[];
}

// Road Order Details Interface
export interface PLT1RoadOrderDetailsDto extends PLT1OrderDetailsDto {
  cmrDocuments: PLT1RoadCMR[];
}

// Main T1Order Interface
export interface PLT1Order extends PLT1OrderDetailsDto {
  // Document collections specific to Air orders - left here for backwards compatibility
  houseAirWaybills: PLT1AirHouseAirWaybill[];
  masterAirWaybills: PLT1AirMasterAirWaybill[];
}

// API Response Interface
export interface PLT1OrderDetailsResponse {
  isSuccessful: boolean;
  errorMessages?: string[];
  data?: PLT1OrderDetailsDto;
}

// Bill of Lading Interface
export interface PLT1SeaBillOfLading {
  id: string | null;
  billOfLadingNumber: string;
  containerNumber: string;
  containerType: string;
  grossWeight?: number;
  grossWeightUnit?: string;
  numberOfPieces?: number;
  volumeMeasurement?: number;
  shipperId?: string;
  shipper?: PartyAddress;
  consigneeId?: string;
  consignee?: PartyAddress;
  plT1SeaOrderId: string | null;
}

// Master Bill of Lading Interface
export interface PLT1SeaWaybill {
  id: string | null;
  seaWaybillNumber: string;
  containerNumber: string;
  containerType: string;
  grossWeight?: number;
  grossWeightUnit?: string;
  numberOfPieces?: number;
  volumeMeasurement?: number;
  shipperId?: string;
  shipper?: PartyAddress;
  consigneeId?: string;
  consignee?: PartyAddress;
  plT1SeaOrderId: string | null;
}

// Sea Order Details Interface
export interface PLT1SeaOrderDetailsDto extends PLT1OrderDetailsDto {
  billsOfLading: PLT1SeaBillOfLading[];
  seaWaybills: PLT1SeaWaybill[];
}