import { EnumConfigMap } from "src/layouts/components/app-data-grid/components/enum-tag/types";

export const PLT1_ORDER_TYPE = {
  Air: 'Air',
  Sea: 'Sea',
  Road: 'Road'
} as const;

export type PLT1OrderType = (typeof PLT1_ORDER_TYPE)[keyof typeof PLT1_ORDER_TYPE];

export const PLT1_ORDER_TYPE_CONFIG: EnumConfigMap = {
  [PLT1_ORDER_TYPE.Air]: {
    color: 'primary',
    icon: 'mingcute:airplane-line',
    translationKey: 'air'
  },
  [PLT1_ORDER_TYPE.Sea]: {
    color: 'info',
    icon: 'mingcute:anchor-line',
    translationKey: 'sea'
  },
  [PLT1_ORDER_TYPE.Road]: {
    color: 'success',
    icon: 'mingcute:truck-line',
    translationKey: 'road'
  }
};