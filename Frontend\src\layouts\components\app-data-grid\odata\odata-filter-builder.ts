import { ODataFilter } from "./odata";
import { formatODataValue } from "./odata-formatting";
import { mapOperator } from "./odata-operators";

export class ODataFilterBuilder {
  private buildEnumFilter(filter: ODataFilter): string {
    const operator = mapOperator(filter.operator);

    if (operator === 'isAnyOf' && Array.isArray(filter.value)) {
      const values = filter.value
        .map(v => `'${v}'`)
        .filter(Boolean);

      if (values.length === 0) return '';

      return `(${values
        .map(v => `${filter.field} eq ${v}`)
        .join(' or ')})`;
    }

    return `${filter.field} eq '${filter.value}'`;
  }

  private buildDateFilter(filter: ODataFilter): string {
    if (filter.operator === 'between' && Array.isArray(filter.value)) {
      const [start, end] = filter.value;
      const conditions: string[] = [];

      if (start) {
        const startDate = new Date(start);
        conditions.push(`${filter.field} ge ${startDate.toISOString()}`);
      }
      if (end) {
        const endDate = new Date(end);
        conditions.push(`${filter.field} le ${endDate.toISOString()}`);
      }

      return conditions.length > 0 ? `(${conditions.join(' and ')})` : '';
    }

    const operator = mapOperator(filter.operator);
    const formattedValue = formatODataValue(filter.value, filter.fieldType);

    if (formattedValue === "null") {
      return operator === 'ne'
        ? `${filter.field} ne null`
        : `${filter.field} eq null`;
    }

    return formattedValue ? `${filter.field} ${operator} ${formattedValue}` : '';
  }

  private buildStandardFilter(filter: ODataFilter): string {
    const operator = mapOperator(filter.operator);

    switch (operator) {
      case 'isEmpty':
        return `${filter.field} eq null`;

      case 'isNotEmpty':
        return `${filter.field} ne null`;

      case 'contains':
        return `contains(${filter.field}, ${formatODataValue(filter.value, filter.fieldType)})`;

      case 'notcontains':
        return `not contains(${filter.field}, ${formatODataValue(filter.value, filter.fieldType)})`;

      case 'startswith':
        return `startswith(${filter.field}, ${formatODataValue(filter.value, filter.fieldType)})`;

      case 'endswith':
        return `endswith(${filter.field}, ${formatODataValue(filter.value, filter.fieldType)})`;

      case 'isAnyOf':
        if (Array.isArray(filter.value)) {
          return `(${filter.value
            .map(v => `${filter.field} eq ${formatODataValue(v, filter.fieldType)}`)
            .join(' or ')})`;
        }
        return '';

      default:
        return `${filter.field} ${operator} ${formatODataValue(filter.value, filter.fieldType)}`;
    }
  }

  public buildFilter(filter: ODataFilter): string {
    switch (filter.fieldType) {
      case 'enum':
        return this.buildEnumFilter(filter);
      case 'date':
        return this.buildDateFilter(filter);
      default:
        return this.buildStandardFilter(filter);
    }
  }
}