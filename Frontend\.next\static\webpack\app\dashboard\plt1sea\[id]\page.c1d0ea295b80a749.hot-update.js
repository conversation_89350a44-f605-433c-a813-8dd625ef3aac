"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1sea/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-merchandise-positions-form.tsx":
/*!****************************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-merchandise-positions-form.tsx ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1MerchandisePositionsForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material/Box */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/Card */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material/Stack */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material_Button__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/material/Button */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/material/Divider */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/CardHeader */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/Typography */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/IconButton */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _mui_material_Table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material/Table */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _mui_material_TableBody__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mui/material/TableBody */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material/TableCell */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _mui_material_TableContainer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material/TableContainer */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _mui_material_TableHead__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/TableHead */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/TableRow */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _mui_material_Paper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/Paper */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _mui_material_Dialog__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/material/Dialog */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _mui_material_DialogTitle__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/material/DialogTitle */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _mui_material_DialogContent__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/material/DialogContent */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _mui_material_DialogActions__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/material/DialogActions */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/Tooltip */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/material/Grid2 */ \"(app-pages-browser)/./node_modules/@mui/material/Grid2/Grid2.js\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/custom-dialog */ \"(app-pages-browser)/./src/components/custom-dialog/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst DEFAULT_POSITION = {\n    id: null,\n    invoicePositionNumber: 0,\n    goodsDescription: '',\n    translatedGoodsDescription: '',\n    packageMarks: '',\n    numberOfPackages: 0,\n    packageType: '',\n    packageTypeDescription: '',\n    commodityCode: '',\n    fullTariffCode: '',\n    grossMass: 0,\n    grossMassUnit: 'kg',\n    netMass: 0,\n    netMassUnit: 'kg',\n    positionValue: 0,\n    positionValueCurrency: 'USD',\n    containerNumbers: [],\n    previousDocuments: [],\n    merchandisePositionsId: ''\n};\nfunction PLT1MerchandisePositionsForm(param) {\n    let { t1OrderId, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_4__.useTranslate)();\n    const { control, watch, getValues } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFormContext)();\n    const fieldArray = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFieldArray)({\n        control,\n        name: 'merchandisePositions.positions'\n    });\n    const merchandisePositions = watch('merchandisePositions');\n    // State for position dialog\n    const [openPositionDialog, setOpenPositionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPositionIndex, setCurrentPositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletePositionIndex, setDeletePositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Handle opening the position dialog for adding a new position\n    const handleAddPosition = ()=>{\n        setCurrentPositionIndex(null);\n        // Add a new position with default values\n        const newPosition = {\n            ...DEFAULT_POSITION,\n            merchandisePositionsId: (merchandisePositions === null || merchandisePositions === void 0 ? void 0 : merchandisePositions.id) || ''\n        };\n        fieldArray.append(newPosition);\n        setCurrentPositionIndex(fieldArray.fields.length); // Set to the new index\n        setOpenPositionDialog(true);\n    };\n    // Handle opening the position dialog for editing an existing position\n    const handleEditPosition = (index)=>{\n        setCurrentPositionIndex(index);\n        setOpenPositionDialog(true);\n    };\n    // Handle closing the position dialog\n    const handleClosePositionDialog = ()=>{\n        setOpenPositionDialog(false);\n        // If we were adding a new position and user cancels, remove the empty position\n        if (currentPositionIndex !== null && currentPositionIndex === fieldArray.fields.length - 1) {\n            const position = getValues(\"merchandisePositions.positions.\".concat(currentPositionIndex));\n            // Check if it's an empty position (all fields are default values)\n            const isEmpty = !position.goodsDescription && !position.translatedGoodsDescription && !position.packageMarks && !position.commodityCode && !position.fullTariffCode && position.numberOfPackages === 0 && position.grossMass === 0 && position.netMass === 0;\n            if (isEmpty) {\n                fieldArray.remove(currentPositionIndex);\n            }\n        }\n        setCurrentPositionIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeletePositionIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deletePositionIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting a position\n    const handleDeletePosition = ()=>{\n        if (deletePositionIndex !== null) {\n            fieldArray.remove(deletePositionIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving a position (just close the dialog since form is already updated)\n    const handleSavePosition = ()=>{\n        handleClosePositionDialog();\n    };\n    // Render the position form in the dialog - using React Hook Form fields\n    const renderPositionForm = ()=>{\n        if (currentPositionIndex === null) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".invoicePositionNumber\"),\n                    label: t('plt1.details.documents.merchandisePositions.form.invoicePositionNumber'),\n                    type: \"number\",\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 3,\n                        pt: 2,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(2, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".goodsDescription\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.goodsDescription'),\n                            multiline: true,\n                            rows: 2,\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".translatedGoodsDescription\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.translatedGoodsDescription'),\n                            multiline: true,\n                            rows: 2,\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 2.5,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(3, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".numberOfPackages\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.numberOfPackages'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".packageType\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.packageType'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".packageTypeDescription\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.packageTypeDescription'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".packageMarks\"),\n                    label: t('plt1.details.documents.merchandisePositions.form.packageMarks'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 3,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(2, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".commodityCode\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.commodityCode'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".fullTariffCode\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.fullTariffCode'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 3,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(4, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".grossMass\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.grossMass'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".grossMassUnit\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.grossMassUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".netMass\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.netMass'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".netMassUnit\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.netMassUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 2.5,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(2, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".positionValue\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.positionValue'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".positionValueCurrency\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.positionValueCurrency'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the positions table\n    const renderPositionsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableContainer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            component: _mui_material_Paper__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Table__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"merchandise positions table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableHead__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.invoicePositionNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.goodsDescription')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.numberOfPackages')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.packageType')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.grossMass')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.netMass')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.positionValue')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableBody__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        children: fieldArray.fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                colSpan: 8,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.merchandisePositions.noPositions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 13\n                        }, this) : fieldArray.fields.map((field, index)=>{\n                            const position = getValues(\"merchandisePositions.positions.\".concat(index)) || {};\n                            var _position_invoicePositionNumber, _position_numberOfPackages, _position_grossMass, _position_netMass, _position_positionValue;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: (_position_invoicePositionNumber = position.invoicePositionNumber) !== null && _position_invoicePositionNumber !== void 0 ? _position_invoicePositionNumber : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                variant: \"body2\",\n                                                sx: {\n                                                    maxWidth: 200\n                                                },\n                                                children: position.goodsDescription || '-'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 21\n                                            }, this),\n                                            position.translatedGoodsDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                variant: \"caption\",\n                                                color: \"text.secondary\",\n                                                sx: {\n                                                    display: 'block',\n                                                    mt: 0.5\n                                                },\n                                                children: position.translatedGoodsDescription\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: (_position_numberOfPackages = position.numberOfPackages) !== null && _position_numberOfPackages !== void 0 ? _position_numberOfPackages : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: position.packageTypeDescription || position.packageType || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_position_grossMass = position.grossMass) !== null && _position_grossMass !== void 0 ? _position_grossMass : '-',\n                                            \" \",\n                                            position.grossMassUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_position_netMass = position.netMass) !== null && _position_netMass !== void 0 ? _position_netMass : '-',\n                                            \" \",\n                                            position.netMassUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_position_positionValue = position.positionValue) !== null && _position_positionValue !== void 0 ? _position_positionValue : '-',\n                                            \" \",\n                                            position.positionValueCurrency || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditPosition(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deletePositionIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 326,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n            lineNumber: 325,\n            columnNumber: 5\n        }, this);\n    // Render the totals summary\n    const renderTotalsSummary = ()=>{\n        const totals = merchandisePositions === null || merchandisePositions === void 0 ? void 0 : merchandisePositions.totals;\n        if (!totals) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            sx: {\n                boxShadow: 'none',\n                mb: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    title: t('plt1.details.documents.merchandisePositions.form.totalsTitle')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 425,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    sx: {\n                        borderStyle: 'dashed'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 428,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalNumberOfPositions')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: totals.totalNumberOfPositions || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalNumberOfPackages')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: totals.totalNumberOfPackages || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalGrossMass')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: [\n                                                totals.totalGrossMass || 0,\n                                                \" \",\n                                                totals.totalGrossMassUnit || ''\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalNetMass')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: [\n                                                totals.totalNetMass || 0,\n                                                \" \",\n                                                totals.totalNetMassUnit || ''\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalPositionsValue')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: [\n                                                totals.totalPositionsValue || 0,\n                                                \" \",\n                                                totals.totalPositionsValueCurrency || ''\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n            lineNumber: 424,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        spacing: 3,\n        children: [\n            renderTotalsSummary(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        title: t('plt1.details.documents.merchandisePositions.form.positionsTitle'),\n                        action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            variant: \"contained\",\n                            size: \"small\",\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                icon: \"eva:plus-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: handleAddPosition,\n                            sx: {\n                                mb: 2\n                            },\n                            disabled: readOnly,\n                            children: t('plt1.details.documents.merchandisePositions.addPosition')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 509,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: [\n                            renderPositionsTable(),\n                            fieldArray.fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                sx: {\n                                    mt: 2,\n                                    display: 'flex',\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    variant: \"soft\",\n                                    color: \"primary\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                        icon: \"eva:plus-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    onClick: handleAddPosition,\n                                    disabled: readOnly,\n                                    children: t('plt1.details.documents.merchandisePositions.addPosition')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 493,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Dialog__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                open: openPositionDialog,\n                onClose: handleClosePositionDialog,\n                maxWidth: \"md\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_DialogTitle__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        children: currentPositionIndex === null ? t('plt1.details.documents.merchandisePositions.addPosition') : t('plt1.details.documents.merchandisePositions.editPosition')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_DialogContent__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        children: renderPositionForm()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 541,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_DialogActions__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                onClick: handleClosePositionDialog,\n                                children: t('common.cancel')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 545,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                variant: \"contained\",\n                                onClick: handleSavePosition,\n                                children: t('common.save')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 544,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 530,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__.ConfirmDialog, {\n                open: openDeleteConfirm,\n                onClose: handleCloseDeleteConfirm,\n                title: t('plt1.details.documents.merchandisePositions.confirmDeleteDialog.title'),\n                content: t('plt1.details.documents.merchandisePositions.confirmDeleteDialog.content'),\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                    variant: \"contained\",\n                    color: \"error\",\n                    onClick: handleDeletePosition,\n                    children: t('common.delete')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 561,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 555,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n        lineNumber: 488,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1MerchandisePositionsForm, \"rzlgvldrAdFsKfES/DPx4X+02i8=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_4__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFormContext,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFieldArray\n    ];\n});\n_c = PLT1MerchandisePositionsForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1MerchandisePositionsForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-merchandise-positions-form.tsx\n"));

/***/ })

});