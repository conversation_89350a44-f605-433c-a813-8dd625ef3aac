// src/sections/plt1-order-details-base/PLT1OrderDetailsBase.tsx
import { useState, useRef, useEffect, ReactNode, useCallback } from 'react';
import { useFormContext } from 'react-hook-form';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid2';
import LoadingButton from '@mui/lab/LoadingButton';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';
import { useTranslate } from 'src/locales';
import { DashboardContent } from 'src/layouts/dashboard';
import { Iconify } from 'src/components/iconify';
import { EnumTag } from 'src/layouts/components/app-data-grid/components/enum-tag/EnumTag';
import { formatters } from 'src/lib/formatters';
import PLT1OrderFileManagement from '../components/plt1-file-management';
import { PLT1FileContentPreview } from '../components/plt1-file-content-preview';
import { PLT1FileListSidebar, PLT1FileListSidebarRef } from '../components/plt1-file-list-sidebar';
import { fileCache } from '../components/plt1-file-cache';
import { PLT1_ORDER_STATUS, PLT1_ORDER_STATUS_CONFIG, PLT1OrderStatus } from '../plt1-status';
import PLT1OtherDataTabs from 'src/sections/plt1-other-data-tabs';
import { PLT1OrderDetailsDto } from '../types/plt1-details.types';
import { axiosInstance, endpoints } from 'src/lib/axios';
import { PLT1OrderFile } from '../components/plt1-file-management';
import useSWR from 'swr';
import { PLT1_ORDER_TYPE_CONFIG, PLT1OrderType } from '../plt1-order-type';

// ----------------------------------------------------------------------

export interface PLT1OrderDetailsBaseProps<TFormValues> {
  orderId: string;
  readOnly?: boolean;
  isLoading: boolean;
  error?: Error | null;
  order?: PLT1OrderDetailsDto | null;
  orderStatus?: PLT1OrderStatus | null;
  orderNumber?: string | null;
  statusError?: string | null;
  onSaveOrder: (formData: TFormValues) => Promise<void>;
  documentTabs: ReactNode;
  formChanged: boolean;
  isSaving: boolean;
  onCancel: () => void;
  isValid: boolean;
  // Optional download functionality
  downloadEndpoint?: string;
  downloadFileName?: string;
  showDownloadButton?: boolean;
  // SADEC TXT export functionality
  sadecTxtEndpoint?: string;
  sadecTxtFileName?: string;
  showSadecTxtButton?: boolean;
}

export function PLT1OrderDetailsBase<TFormValues extends Record<string, any>>({
  orderId,
  readOnly: propReadOnly = false,
  isLoading,
  error,
  order,
  orderStatus,
  orderNumber,
  statusError,
  onSaveOrder,
  documentTabs,
  formChanged,
  isSaving,
  onCancel,
  isValid,
  downloadEndpoint,
  downloadFileName,
  showDownloadButton = false,
  sadecTxtEndpoint,
  sadecTxtFileName,
  showSadecTxtButton = false,
}: PLT1OrderDetailsBaseProps<TFormValues>) {
  const { t } = useTranslate();
  const router = useRouter();

  const handleBack = (): void => {
    router.push(paths.dashboard.plt1.list);
  };

  // State management
  const [splitViewActive, setSplitViewActive] = useState(false);
  const [orderFiles, setOrderFiles] = useState<PLT1OrderFile[]>([]);
  const [selectedFile, setSelectedFile] = useState<PLT1OrderFile | null>(null);
  const fileListRef = useRef<PLT1FileListSidebarRef>(null);

  const methods = useFormContext<TFormValues>();

  // Determine if the form should be read-only
  // Form is read-only if explicitly set to read-only or if the order status is "Scanning" or "MerchandisePositionGeneration"
  const isScanning = orderStatus === PLT1_ORDER_STATUS.Scanning;
  const isMerchandisePositionGeneration = orderStatus === PLT1_ORDER_STATUS.MerchandisePositionGeneration;
  const readOnly = propReadOnly || isScanning || isMerchandisePositionGeneration;

  // Handle save order
  const handleSaveOrderSubmit = async (formData: TFormValues) => {
    await onSaveOrder(formData);
  };

  // Handle download XLSX
  const handleDownloadXlsx = async () => {
    if (!downloadEndpoint || !orderId || isDownloading) return;

    setIsDownloading(true);
    try {
      const response = await axiosInstance.get(`${downloadEndpoint}/${orderId}/download/xlsx`, {
        responseType: 'blob',
      });

      // Create blob URL and trigger download
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = downloadFileName || `plt1-order-${orderId}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading XLSX file:', error);
      // You might want to show a toast error here
    } finally {
      setIsDownloading(false);
    }
  };

  // Handle download SADEC TXT
  const handleDownloadSadecTxt = async () => {
    if (!sadecTxtEndpoint || !orderId || isDownloadingSadecTxt) return;

    setIsDownloadingSadecTxt(true);
    try {
      const response = await axiosInstance.get(`${sadecTxtEndpoint}/${orderId}/download/sadec-txt`, {
        responseType: 'blob',
      });

      // Create blob URL and trigger download
      const blob = new Blob([response.data], {
        type: 'text/plain'
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = sadecTxtFileName || `plt1-order-${orderId}-sadec.txt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading SADEC TXT file:', error);
      // You might want to show a toast error here
    } finally {
      setIsDownloadingSadecTxt(false);
    }
  };

  // State to track if files are being prefetched
  const [isPrefetching, setIsPrefetching] = useState(false);

  // State for download functionality
  const [isDownloading, setIsDownloading] = useState(false);
  const [isDownloadingSadecTxt, setIsDownloadingSadecTxt] = useState(false);

  // SWR fetcher function
  const filesFetcher = useCallback(async (url: string) => {
    const response = await axiosInstance.get(url);
    return response.data.data || [];
  }, []);

  // Use SWR to fetch and refresh order files every 10 seconds
  const {
    data: files,
    mutate: refreshFiles,
    isValidating,
  } = useSWR(`${endpoints.plt1.filePreviews}/${orderId}`, filesFetcher, {
    refreshInterval: 10000, // Refresh every 10 seconds
    revalidateOnFocus: true,
    dedupingInterval: 5000, // Deduplicate requests within 5 seconds
    onSuccess: (data) => {
      setOrderFiles(data);
      // Only prefetch if there are files
      if (data && data.length > 0) {
        prefetchFiles(data);
      } else {
        // Reset prefetching state if there are no files
        setIsPrefetching(false);
      }
    },
  });

  // Update isPrefetching state based on SWR validation state
  useEffect(() => {
    if (isValidating && !isPrefetching) {
      setIsPrefetching(true);
    } else if (!isValidating && isPrefetching && (!files || files.length === 0)) {
      // Reset prefetching state when validation completes and there are no files
      setIsPrefetching(false);
    }
  }, [isValidating, isPrefetching, files]);

  // Prefetch files with different priorities
  const prefetchFiles = useCallback(
    async (files: PLT1OrderFile[]) => {
      // Early return with state reset if no files
      if (!files || files.length === 0) {
        setIsPrefetching(false);
        return;
      }

      setIsPrefetching(true);

      try {
        // Try to get the last viewed file ID from localStorage
        const lastViewedFileId = localStorage.getItem(`plt1-last-viewed-file-${orderId}`);

        if (lastViewedFileId) {
          // Find the last viewed file and prefetch it with highest priority
          const lastViewedFile = files.find((file: PLT1OrderFile) => file.id === lastViewedFileId);
          if (lastViewedFile) {
            // Prefetch the last viewed file with highest priority (10)
            await fileCache.prefetchFile(lastViewedFile, 10);
          }
        }

        // Prefetch the first few files with high priority (5)
        const highPriorityFiles = files.slice(0, Math.min(5, files.length));
        if (highPriorityFiles.length > 0) {
          await fileCache.prefetchFiles(highPriorityFiles, 5);
        }

        // Prefetch all remaining files with normal priority (1)
        const remainingFiles = files.slice(Math.min(5, files.length));
        if (remainingFiles.length > 0) {
          await fileCache.prefetchFiles(remainingFiles, 1);
        }
      } catch (error) {
        console.error('Error prefetching files:', error);
      } finally {
        setIsPrefetching(false);
      }
    },
    [orderId]
  );

  // Set initial files when SWR loads them
  useEffect(() => {
    if (files) {
      setOrderFiles(files);
    }
  }, [files]);

  // Function to manually refresh files
  const handleRefreshFiles = useCallback(() => {
    refreshFiles();
  }, [refreshFiles]);

  // Clean up cache when component unmounts
  useEffect(() => {
    return () => {
      fileCache.clearCache();
    };
  }, []);

  // Select the first file or the last viewed file
  const selectFirstOrLastViewedFile = useCallback(() => {
    if (orderFiles.length === 0) return;

    // Try to get the last viewed file ID from localStorage
    const lastViewedFileId = localStorage.getItem(`plt1-last-viewed-file-${orderId}`);

    if (lastViewedFileId) {
      // Find the file with the saved ID
      const lastFile = orderFiles.find((file: PLT1OrderFile) => file.id === lastViewedFileId);
      if (lastFile) {
        setSelectedFile(lastFile);
        return;
      }
    }

    // If no last viewed file or it's not found, select the first file
    setSelectedFile(orderFiles[0]);
  }, [orderId, orderFiles]);

  // Select first file or last viewed file when files are loaded and no file is selected
  useEffect(() => {
    if (orderFiles.length > 0 && !selectedFile && splitViewActive) {
      selectFirstOrLastViewedFile();
    }
  }, [orderFiles, selectedFile, splitViewActive, selectFirstOrLastViewedFile]);

  // Focus the file list and prefetch files when the split view is activated
  useEffect(() => {
    if (splitViewActive) {
      // If there are no files, close the split view
      if (orderFiles.length === 0) {
        setSplitViewActive(false);
        setIsPrefetching(false);
        return;
      }

      // Focus the file list
      if (fileListRef.current) {
        // Use a small timeout to ensure the DOM is fully rendered
        const timeoutId = setTimeout(() => {
          fileListRef.current?.focus();
        }, 100);

        return () => clearTimeout(timeoutId);
      }

      // Prefetch all files in the background
      prefetchFiles(orderFiles);
    }
  }, [splitViewActive, orderFiles, prefetchFiles]);

  // Handle closing file preview split view
  const handleCloseFilePreview = () => {
    setSplitViewActive(false);
    setSelectedFile(null);
  };

  // Handle keyboard shortcut (Ctrl+Q) to toggle file preview split view
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Check if Ctrl+Q is pressed
      if ((event.ctrlKey || event.metaKey) && event.key === 'q') {
        event.preventDefault(); // Prevent any default browser action

        // If split view is active, always allow closing it
        if (splitViewActive) {
          setSplitViewActive(false);
          setSelectedFile(null);
        }
        // Only open the split view if there are files to preview
        else if (orderFiles.length > 0) {
          setSplitViewActive(true);

          // If no file is selected, select the first file or last viewed file
          if (!selectedFile) {
            selectFirstOrLastViewedFile();
          }
        }
      }
    };

    // Add event listener
    window.addEventListener('keydown', handleKeyDown);

    // Remove event listener on cleanup
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [splitViewActive, orderFiles, orderId, selectedFile, selectFirstOrLastViewedFile]);

  // Handle selecting a file to preview
  const handleSelectFile = (file: PLT1OrderFile) => {
    setSelectedFile(file);
    setSplitViewActive(true);

    // Save the selected file ID to localStorage for future reference
    localStorage.setItem(`plt1-last-viewed-file-${orderId}`, file.id);
  };

  if (isLoading) {
    return (
      <DashboardContent>
        <Container>
          <Box
            sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '60vh' }}
          >
            <CircularProgress />
          </Box>
        </Container>
      </DashboardContent>
    );
  }

  if (error || !order) {
    return (
      <DashboardContent>
        <Container>
          <Box sx={{ textAlign: 'center', py: 5 }}>
            <Typography variant="h6" color="error" sx={{ mb: 2 }}>
              {t('plt1.details.notFound')}
            </Typography>
            <Button
              variant="contained"
              onClick={handleBack}
              startIcon={<Iconify icon="eva:arrow-back-fill" />}
            >
              {t('common.back')}
            </Button>
          </Box>
        </Container>
      </DashboardContent>
    );
  }

  return (
    <DashboardContent fullWidth={splitViewActive}>
      <Container maxWidth={splitViewActive ? false : 'lg'} disableGutters={splitViewActive}>
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          sx={{ mb: 3, mt: 2 }}
        >
          <Stack direction="row" alignItems="center" spacing={2}>
            <IconButton
              onClick={handleBack}
              sx={{
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1,
              }}
            >
              <Iconify icon="eva:arrow-back-fill" />
            </IconButton>
            <EnumTag
              value={order?.orderType as PLT1OrderType}
              config={PLT1_ORDER_TYPE_CONFIG}
              translationPrefix="plt1.type"
            />
            <Typography variant="h4">
              {orderNumber || order?.number || t('plt1.details.orderDetails')}
            </Typography>
          </Stack>

          <Stack direction="row" spacing={2}>
            <Box sx={{ display: { xs: 'none', sm: 'flex' }, justifyContent: 'center' }}>
              <Tooltip
                title={
                  orderFiles.length > 0
                    ? isPrefetching
                      ? t('plt1.details.filePreview.prefetchingFiles', {
                          defaultValue: 'Prefetching files...',
                        })
                      : t('plt1.details.filePreview.shortcutHint', {
                          defaultValue: 'Press Ctrl+Q to toggle preview',
                        })
                    : t('plt1.details.filePreview.noFiles', {
                        defaultValue: 'No files available to preview',
                      })
                }
                arrow
              >
                <span>
                  {isPrefetching ? (
                    <Button
                      variant="outlined"
                      color="primary"
                      startIcon={<CircularProgress size={16} />}
                      disabled
                    >
                      {t('plt1.details.filePreview.prefetchingFiles', {
                        defaultValue: 'Prefetching files...',
                      })}
                    </Button>
                  ) : (
                    <Button
                      variant="outlined"
                      color="primary"
                      startIcon={<Iconify icon="eva:eye-fill" />}
                      onClick={() => {
                        if (splitViewActive) {
                          setSplitViewActive(false);
                          setSelectedFile(null);
                        } else if (orderFiles.length > 0) {
                          setSplitViewActive(true);
                          if (!selectedFile) {
                            selectFirstOrLastViewedFile();
                          }
                        }
                      }}
                      disabled={!splitViewActive && orderFiles.length === 0}
                    >
                      {splitViewActive
                        ? t('plt1.details.filePreview.closePreview')
                        : t('plt1.details.filePreview.openPreview')}
                    </Button>
                  )}
                </span>
              </Tooltip>
            </Box>
          </Stack>
        </Stack>

        {(isScanning || isMerchandisePositionGeneration) && (
          <Box sx={{ mb: 3, p: 2, bgcolor: 'warning.lighter', borderRadius: 1 }}>
            <Typography variant="subtitle1" color="warning.darker">
              <Iconify icon="eva:alert-triangle-fill" sx={{ mr: 1, verticalAlign: 'middle' }} />
              {isScanning
                ? t('plt1.details.scanningInProgress')
                : t('plt1.details.merchandisePositionGenerationInProgress')
              }
            </Typography>
          </Box>
        )}

        <form onSubmit={methods.handleSubmit(handleSaveOrderSubmit)}>
          <Grid container spacing={3}>
            {/* Order Details Card and File Management - Hidden when split view is active */}
            {!splitViewActive && (
              <>
                {/* Order Details Card */}
                <Grid size={{ xs: 12, md: 5 }}>
                  <Card sx={{ mb: 3 }}>
                    <Stack sx={{ p: 3, pb: 2 }} spacing={2}>
                      <Stack direction="row" justifyContent="space-between" alignItems="center">
                        <Typography variant="h6">{t('plt1.details.orderInfo.heading')}</Typography>
                        <EnumTag
                          value={orderStatus || (order?.status as PLT1OrderStatus)}
                          config={PLT1_ORDER_STATUS_CONFIG}
                          translationPrefix="plt1.status"
                        />
                      </Stack>
                      <Divider sx={{ borderStyle: 'dashed' }} />

                      <Stack spacing={1.5}>
                        <Stack direction="row" justifyContent="space-between">
                          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                            {t('plt1.details.orderInfo.orderingPartyEmail')}
                          </Typography>
                          <Typography variant="body2">{order.orderingPartyEmail}</Typography>
                        </Stack>

                        <Stack direction="row" justifyContent="space-between">
                          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                            {t('plt1.details.orderInfo.orderDate')}
                          </Typography>
                          <Typography variant="body2">
                            {order.orderDate ? formatters.dateTimeOffset(order.orderDate) : '-'}
                          </Typography>
                        </Stack>

                        <Stack direction="row" justifyContent="space-between">
                          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                            {t('plt1.details.orderInfo.confirmationDate')}
                          </Typography>
                          <Typography variant="body2">
                            {order.confirmationDate
                              ? formatters.dateTimeOffset(order.confirmationDate)
                              : '-'}
                          </Typography>
                        </Stack>

                        <Stack direction="row" justifyContent="space-between">
                          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                            {t('plt1.details.orderInfo.forwardedDate')}
                          </Typography>
                          <Typography variant="body2">
                            {order.forwardedToTheCustomsOfficeDate
                              ? formatters.dateTimeOffset(order.forwardedToTheCustomsOfficeDate)
                              : '-'}
                          </Typography>
                        </Stack>

                        <Stack direction="row" justifyContent="space-between">
                          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                            {t('plt1.details.orderInfo.completionDate')}
                          </Typography>
                          <Typography variant="body2">
                            {order.completionDate
                              ? formatters.dateTimeOffset(order.completionDate)
                              : '-'}
                          </Typography>
                        </Stack>
                      </Stack>

                      {/* Download Buttons */}
                      {(showDownloadButton || showSadecTxtButton) && orderStatus === PLT1_ORDER_STATUS.Scanned && (
                        <>
                          <Divider sx={{ borderStyle: 'dashed' }} />
                          <Stack direction="row" spacing={2} sx={{ justifyContent: 'center', pt: 1 }}>
                            {/* Download XLSX Button */}
                            {showDownloadButton && (
                              <LoadingButton
                                variant="contained"
                                color="success"
                                size="medium"
                                loading={isDownloading}
                                startIcon={<Iconify icon="eva:download-fill" />}
                                onClick={handleDownloadXlsx}
                                disabled={isDownloading}
                                sx={{ minWidth: 140 }}
                              >
                                {t('plt1.details.downloadXlsx', { defaultValue: 'Download XLSX' })}
                              </LoadingButton>
                            )}

                            {/* Download SADEC TXT Button */}
                            {showSadecTxtButton && (
                              <LoadingButton
                                variant="contained"
                                color="info"
                                size="medium"
                                loading={isDownloadingSadecTxt}
                                startIcon={<Iconify icon="eva:file-text-fill" />}
                                onClick={handleDownloadSadecTxt}
                                disabled={isDownloadingSadecTxt}
                                sx={{ minWidth: 140 }}
                              >
                                {t('plt1.details.downloadSadecTxt', { defaultValue: 'Download SADEC' })}
                              </LoadingButton>
                            )}
                          </Stack>
                        </>
                      )}
                    </Stack>
                  </Card>
                </Grid>

                {/* PLT1 order files management */}
                <Grid size={{ xs: 12, md: 7 }}>
                  <Stack spacing={2}>
                    <PLT1OrderFileManagement
                      orderId={orderId}
                      orderType={order?.orderType}
                      hasPendingChanges={formChanged}
                      isScanning={isScanning || isMerchandisePositionGeneration}
                    />
                  </Stack>
                </Grid>
              </>
            )}

            {/* Documents Tab Section or Split View */}
            <Grid size={{ xs: 12, md: 12 }}>
              {splitViewActive ? (
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    height: 'calc(100vh - 150px)',
                    mb: 3,
                    mt: 1,
                  }}
                >
                  {/* Left side: PLT1DocumentsTabs (40% width) */}
                  <Box sx={{ width: '40%', pr: 2, overflow: 'auto' }}>{documentTabs}</Box>

                  {/* Right side: File Preview (60% width) */}
                  <Box
                    sx={{
                      width: '60%',
                      display: 'flex',
                      flexDirection: 'column',
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 1,
                      overflow: 'hidden',
                    }}
                  >
                    {/* File Preview Header */}
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        p: 2,
                        borderBottom: '1px solid',
                        borderColor: 'divider',
                      }}
                    >
                      <Box>
                        <Typography variant="h6" component="div">
                          {t('plt1.details.filePreview.title')}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {t('plt1.details.filePreview.shortcutHint', {
                            defaultValue: 'Press Ctrl+Q to toggle preview',
                          })}
                        </Typography>
                      </Box>
                      <Stack direction="row" spacing={1}>
                        <IconButton
                          edge="end"
                          color="inherit"
                          onClick={handleCloseFilePreview}
                          aria-label="close"
                        >
                          <Iconify icon="eva:close-fill" />
                        </IconButton>
                      </Stack>
                    </Box>

                    {/* File Preview Content */}
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: { xs: 'column', md: 'row' },
                        flexGrow: 1,
                        overflow: 'hidden',
                      }}
                    >
                      {/* File preview area */}
                      <Box
                        sx={{
                          flexGrow: 1,
                          display: 'flex',
                          alignItems: 'stretch',
                          justifyContent: 'stretch',
                          p: 0,
                          overflow: 'hidden',
                          width: '100%',
                        }}
                      >
                        <PLT1FileContentPreview file={selectedFile} />
                      </Box>
                      {/* File list sidebar */}
                      <PLT1FileListSidebar
                        ref={fileListRef}
                        files={orderFiles}
                        selectedFile={selectedFile}
                        onSelectFile={handleSelectFile}
                        autoFocus={true}
                      />
                    </Box>
                  </Box>
                </Box>
              ) : (
                <>
                  {documentTabs}
                  <Box sx={{ mt: 3 }}>
                    <PLT1OtherDataTabs readOnly={readOnly} />
                  </Box>
                </>
              )}
            </Grid>
          </Grid>
          {/* Bottom Save Bar - only shown when form has changed */}
          {!readOnly && formChanged && (
            <Box
              sx={{
                position: 'fixed',
                bottom: 0,
                left: 0,
                right: 0,
                py: 2,
                px: 3,
                bgcolor: 'background.paper',
                borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                zIndex: (theme) => theme.zIndex.appBar - 1,
                boxShadow: (theme) => theme.shadows[3],
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-end',
              }}
            >
              <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
                {t('plt1.details.unsavedChanges')}
              </Typography>
              <Stack direction="row" spacing={2}>
                <Button
                  color="inherit"
                  variant="outlined"
                  onClick={onCancel}
                  disabled={isSaving}
                >
                  {t('common.cancel')}
                </Button>
                <LoadingButton
                  variant="contained"
                  color="primary"
                  loading={isSaving}
                  type="submit"
                  disabled={!isValid}
                  startIcon={<Iconify icon="eva:save-fill" />}
                >
                  {t('common.save')}
                </LoadingButton>
              </Stack>
            </Box>
          )}
        </form>
      </Container>
    </DashboardContent>
  );
}
