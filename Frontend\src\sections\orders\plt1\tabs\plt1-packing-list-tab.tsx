'use client';

import { useRef, useState, useEffect } from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';

import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { useTranslate } from 'src/locales';
import PLT1DocumentTabBase from './plt1-document-tab-base';
import PLT1PackingListForm, { PLT1PackingListData } from '../forms/plt1-packing-list-form';
import { PLT1Order } from '../types/plt1-details.types';

// ----------------------------------------------------------------------

interface PLT1PackingListsTabProps {
  t1OrderId: string;
  order?: PLT1Order;
  readOnly?: boolean;
}

interface FormValues {
  packingLists: PLT1PackingListData[];
}

export default function PLT1PackingListsTab({
  t1OrderId,
  order,
  readOnly = false,
}: PLT1PackingListsTabProps) {
  const { t } = useTranslate();
  const { control } = useFormContext<FormValues>();

  // Try to restore the expanded state from sessionStorage
  const [expandedPackingList, setExpandedPackingList] = useState<number | null>(() => {
    const saved = sessionStorage.getItem(`plt1-expanded-packing-list-${t1OrderId}`);
    return saved ? parseInt(saved, 10) : null;
  });

  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const deleteButtonRef = useRef<HTMLButtonElement>(null);

  const fieldArray = useFieldArray({
    control,
    name: 'packingLists',
  });

  // Save expanded state to sessionStorage whenever it changes
  useEffect(() => {
    if (expandedPackingList !== null) {
      sessionStorage.setItem(
        `plt1-expanded-packing-list-${t1OrderId}`,
        expandedPackingList.toString()
      );
    } else {
      sessionStorage.removeItem(`plt1-expanded-packing-list-${t1OrderId}`);
    }
  }, [expandedPackingList, t1OrderId]);

  const handleAddPackingList = (): void => {
    const newPackingList: PLT1PackingListData = {
      id: undefined,
      t1OrderId,
      packingListPositions: [],
      listTotal: {
        id: undefined,
        shipmentOrPackingId: '',
        totalQuantity: 0,
        totalPackagesUnit: 'CTNS',
        totalNumberOfPackages: 0,
        totalNumberOfPallets: 0,
        totalNetWeight: 0,
        totalNetWeightUnit: 'KGM',
        totalGrossWeight: 0,
        totalGrossWeightUnit: 'KGM',
        totalVolume: 0,
        totalVolumeMeasurementUnit: 'CBM',
        packingListId: undefined,
      },
    };

    fieldArray.append(newPackingList);
    setExpandedPackingList(fieldArray.fields.length);
  };

  const handleToggleExpand = (index: number): void => {
    setExpandedPackingList(expandedPackingList === index ? null : index);
  };

  const handleOpenConfirm = (index: number): void => {
    setDeleteIndex(index);
    setOpenConfirm(true);
  };

  const handleCloseConfirm = (): void => {
    setOpenConfirm(false);
    if (deleteIndex !== null) {
      deleteButtonRef.current?.focus();
    }
  };

  const handleDeletePackingList = (): void => {
    if (deleteIndex !== null) {
      fieldArray.remove(deleteIndex);
      if (expandedPackingList === deleteIndex) {
        setExpandedPackingList(null);
      }
    }
    handleCloseConfirm();
  };

  // Render preview of the packing list item when collapsed
  const renderPreview = (packingList: any, _index: number) => {
    // Calculate total packed items across all positions
    const totalPackedItems = packingList.packingListPositions?.reduce(
      (total: number, position: any) => total + (position.packedItems?.length || 0),
      0
    ) || 0;

    return (
      <Stack
        direction="row"
        spacing={2}
        sx={{
          px: 3,
          pb: 2,
          display: 'flex',
          flexWrap: 'wrap',
          '& > *': { mr: 3, mb: 1 },
        }}
      >
        {/* Show total packed items */}
        {totalPackedItems > 0 && (
          <Typography variant="body2" color="text.secondary">
            {t('common.itemsCount')}: {totalPackedItems}
          </Typography>
        )}

        {/* Show total packages if available */}
        {packingList.listTotal?.totalNumberOfPackages !== undefined &&
          packingList.listTotal?.totalNumberOfPackages !== null &&
          packingList.listTotal?.totalNumberOfPackages > 0 && (
            <Typography variant="body2" color="text.secondary">
              {t('plt1.details.documents.packingList.total.totalNumberOfPackages')}:{' '}
              {packingList.listTotal.totalNumberOfPackages}{' '}
              {packingList.listTotal.totalPackagesUnit || ''}
            </Typography>
          )}

        {/* Show total pallets if available */}
        {packingList.listTotal?.totalNumberOfPallets !== undefined &&
          packingList.listTotal?.totalNumberOfPallets !== null &&
          packingList.listTotal?.totalNumberOfPallets > 0 && (
            <Typography variant="body2" color="text.secondary">
              {t('plt1.details.documents.packingList.total.totalNumberOfPallets')}:{' '}
              {packingList.listTotal.totalNumberOfPallets}
            </Typography>
          )}

        {/* Show total gross weight if available */}
        {packingList.listTotal?.totalGrossWeight !== undefined &&
          packingList.listTotal?.totalGrossWeight !== null &&
          packingList.listTotal?.totalGrossWeight > 0 && (
            <Typography variant="body2" color="text.secondary">
              {t('plt1.details.documents.packingList.total.totalGrossWeight')}:{' '}
              {packingList.listTotal.totalGrossWeight}{' '}
              {packingList.listTotal.totalGrossWeightUnit || 'KGM'}
            </Typography>
          )}

        {/* Show total net weight if available */}
        {packingList.listTotal?.totalNetWeight !== undefined &&
          packingList.listTotal?.totalNetWeight !== null &&
          packingList.listTotal?.totalNetWeight > 0 && (
            <Typography variant="body2" color="text.secondary">
              {t('plt1.details.documents.packingList.total.totalNetWeight')}:{' '}
              {packingList.listTotal.totalNetWeight}{' '}
              {packingList.listTotal.totalNetWeightUnit || 'KGM'}
            </Typography>
          )}

        {/* Show shipment/packing ID if available */}
        {packingList.listTotal?.shipmentOrPackingId && (
          <Typography variant="body2" color="text.secondary">
            ID: {packingList.listTotal.shipmentOrPackingId}
          </Typography>
        )}
      </Stack>
    );
  };

  // Render the form when expanded
  const renderForm = (index: number) => (
    <PLT1PackingListForm formPath="packingLists" index={index} readOnly={readOnly} />
  );

  // Render the title of each item
  const getItemTitle = (packingList: any) => {
    // Get the first packed item name from the first position
    const firstPackedItemName = packingList.packingListPositions?.[0]?.packedItems?.[0]?.name;

    // Get shipment/packing ID if available
    const shipmentId = packingList.listTotal?.shipmentOrPackingId;

    // Generate title based on available data
    let title = '';
    if (firstPackedItemName) {
      title = firstPackedItemName;
    } else if (shipmentId) {
      title = `${t('plt1.details.documents.packingList.preview.newPackingList')} - ${shipmentId}`;
    } else if (packingList.listTotal?.totalQuantity) {
      title = `${t('plt1.details.documents.packingList.preview.totalItems')}: ${packingList.packingListPositions.length}`;
    } else {
      title = t('plt1.details.documents.packingList.preview.newPackingList');
    }

    return (
      <Typography variant="subtitle1">
        {title}
      </Typography>
    );
  };

  return (
    <PLT1DocumentTabBase
      t1OrderId={t1OrderId}
      order={order}
      readOnly={readOnly}
      title={t('plt1.details.documents.packingList.heading')}
      emptyTitle={t('plt1.details.documents.packingList.noData')}
      emptyDescription={t('plt1.details.documents.packingList.addYourFirst')}
      expandedIndex={expandedPackingList}
      deleteIndex={deleteIndex}
      openConfirm={openConfirm}
      fieldArray={fieldArray}
      fieldArrayName="packingLists"
      onToggleExpand={handleToggleExpand}
      onOpenConfirm={handleOpenConfirm}
      onCloseConfirm={handleCloseConfirm}
      onDelete={handleDeletePackingList}
      onAdd={handleAddPackingList}
      renderPreview={renderPreview}
      renderForm={renderForm}
      getItemTitle={getItemTitle}
    />
  );
}
