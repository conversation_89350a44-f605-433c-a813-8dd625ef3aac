'use client';

import type { LinkProps } from '@mui/material/Link';
import Link from '@mui/material/Link';
import { useCallback } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { useSearchParams } from 'src/routes/hooks';
import { LANGUAGE_LOCAL_STORAGE_KEY, useTranslate } from 'src/locales';

export type SignUpLinkProps = LinkProps & {
    forceReauthentication?: boolean;
} 

export function SignUpLink({ sx, forceReauthentication = false, ...other }: SignUpLinkProps) {
  const { loginWithRedirect } = useAuth0();
  const searchParams = useSearchParams();
  const returnTo = searchParams.get('returnTo') ?? "";
  const { t } = useTranslate();

  const handleSignUpWithRedirect = useCallback(async () => {
    try {
      await loginWithRedirect({
        appState: { returnTo: returnTo },
        authorizationParams: { 
          screen_hint: 'signup', 
          ui_locales: localStorage.getItem(LANGUAGE_LOCAL_STORAGE_KEY) || 'en',
          ...(forceReauthentication && { max_age: 0 })
        },
      });
    } catch (error) {
      console.error(error);
    }
  }, [loginWithRedirect, returnTo, forceReauthentication]);

  return (
    <Link
      component="button"
      variant="subtitle2"
      onClick={handleSignUpWithRedirect}
      sx={{ 
        cursor: 'pointer',
        textDecoration: 'none',
        '&:hover': {
          textDecoration: 'underline'
        },
        ...sx 
      }}
      {...other}
    >
      {t('components.signUpLink.label')}
    </Link>
  );
}