"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx":
/*!*******************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1PackingListForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/custom-dialog */ \"(app-pages-browser)/./src/components/custom-dialog/index.ts\");\n/* harmony import */ var src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/components/party-address/hooks/usePartyAddressForm */ \"(app-pages-browser)/./src/components/party-address/hooks/usePartyAddressForm.tsx\");\n/* harmony import */ var src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/components/party-address/party-address-dialog */ \"(app-pages-browser)/./src/components/party-address/party-address-dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst DEFAULT_PACKED_ITEM = {\n    id: '',\n    name: '',\n    hsCode: '',\n    modelNumber: '',\n    quantity: 0,\n    itemNetWeight: 0,\n    itemNetWeightUnit: 'KGM',\n    itemGrossWeight: 0,\n    hsCodeHints: '',\n    packingListPositionId: ''\n};\nconst DEFAULT_POSITION = {\n    id: '',\n    positionNumber: 1,\n    commercialInvoiceNumber: '',\n    packageNetWeight: 0,\n    packageNetWeightUnit: 'KGM',\n    packageGrossWeight: 0,\n    packageGrossWeightUnit: 'KGM',\n    packagesNetWeight: 0,\n    packagesNetWeightUnit: 'KGM',\n    packagesGrossWeight: 0,\n    packagesGrossWeightUnit: 'KGM',\n    packageUnit: 'CTNS',\n    packageSize: '',\n    packageVolume: 0,\n    packageVolumeUnit: 'CBM',\n    numberOfPackages: 0,\n    packedItems: [],\n    packingListId: ''\n};\nconst DEFAULT_TOTAL = {\n    id: '',\n    shipmentOrPackingId: '',\n    totalQuantity: 0,\n    totalPackagesUnit: 'CTNS',\n    totalNumberOfPackages: 0,\n    totalNumberOfPallets: 0,\n    totalNetWeight: 0,\n    totalNetWeightUnit: 'KGM',\n    totalGrossWeight: 0,\n    totalGrossWeightUnit: 'KGM',\n    totalVolume: 0,\n    totalVolumeMeasurementUnit: 'CBM',\n    packingListId: ''\n};\nfunction PLT1PackingListForm(param) {\n    let { formPath, index, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    const { control, getValues, watch, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext)();\n    const fieldPrefix = index !== undefined ? \"\".concat(formPath, \".\").concat(index) : formPath;\n    const positionsFieldName = \"\".concat(fieldPrefix, \".packingListPositions\");\n    const totalFieldName = \"\".concat(fieldPrefix, \".listTotal\");\n    // Use the party address form hook\n    const { openPartyDialog, currentPartyType, handleClosePartyDialog, handleUpdateParty } = (0,src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm)({\n        fieldPrefix\n    });\n    // State for item dialog\n    const [openItemDialog, setOpenItemDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteItemIndex, setDeleteItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize listTotal if it doesn't exist\n    const listTotal = watch(totalFieldName);\n    if (!listTotal) {\n        setValue(totalFieldName, DEFAULT_TOTAL);\n    }\n    // UseFieldArray hook to manage the positions array\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray)({\n        control,\n        name: positionsFieldName\n    });\n    // Handle opening the position dialog for adding a new position\n    const handleAddPosition = ()=>{\n        setCurrentItemIndex(null);\n        // Add a new position with default values\n        const newPosition = {\n            ...DEFAULT_POSITION\n        };\n        append(newPosition);\n        setCurrentItemIndex(fields.length); // Set to the new index\n        setOpenItemDialog(true);\n    };\n    // Handle opening the position dialog for editing an existing position\n    const handleEditPosition = (index)=>{\n        setCurrentItemIndex(index);\n        setOpenItemDialog(true);\n    };\n    // Handle closing the position dialog\n    const handleClosePositionDialog = ()=>{\n        setOpenItemDialog(false);\n        // If we were adding a new position and user cancels, remove the empty position\n        if (currentItemIndex !== null && currentItemIndex === fields.length - 1) {\n            const position = getValues(\"\".concat(positionsFieldName, \".\").concat(currentItemIndex));\n            // Check if it's an empty position (all fields are default values)\n            const isEmpty = !position.commercialInvoiceNumber && position.numberOfPackages === 0 && position.packageNetWeight === 0 && position.packageGrossWeight === 0 && position.packageVolume === 0 && (!position.packedItems || position.packedItems.length === 0);\n            if (isEmpty) {\n                remove(currentItemIndex);\n            }\n        }\n        setCurrentItemIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeleteItemIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deleteItemIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting an item\n    const handleDeleteItem = ()=>{\n        if (deleteItemIndex !== null) {\n            remove(deleteItemIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving a position (just close the dialog since form is already updated)\n    const handleSavePosition = ()=>{\n        handleClosePositionDialog();\n    };\n    // Render the item form in the dialog - using React Hook Form fields\n    const renderItemForm = ()=>{\n        if (currentItemIndex === null) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".name\"),\n                    label: t('plt1.details.documents.packingList.item.name'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".modelNumber\"),\n                    label: t('plt1.details.documents.packingList.item.modelNumber'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".purchaseOrderNumber\"),\n                            label: t('plt1.details.documents.packingList.item.purchaseOrderNumber'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".commercialInvoiceNumber\"),\n                            label: t('plt1.details.documents.packingList.item.commercialInvoiceNumber'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageNetWeight\"),\n                            label: t('plt1.details.documents.packingList.item.packageNetWeight'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageNetWeightUnit\"),\n                            label: t('plt1.details.documents.packingList.item.packageNetWeightUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageGrossWeight\"),\n                            label: t('plt1.details.documents.packingList.item.packageGrossWeight'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageGrossWeightUnit\"),\n                            label: t('plt1.details.documents.packingList.item.packageGrossWeightUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".quantity\"),\n                            label: t('plt1.details.documents.packingList.item.quantity'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".volume\"),\n                            label: t('plt1.details.documents.packingList.item.volume'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 243,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the item table\n    const renderItemsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            component: _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"packing list items table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.name')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.modelNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.quantity')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.packageNetWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.packageGrossWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                colSpan: 6,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.packingList.item.noItems')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this) : fields.map((field, index)=>{\n                            const item = getValues(\"\".concat(itemsFieldName, \".\").concat(index)) || {};\n                            var _item_quantity, _item_packageNetWeight, _item_packageGrossWeight;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: item.name || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: item.modelNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: (_item_quantity = item.quantity) !== null && _item_quantity !== void 0 ? _item_quantity : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: [\n                                            (_item_packageNetWeight = item.packageNetWeight) !== null && _item_packageNetWeight !== void 0 ? _item_packageNetWeight : '-',\n                                            \" \",\n                                            item.packageNetWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: [\n                                            (_item_packageGrossWeight = item.packageGrossWeight) !== null && _item_packageGrossWeight !== void 0 ? _item_packageGrossWeight : '-',\n                                            \" \",\n                                            item.packageGrossWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditItem(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deleteItemIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 318,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 317,\n            columnNumber: 5\n        }, this);\n    const renderPartyDialog = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            open: openPartyDialog,\n            onClose: handleClosePartyDialog,\n            onSave: handleUpdateParty,\n            formPath: fieldPrefix,\n            currentPartyType: currentPartyType,\n            readOnly: readOnly,\n            titlePrefix: \"plt1.details.documents.packingList.partyAddress\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 391,\n            columnNumber: 5\n        }, this);\n    // Handle change for totals fields\n    const handleTotalChange = (field, value)=>{\n        setValue(\"\".concat(totalFieldName, \".\").concat(field), value);\n    };\n    // Render the totals section\n    const renderTotalsSection = ()=>{\n        const total = watch(totalFieldName) || DEFAULT_TOTAL;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n            sx: {\n                boxShadow: 'none',\n                mt: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    title: t('plt1.details.documents.packingList.form.totalsTitle')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    sx: {\n                        borderStyle: 'dashed'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        spacing: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalQuantity\"),\n                                        label: t('plt1.details.documents.packingList.total.totalQuantity'),\n                                        type: \"number\",\n                                        value: total.totalQuantity,\n                                        onChange: (e)=>handleTotalChange('totalQuantity', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPallets\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPallets'),\n                                        type: \"number\",\n                                        value: total.totalNumberOfPallets,\n                                        onChange: (e)=>handleTotalChange('totalNumberOfPallets', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPackages\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPackages'),\n                                        type: \"number\",\n                                        value: total.totalNumberOfPackages,\n                                        onChange: (e)=>handleTotalChange('totalNumberOfPackages', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalPackagesUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalPackagesUnit'),\n                                        value: total.totalPackagesUnit,\n                                        onChange: (e)=>handleTotalChange('totalPackagesUnit', e.target.value),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeight'),\n                                        type: \"number\",\n                                        value: total.totalNetWeight,\n                                        onChange: (e)=>handleTotalChange('totalNetWeight', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeightUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeightUnit'),\n                                        value: total.totalNetWeightUnit,\n                                        onChange: (e)=>handleTotalChange('totalNetWeightUnit', e.target.value || 'kg'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeight'),\n                                        type: \"number\",\n                                        value: total.totalGrossWeight,\n                                        onChange: (e)=>handleTotalChange('totalGrossWeight', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeightUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeightUnit'),\n                                        value: total.totalGrossWeightUnit,\n                                        onChange: (e)=>handleTotalChange('totalGrossWeightUnit', e.target.value || 'kg'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolume\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolume'),\n                                        type: \"number\",\n                                        value: total.totalVolume,\n                                        onChange: (e)=>handleTotalChange('totalVolume', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolumeMeasurementUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolumeMeasurementUnit'),\n                                        value: total.totalVolumeMeasurementUnit,\n                                        onChange: (e)=>handleTotalChange('totalVolumeMeasurementUnit', e.target.value),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 410,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        spacing: 3,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        title: t('plt1.details.documents.packingList.form.itemsTitle'),\n                        action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            variant: \"contained\",\n                            size: \"small\",\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                icon: \"eva:plus-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: handleAddItem,\n                            sx: {\n                                mb: 2\n                            },\n                            disabled: readOnly,\n                            children: t('common.addItem')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: [\n                            renderItemsTable(),\n                            fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                sx: {\n                                    mt: 2,\n                                    display: 'flex',\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    variant: \"soft\",\n                                    color: \"primary\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                        icon: \"eva:plus-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    onClick: handleAddItem,\n                                    disabled: readOnly,\n                                    children: t('common.addItem')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 520,\n                columnNumber: 7\n            }, this),\n            renderTotalsSection(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                open: openItemDialog,\n                onClose: handleCloseItemDialog,\n                fullWidth: true,\n                maxWidth: \"md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        children: currentItemIndex === null ? t('plt1.details.documents.packingList.item.addNew') : t('plt1.details.documents.packingList.item.edit')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            sx: {\n                                pt: 3\n                            },\n                            children: renderItemForm()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                onClick: handleCloseItemDialog,\n                                color: \"inherit\",\n                                children: t('common.cancel')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 570,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                onClick: handleSaveItem,\n                                variant: \"contained\",\n                                children: t('common.save')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 569,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 560,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__.ConfirmDialog, {\n                open: openDeleteConfirm,\n                onClose: handleCloseDeleteConfirm,\n                title: t('plt1.details.documents.packingList.item.confirmDeleteDialog.title'),\n                content: t('plt1.details.documents.packingList.item.confirmDeleteDialog.content'),\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                    variant: \"contained\",\n                    color: \"error\",\n                    onClick: handleDeleteItem,\n                    children: t('common.delete')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 586,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 580,\n                columnNumber: 7\n            }, this),\n            renderPartyDialog()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n        lineNumber: 518,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1PackingListForm, \"rQqfRn3lyqnpgHcb7yCWKoSvnLA=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext,\n        src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray\n    ];\n});\n_c = PLT1PackingListForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1PackingListForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx\n"));

/***/ })

});