"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1sea/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-merchandise-positions-form.tsx":
/*!****************************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-merchandise-positions-form.tsx ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1MerchandisePositionsForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material/Box */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/Card */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material/Stack */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material_Button__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/material/Button */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/material/Divider */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/CardHeader */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/Typography */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/IconButton */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _mui_material_Table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material/Table */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _mui_material_TableBody__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mui/material/TableBody */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material/TableCell */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _mui_material_TableContainer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material/TableContainer */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _mui_material_TableHead__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/TableHead */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/TableRow */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _mui_material_Paper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/Paper */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _mui_material_Dialog__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/material/Dialog */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _mui_material_DialogTitle__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/material/DialogTitle */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _mui_material_DialogContent__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/material/DialogContent */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _mui_material_DialogActions__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/material/DialogActions */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/Tooltip */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/material/Grid2 */ \"(app-pages-browser)/./node_modules/@mui/material/Grid2/Grid2.js\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/custom-dialog */ \"(app-pages-browser)/./src/components/custom-dialog/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst DEFAULT_POSITION = {\n    id: null,\n    invoicePositionNumber: 0,\n    goodsDescription: '',\n    translatedGoodsDescription: '',\n    packageMarks: '',\n    numberOfPackages: 0,\n    packageType: '',\n    packageTypeDescription: '',\n    commodityCode: '',\n    fullTariffCode: '',\n    grossMass: 0,\n    grossMassUnit: 'kg',\n    netMass: 0,\n    netMassUnit: 'kg',\n    positionValue: 0,\n    positionValueCurrency: 'USD',\n    containerNumbers: [],\n    previousDocuments: [],\n    merchandisePositionsId: ''\n};\nfunction PLT1MerchandisePositionsForm(param) {\n    let { t1OrderId, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_4__.useTranslate)();\n    const { control, watch, getValues } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFormContext)();\n    const fieldArray = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFieldArray)({\n        control,\n        name: 'merchandisePositions.positions'\n    });\n    const merchandisePositions = watch('merchandisePositions');\n    // State for position dialog\n    const [openPositionDialog, setOpenPositionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPositionIndex, setCurrentPositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletePositionIndex, setDeletePositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Handle opening the position dialog for adding a new position\n    const handleAddPosition = ()=>{\n        setCurrentPositionIndex(null);\n        // Add a new position with default values\n        const newPosition = {\n            ...DEFAULT_POSITION,\n            merchandisePositionsId: (merchandisePositions === null || merchandisePositions === void 0 ? void 0 : merchandisePositions.id) || ''\n        };\n        fieldArray.append(newPosition);\n        setCurrentPositionIndex(fieldArray.fields.length); // Set to the new index\n        setOpenPositionDialog(true);\n    };\n    // Handle opening the position dialog for editing an existing position\n    const handleEditPosition = (index)=>{\n        setCurrentPositionIndex(index);\n        setOpenPositionDialog(true);\n    };\n    // Handle closing the position dialog\n    const handleClosePositionDialog = ()=>{\n        setOpenPositionDialog(false);\n        // If we were adding a new position and user cancels, remove the empty position\n        if (currentPositionIndex !== null && currentPositionIndex === fieldArray.fields.length - 1) {\n            const position = getValues(\"merchandisePositions.positions.\".concat(currentPositionIndex));\n            // Check if it's an empty position (all fields are default values)\n            const isEmpty = !position.goodsDescription && !position.translatedGoodsDescription && !position.packageMarks && !position.commodityCode && !position.fullTariffCode && position.numberOfPackages === 0 && position.grossMass === 0 && position.netMass === 0;\n            if (isEmpty) {\n                fieldArray.remove(currentPositionIndex);\n            }\n        }\n        setCurrentPositionIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeletePositionIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deletePositionIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting a position\n    const handleDeletePosition = ()=>{\n        if (deletePositionIndex !== null) {\n            fieldArray.remove(deletePositionIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving a position (just close the dialog since form is already updated)\n    const handleSavePosition = ()=>{\n        handleClosePositionDialog();\n    };\n    // Render the position form in the dialog - using React Hook Form fields\n    const renderPositionForm = ()=>{\n        if (currentPositionIndex === null) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".invoicePositionNumber\"),\n                    label: t('plt1.details.documents.merchandisePositions.form.invoicePositionNumber'),\n                    type: \"number\",\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 3,\n                        pt: 2,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(2, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".goodsDescription\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.goodsDescription'),\n                            multiline: true,\n                            rows: 2,\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".translatedGoodsDescription\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.translatedGoodsDescription'),\n                            multiline: true,\n                            rows: 2,\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 2.5,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(3, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".numberOfPackages\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.numberOfPackages'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".packageType\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.packageType'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".packageTypeDescription\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.packageTypeDescription'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".packageMarks\"),\n                    label: t('plt1.details.documents.merchandisePositions.form.packageMarks'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 3,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(2, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".commodityCode\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.commodityCode'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".fullTariffCode\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.fullTariffCode'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 3,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(4, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".grossMass\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.grossMass'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".grossMassUnit\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.grossMassUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".netMass\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.netMass'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".netMassUnit\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.netMassUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 2.5,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(2, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".positionValue\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.positionValue'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".positionValueCurrency\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.positionValueCurrency'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the positions table\n    const renderPositionsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableContainer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            component: _mui_material_Paper__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Table__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"merchandise positions table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableHead__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.goodsDescription')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.numberOfPackages')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.packageType')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.grossMass')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.netMass')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableBody__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        children: fieldArray.fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                colSpan: 6,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.merchandisePositions.noPositions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 13\n                        }, this) : fieldArray.fields.map((field, index)=>{\n                            const position = getValues(\"merchandisePositions.positions.\".concat(index)) || {};\n                            var _position_numberOfPackages, _position_grossMass, _position_netMass;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                variant: \"body2\",\n                                                sx: {\n                                                    maxWidth: 200\n                                                },\n                                                children: position.goodsDescription || '-'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 21\n                                            }, this),\n                                            position.translatedGoodsDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                variant: \"caption\",\n                                                color: \"text.secondary\",\n                                                sx: {\n                                                    display: 'block',\n                                                    mt: 0.5\n                                                },\n                                                children: position.translatedGoodsDescription\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: (_position_numberOfPackages = position.numberOfPackages) !== null && _position_numberOfPackages !== void 0 ? _position_numberOfPackages : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: position.packageTypeDescription || position.packageType || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_position_grossMass = position.grossMass) !== null && _position_grossMass !== void 0 ? _position_grossMass : '-',\n                                            \" \",\n                                            position.grossMassUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_position_netMass = position.netMass) !== null && _position_netMass !== void 0 ? _position_netMass : '-',\n                                            \" \",\n                                            position.netMassUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditPosition(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deletePositionIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 326,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n            lineNumber: 325,\n            columnNumber: 5\n        }, this);\n    // Render the totals summary\n    const renderTotalsSummary = ()=>{\n        const totals = merchandisePositions === null || merchandisePositions === void 0 ? void 0 : merchandisePositions.totals;\n        if (!totals) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            sx: {\n                boxShadow: 'none',\n                mb: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    title: t('plt1.details.documents.merchandisePositions.form.totalsTitle')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    sx: {\n                        borderStyle: 'dashed'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 420,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalNumberOfPositions')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: totals.totalNumberOfPositions || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalNumberOfPackages')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: totals.totalNumberOfPackages || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalGrossMass')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: [\n                                                totals.totalGrossMass || 0,\n                                                \" \",\n                                                totals.totalGrossMassUnit || ''\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalNetMass')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: [\n                                                totals.totalNetMass || 0,\n                                                \" \",\n                                                totals.totalNetMassUnit || ''\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n            lineNumber: 416,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        spacing: 3,\n        children: [\n            renderTotalsSummary(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        title: t('plt1.details.documents.merchandisePositions.form.positionsTitle'),\n                        action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            variant: \"contained\",\n                            size: \"small\",\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                icon: \"eva:plus-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: handleAddPosition,\n                            sx: {\n                                mb: 2\n                            },\n                            disabled: readOnly,\n                            children: t('plt1.details.documents.merchandisePositions.addPosition')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: [\n                            renderPositionsTable(),\n                            fieldArray.fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                sx: {\n                                    mt: 2,\n                                    display: 'flex',\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    variant: \"soft\",\n                                    color: \"primary\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                        icon: \"eva:plus-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    onClick: handleAddPosition,\n                                    disabled: readOnly,\n                                    children: t('plt1.details.documents.merchandisePositions.addPosition')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 475,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Dialog__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                open: openPositionDialog,\n                onClose: handleClosePositionDialog,\n                maxWidth: \"md\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_DialogTitle__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        children: currentPositionIndex === null ? t('plt1.details.documents.merchandisePositions.addPosition') : t('plt1.details.documents.merchandisePositions.editPosition')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_DialogContent__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        children: renderPositionForm()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 523,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_DialogActions__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                onClick: handleClosePositionDialog,\n                                children: t('common.cancel')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                variant: \"contained\",\n                                onClick: handleSavePosition,\n                                children: t('common.save')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 530,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 512,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__.ConfirmDialog, {\n                open: openDeleteConfirm,\n                onClose: handleCloseDeleteConfirm,\n                title: t('plt1.details.documents.merchandisePositions.confirmDeleteDialog.title'),\n                content: t('plt1.details.documents.merchandisePositions.confirmDeleteDialog.content'),\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                    variant: \"contained\",\n                    color: \"error\",\n                    onClick: handleDeletePosition,\n                    children: t('common.delete')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 543,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 537,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n        lineNumber: 470,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1MerchandisePositionsForm, \"rzlgvldrAdFsKfES/DPx4X+02i8=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_4__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFormContext,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFieldArray\n    ];\n});\n_c = PLT1MerchandisePositionsForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1MerchandisePositionsForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZWN0aW9ucy9vcmRlcnMvcGx0MS9mb3Jtcy9wbHQxLW1lcmNoYW5kaXNlLXBvc2l0aW9ucy1mb3JtLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUV5QztBQUN1QjtBQUU1QjtBQUNFO0FBQ0U7QUFDRTtBQUNFO0FBQ007QUFDQTtBQUNBO0FBQ1Y7QUFDUTtBQUNBO0FBQ1U7QUFDVjtBQUNGO0FBQ047QUFDRTtBQUNVO0FBQ0k7QUFDQTtBQUNaO0FBQ0w7QUFFVTtBQUNBO0FBQ047QUFDa0I7QUEwQjdELE1BQU02QixtQkFBbUI7SUFDdkJDLElBQUk7SUFDSkMsdUJBQXVCO0lBQ3ZCQyxrQkFBa0I7SUFDbEJDLDRCQUE0QjtJQUM1QkMsY0FBYztJQUNkQyxrQkFBa0I7SUFDbEJDLGFBQWE7SUFDYkMsd0JBQXdCO0lBQ3hCQyxlQUFlO0lBQ2ZDLGdCQUFnQjtJQUNoQkMsV0FBVztJQUNYQyxlQUFlO0lBQ2ZDLFNBQVM7SUFDVEMsYUFBYTtJQUNiQyxlQUFlO0lBQ2ZDLHVCQUF1QjtJQUN2QkMsa0JBQWtCLEVBQUU7SUFDcEJDLG1CQUFtQixFQUFFO0lBQ3JCQyx3QkFBd0I7QUFDMUI7QUFTZSxTQUFTQyw2QkFBNkIsS0FHakI7UUFIaUIsRUFDbkRDLFNBQVMsRUFDVEMsV0FBVyxLQUFLLEVBQ2tCLEdBSGlCOztJQUluRCxNQUFNLEVBQUVDLENBQUMsRUFBRSxHQUFHekIseURBQVlBO0lBQzFCLE1BQU0sRUFBRTBCLE9BQU8sRUFBRUMsS0FBSyxFQUFFQyxTQUFTLEVBQUUsR0FBR3JELCtEQUFjQTtJQUVwRCxNQUFNc0QsYUFBYXJELDhEQUFhQSxDQUFDO1FBQy9Ca0Q7UUFDQUksTUFBTTtJQUNSO0lBRUEsTUFBTUMsdUJBQXVCSixNQUFNO0lBRW5DLDRCQUE0QjtJQUM1QixNQUFNLENBQUNLLG9CQUFvQkMsc0JBQXNCLEdBQUc1RCwrQ0FBUUEsQ0FBQztJQUM3RCxNQUFNLENBQUM2RCxzQkFBc0JDLHdCQUF3QixHQUFHOUQsK0NBQVFBLENBQWdCO0lBQ2hGLE1BQU0sQ0FBQytELHFCQUFxQkMsdUJBQXVCLEdBQUdoRSwrQ0FBUUEsQ0FBZ0I7SUFDOUUsTUFBTSxDQUFDaUUsbUJBQW1CQyxxQkFBcUIsR0FBR2xFLCtDQUFRQSxDQUFDO0lBQzNELE1BQU1tRSxrQkFBa0JsRSw2Q0FBTUEsQ0FBb0I7SUFFbEQsK0RBQStEO0lBQy9ELE1BQU1tRSxvQkFBb0I7UUFDeEJOLHdCQUF3QjtRQUN4Qix5Q0FBeUM7UUFDekMsTUFBTU8sY0FBYztZQUNsQixHQUFHeEMsZ0JBQWdCO1lBQ25CbUIsd0JBQXdCVSxDQUFBQSxpQ0FBQUEsMkNBQUFBLHFCQUFzQjVCLEVBQUUsS0FBSTtRQUN0RDtRQUNBMEIsV0FBV2MsTUFBTSxDQUFDRDtRQUNsQlAsd0JBQXdCTixXQUFXZSxNQUFNLENBQUNDLE1BQU0sR0FBRyx1QkFBdUI7UUFDMUVaLHNCQUFzQjtJQUN4QjtJQUVBLHNFQUFzRTtJQUN0RSxNQUFNYSxxQkFBcUIsQ0FBQ0M7UUFDMUJaLHdCQUF3Qlk7UUFDeEJkLHNCQUFzQjtJQUN4QjtJQUVBLHFDQUFxQztJQUNyQyxNQUFNZSw0QkFBNEI7UUFDaENmLHNCQUFzQjtRQUN0QiwrRUFBK0U7UUFDL0UsSUFBSUMseUJBQXlCLFFBQVFBLHlCQUF5QkwsV0FBV2UsTUFBTSxDQUFDQyxNQUFNLEdBQUcsR0FBRztZQUMxRixNQUFNSSxXQUFXckIsVUFBVSxrQ0FBdUQsT0FBckJNO1lBQzdELGtFQUFrRTtZQUNsRSxNQUFNZ0IsVUFBVSxDQUFDRCxTQUFTNUMsZ0JBQWdCLElBQUksQ0FBQzRDLFNBQVMzQywwQkFBMEIsSUFDbkUsQ0FBQzJDLFNBQVMxQyxZQUFZLElBQUksQ0FBQzBDLFNBQVN0QyxhQUFhLElBQUksQ0FBQ3NDLFNBQVNyQyxjQUFjLElBQzdFcUMsU0FBU3pDLGdCQUFnQixLQUFLLEtBQUt5QyxTQUFTcEMsU0FBUyxLQUFLLEtBQUtvQyxTQUFTbEMsT0FBTyxLQUFLO1lBQ25HLElBQUltQyxTQUFTO2dCQUNYckIsV0FBV3NCLE1BQU0sQ0FBQ2pCO1lBQ3BCO1FBQ0Y7UUFDQUMsd0JBQXdCO0lBQzFCO0lBRUEsZ0RBQWdEO0lBQ2hELE1BQU1pQiwwQkFBMEIsQ0FBQ0w7UUFDL0JWLHVCQUF1QlU7UUFDdkJSLHFCQUFxQjtJQUN2QjtJQUVBLGdEQUFnRDtJQUNoRCxNQUFNYywyQkFBMkI7UUFDL0JkLHFCQUFxQjtRQUNyQixJQUFJSCx3QkFBd0IsTUFBTTtnQkFDaENJO2FBQUFBLDJCQUFBQSxnQkFBZ0JjLE9BQU8sY0FBdkJkLCtDQUFBQSx5QkFBeUJlLEtBQUs7UUFDaEM7SUFDRjtJQUVBLDZCQUE2QjtJQUM3QixNQUFNQyx1QkFBdUI7UUFDM0IsSUFBSXBCLHdCQUF3QixNQUFNO1lBQ2hDUCxXQUFXc0IsTUFBTSxDQUFDZjtRQUNwQjtRQUNBaUI7SUFDRjtJQUVBLGlGQUFpRjtJQUNqRixNQUFNSSxxQkFBcUI7UUFDekJUO0lBQ0Y7SUFFQSx3RUFBd0U7SUFDeEUsTUFBTVUscUJBQXFCO1FBQ3pCLElBQUl4Qix5QkFBeUIsTUFBTSxPQUFPO1FBRTFDLHFCQUNFLDhEQUFDdkQsMkRBQUtBO1lBQUNnRixTQUFTOzs4QkFFZCw4REFBQzVELDJEQUFLQSxDQUFDNkQsSUFBSTtvQkFDVDlCLE1BQU0sa0NBQXVELE9BQXJCSSxzQkFBcUI7b0JBQzdEMkIsT0FBT3BDLEVBQUU7b0JBQ1RxQyxNQUFLO29CQUNMQyxVQUFVdkM7Ozs7Ozs4QkFJWiw4REFBQy9DLHlEQUFHQTtvQkFDRnVGLElBQUk7d0JBQ0ZDLFFBQVE7d0JBQ1JDLElBQUk7d0JBQ0pDLFdBQVc7d0JBQ1hDLFNBQVM7d0JBQ1RDLHFCQUFxQjs0QkFBRUMsSUFBSTs0QkFBa0JDLElBQUk7d0JBQWlCO29CQUNwRTs7c0NBRUEsOERBQUN4RSwyREFBS0EsQ0FBQzZELElBQUk7NEJBQ1Q5QixNQUFNLGtDQUF1RCxPQUFyQkksc0JBQXFCOzRCQUM3RDJCLE9BQU9wQyxFQUFFOzRCQUNUK0MsU0FBUzs0QkFDVEMsTUFBTTs0QkFDTlYsVUFBVXZDOzs7Ozs7c0NBRVosOERBQUN6QiwyREFBS0EsQ0FBQzZELElBQUk7NEJBQ1Q5QixNQUFNLGtDQUF1RCxPQUFyQkksc0JBQXFCOzRCQUM3RDJCLE9BQU9wQyxFQUFFOzRCQUNUK0MsU0FBUzs0QkFDVEMsTUFBTTs0QkFDTlYsVUFBVXZDOzs7Ozs7Ozs7Ozs7OEJBS2QsOERBQUMvQyx5REFBR0E7b0JBQ0Z1RixJQUFJO3dCQUNGQyxRQUFRO3dCQUNSRSxXQUFXO3dCQUNYQyxTQUFTO3dCQUNUQyxxQkFBcUI7NEJBQUVDLElBQUk7NEJBQWtCQyxJQUFJO3dCQUFpQjtvQkFDcEU7O3NDQUVBLDhEQUFDeEUsMkRBQUtBLENBQUM2RCxJQUFJOzRCQUNUOUIsTUFBTSxrQ0FBdUQsT0FBckJJLHNCQUFxQjs0QkFDN0QyQixPQUFPcEMsRUFBRTs0QkFDVHFDLE1BQUs7NEJBQ0xDLFVBQVV2Qzs7Ozs7O3NDQUVaLDhEQUFDekIsMkRBQUtBLENBQUM2RCxJQUFJOzRCQUNUOUIsTUFBTSxrQ0FBdUQsT0FBckJJLHNCQUFxQjs0QkFDN0QyQixPQUFPcEMsRUFBRTs0QkFFVHNDLFVBQVV2Qzs7Ozs7O3NDQUVaLDhEQUFDekIsMkRBQUtBLENBQUM2RCxJQUFJOzRCQUNUOUIsTUFBTSxrQ0FBdUQsT0FBckJJLHNCQUFxQjs0QkFDN0QyQixPQUFPcEMsRUFBRTs0QkFDVHNDLFVBQVV2Qzs7Ozs7Ozs7Ozs7OzhCQUlkLDhEQUFDekIsMkRBQUtBLENBQUM2RCxJQUFJO29CQUNUOUIsTUFBTSxrQ0FBdUQsT0FBckJJLHNCQUFxQjtvQkFDN0QyQixPQUFPcEMsRUFBRTtvQkFDVHNDLFVBQVV2Qzs7Ozs7OzhCQUlaLDhEQUFDL0MseURBQUdBO29CQUNGdUYsSUFBSTt3QkFDRkMsUUFBUTt3QkFDUkUsV0FBVzt3QkFDWEMsU0FBUzt3QkFDVEMscUJBQXFCOzRCQUFFQyxJQUFJOzRCQUFrQkMsSUFBSTt3QkFBaUI7b0JBQ3BFOztzQ0FFQSw4REFBQ3hFLDJEQUFLQSxDQUFDNkQsSUFBSTs0QkFDVDlCLE1BQU0sa0NBQXVELE9BQXJCSSxzQkFBcUI7NEJBQzdEMkIsT0FBT3BDLEVBQUU7NEJBQ1RzQyxVQUFVdkM7Ozs7OztzQ0FFWiw4REFBQ3pCLDJEQUFLQSxDQUFDNkQsSUFBSTs0QkFDVDlCLE1BQU0sa0NBQXVELE9BQXJCSSxzQkFBcUI7NEJBQzdEMkIsT0FBT3BDLEVBQUU7NEJBQ1RzQyxVQUFVdkM7Ozs7Ozs7Ozs7Ozs4QkFLZCw4REFBQy9DLHlEQUFHQTtvQkFDRnVGLElBQUk7d0JBQ0ZDLFFBQVE7d0JBQ1JFLFdBQVc7d0JBQ1hDLFNBQVM7d0JBQ1RDLHFCQUFxQjs0QkFBRUMsSUFBSTs0QkFBa0JDLElBQUk7d0JBQWlCO29CQUNwRTs7c0NBRUEsOERBQUN4RSwyREFBS0EsQ0FBQzZELElBQUk7NEJBQ1Q5QixNQUFNLGtDQUF1RCxPQUFyQkksc0JBQXFCOzRCQUM3RDJCLE9BQU9wQyxFQUFFOzRCQUNUcUMsTUFBSzs0QkFDTEMsVUFBVXZDOzs7Ozs7c0NBRVosOERBQUN6QiwyREFBS0EsQ0FBQzZELElBQUk7NEJBQ1Q5QixNQUFNLGtDQUF1RCxPQUFyQkksc0JBQXFCOzRCQUM3RDJCLE9BQU9wQyxFQUFFOzRCQUNUc0MsVUFBVXZDOzs7Ozs7c0NBRVosOERBQUN6QiwyREFBS0EsQ0FBQzZELElBQUk7NEJBQ1Q5QixNQUFNLGtDQUF1RCxPQUFyQkksc0JBQXFCOzRCQUM3RDJCLE9BQU9wQyxFQUFFOzRCQUNUcUMsTUFBSzs0QkFDTEMsVUFBVXZDOzs7Ozs7c0NBRVosOERBQUN6QiwyREFBS0EsQ0FBQzZELElBQUk7NEJBQ1Q5QixNQUFNLGtDQUF1RCxPQUFyQkksc0JBQXFCOzRCQUM3RDJCLE9BQU9wQyxFQUFFOzRCQUNUc0MsVUFBVXZDOzs7Ozs7Ozs7Ozs7OEJBS2QsOERBQUMvQyx5REFBR0E7b0JBQ0Z1RixJQUFJO3dCQUNGQyxRQUFRO3dCQUNSRSxXQUFXO3dCQUNYQyxTQUFTO3dCQUNUQyxxQkFBcUI7NEJBQUVDLElBQUk7NEJBQWtCQyxJQUFJO3dCQUFpQjtvQkFDcEU7O3NDQUVBLDhEQUFDeEUsMkRBQUtBLENBQUM2RCxJQUFJOzRCQUNUOUIsTUFBTSxrQ0FBdUQsT0FBckJJLHNCQUFxQjs0QkFDN0QyQixPQUFPcEMsRUFBRTs0QkFDVHFDLE1BQUs7NEJBQ0xDLFVBQVV2Qzs7Ozs7O3NDQUVaLDhEQUFDekIsMkRBQUtBLENBQUM2RCxJQUFJOzRCQUNUOUIsTUFBTSxrQ0FBdUQsT0FBckJJLHNCQUFxQjs0QkFDN0QyQixPQUFPcEMsRUFBRTs0QkFDVHNDLFVBQVV2Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS3BCO0lBRUEsNkJBQTZCO0lBQzdCLE1BQU1rRCx1QkFBdUIsa0JBQzNCLDhEQUFDdEYsb0VBQWNBO1lBQUN1RixXQUFXcEYsNERBQUtBO3NCQUM5Qiw0RUFBQ04sNERBQUtBO2dCQUFDK0UsSUFBSTtvQkFBRVksVUFBVTtnQkFBSTtnQkFBR0MsY0FBVzs7a0NBQ3ZDLDhEQUFDeEYsZ0VBQVNBO2tDQUNSLDRFQUFDQywrREFBUUE7OzhDQUNQLDhEQUFDSCxnRUFBU0E7OENBQUVzQyxFQUFFOzs7Ozs7OENBQ2QsOERBQUN0QyxnRUFBU0E7OENBQUVzQyxFQUFFOzs7Ozs7OENBQ2QsOERBQUN0QyxnRUFBU0E7OENBQUVzQyxFQUFFOzs7Ozs7OENBQ2QsOERBQUN0QyxnRUFBU0E7OENBQUVzQyxFQUFFOzs7Ozs7OENBQ2QsOERBQUN0QyxnRUFBU0E7OENBQUVzQyxFQUFFOzs7Ozs7OENBQ2QsOERBQUN0QyxnRUFBU0E7b0NBQUMyRixPQUFNOzhDQUFTckQsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBR2hDLDhEQUFDdkMsZ0VBQVNBO2tDQUNQMkMsV0FBV2UsTUFBTSxDQUFDQyxNQUFNLEtBQUssa0JBQzVCLDhEQUFDdkQsK0RBQVFBO3NDQUNQLDRFQUFDSCxnRUFBU0E7Z0NBQUM0RixTQUFTO2dDQUFHRCxPQUFNOzBDQUMzQiw0RUFBQy9GLGlFQUFVQTtvQ0FBQ2lHLFNBQVE7b0NBQVFDLE9BQU07OENBQy9CeEQsRUFBRTs7Ozs7Ozs7Ozs7Ozs7O21DQUtUSSxXQUFXZSxNQUFNLENBQUNzQyxHQUFHLENBQUMsQ0FBQ0MsT0FBT3BDOzRCQUM1QixNQUFNRSxXQUFXckIsVUFBVSxrQ0FBd0MsT0FBTm1CLFdBQVksQ0FBQztnQ0FjbkVFLDRCQU1BQSxxQkFHQUE7NEJBdEJQLHFCQUNFLDhEQUFDM0QsK0RBQVFBOztrREFDUCw4REFBQ0gsZ0VBQVNBOzswREFDUiw4REFBQ0osaUVBQVVBO2dEQUFDaUcsU0FBUTtnREFBUWhCLElBQUk7b0RBQUVvQixVQUFVO2dEQUFJOzBEQUM3Q25DLFNBQVM1QyxnQkFBZ0IsSUFBSTs7Ozs7OzRDQUUvQjRDLFNBQVMzQywwQkFBMEIsa0JBQ2xDLDhEQUFDdkIsaUVBQVVBO2dEQUFDaUcsU0FBUTtnREFBVUMsT0FBTTtnREFBaUJqQixJQUFJO29EQUFFSSxTQUFTO29EQUFTaUIsSUFBSTtnREFBSTswREFDbEZwQyxTQUFTM0MsMEJBQTBCOzs7Ozs7Ozs7Ozs7a0RBSTFDLDhEQUFDbkIsZ0VBQVNBO2tEQUNQOEQsQ0FBQUEsNkJBQUFBLFNBQVN6QyxnQkFBZ0IsY0FBekJ5Qyx3Q0FBQUEsNkJBQTZCOzs7Ozs7a0RBRWhDLDhEQUFDOUQsZ0VBQVNBO2tEQUNQOEQsU0FBU3ZDLHNCQUFzQixJQUFJdUMsU0FBU3hDLFdBQVcsSUFBSTs7Ozs7O2tEQUU5RCw4REFBQ3RCLGdFQUFTQTs7NENBQ1A4RCxDQUFBQSxzQkFBQUEsU0FBU3BDLFNBQVMsY0FBbEJvQyxpQ0FBQUEsc0JBQXNCOzRDQUFJOzRDQUFFQSxTQUFTbkMsYUFBYSxJQUFJOzs7Ozs7O2tEQUV6RCw4REFBQzNCLGdFQUFTQTs7NENBQ1A4RCxDQUFBQSxvQkFBQUEsU0FBU2xDLE9BQU8sY0FBaEJrQywrQkFBQUEsb0JBQW9COzRDQUFJOzRDQUFFQSxTQUFTakMsV0FBVyxJQUFJOzs7Ozs7O2tEQUVyRCw4REFBQzdCLGdFQUFTQTt3Q0FBQzJGLE9BQU07a0RBQ2YsNEVBQUNuRywyREFBS0E7NENBQUMyRyxXQUFVOzRDQUFNM0IsU0FBUzs0Q0FBRzRCLGdCQUFlOzs4REFDaEQsOERBQUMzRiw4REFBT0E7b0RBQUM0RixPQUFPL0QsRUFBRTs4REFDaEIsNEVBQUN6QyxpRUFBVUE7d0RBQ1R5RyxNQUFLO3dEQUNMUixPQUFNO3dEQUNOUyxTQUFTLElBQU01QyxtQkFBbUJDO3dEQUNsQ2dCLFVBQVV2QztrRUFFViw0RUFBQzFCLDJEQUFPQTs0REFBQzZGLE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBR2xCLDhEQUFDL0YsOERBQU9BO29EQUFDNEYsT0FBTy9ELEVBQUU7OERBQ2hCLDRFQUFDekMsaUVBQVVBO3dEQUNUeUcsTUFBSzt3REFDTFIsT0FBTTt3REFDTlMsU0FBUyxJQUFNdEMsd0JBQXdCTDt3REFDdkM2QyxLQUFLeEQsd0JBQXdCVyxRQUFRUCxrQkFBa0I7d0RBQ3ZEdUIsVUFBVXZDO2tFQUVWLDRFQUFDMUIsMkRBQU9BOzREQUFDNkYsTUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsrQkEzQ1RSLE1BQU1oRixFQUFFOzs7Ozt3QkFrRDNCOzs7Ozs7Ozs7Ozs7Ozs7OztJQU9WLDRCQUE0QjtJQUM1QixNQUFNMEYsc0JBQXNCO1FBQzFCLE1BQU1DLFNBQVMvRCxpQ0FBQUEsMkNBQUFBLHFCQUFzQitELE1BQU07UUFFM0MsSUFBSSxDQUFDQSxRQUFRO1lBQ1gsT0FBTztRQUNUO1FBRUEscUJBQ0UsOERBQUNwSCwyREFBSUE7WUFBQ3NGLElBQUk7Z0JBQUUrQixXQUFXO2dCQUFRQyxJQUFJO1lBQUU7OzhCQUNuQyw4REFBQ2xILGlFQUFVQTtvQkFDVDBHLE9BQU8vRCxFQUFFOzs7Ozs7OEJBRVgsOERBQUM1Qyw4REFBT0E7b0JBQUNtRixJQUFJO3dCQUFFaUMsYUFBYTtvQkFBUzs7Ozs7OzhCQUNyQyw4REFBQ3hILHlEQUFHQTtvQkFBQ3VGLElBQUk7d0JBQUVrQyxHQUFHO29CQUFFOzhCQUNkLDRFQUFDckcsNERBQUlBO3dCQUFDc0csU0FBUzt3QkFBQ3hDLFNBQVM7OzBDQUN2Qiw4REFBQzlELDREQUFJQTtnQ0FBQzRGLE1BQU07b0NBQUVuQixJQUFJO29DQUFJOEIsSUFBSTtvQ0FBRzdCLElBQUk7Z0NBQUU7MENBQ2pDLDRFQUFDOUYseURBQUdBOztzREFDRiw4REFBQ00saUVBQVVBOzRDQUFDaUcsU0FBUTs0Q0FBUUMsT0FBTTs0Q0FBaUJqQixJQUFJO2dEQUFFZ0MsSUFBSTs0Q0FBSTtzREFDOUR2RSxFQUFFOzs7Ozs7c0RBRUwsOERBQUMxQyxpRUFBVUE7NENBQUNpRyxTQUFRO3NEQUNqQmMsT0FBT08sc0JBQXNCLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUl4Qyw4REFBQ3hHLDREQUFJQTtnQ0FBQzRGLE1BQU07b0NBQUVuQixJQUFJO29DQUFJOEIsSUFBSTtvQ0FBRzdCLElBQUk7Z0NBQUU7MENBQ2pDLDRFQUFDOUYseURBQUdBOztzREFDRiw4REFBQ00saUVBQVVBOzRDQUFDaUcsU0FBUTs0Q0FBUUMsT0FBTTs0Q0FBaUJqQixJQUFJO2dEQUFFZ0MsSUFBSTs0Q0FBSTtzREFDOUR2RSxFQUFFOzs7Ozs7c0RBRUwsOERBQUMxQyxpRUFBVUE7NENBQUNpRyxTQUFRO3NEQUNqQmMsT0FBT1EscUJBQXFCLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUl2Qyw4REFBQ3pHLDREQUFJQTtnQ0FBQzRGLE1BQU07b0NBQUVuQixJQUFJO29DQUFJOEIsSUFBSTtvQ0FBRzdCLElBQUk7Z0NBQUU7MENBQ2pDLDRFQUFDOUYseURBQUdBOztzREFDRiw4REFBQ00saUVBQVVBOzRDQUFDaUcsU0FBUTs0Q0FBUUMsT0FBTTs0Q0FBaUJqQixJQUFJO2dEQUFFZ0MsSUFBSTs0Q0FBSTtzREFDOUR2RSxFQUFFOzs7Ozs7c0RBRUwsOERBQUMxQyxpRUFBVUE7NENBQUNpRyxTQUFROztnREFDakJjLE9BQU9TLGNBQWMsSUFBSTtnREFBRTtnREFBRVQsT0FBT1Usa0JBQWtCLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJakUsOERBQUMzRyw0REFBSUE7Z0NBQUM0RixNQUFNO29DQUFFbkIsSUFBSTtvQ0FBSThCLElBQUk7b0NBQUc3QixJQUFJO2dDQUFFOzBDQUNqQyw0RUFBQzlGLHlEQUFHQTs7c0RBQ0YsOERBQUNNLGlFQUFVQTs0Q0FBQ2lHLFNBQVE7NENBQVFDLE9BQU07NENBQWlCakIsSUFBSTtnREFBRWdDLElBQUk7NENBQUk7c0RBQzlEdkUsRUFBRTs7Ozs7O3NEQUVMLDhEQUFDMUMsaUVBQVVBOzRDQUFDaUcsU0FBUTs7Z0RBQ2pCYyxPQUFPVyxZQUFZLElBQUk7Z0RBQUU7Z0RBQUVYLE9BQU9ZLGdCQUFnQixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQVF2RTtJQUVBLHFCQUNFLDhEQUFDL0gsMkRBQUtBO1FBQUNnRixTQUFTOztZQUVia0M7MEJBR0QsOERBQUNuSCwyREFBSUE7Z0JBQUNzRixJQUFJO29CQUFFK0IsV0FBVztnQkFBTzs7a0NBQzVCLDhEQUFDakgsaUVBQVVBO3dCQUNUMEcsT0FBTy9ELEVBQUU7d0JBQ1RrRixzQkFDRSw4REFBQy9ILDZEQUFNQTs0QkFDTG9HLFNBQVE7NEJBQ1JTLE1BQUs7NEJBQ0xtQix5QkFBVyw4REFBQzlHLDJEQUFPQTtnQ0FBQzZGLE1BQUs7Ozs7Ozs0QkFDekJELFNBQVNqRDs0QkFDVHVCLElBQUk7Z0NBQUVnQyxJQUFJOzRCQUFFOzRCQUNaakMsVUFBVXZDO3NDQUVUQyxFQUFFOzs7Ozs7Ozs7OztrQ0FJVCw4REFBQzVDLDhEQUFPQTt3QkFBQ21GLElBQUk7NEJBQUVpQyxhQUFhO3dCQUFTOzs7Ozs7a0NBQ3JDLDhEQUFDeEgseURBQUdBO3dCQUFDdUYsSUFBSTs0QkFBRWtDLEdBQUc7d0JBQUU7OzRCQUNieEI7NEJBRUE3QyxXQUFXZSxNQUFNLENBQUNDLE1BQU0sR0FBRyxtQkFDMUIsOERBQUNwRSx5REFBR0E7Z0NBQUN1RixJQUFJO29DQUFFcUIsSUFBSTtvQ0FBR2pCLFNBQVM7b0NBQVFtQixnQkFBZ0I7Z0NBQVM7MENBQzFELDRFQUFDM0csNkRBQU1BO29DQUNMb0csU0FBUTtvQ0FDUkMsT0FBTTtvQ0FDTjJCLHlCQUFXLDhEQUFDOUcsMkRBQU9BO3dDQUFDNkYsTUFBSzs7Ozs7O29DQUN6QkQsU0FBU2pEO29DQUNUc0IsVUFBVXZDOzhDQUVUQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRYiw4REFBQ2pDLDZEQUFNQTtnQkFDTHFILE1BQU03RTtnQkFDTjhFLFNBQVM5RDtnQkFDVG9DLFVBQVM7Z0JBQ1QyQixTQUFTOztrQ0FFVCw4REFBQ3RILGtFQUFXQTtrQ0FDVHlDLHlCQUF5QixPQUN0QlQsRUFBRSw2REFDRkEsRUFBRTs7Ozs7O2tDQUVSLDhEQUFDL0Isb0VBQWFBO2tDQUNYZ0U7Ozs7OztrQ0FFSCw4REFBQy9ELG9FQUFhQTs7MENBQ1osOERBQUNmLDZEQUFNQTtnQ0FBQzhHLFNBQVMxQzswQ0FDZHZCLEVBQUU7Ozs7OzswQ0FFTCw4REFBQzdDLDZEQUFNQTtnQ0FBQ29HLFNBQVE7Z0NBQVlVLFNBQVNqQzswQ0FDbENoQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTVQsOERBQUN4Qix1RUFBYUE7Z0JBQ1o0RyxNQUFNdkU7Z0JBQ053RSxTQUFTekQ7Z0JBQ1RtQyxPQUFPL0QsRUFBRTtnQkFDVHVGLFNBQVN2RixFQUFFO2dCQUNYa0Ysc0JBQ0UsOERBQUMvSCw2REFBTUE7b0JBQUNvRyxTQUFRO29CQUFZQyxPQUFNO29CQUFRUyxTQUFTbEM7OEJBQ2hEL0IsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNZjtHQWhkd0JIOztRQUlSdEIscURBQVlBO1FBQ1l6QiwyREFBY0E7UUFFakNDLDBEQUFhQTs7O0tBUFY4QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxtLm1hbGlrXFxzb3VyY2VcXHJlcG9zXFxSb3NzZXRhXFxGcm9udGVuZFxcc3JjXFxzZWN0aW9uc1xcb3JkZXJzXFxwbHQxXFxmb3Jtc1xccGx0MS1tZXJjaGFuZGlzZS1wb3NpdGlvbnMtZm9ybS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgdXNlRm9ybUNvbnRleHQsIHVzZUZpZWxkQXJyYXkgfSBmcm9tICdyZWFjdC1ob29rLWZvcm0nO1xyXG5cclxuaW1wb3J0IEJveCBmcm9tICdAbXVpL21hdGVyaWFsL0JveCc7XHJcbmltcG9ydCBDYXJkIGZyb20gJ0BtdWkvbWF0ZXJpYWwvQ2FyZCc7XHJcbmltcG9ydCBTdGFjayBmcm9tICdAbXVpL21hdGVyaWFsL1N0YWNrJztcclxuaW1wb3J0IEJ1dHRvbiBmcm9tICdAbXVpL21hdGVyaWFsL0J1dHRvbic7XHJcbmltcG9ydCBEaXZpZGVyIGZyb20gJ0BtdWkvbWF0ZXJpYWwvRGl2aWRlcic7XHJcbmltcG9ydCBDYXJkSGVhZGVyIGZyb20gJ0BtdWkvbWF0ZXJpYWwvQ2FyZEhlYWRlcic7XHJcbmltcG9ydCBUeXBvZ3JhcGh5IGZyb20gJ0BtdWkvbWF0ZXJpYWwvVHlwb2dyYXBoeSc7XHJcbmltcG9ydCBJY29uQnV0dG9uIGZyb20gJ0BtdWkvbWF0ZXJpYWwvSWNvbkJ1dHRvbic7XHJcbmltcG9ydCBUYWJsZSBmcm9tICdAbXVpL21hdGVyaWFsL1RhYmxlJztcclxuaW1wb3J0IFRhYmxlQm9keSBmcm9tICdAbXVpL21hdGVyaWFsL1RhYmxlQm9keSc7XHJcbmltcG9ydCBUYWJsZUNlbGwgZnJvbSAnQG11aS9tYXRlcmlhbC9UYWJsZUNlbGwnO1xyXG5pbXBvcnQgVGFibGVDb250YWluZXIgZnJvbSAnQG11aS9tYXRlcmlhbC9UYWJsZUNvbnRhaW5lcic7XHJcbmltcG9ydCBUYWJsZUhlYWQgZnJvbSAnQG11aS9tYXRlcmlhbC9UYWJsZUhlYWQnO1xyXG5pbXBvcnQgVGFibGVSb3cgZnJvbSAnQG11aS9tYXRlcmlhbC9UYWJsZVJvdyc7XHJcbmltcG9ydCBQYXBlciBmcm9tICdAbXVpL21hdGVyaWFsL1BhcGVyJztcclxuaW1wb3J0IERpYWxvZyBmcm9tICdAbXVpL21hdGVyaWFsL0RpYWxvZyc7XHJcbmltcG9ydCBEaWFsb2dUaXRsZSBmcm9tICdAbXVpL21hdGVyaWFsL0RpYWxvZ1RpdGxlJztcclxuaW1wb3J0IERpYWxvZ0NvbnRlbnQgZnJvbSAnQG11aS9tYXRlcmlhbC9EaWFsb2dDb250ZW50JztcclxuaW1wb3J0IERpYWxvZ0FjdGlvbnMgZnJvbSAnQG11aS9tYXRlcmlhbC9EaWFsb2dBY3Rpb25zJztcclxuaW1wb3J0IFRvb2x0aXAgZnJvbSAnQG11aS9tYXRlcmlhbC9Ub29sdGlwJztcclxuaW1wb3J0IEdyaWQgZnJvbSAnQG11aS9tYXRlcmlhbC9HcmlkMic7XHJcblxyXG5pbXBvcnQgeyBJY29uaWZ5IH0gZnJvbSAnc3JjL2NvbXBvbmVudHMvaWNvbmlmeSc7XHJcbmltcG9ydCB7IEZpZWxkIH0gZnJvbSAnc3JjL2NvbXBvbmVudHMvaG9vay1mb3JtJztcclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRlIH0gZnJvbSAnc3JjL2xvY2FsZXMnO1xyXG5pbXBvcnQgeyBDb25maXJtRGlhbG9nIH0gZnJvbSAnc3JjL2NvbXBvbmVudHMvY3VzdG9tLWRpYWxvZyc7XHJcblxyXG4vLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFBMVDFNZXJjaGFuZGlzZVBvc2l0aW9uRGF0YSB7XHJcbiAgaWQ/OiBzdHJpbmc7XHJcbiAgaW52b2ljZVBvc2l0aW9uTnVtYmVyOiBudW1iZXI7XHJcbiAgZ29vZHNEZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gIHRyYW5zbGF0ZWRHb29kc0Rlc2NyaXB0aW9uOiBzdHJpbmc7XHJcbiAgcGFja2FnZU1hcmtzOiBzdHJpbmc7XHJcbiAgbnVtYmVyT2ZQYWNrYWdlczogbnVtYmVyO1xyXG4gIHBhY2thZ2VUeXBlOiBzdHJpbmc7XHJcbiAgcGFja2FnZVR5cGVEZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gIGNvbW1vZGl0eUNvZGU6IHN0cmluZztcclxuICBmdWxsVGFyaWZmQ29kZTogc3RyaW5nO1xyXG4gIGdyb3NzTWFzczogbnVtYmVyO1xyXG4gIGdyb3NzTWFzc1VuaXQ6IHN0cmluZztcclxuICBuZXRNYXNzOiBudW1iZXI7XHJcbiAgbmV0TWFzc1VuaXQ6IHN0cmluZztcclxuICBwb3NpdGlvblZhbHVlOiBudW1iZXI7XHJcbiAgcG9zaXRpb25WYWx1ZUN1cnJlbmN5OiBzdHJpbmc7XHJcbiAgY29udGFpbmVyTnVtYmVyczogc3RyaW5nW107XHJcbiAgcHJldmlvdXNEb2N1bWVudHM6IHN0cmluZ1tdO1xyXG4gIG1lcmNoYW5kaXNlUG9zaXRpb25zSWQ6IHN0cmluZztcclxufVxyXG5cclxuY29uc3QgREVGQVVMVF9QT1NJVElPTiA9IHtcclxuICBpZDogbnVsbCxcclxuICBpbnZvaWNlUG9zaXRpb25OdW1iZXI6IDAsXHJcbiAgZ29vZHNEZXNjcmlwdGlvbjogJycsXHJcbiAgdHJhbnNsYXRlZEdvb2RzRGVzY3JpcHRpb246ICcnLFxyXG4gIHBhY2thZ2VNYXJrczogJycsXHJcbiAgbnVtYmVyT2ZQYWNrYWdlczogMCxcclxuICBwYWNrYWdlVHlwZTogJycsXHJcbiAgcGFja2FnZVR5cGVEZXNjcmlwdGlvbjogJycsXHJcbiAgY29tbW9kaXR5Q29kZTogJycsXHJcbiAgZnVsbFRhcmlmZkNvZGU6ICcnLFxyXG4gIGdyb3NzTWFzczogMCxcclxuICBncm9zc01hc3NVbml0OiAna2cnLFxyXG4gIG5ldE1hc3M6IDAsXHJcbiAgbmV0TWFzc1VuaXQ6ICdrZycsXHJcbiAgcG9zaXRpb25WYWx1ZTogMCxcclxuICBwb3NpdGlvblZhbHVlQ3VycmVuY3k6ICdVU0QnLFxyXG4gIGNvbnRhaW5lck51bWJlcnM6IFtdLFxyXG4gIHByZXZpb3VzRG9jdW1lbnRzOiBbXSxcclxuICBtZXJjaGFuZGlzZVBvc2l0aW9uc0lkOiAnJyxcclxufTtcclxuXHJcbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuXHJcbmludGVyZmFjZSBQTFQxTWVyY2hhbmRpc2VQb3NpdGlvbnNGb3JtUHJvcHMge1xyXG4gIHQxT3JkZXJJZDogc3RyaW5nO1xyXG4gIHJlYWRPbmx5PzogYm9vbGVhbjtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUExUMU1lcmNoYW5kaXNlUG9zaXRpb25zRm9ybSh7XHJcbiAgdDFPcmRlcklkLFxyXG4gIHJlYWRPbmx5ID0gZmFsc2UsXHJcbn06IFBMVDFNZXJjaGFuZGlzZVBvc2l0aW9uc0Zvcm1Qcm9wcykge1xyXG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRlKCk7XHJcbiAgY29uc3QgeyBjb250cm9sLCB3YXRjaCwgZ2V0VmFsdWVzIH0gPSB1c2VGb3JtQ29udGV4dCgpO1xyXG5cclxuICBjb25zdCBmaWVsZEFycmF5ID0gdXNlRmllbGRBcnJheSh7XHJcbiAgICBjb250cm9sLFxyXG4gICAgbmFtZTogJ21lcmNoYW5kaXNlUG9zaXRpb25zLnBvc2l0aW9ucycsXHJcbiAgfSk7XHJcblxyXG4gIGNvbnN0IG1lcmNoYW5kaXNlUG9zaXRpb25zID0gd2F0Y2goJ21lcmNoYW5kaXNlUG9zaXRpb25zJyk7XHJcblxyXG4gIC8vIFN0YXRlIGZvciBwb3NpdGlvbiBkaWFsb2dcclxuICBjb25zdCBbb3BlblBvc2l0aW9uRGlhbG9nLCBzZXRPcGVuUG9zaXRpb25EaWFsb2ddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtjdXJyZW50UG9zaXRpb25JbmRleCwgc2V0Q3VycmVudFBvc2l0aW9uSW5kZXhdID0gdXNlU3RhdGU8bnVtYmVyIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2RlbGV0ZVBvc2l0aW9uSW5kZXgsIHNldERlbGV0ZVBvc2l0aW9uSW5kZXhdID0gdXNlU3RhdGU8bnVtYmVyIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW29wZW5EZWxldGVDb25maXJtLCBzZXRPcGVuRGVsZXRlQ29uZmlybV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgZGVsZXRlQnV0dG9uUmVmID0gdXNlUmVmPEhUTUxCdXR0b25FbGVtZW50PihudWxsKTtcclxuXHJcbiAgLy8gSGFuZGxlIG9wZW5pbmcgdGhlIHBvc2l0aW9uIGRpYWxvZyBmb3IgYWRkaW5nIGEgbmV3IHBvc2l0aW9uXHJcbiAgY29uc3QgaGFuZGxlQWRkUG9zaXRpb24gPSAoKSA9PiB7XHJcbiAgICBzZXRDdXJyZW50UG9zaXRpb25JbmRleChudWxsKTtcclxuICAgIC8vIEFkZCBhIG5ldyBwb3NpdGlvbiB3aXRoIGRlZmF1bHQgdmFsdWVzXHJcbiAgICBjb25zdCBuZXdQb3NpdGlvbiA9IHtcclxuICAgICAgLi4uREVGQVVMVF9QT1NJVElPTixcclxuICAgICAgbWVyY2hhbmRpc2VQb3NpdGlvbnNJZDogbWVyY2hhbmRpc2VQb3NpdGlvbnM/LmlkIHx8ICcnLFxyXG4gICAgfTtcclxuICAgIGZpZWxkQXJyYXkuYXBwZW5kKG5ld1Bvc2l0aW9uKTtcclxuICAgIHNldEN1cnJlbnRQb3NpdGlvbkluZGV4KGZpZWxkQXJyYXkuZmllbGRzLmxlbmd0aCk7IC8vIFNldCB0byB0aGUgbmV3IGluZGV4XHJcbiAgICBzZXRPcGVuUG9zaXRpb25EaWFsb2codHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIG9wZW5pbmcgdGhlIHBvc2l0aW9uIGRpYWxvZyBmb3IgZWRpdGluZyBhbiBleGlzdGluZyBwb3NpdGlvblxyXG4gIGNvbnN0IGhhbmRsZUVkaXRQb3NpdGlvbiA9IChpbmRleDogbnVtYmVyKSA9PiB7XHJcbiAgICBzZXRDdXJyZW50UG9zaXRpb25JbmRleChpbmRleCk7XHJcbiAgICBzZXRPcGVuUG9zaXRpb25EaWFsb2codHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIGNsb3NpbmcgdGhlIHBvc2l0aW9uIGRpYWxvZ1xyXG4gIGNvbnN0IGhhbmRsZUNsb3NlUG9zaXRpb25EaWFsb2cgPSAoKSA9PiB7XHJcbiAgICBzZXRPcGVuUG9zaXRpb25EaWFsb2coZmFsc2UpO1xyXG4gICAgLy8gSWYgd2Ugd2VyZSBhZGRpbmcgYSBuZXcgcG9zaXRpb24gYW5kIHVzZXIgY2FuY2VscywgcmVtb3ZlIHRoZSBlbXB0eSBwb3NpdGlvblxyXG4gICAgaWYgKGN1cnJlbnRQb3NpdGlvbkluZGV4ICE9PSBudWxsICYmIGN1cnJlbnRQb3NpdGlvbkluZGV4ID09PSBmaWVsZEFycmF5LmZpZWxkcy5sZW5ndGggLSAxKSB7XHJcbiAgICAgIGNvbnN0IHBvc2l0aW9uID0gZ2V0VmFsdWVzKGBtZXJjaGFuZGlzZVBvc2l0aW9ucy5wb3NpdGlvbnMuJHtjdXJyZW50UG9zaXRpb25JbmRleH1gKTtcclxuICAgICAgLy8gQ2hlY2sgaWYgaXQncyBhbiBlbXB0eSBwb3NpdGlvbiAoYWxsIGZpZWxkcyBhcmUgZGVmYXVsdCB2YWx1ZXMpXHJcbiAgICAgIGNvbnN0IGlzRW1wdHkgPSAhcG9zaXRpb24uZ29vZHNEZXNjcmlwdGlvbiAmJiAhcG9zaXRpb24udHJhbnNsYXRlZEdvb2RzRGVzY3JpcHRpb24gJiZcclxuICAgICAgICAgICAgICAgICAgICAgIXBvc2l0aW9uLnBhY2thZ2VNYXJrcyAmJiAhcG9zaXRpb24uY29tbW9kaXR5Q29kZSAmJiAhcG9zaXRpb24uZnVsbFRhcmlmZkNvZGUgJiZcclxuICAgICAgICAgICAgICAgICAgICAgcG9zaXRpb24ubnVtYmVyT2ZQYWNrYWdlcyA9PT0gMCAmJiBwb3NpdGlvbi5ncm9zc01hc3MgPT09IDAgJiYgcG9zaXRpb24ubmV0TWFzcyA9PT0gMDtcclxuICAgICAgaWYgKGlzRW1wdHkpIHtcclxuICAgICAgICBmaWVsZEFycmF5LnJlbW92ZShjdXJyZW50UG9zaXRpb25JbmRleCk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgIHNldEN1cnJlbnRQb3NpdGlvbkluZGV4KG51bGwpO1xyXG4gIH07XHJcblxyXG4gIC8vIEhhbmRsZSBvcGVuaW5nIHRoZSBkZWxldGUgY29uZmlybWF0aW9uIGRpYWxvZ1xyXG4gIGNvbnN0IGhhbmRsZU9wZW5EZWxldGVDb25maXJtID0gKGluZGV4OiBudW1iZXIpID0+IHtcclxuICAgIHNldERlbGV0ZVBvc2l0aW9uSW5kZXgoaW5kZXgpO1xyXG4gICAgc2V0T3BlbkRlbGV0ZUNvbmZpcm0odHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIGNsb3NpbmcgdGhlIGRlbGV0ZSBjb25maXJtYXRpb24gZGlhbG9nXHJcbiAgY29uc3QgaGFuZGxlQ2xvc2VEZWxldGVDb25maXJtID0gKCkgPT4ge1xyXG4gICAgc2V0T3BlbkRlbGV0ZUNvbmZpcm0oZmFsc2UpO1xyXG4gICAgaWYgKGRlbGV0ZVBvc2l0aW9uSW5kZXggIT09IG51bGwpIHtcclxuICAgICAgZGVsZXRlQnV0dG9uUmVmLmN1cnJlbnQ/LmZvY3VzKCk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIGRlbGV0aW5nIGEgcG9zaXRpb25cclxuICBjb25zdCBoYW5kbGVEZWxldGVQb3NpdGlvbiA9ICgpID0+IHtcclxuICAgIGlmIChkZWxldGVQb3NpdGlvbkluZGV4ICE9PSBudWxsKSB7XHJcbiAgICAgIGZpZWxkQXJyYXkucmVtb3ZlKGRlbGV0ZVBvc2l0aW9uSW5kZXgpO1xyXG4gICAgfVxyXG4gICAgaGFuZGxlQ2xvc2VEZWxldGVDb25maXJtKCk7XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIHNhdmluZyBhIHBvc2l0aW9uIChqdXN0IGNsb3NlIHRoZSBkaWFsb2cgc2luY2UgZm9ybSBpcyBhbHJlYWR5IHVwZGF0ZWQpXHJcbiAgY29uc3QgaGFuZGxlU2F2ZVBvc2l0aW9uID0gKCkgPT4ge1xyXG4gICAgaGFuZGxlQ2xvc2VQb3NpdGlvbkRpYWxvZygpO1xyXG4gIH07XHJcblxyXG4gIC8vIFJlbmRlciB0aGUgcG9zaXRpb24gZm9ybSBpbiB0aGUgZGlhbG9nIC0gdXNpbmcgUmVhY3QgSG9vayBGb3JtIGZpZWxkc1xyXG4gIGNvbnN0IHJlbmRlclBvc2l0aW9uRm9ybSA9ICgpID0+IHtcclxuICAgIGlmIChjdXJyZW50UG9zaXRpb25JbmRleCA9PT0gbnVsbCkgcmV0dXJuIG51bGw7XHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgPFN0YWNrIHNwYWNpbmc9ezN9PlxyXG4gICAgICAgIHsvKiBJbnZvaWNlIFBvc2l0aW9uIE51bWJlciAqL31cclxuICAgICAgICA8RmllbGQuVGV4dFxyXG4gICAgICAgICAgbmFtZT17YG1lcmNoYW5kaXNlUG9zaXRpb25zLnBvc2l0aW9ucy4ke2N1cnJlbnRQb3NpdGlvbkluZGV4fS5pbnZvaWNlUG9zaXRpb25OdW1iZXJgfVxyXG4gICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMubWVyY2hhbmRpc2VQb3NpdGlvbnMuZm9ybS5pbnZvaWNlUG9zaXRpb25OdW1iZXInKX1cclxuICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxyXG4gICAgICAgICAgZGlzYWJsZWQ9e3JlYWRPbmx5fVxyXG4gICAgICAgIC8+XHJcblxyXG4gICAgICAgIHsvKiBHb29kcyBEZXNjcmlwdGlvbiBTZWN0aW9uICovfVxyXG4gICAgICAgIDxCb3hcclxuICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgIHJvd0dhcDogMyxcclxuICAgICAgICAgICAgcHQ6IDIsXHJcbiAgICAgICAgICAgIGNvbHVtbkdhcDogMixcclxuICAgICAgICAgICAgZGlzcGxheTogJ2dyaWQnLFxyXG4gICAgICAgICAgICBncmlkVGVtcGxhdGVDb2x1bW5zOiB7IHhzOiAncmVwZWF0KDEsIDFmciknLCBtZDogJ3JlcGVhdCgyLCAxZnIpJyB9LFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICA8RmllbGQuVGV4dFxyXG4gICAgICAgICAgICBuYW1lPXtgbWVyY2hhbmRpc2VQb3NpdGlvbnMucG9zaXRpb25zLiR7Y3VycmVudFBvc2l0aW9uSW5kZXh9Lmdvb2RzRGVzY3JpcHRpb25gfVxyXG4gICAgICAgICAgICBsYWJlbD17dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5tZXJjaGFuZGlzZVBvc2l0aW9ucy5mb3JtLmdvb2RzRGVzY3JpcHRpb24nKX1cclxuICAgICAgICAgICAgbXVsdGlsaW5lXHJcbiAgICAgICAgICAgIHJvd3M9ezJ9XHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8RmllbGQuVGV4dFxyXG4gICAgICAgICAgICBuYW1lPXtgbWVyY2hhbmRpc2VQb3NpdGlvbnMucG9zaXRpb25zLiR7Y3VycmVudFBvc2l0aW9uSW5kZXh9LnRyYW5zbGF0ZWRHb29kc0Rlc2NyaXB0aW9uYH1cclxuICAgICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMubWVyY2hhbmRpc2VQb3NpdGlvbnMuZm9ybS50cmFuc2xhdGVkR29vZHNEZXNjcmlwdGlvbicpfVxyXG4gICAgICAgICAgICBtdWx0aWxpbmVcclxuICAgICAgICAgICAgcm93cz17Mn1cclxuICAgICAgICAgICAgZGlzYWJsZWQ9e3JlYWRPbmx5fVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L0JveD5cclxuXHJcbiAgICAgICAgey8qIFBhY2thZ2UgSW5mb3JtYXRpb24gKi99XHJcbiAgICAgICAgPEJveFxyXG4gICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgcm93R2FwOiAyLjUsXHJcbiAgICAgICAgICAgIGNvbHVtbkdhcDogMixcclxuICAgICAgICAgICAgZGlzcGxheTogJ2dyaWQnLFxyXG4gICAgICAgICAgICBncmlkVGVtcGxhdGVDb2x1bW5zOiB7IHhzOiAncmVwZWF0KDEsIDFmciknLCBtZDogJ3JlcGVhdCgzLCAxZnIpJyB9LFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICA8RmllbGQuVGV4dFxyXG4gICAgICAgICAgICBuYW1lPXtgbWVyY2hhbmRpc2VQb3NpdGlvbnMucG9zaXRpb25zLiR7Y3VycmVudFBvc2l0aW9uSW5kZXh9Lm51bWJlck9mUGFja2FnZXNgfVxyXG4gICAgICAgICAgICBsYWJlbD17dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5tZXJjaGFuZGlzZVBvc2l0aW9ucy5mb3JtLm51bWJlck9mUGFja2FnZXMnKX1cclxuICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8RmllbGQuVGV4dFxyXG4gICAgICAgICAgICBuYW1lPXtgbWVyY2hhbmRpc2VQb3NpdGlvbnMucG9zaXRpb25zLiR7Y3VycmVudFBvc2l0aW9uSW5kZXh9LnBhY2thZ2VUeXBlYH1cclxuICAgICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMubWVyY2hhbmRpc2VQb3NpdGlvbnMuZm9ybS5wYWNrYWdlVHlwZScpfVxyXG5cclxuICAgICAgICAgICAgZGlzYWJsZWQ9e3JlYWRPbmx5fVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICAgIDxGaWVsZC5UZXh0XHJcbiAgICAgICAgICAgIG5hbWU9e2BtZXJjaGFuZGlzZVBvc2l0aW9ucy5wb3NpdGlvbnMuJHtjdXJyZW50UG9zaXRpb25JbmRleH0ucGFja2FnZVR5cGVEZXNjcmlwdGlvbmB9XHJcbiAgICAgICAgICAgIGxhYmVsPXt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLm1lcmNoYW5kaXNlUG9zaXRpb25zLmZvcm0ucGFja2FnZVR5cGVEZXNjcmlwdGlvbicpfVxyXG4gICAgICAgICAgICBkaXNhYmxlZD17cmVhZE9ubHl9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvQm94PlxyXG5cclxuICAgICAgICA8RmllbGQuVGV4dFxyXG4gICAgICAgICAgbmFtZT17YG1lcmNoYW5kaXNlUG9zaXRpb25zLnBvc2l0aW9ucy4ke2N1cnJlbnRQb3NpdGlvbkluZGV4fS5wYWNrYWdlTWFya3NgfVxyXG4gICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMubWVyY2hhbmRpc2VQb3NpdGlvbnMuZm9ybS5wYWNrYWdlTWFya3MnKX1cclxuICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAvPlxyXG5cclxuICAgICAgICB7LyogQ29kZXMgKi99XHJcbiAgICAgICAgPEJveFxyXG4gICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgcm93R2FwOiAzLFxyXG4gICAgICAgICAgICBjb2x1bW5HYXA6IDIsXHJcbiAgICAgICAgICAgIGRpc3BsYXk6ICdncmlkJyxcclxuICAgICAgICAgICAgZ3JpZFRlbXBsYXRlQ29sdW1uczogeyB4czogJ3JlcGVhdCgxLCAxZnIpJywgbWQ6ICdyZXBlYXQoMiwgMWZyKScgfSxcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPEZpZWxkLlRleHRcclxuICAgICAgICAgICAgbmFtZT17YG1lcmNoYW5kaXNlUG9zaXRpb25zLnBvc2l0aW9ucy4ke2N1cnJlbnRQb3NpdGlvbkluZGV4fS5jb21tb2RpdHlDb2RlYH1cclxuICAgICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMubWVyY2hhbmRpc2VQb3NpdGlvbnMuZm9ybS5jb21tb2RpdHlDb2RlJyl9XHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8RmllbGQuVGV4dFxyXG4gICAgICAgICAgICBuYW1lPXtgbWVyY2hhbmRpc2VQb3NpdGlvbnMucG9zaXRpb25zLiR7Y3VycmVudFBvc2l0aW9uSW5kZXh9LmZ1bGxUYXJpZmZDb2RlYH1cclxuICAgICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMubWVyY2hhbmRpc2VQb3NpdGlvbnMuZm9ybS5mdWxsVGFyaWZmQ29kZScpfVxyXG4gICAgICAgICAgICBkaXNhYmxlZD17cmVhZE9ubHl9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvQm94PlxyXG5cclxuICAgICAgICB7LyogV2VpZ2h0IEluZm9ybWF0aW9uICovfVxyXG4gICAgICAgIDxCb3hcclxuICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgIHJvd0dhcDogMyxcclxuICAgICAgICAgICAgY29sdW1uR2FwOiAyLFxyXG4gICAgICAgICAgICBkaXNwbGF5OiAnZ3JpZCcsXHJcbiAgICAgICAgICAgIGdyaWRUZW1wbGF0ZUNvbHVtbnM6IHsgeHM6ICdyZXBlYXQoMSwgMWZyKScsIG1kOiAncmVwZWF0KDQsIDFmciknIH0sXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxGaWVsZC5UZXh0XHJcbiAgICAgICAgICAgIG5hbWU9e2BtZXJjaGFuZGlzZVBvc2l0aW9ucy5wb3NpdGlvbnMuJHtjdXJyZW50UG9zaXRpb25JbmRleH0uZ3Jvc3NNYXNzYH1cclxuICAgICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMubWVyY2hhbmRpc2VQb3NpdGlvbnMuZm9ybS5ncm9zc01hc3MnKX1cclxuICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8RmllbGQuVGV4dFxyXG4gICAgICAgICAgICBuYW1lPXtgbWVyY2hhbmRpc2VQb3NpdGlvbnMucG9zaXRpb25zLiR7Y3VycmVudFBvc2l0aW9uSW5kZXh9Lmdyb3NzTWFzc1VuaXRgfVxyXG4gICAgICAgICAgICBsYWJlbD17dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5tZXJjaGFuZGlzZVBvc2l0aW9ucy5mb3JtLmdyb3NzTWFzc1VuaXQnKX1cclxuICAgICAgICAgICAgZGlzYWJsZWQ9e3JlYWRPbmx5fVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICAgIDxGaWVsZC5UZXh0XHJcbiAgICAgICAgICAgIG5hbWU9e2BtZXJjaGFuZGlzZVBvc2l0aW9ucy5wb3NpdGlvbnMuJHtjdXJyZW50UG9zaXRpb25JbmRleH0ubmV0TWFzc2B9XHJcbiAgICAgICAgICAgIGxhYmVsPXt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLm1lcmNoYW5kaXNlUG9zaXRpb25zLmZvcm0ubmV0TWFzcycpfVxyXG4gICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcclxuICAgICAgICAgICAgZGlzYWJsZWQ9e3JlYWRPbmx5fVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICAgIDxGaWVsZC5UZXh0XHJcbiAgICAgICAgICAgIG5hbWU9e2BtZXJjaGFuZGlzZVBvc2l0aW9ucy5wb3NpdGlvbnMuJHtjdXJyZW50UG9zaXRpb25JbmRleH0ubmV0TWFzc1VuaXRgfVxyXG4gICAgICAgICAgICBsYWJlbD17dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5tZXJjaGFuZGlzZVBvc2l0aW9ucy5mb3JtLm5ldE1hc3NVbml0Jyl9XHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtyZWFkT25seX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9Cb3g+XHJcblxyXG4gICAgICAgIHsvKiBQb3NpdGlvbiBWYWx1ZSAqL31cclxuICAgICAgICA8Qm94XHJcbiAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICByb3dHYXA6IDIuNSxcclxuICAgICAgICAgICAgY29sdW1uR2FwOiAyLFxyXG4gICAgICAgICAgICBkaXNwbGF5OiAnZ3JpZCcsXHJcbiAgICAgICAgICAgIGdyaWRUZW1wbGF0ZUNvbHVtbnM6IHsgeHM6ICdyZXBlYXQoMSwgMWZyKScsIG1kOiAncmVwZWF0KDIsIDFmciknIH0sXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxGaWVsZC5UZXh0XHJcbiAgICAgICAgICAgIG5hbWU9e2BtZXJjaGFuZGlzZVBvc2l0aW9ucy5wb3NpdGlvbnMuJHtjdXJyZW50UG9zaXRpb25JbmRleH0ucG9zaXRpb25WYWx1ZWB9XHJcbiAgICAgICAgICAgIGxhYmVsPXt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLm1lcmNoYW5kaXNlUG9zaXRpb25zLmZvcm0ucG9zaXRpb25WYWx1ZScpfVxyXG4gICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcclxuICAgICAgICAgICAgZGlzYWJsZWQ9e3JlYWRPbmx5fVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICAgIDxGaWVsZC5UZXh0XHJcbiAgICAgICAgICAgIG5hbWU9e2BtZXJjaGFuZGlzZVBvc2l0aW9ucy5wb3NpdGlvbnMuJHtjdXJyZW50UG9zaXRpb25JbmRleH0ucG9zaXRpb25WYWx1ZUN1cnJlbmN5YH1cclxuICAgICAgICAgICAgbGFiZWw9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMubWVyY2hhbmRpc2VQb3NpdGlvbnMuZm9ybS5wb3NpdGlvblZhbHVlQ3VycmVuY3knKX1cclxuICAgICAgICAgICAgZGlzYWJsZWQ9e3JlYWRPbmx5fVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L0JveD5cclxuICAgICAgPC9TdGFjaz5cclxuICAgICk7XHJcbiAgfTtcclxuXHJcbiAgLy8gUmVuZGVyIHRoZSBwb3NpdGlvbnMgdGFibGVcclxuICBjb25zdCByZW5kZXJQb3NpdGlvbnNUYWJsZSA9ICgpID0+IChcclxuICAgIDxUYWJsZUNvbnRhaW5lciBjb21wb25lbnQ9e1BhcGVyfT5cclxuICAgICAgPFRhYmxlIHN4PXt7IG1pbldpZHRoOiA2NTAgfX0gYXJpYS1sYWJlbD1cIm1lcmNoYW5kaXNlIHBvc2l0aW9ucyB0YWJsZVwiPlxyXG4gICAgICAgIDxUYWJsZUhlYWQ+XHJcbiAgICAgICAgICA8VGFibGVSb3c+XHJcbiAgICAgICAgICAgIDxUYWJsZUNlbGw+e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMubWVyY2hhbmRpc2VQb3NpdGlvbnMuZm9ybS5nb29kc0Rlc2NyaXB0aW9uJyl9PC9UYWJsZUNlbGw+XHJcbiAgICAgICAgICAgIDxUYWJsZUNlbGw+e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMubWVyY2hhbmRpc2VQb3NpdGlvbnMuZm9ybS5udW1iZXJPZlBhY2thZ2VzJyl9PC9UYWJsZUNlbGw+XHJcbiAgICAgICAgICAgIDxUYWJsZUNlbGw+e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMubWVyY2hhbmRpc2VQb3NpdGlvbnMuZm9ybS5wYWNrYWdlVHlwZScpfTwvVGFibGVDZWxsPlxyXG4gICAgICAgICAgICA8VGFibGVDZWxsPnt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLm1lcmNoYW5kaXNlUG9zaXRpb25zLmZvcm0uZ3Jvc3NNYXNzJyl9PC9UYWJsZUNlbGw+XHJcbiAgICAgICAgICAgIDxUYWJsZUNlbGw+e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMubWVyY2hhbmRpc2VQb3NpdGlvbnMuZm9ybS5uZXRNYXNzJyl9PC9UYWJsZUNlbGw+XHJcbiAgICAgICAgICAgIDxUYWJsZUNlbGwgYWxpZ249XCJyaWdodFwiPnt0KCdjb21tb24uYWN0aW9ucycpfTwvVGFibGVDZWxsPlxyXG4gICAgICAgICAgPC9UYWJsZVJvdz5cclxuICAgICAgICA8L1RhYmxlSGVhZD5cclxuICAgICAgICA8VGFibGVCb2R5PlxyXG4gICAgICAgICAge2ZpZWxkQXJyYXkuZmllbGRzLmxlbmd0aCA9PT0gMCA/IChcclxuICAgICAgICAgICAgPFRhYmxlUm93PlxyXG4gICAgICAgICAgICAgIDxUYWJsZUNlbGwgY29sU3Bhbj17Nn0gYWxpZ249XCJjZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIGNvbG9yPVwidGV4dC5zZWNvbmRhcnlcIj5cclxuICAgICAgICAgICAgICAgICAge3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMubWVyY2hhbmRpc2VQb3NpdGlvbnMubm9Qb3NpdGlvbnMnKX1cclxuICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cclxuICAgICAgICAgICAgPC9UYWJsZVJvdz5cclxuICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgIGZpZWxkQXJyYXkuZmllbGRzLm1hcCgoZmllbGQsIGluZGV4KSA9PiB7XHJcbiAgICAgICAgICAgICAgY29uc3QgcG9zaXRpb24gPSBnZXRWYWx1ZXMoYG1lcmNoYW5kaXNlUG9zaXRpb25zLnBvc2l0aW9ucy4ke2luZGV4fWApIHx8IHt9O1xyXG4gICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICA8VGFibGVSb3cga2V5PXtmaWVsZC5pZH0+XHJcbiAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XHJcbiAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgc3g9e3sgbWF4V2lkdGg6IDIwMCB9fT5cclxuICAgICAgICAgICAgICAgICAgICAgIHtwb3NpdGlvbi5nb29kc0Rlc2NyaXB0aW9uIHx8ICctJ31cclxuICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAge3Bvc2l0aW9uLnRyYW5zbGF0ZWRHb29kc0Rlc2NyaXB0aW9uICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJjYXB0aW9uXCIgY29sb3I9XCJ0ZXh0LnNlY29uZGFyeVwiIHN4PXt7IGRpc3BsYXk6ICdibG9jaycsIG10OiAwLjUgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtwb3NpdGlvbi50cmFuc2xhdGVkR29vZHNEZXNjcmlwdGlvbn1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cclxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5cclxuICAgICAgICAgICAgICAgICAgICB7cG9zaXRpb24ubnVtYmVyT2ZQYWNrYWdlcyA/PyAnLSd9XHJcbiAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxyXG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxyXG4gICAgICAgICAgICAgICAgICAgIHtwb3NpdGlvbi5wYWNrYWdlVHlwZURlc2NyaXB0aW9uIHx8IHBvc2l0aW9uLnBhY2thZ2VUeXBlIHx8ICctJ31cclxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XHJcbiAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XHJcbiAgICAgICAgICAgICAgICAgICAge3Bvc2l0aW9uLmdyb3NzTWFzcyA/PyAnLSd9IHtwb3NpdGlvbi5ncm9zc01hc3NVbml0IHx8ICcnfVxyXG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cclxuICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD5cclxuICAgICAgICAgICAgICAgICAgICB7cG9zaXRpb24ubmV0TWFzcyA/PyAnLSd9IHtwb3NpdGlvbi5uZXRNYXNzVW5pdCB8fCAnJ31cclxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XHJcbiAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgYWxpZ249XCJyaWdodFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxTdGFjayBkaXJlY3Rpb249XCJyb3dcIiBzcGFjaW5nPXsxfSBqdXN0aWZ5Q29udGVudD1cImZsZXgtZW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCB0aXRsZT17dCgnY29tbW9uLmVkaXQnKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uQnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtYWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj1cInByaW1hcnlcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUVkaXRQb3NpdGlvbihpbmRleCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3JlYWRPbmx5fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb25pZnkgaWNvbj1cImV2YTplZGl0LWZpbGxcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0ljb25CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCB0aXRsZT17dCgnY29tbW9uLmRlbGV0ZScpfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEljb25CdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yPVwiZXJyb3JcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZU9wZW5EZWxldGVDb25maXJtKGluZGV4KX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICByZWY9e2RlbGV0ZVBvc2l0aW9uSW5kZXggPT09IGluZGV4ID8gZGVsZXRlQnV0dG9uUmVmIDogbnVsbH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17cmVhZE9ubHl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvbmlmeSBpY29uPVwiZXZhOnRyYXNoLTItb3V0bGluZVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvSWNvbkJ1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcD5cclxuICAgICAgICAgICAgICAgICAgICA8L1N0YWNrPlxyXG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cclxuICAgICAgICAgICAgICAgIDwvVGFibGVSb3c+XHJcbiAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9UYWJsZUJvZHk+XHJcbiAgICAgIDwvVGFibGU+XHJcbiAgICA8L1RhYmxlQ29udGFpbmVyPlxyXG4gICk7XHJcblxyXG4gIC8vIFJlbmRlciB0aGUgdG90YWxzIHN1bW1hcnlcclxuICBjb25zdCByZW5kZXJUb3RhbHNTdW1tYXJ5ID0gKCkgPT4ge1xyXG4gICAgY29uc3QgdG90YWxzID0gbWVyY2hhbmRpc2VQb3NpdGlvbnM/LnRvdGFscztcclxuXHJcbiAgICBpZiAoIXRvdGFscykge1xyXG4gICAgICByZXR1cm4gbnVsbDtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8Q2FyZCBzeD17eyBib3hTaGFkb3c6ICdub25lJywgbWI6IDMgfX0+XHJcbiAgICAgICAgPENhcmRIZWFkZXJcclxuICAgICAgICAgIHRpdGxlPXt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLm1lcmNoYW5kaXNlUG9zaXRpb25zLmZvcm0udG90YWxzVGl0bGUnKX1cclxuICAgICAgICAvPlxyXG4gICAgICAgIDxEaXZpZGVyIHN4PXt7IGJvcmRlclN0eWxlOiAnZGFzaGVkJyB9fSAvPlxyXG4gICAgICAgIDxCb3ggc3g9e3sgcDogMyB9fT5cclxuICAgICAgICAgIDxHcmlkIGNvbnRhaW5lciBzcGFjaW5nPXszfT5cclxuICAgICAgICAgICAgPEdyaWQgc2l6ZT17eyB4czogMTIsIHNtOiA2LCBtZDogMyB9fT5cclxuICAgICAgICAgICAgICA8Qm94PlxyXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgY29sb3I9XCJ0ZXh0LnNlY29uZGFyeVwiIHN4PXt7IG1iOiAwLjUgfX0+XHJcbiAgICAgICAgICAgICAgICAgIHt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLm1lcmNoYW5kaXNlUG9zaXRpb25zLmZvcm0udG90YWxOdW1iZXJPZlBvc2l0aW9ucycpfVxyXG4gICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImg2XCI+XHJcbiAgICAgICAgICAgICAgICAgIHt0b3RhbHMudG90YWxOdW1iZXJPZlBvc2l0aW9ucyB8fCAwfVxyXG4gICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICAgIDxHcmlkIHNpemU9e3sgeHM6IDEyLCBzbTogNiwgbWQ6IDMgfX0+XHJcbiAgICAgICAgICAgICAgPEJveD5cclxuICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIGNvbG9yPVwidGV4dC5zZWNvbmRhcnlcIiBzeD17eyBtYjogMC41IH19PlxyXG4gICAgICAgICAgICAgICAgICB7dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5tZXJjaGFuZGlzZVBvc2l0aW9ucy5mb3JtLnRvdGFsTnVtYmVyT2ZQYWNrYWdlcycpfVxyXG4gICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImg2XCI+XHJcbiAgICAgICAgICAgICAgICAgIHt0b3RhbHMudG90YWxOdW1iZXJPZlBhY2thZ2VzIHx8IDB9XHJcbiAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgIDwvR3JpZD5cclxuICAgICAgICAgICAgPEdyaWQgc2l6ZT17eyB4czogMTIsIHNtOiA2LCBtZDogMyB9fT5cclxuICAgICAgICAgICAgICA8Qm94PlxyXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgY29sb3I9XCJ0ZXh0LnNlY29uZGFyeVwiIHN4PXt7IG1iOiAwLjUgfX0+XHJcbiAgICAgICAgICAgICAgICAgIHt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLm1lcmNoYW5kaXNlUG9zaXRpb25zLmZvcm0udG90YWxHcm9zc01hc3MnKX1cclxuICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJoNlwiPlxyXG4gICAgICAgICAgICAgICAgICB7dG90YWxzLnRvdGFsR3Jvc3NNYXNzIHx8IDB9IHt0b3RhbHMudG90YWxHcm9zc01hc3NVbml0IHx8ICcnfVxyXG4gICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICAgIDxHcmlkIHNpemU9e3sgeHM6IDEyLCBzbTogNiwgbWQ6IDMgfX0+XHJcbiAgICAgICAgICAgICAgPEJveD5cclxuICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIGNvbG9yPVwidGV4dC5zZWNvbmRhcnlcIiBzeD17eyBtYjogMC41IH19PlxyXG4gICAgICAgICAgICAgICAgICB7dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5tZXJjaGFuZGlzZVBvc2l0aW9ucy5mb3JtLnRvdGFsTmV0TWFzcycpfVxyXG4gICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImg2XCI+XHJcbiAgICAgICAgICAgICAgICAgIHt0b3RhbHMudG90YWxOZXRNYXNzIHx8IDB9IHt0b3RhbHMudG90YWxOZXRNYXNzVW5pdCB8fCAnJ31cclxuICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgPC9HcmlkPlxyXG4gICAgICAgICAgPC9HcmlkPlxyXG4gICAgICAgIDwvQm94PlxyXG4gICAgICA8L0NhcmQ+XHJcbiAgICApO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8U3RhY2sgc3BhY2luZz17M30+XHJcbiAgICAgIHsvKiBUb3RhbHMgU3VtbWFyeSAqL31cclxuICAgICAge3JlbmRlclRvdGFsc1N1bW1hcnkoKX1cclxuXHJcbiAgICAgIHsvKiBQb3NpdGlvbnMgVGFibGUgKi99XHJcbiAgICAgIDxDYXJkIHN4PXt7IGJveFNoYWRvdzogJ25vbmUnIH19PlxyXG4gICAgICAgIDxDYXJkSGVhZGVyXHJcbiAgICAgICAgICB0aXRsZT17dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5tZXJjaGFuZGlzZVBvc2l0aW9ucy5mb3JtLnBvc2l0aW9uc1RpdGxlJyl9XHJcbiAgICAgICAgICBhY3Rpb249e1xyXG4gICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgdmFyaWFudD1cImNvbnRhaW5lZFwiXHJcbiAgICAgICAgICAgICAgc2l6ZT1cInNtYWxsXCJcclxuICAgICAgICAgICAgICBzdGFydEljb249ezxJY29uaWZ5IGljb249XCJldmE6cGx1cy1maWxsXCIgLz59XHJcbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQWRkUG9zaXRpb259XHJcbiAgICAgICAgICAgICAgc3g9e3sgbWI6IDIgfX1cclxuICAgICAgICAgICAgICBkaXNhYmxlZD17cmVhZE9ubHl9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7dCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5tZXJjaGFuZGlzZVBvc2l0aW9ucy5hZGRQb3NpdGlvbicpfVxyXG4gICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgIH1cclxuICAgICAgICAvPlxyXG4gICAgICAgIDxEaXZpZGVyIHN4PXt7IGJvcmRlclN0eWxlOiAnZGFzaGVkJyB9fSAvPlxyXG4gICAgICAgIDxCb3ggc3g9e3sgcDogMyB9fT5cclxuICAgICAgICAgIHtyZW5kZXJQb3NpdGlvbnNUYWJsZSgpfVxyXG5cclxuICAgICAgICAgIHtmaWVsZEFycmF5LmZpZWxkcy5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgPEJveCBzeD17eyBtdDogMiwgZGlzcGxheTogJ2ZsZXgnLCBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicgfX0+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cInNvZnRcIlxyXG4gICAgICAgICAgICAgICAgY29sb3I9XCJwcmltYXJ5XCJcclxuICAgICAgICAgICAgICAgIHN0YXJ0SWNvbj17PEljb25pZnkgaWNvbj1cImV2YTpwbHVzLWZpbGxcIiAvPn1cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUFkZFBvc2l0aW9ufVxyXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3JlYWRPbmx5fVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHt0KCdwbHQxLmRldGFpbHMuZG9jdW1lbnRzLm1lcmNoYW5kaXNlUG9zaXRpb25zLmFkZFBvc2l0aW9uJyl9XHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICA8L0JveD5cclxuICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgey8qIFBvc2l0aW9uIERpYWxvZyAqL31cclxuICAgICAgPERpYWxvZ1xyXG4gICAgICAgIG9wZW49e29wZW5Qb3NpdGlvbkRpYWxvZ31cclxuICAgICAgICBvbkNsb3NlPXtoYW5kbGVDbG9zZVBvc2l0aW9uRGlhbG9nfVxyXG4gICAgICAgIG1heFdpZHRoPVwibWRcIlxyXG4gICAgICAgIGZ1bGxXaWR0aFxyXG4gICAgICA+XHJcbiAgICAgICAgPERpYWxvZ1RpdGxlPlxyXG4gICAgICAgICAge2N1cnJlbnRQb3NpdGlvbkluZGV4ID09PSBudWxsXHJcbiAgICAgICAgICAgID8gdCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5tZXJjaGFuZGlzZVBvc2l0aW9ucy5hZGRQb3NpdGlvbicpXHJcbiAgICAgICAgICAgIDogdCgncGx0MS5kZXRhaWxzLmRvY3VtZW50cy5tZXJjaGFuZGlzZVBvc2l0aW9ucy5lZGl0UG9zaXRpb24nKX1cclxuICAgICAgICA8L0RpYWxvZ1RpdGxlPlxyXG4gICAgICAgIDxEaWFsb2dDb250ZW50PlxyXG4gICAgICAgICAge3JlbmRlclBvc2l0aW9uRm9ybSgpfVxyXG4gICAgICAgIDwvRGlhbG9nQ29udGVudD5cclxuICAgICAgICA8RGlhbG9nQWN0aW9ucz5cclxuICAgICAgICAgIDxCdXR0b24gb25DbGljaz17aGFuZGxlQ2xvc2VQb3NpdGlvbkRpYWxvZ30+XHJcbiAgICAgICAgICAgIHt0KCdjb21tb24uY2FuY2VsJyl9XHJcbiAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImNvbnRhaW5lZFwiIG9uQ2xpY2s9e2hhbmRsZVNhdmVQb3NpdGlvbn0+XHJcbiAgICAgICAgICAgIHt0KCdjb21tb24uc2F2ZScpfVxyXG4gICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgPC9EaWFsb2dBY3Rpb25zPlxyXG4gICAgICA8L0RpYWxvZz5cclxuXHJcbiAgICAgIHsvKiBEZWxldGUgQ29uZmlybWF0aW9uIERpYWxvZyAqL31cclxuICAgICAgPENvbmZpcm1EaWFsb2dcclxuICAgICAgICBvcGVuPXtvcGVuRGVsZXRlQ29uZmlybX1cclxuICAgICAgICBvbkNsb3NlPXtoYW5kbGVDbG9zZURlbGV0ZUNvbmZpcm19XHJcbiAgICAgICAgdGl0bGU9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMubWVyY2hhbmRpc2VQb3NpdGlvbnMuY29uZmlybURlbGV0ZURpYWxvZy50aXRsZScpfVxyXG4gICAgICAgIGNvbnRlbnQ9e3QoJ3BsdDEuZGV0YWlscy5kb2N1bWVudHMubWVyY2hhbmRpc2VQb3NpdGlvbnMuY29uZmlybURlbGV0ZURpYWxvZy5jb250ZW50Jyl9XHJcbiAgICAgICAgYWN0aW9uPXtcclxuICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImNvbnRhaW5lZFwiIGNvbG9yPVwiZXJyb3JcIiBvbkNsaWNrPXtoYW5kbGVEZWxldGVQb3NpdGlvbn0+XHJcbiAgICAgICAgICAgIHt0KCdjb21tb24uZGVsZXRlJyl9XHJcbiAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICB9XHJcbiAgICAgIC8+XHJcbiAgICA8L1N0YWNrPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlUmVmIiwidXNlRm9ybUNvbnRleHQiLCJ1c2VGaWVsZEFycmF5IiwiQm94IiwiQ2FyZCIsIlN0YWNrIiwiQnV0dG9uIiwiRGl2aWRlciIsIkNhcmRIZWFkZXIiLCJUeXBvZ3JhcGh5IiwiSWNvbkJ1dHRvbiIsIlRhYmxlIiwiVGFibGVCb2R5IiwiVGFibGVDZWxsIiwiVGFibGVDb250YWluZXIiLCJUYWJsZUhlYWQiLCJUYWJsZVJvdyIsIlBhcGVyIiwiRGlhbG9nIiwiRGlhbG9nVGl0bGUiLCJEaWFsb2dDb250ZW50IiwiRGlhbG9nQWN0aW9ucyIsIlRvb2x0aXAiLCJHcmlkIiwiSWNvbmlmeSIsIkZpZWxkIiwidXNlVHJhbnNsYXRlIiwiQ29uZmlybURpYWxvZyIsIkRFRkFVTFRfUE9TSVRJT04iLCJpZCIsImludm9pY2VQb3NpdGlvbk51bWJlciIsImdvb2RzRGVzY3JpcHRpb24iLCJ0cmFuc2xhdGVkR29vZHNEZXNjcmlwdGlvbiIsInBhY2thZ2VNYXJrcyIsIm51bWJlck9mUGFja2FnZXMiLCJwYWNrYWdlVHlwZSIsInBhY2thZ2VUeXBlRGVzY3JpcHRpb24iLCJjb21tb2RpdHlDb2RlIiwiZnVsbFRhcmlmZkNvZGUiLCJncm9zc01hc3MiLCJncm9zc01hc3NVbml0IiwibmV0TWFzcyIsIm5ldE1hc3NVbml0IiwicG9zaXRpb25WYWx1ZSIsInBvc2l0aW9uVmFsdWVDdXJyZW5jeSIsImNvbnRhaW5lck51bWJlcnMiLCJwcmV2aW91c0RvY3VtZW50cyIsIm1lcmNoYW5kaXNlUG9zaXRpb25zSWQiLCJQTFQxTWVyY2hhbmRpc2VQb3NpdGlvbnNGb3JtIiwidDFPcmRlcklkIiwicmVhZE9ubHkiLCJ0IiwiY29udHJvbCIsIndhdGNoIiwiZ2V0VmFsdWVzIiwiZmllbGRBcnJheSIsIm5hbWUiLCJtZXJjaGFuZGlzZVBvc2l0aW9ucyIsIm9wZW5Qb3NpdGlvbkRpYWxvZyIsInNldE9wZW5Qb3NpdGlvbkRpYWxvZyIsImN1cnJlbnRQb3NpdGlvbkluZGV4Iiwic2V0Q3VycmVudFBvc2l0aW9uSW5kZXgiLCJkZWxldGVQb3NpdGlvbkluZGV4Iiwic2V0RGVsZXRlUG9zaXRpb25JbmRleCIsIm9wZW5EZWxldGVDb25maXJtIiwic2V0T3BlbkRlbGV0ZUNvbmZpcm0iLCJkZWxldGVCdXR0b25SZWYiLCJoYW5kbGVBZGRQb3NpdGlvbiIsIm5ld1Bvc2l0aW9uIiwiYXBwZW5kIiwiZmllbGRzIiwibGVuZ3RoIiwiaGFuZGxlRWRpdFBvc2l0aW9uIiwiaW5kZXgiLCJoYW5kbGVDbG9zZVBvc2l0aW9uRGlhbG9nIiwicG9zaXRpb24iLCJpc0VtcHR5IiwicmVtb3ZlIiwiaGFuZGxlT3BlbkRlbGV0ZUNvbmZpcm0iLCJoYW5kbGVDbG9zZURlbGV0ZUNvbmZpcm0iLCJjdXJyZW50IiwiZm9jdXMiLCJoYW5kbGVEZWxldGVQb3NpdGlvbiIsImhhbmRsZVNhdmVQb3NpdGlvbiIsInJlbmRlclBvc2l0aW9uRm9ybSIsInNwYWNpbmciLCJUZXh0IiwibGFiZWwiLCJ0eXBlIiwiZGlzYWJsZWQiLCJzeCIsInJvd0dhcCIsInB0IiwiY29sdW1uR2FwIiwiZGlzcGxheSIsImdyaWRUZW1wbGF0ZUNvbHVtbnMiLCJ4cyIsIm1kIiwibXVsdGlsaW5lIiwicm93cyIsInJlbmRlclBvc2l0aW9uc1RhYmxlIiwiY29tcG9uZW50IiwibWluV2lkdGgiLCJhcmlhLWxhYmVsIiwiYWxpZ24iLCJjb2xTcGFuIiwidmFyaWFudCIsImNvbG9yIiwibWFwIiwiZmllbGQiLCJtYXhXaWR0aCIsIm10IiwiZGlyZWN0aW9uIiwianVzdGlmeUNvbnRlbnQiLCJ0aXRsZSIsInNpemUiLCJvbkNsaWNrIiwiaWNvbiIsInJlZiIsInJlbmRlclRvdGFsc1N1bW1hcnkiLCJ0b3RhbHMiLCJib3hTaGFkb3ciLCJtYiIsImJvcmRlclN0eWxlIiwicCIsImNvbnRhaW5lciIsInNtIiwidG90YWxOdW1iZXJPZlBvc2l0aW9ucyIsInRvdGFsTnVtYmVyT2ZQYWNrYWdlcyIsInRvdGFsR3Jvc3NNYXNzIiwidG90YWxHcm9zc01hc3NVbml0IiwidG90YWxOZXRNYXNzIiwidG90YWxOZXRNYXNzVW5pdCIsImFjdGlvbiIsInN0YXJ0SWNvbiIsIm9wZW4iLCJvbkNsb3NlIiwiZnVsbFdpZHRoIiwiY29udGVudCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-merchandise-positions-form.tsx\n"));

/***/ })

});