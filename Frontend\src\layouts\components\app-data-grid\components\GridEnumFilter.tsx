import React from 'react';
import { <PERSON>complete, <PERSON><PERSON>ield, Stack } from '@mui/material';
import { useTranslate } from 'src/locales';
import { GridFilterInputValueProps } from '@mui/x-data-grid';
import { useEnumFilterContext } from './enum-tag/enum-filter-context';

export const GridEnumFilter = ({ 
  item,
  applyValue,
  // Destructure but don't use these props to prevent them from being passed to Autocomplete
  apiRef,
  focusElementRef,
  ...other 
}: GridFilterInputValueProps) => {
  const { t } = useTranslate();
  const { options } = useEnumFilterContext(item.field);
  const multiple = item.operator === 'isAnyOf';

  // Filter out any DataGrid specific props that shouldn't be passed to Autocomplete
  const autocompleteProps = Object.fromEntries(
    Object.entries(other).filter(([key]) => !key.startsWith('grid'))
  );

  return (
    <Stack spacing={2}>
      <Autocomplete
        multiple={multiple}
        options={options}
        value={multiple 
          ? options.filter(option => Array.isArray(item.value) && item.value.includes(option.value))
          : options.find(option => option.value === item.value) || null
        }
        onChange={(_, newValue) => {
          const value = multiple
            ? (newValue as Array<{ value: string; label: string }>).map(item => item.value)
            : (newValue as { value: string; label: string } | null)?.value || null;
          
          applyValue({ ...item, value });
        }}
        getOptionLabel={(option) => option.label}
        renderInput={(params) => (
          <TextField
            {...params}
            variant="outlined"
            size="small"
            fullWidth
            placeholder={t('dataGrid.filterPanel.inputPlaceholder')}
          />
        )}
        disableCloseOnSelect={multiple}
        {...autocompleteProps}
      />
    </Stack>
  );
};