import dayjs from 'dayjs';
import 'dayjs/locale/pl';
import localizedFormat from 'dayjs/plugin/localizedFormat';

dayjs.extend(localizedFormat);

export const formatters = {
    isDefaultDate: (dateStr: string): boolean => {
        return dateStr.startsWith('0001-01-01');
    },

    dateTimeOffset: (value: Date | string | null | undefined): string => {
        if (!value) return '-';

        if (typeof value === 'string' && formatters.isDefaultDate(value)) {
            return '-';
        }

        try {
            return dayjs(value).format('L LT');
        } catch {
            return '-';
        }
    },

    /**
     * Formats a date to a plain date string without time or timezone offset
     * Example: "2025-02-07T00:00:00Z" -> "07.02.2025" (in pl locale)
     *
     * @param value The date to format (Date object, ISO string, or any valid date string)
     * @returns Formatted date string or "-" if the date is invalid or null
     */
    plainDate: (value: Date | string | null | undefined): string => {
        if (!value) return '-';

        if (typeof value === 'string' && formatters.isDefaultDate(value)) {
            return '-';
        }

        try {
            return dayjs(value).format('L'); // 'L' is the localized date format (e.g., MM/DD/YYYY in en-US, DD.MM.YYYY in pl)
        } catch {
            return '-';
        }
    }
};
