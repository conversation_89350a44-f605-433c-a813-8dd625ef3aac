import type { TextFieldProps } from '@mui/material/TextField';

import { Controller, useFormContext } from 'react-hook-form';

import TextField from '@mui/material/TextField';

// ----------------------------------------------------------------------

/**
 * Transform value for display in number input fields
 * Converts null/undefined to empty string for display
 */
function transformValue(value: any): string {
  if (value === null || value === undefined) return '';
  return String(value);
}

/**
 * Transform value on change for number input fields
 * Allows empty string during typing, converts to number when valid
 */
function transformValueOnChange(value: string): string | number {
  if (value === '' || value === null || value === undefined) {
    return value; // Allow empty string during typing
  }

  const numericValue = Number(value);
  if (!Number.isNaN(numericValue)) {
    return numericValue;
  }

  return value; // Return original value if not a valid number
}

/**
 * Transform value on blur for number input fields
 * Converts empty string to 0 to prevent backend validation errors
 */
function transformValueOnBlur(value: string): number {
  if (value === '' || value === null || value === undefined) {
    return 0; // Convert empty to 0 on blur to prevent backend errors
  }

  const numericValue = Number(value);
  if (!Number.isNaN(numericValue)) {
    return numericValue;
  }

  return 0; // Return 0 for invalid numbers
}

export type RHFTextFieldProps = TextFieldProps & {
  name: string;
};

export function RHFTextField({
  name,
  helperText,
  slotProps,
  type = 'text',
  ...other
}: RHFTextFieldProps) {
  const { control } = useFormContext();

  const isNumberType = type === 'number';

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <TextField
          {...field}
          fullWidth
          value={isNumberType ? transformValue(field.value) : field.value}
          onChange={(event) => {
            const transformedValue = isNumberType
              ? transformValueOnChange(event.target.value)
              : event.target.value;

            field.onChange(transformedValue);
          }}
          onBlur={(event) => {
            const transformedValue = isNumberType
              ? transformValueOnBlur(event.target.value)
              : event.target.value;

            field.onChange(transformedValue);
          }}
          type={isNumberType ? 'text' : type}
          error={!!error}
          helperText={error?.message ?? helperText}
          slotProps={{
            ...slotProps,
            htmlInput: {
              autoComplete: 'off',
              ...slotProps?.htmlInput,
              ...(isNumberType && { inputMode: 'decimal', pattern: '[0-9]*\\.?[0-9]*' }),
            },
          }}
          {...other}
        />
      )}
    />
  );
}
