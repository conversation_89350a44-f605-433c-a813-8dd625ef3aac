"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-dropcursor";
exports.ids = ["vendor-chunks/prosemirror-dropcursor"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-dropcursor/dist/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/prosemirror-dropcursor/dist/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dropCursor: () => (/* binding */ dropCursor)\n/* harmony export */ });\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/prosemirror-state/dist/index.js\");\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/prosemirror-transform/dist/index.js\");\n\n\n\n/**\nCreate a plugin that, when added to a ProseMirror instance,\ncauses a decoration to show up at the drop position when something\nis dragged over the editor.\n\nNodes may add a `disableDropCursor` property to their spec to\ncontrol the showing of a drop cursor inside them. This may be a\nboolean or a function, which will be called with a view and a\nposition, and should return a boolean.\n*/\nfunction dropCursor(options = {}) {\n    return new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n        view(editorView) { return new DropCursorView(editorView, options); }\n    });\n}\nclass DropCursorView {\n    constructor(editorView, options) {\n        var _a;\n        this.editorView = editorView;\n        this.cursorPos = null;\n        this.element = null;\n        this.timeout = -1;\n        this.width = (_a = options.width) !== null && _a !== void 0 ? _a : 1;\n        this.color = options.color === false ? undefined : (options.color || \"black\");\n        this.class = options.class;\n        this.handlers = [\"dragover\", \"dragend\", \"drop\", \"dragleave\"].map(name => {\n            let handler = (e) => { this[name](e); };\n            editorView.dom.addEventListener(name, handler);\n            return { name, handler };\n        });\n    }\n    destroy() {\n        this.handlers.forEach(({ name, handler }) => this.editorView.dom.removeEventListener(name, handler));\n    }\n    update(editorView, prevState) {\n        if (this.cursorPos != null && prevState.doc != editorView.state.doc) {\n            if (this.cursorPos > editorView.state.doc.content.size)\n                this.setCursor(null);\n            else\n                this.updateOverlay();\n        }\n    }\n    setCursor(pos) {\n        if (pos == this.cursorPos)\n            return;\n        this.cursorPos = pos;\n        if (pos == null) {\n            this.element.parentNode.removeChild(this.element);\n            this.element = null;\n        }\n        else {\n            this.updateOverlay();\n        }\n    }\n    updateOverlay() {\n        let $pos = this.editorView.state.doc.resolve(this.cursorPos);\n        let isBlock = !$pos.parent.inlineContent, rect;\n        if (isBlock) {\n            let before = $pos.nodeBefore, after = $pos.nodeAfter;\n            if (before || after) {\n                let node = this.editorView.nodeDOM(this.cursorPos - (before ? before.nodeSize : 0));\n                if (node) {\n                    let nodeRect = node.getBoundingClientRect();\n                    let top = before ? nodeRect.bottom : nodeRect.top;\n                    if (before && after)\n                        top = (top + this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top) / 2;\n                    rect = { left: nodeRect.left, right: nodeRect.right, top: top - this.width / 2, bottom: top + this.width / 2 };\n                }\n            }\n        }\n        if (!rect) {\n            let coords = this.editorView.coordsAtPos(this.cursorPos);\n            rect = { left: coords.left - this.width / 2, right: coords.left + this.width / 2, top: coords.top, bottom: coords.bottom };\n        }\n        let parent = this.editorView.dom.offsetParent;\n        if (!this.element) {\n            this.element = parent.appendChild(document.createElement(\"div\"));\n            if (this.class)\n                this.element.className = this.class;\n            this.element.style.cssText = \"position: absolute; z-index: 50; pointer-events: none;\";\n            if (this.color) {\n                this.element.style.backgroundColor = this.color;\n            }\n        }\n        this.element.classList.toggle(\"prosemirror-dropcursor-block\", isBlock);\n        this.element.classList.toggle(\"prosemirror-dropcursor-inline\", !isBlock);\n        let parentLeft, parentTop;\n        if (!parent || parent == document.body && getComputedStyle(parent).position == \"static\") {\n            parentLeft = -pageXOffset;\n            parentTop = -pageYOffset;\n        }\n        else {\n            let rect = parent.getBoundingClientRect();\n            parentLeft = rect.left - parent.scrollLeft;\n            parentTop = rect.top - parent.scrollTop;\n        }\n        this.element.style.left = (rect.left - parentLeft) + \"px\";\n        this.element.style.top = (rect.top - parentTop) + \"px\";\n        this.element.style.width = (rect.right - rect.left) + \"px\";\n        this.element.style.height = (rect.bottom - rect.top) + \"px\";\n    }\n    scheduleRemoval(timeout) {\n        clearTimeout(this.timeout);\n        this.timeout = setTimeout(() => this.setCursor(null), timeout);\n    }\n    dragover(event) {\n        if (!this.editorView.editable)\n            return;\n        let pos = this.editorView.posAtCoords({ left: event.clientX, top: event.clientY });\n        let node = pos && pos.inside >= 0 && this.editorView.state.doc.nodeAt(pos.inside);\n        let disableDropCursor = node && node.type.spec.disableDropCursor;\n        let disabled = typeof disableDropCursor == \"function\" ? disableDropCursor(this.editorView, pos, event) : disableDropCursor;\n        if (pos && !disabled) {\n            let target = pos.pos;\n            if (this.editorView.dragging && this.editorView.dragging.slice) {\n                let point = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.dropPoint)(this.editorView.state.doc, target, this.editorView.dragging.slice);\n                if (point != null)\n                    target = point;\n            }\n            this.setCursor(target);\n            this.scheduleRemoval(5000);\n        }\n    }\n    dragend() {\n        this.scheduleRemoval(20);\n    }\n    drop() {\n        this.scheduleRemoval(20);\n    }\n    dragleave(event) {\n        if (event.target == this.editorView.dom || !this.editorView.dom.contains(event.relatedTarget))\n            this.setCursor(null);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-dropcursor/dist/index.js\n");

/***/ })

};
;