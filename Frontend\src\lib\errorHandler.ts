import { AxiosError } from 'axios';
import { toast } from 'src/components/snackbar';

interface ApiError {
  statusCode: number;
  messages: string[];
  isSuccess: boolean;
}

export const handleApiError = (error: unknown) => {
  if (error instanceof AxiosError && error.response?.data && error.response?.status < 500) {
    const apiError = error.response.data as ApiError;
    
    apiError.messages.forEach((message) => {
      toast.error(message);
    });
  } 
};