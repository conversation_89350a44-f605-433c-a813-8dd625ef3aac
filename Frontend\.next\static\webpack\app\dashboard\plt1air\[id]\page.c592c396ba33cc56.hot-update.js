"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx":
/*!*******************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1PackingListForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/material/Grid2 */ \"(app-pages-browser)/./node_modules/@mui/material/Grid2/Grid2.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/custom-dialog */ \"(app-pages-browser)/./src/components/custom-dialog/index.ts\");\n/* harmony import */ var src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/components/party-address/hooks/usePartyAddressForm */ \"(app-pages-browser)/./src/components/party-address/hooks/usePartyAddressForm.tsx\");\n/* harmony import */ var src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/components/party-address/party-address-dialog */ \"(app-pages-browser)/./src/components/party-address/party-address-dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst DEFAULT_PACKED_ITEM = {\n    id: '',\n    name: '',\n    hsCode: '',\n    modelNumber: '',\n    quantity: 0,\n    itemNetWeight: 0,\n    itemNetWeightUnit: 'KGM',\n    itemGrossWeight: 0,\n    hsCodeHints: '',\n    packingListPositionId: ''\n};\nconst DEFAULT_POSITION = {\n    id: '',\n    positionNumber: 1,\n    commercialInvoiceNumber: '',\n    packageNetWeight: 0,\n    packageNetWeightUnit: 'KGM',\n    packageGrossWeight: 0,\n    packageGrossWeightUnit: 'KGM',\n    packagesNetWeight: 0,\n    packagesNetWeightUnit: 'KGM',\n    packagesGrossWeight: 0,\n    packagesGrossWeightUnit: 'KGM',\n    packageUnit: 'CTNS',\n    packageSize: '',\n    packageVolume: 0,\n    packageVolumeUnit: 'CBM',\n    numberOfPackages: 0,\n    packedItems: [],\n    packingListId: ''\n};\nconst DEFAULT_TOTAL = {\n    id: '',\n    shipmentOrPackingId: '',\n    totalQuantity: 0,\n    totalPackagesUnit: 'CTNS',\n    totalNumberOfPackages: 0,\n    totalNumberOfPallets: 0,\n    totalNetWeight: 0,\n    totalNetWeightUnit: 'KGM',\n    totalGrossWeight: 0,\n    totalGrossWeightUnit: 'KGM',\n    totalVolume: 0,\n    totalVolumeMeasurementUnit: 'CBM',\n    packingListId: ''\n};\nfunction PLT1PackingListForm(param) {\n    let { formPath, index, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    const { control, getValues, watch, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext)();\n    const fieldPrefix = index !== undefined ? \"\".concat(formPath, \".\").concat(index) : formPath;\n    const positionsFieldName = \"\".concat(fieldPrefix, \".packingListPositions\");\n    const totalFieldName = \"\".concat(fieldPrefix, \".listTotal\");\n    // Use the party address form hook\n    const { openPartyDialog, currentPartyType, handleClosePartyDialog, handleUpdateParty } = (0,src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm)({\n        fieldPrefix\n    });\n    // State for position dialog\n    const [openPositionDialog, setOpenPositionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPositionIndex, setCurrentPositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletePositionIndex, setDeletePositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize listTotal if it doesn't exist\n    const listTotal = watch(totalFieldName);\n    if (!listTotal) {\n        setValue(totalFieldName, DEFAULT_TOTAL);\n    }\n    // UseFieldArray hook to manage the positions array\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray)({\n        control,\n        name: positionsFieldName\n    });\n    // UseFieldArray for packed items within the current position\n    const packedItemsFieldName = currentPositionIndex !== null ? \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packedItems\") : '';\n    const packedItemsFieldArray = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray)({\n        control,\n        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packedItems\")\n    });\n    // Handle opening the position dialog for adding a new position\n    const handleAddPosition = ()=>{\n        setCurrentPositionIndex(null);\n        // Add a new position with default values\n        const newPosition = {\n            ...DEFAULT_POSITION,\n            positionNumber: fields.length + 1\n        };\n        append(newPosition);\n        setCurrentPositionIndex(fields.length); // Set to the new index\n        setOpenPositionDialog(true);\n    };\n    // Handle opening the position dialog for editing an existing position\n    const handleEditPosition = (index)=>{\n        setCurrentPositionIndex(index);\n        setOpenPositionDialog(true);\n    };\n    // Handle closing the position dialog\n    const handleClosePositionDialog = ()=>{\n        setOpenPositionDialog(false);\n        // If we were adding a new position and user cancels, remove the empty position\n        if (currentPositionIndex !== null && currentPositionIndex === fields.length - 1) {\n            const position = getValues(\"\".concat(positionsFieldName, \".\").concat(currentPositionIndex));\n            // Check if it's an empty position (all fields are default values)\n            const isEmpty = !position.commercialInvoiceNumber && position.numberOfPackages === 0 && position.packageNetWeight === 0 && position.packageGrossWeight === 0 && position.packageVolume === 0 && (!position.packedItems || position.packedItems.length === 0);\n            if (isEmpty) {\n                remove(currentPositionIndex);\n            }\n        }\n        setCurrentPositionIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeletePositionIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deletePositionIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting a position\n    const handleDeletePosition = ()=>{\n        if (deletePositionIndex !== null) {\n            remove(deletePositionIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving a position (just close the dialog since form is already updated)\n    const handleSavePosition = ()=>{\n        handleClosePositionDialog();\n    };\n    // Handle adding a new packed item\n    const handleAddPackedItem = ()=>{\n        if (currentPositionIndex !== null) {\n            const newPackedItem = {\n                ...DEFAULT_PACKED_ITEM\n            };\n            packedItemsFieldArray.append(newPackedItem);\n        }\n    };\n    // Handle removing a packed item\n    const handleRemovePackedItem = (itemIndex)=>{\n        if (currentPositionIndex !== null) {\n            packedItemsFieldArray.remove(itemIndex);\n        }\n    };\n    // Render the position form in the dialog\n    const renderPositionForm = ()=>{\n        if (currentPositionIndex === null) return null;\n        // Render inline editable table for packed items\n        const renderPackedItemsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        direction: \"row\",\n                        justifyContent: \"space-between\",\n                        alignItems: \"center\",\n                        sx: {\n                            mb: 2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                variant: \"h6\",\n                                children: t('plt1.details.documents.packingList.packedItem.title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                variant: \"outlined\",\n                                size: \"small\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                    icon: \"eva:plus-fill\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 24\n                                }, void 0),\n                                onClick: handleAddPackedItem,\n                                disabled: readOnly,\n                                children: t('plt1.details.documents.packingList.packedItem.addNew')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        component: _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                        sx: {\n                            maxHeight: 400\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            size: \"small\",\n                            stickyHeader: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.name')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.hsCode')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.modelNumber')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.quantity')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.itemNetWeight')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                align: \"right\",\n                                                children: t('common.actions')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    children: packedItemsFieldArray.fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            colSpan: 6,\n                                            align: \"center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"body2\",\n                                                color: \"text.secondary\",\n                                                children: t('plt1.details.documents.packingList.packedItem.noItems')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 17\n                                    }, this) : packedItemsFieldArray.fields.map((field, itemIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 150\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".name\"),\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 120\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".hsCode\"),\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 120\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".modelNumber\"),\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 100\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".quantity\"),\n                                                        type: \"number\",\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 120\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".itemNetWeight\"),\n                                                        type: \"number\",\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    align: \"right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        title: t('common.delete'),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            size: \"small\",\n                                                            color: \"error\",\n                                                            onClick: ()=>handleRemovePackedItem(itemIndex),\n                                                            disabled: readOnly,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                                icon: \"eva:trash-2-outline\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, field.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            variant: \"h6\",\n                            sx: {\n                                mb: 2\n                            },\n                            children: t('plt1.details.documents.packingList.position.details')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".positionNumber\"),\n                                        label: t('plt1.details.documents.packingList.position.positionNumber'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".commercialInvoiceNumber\"),\n                                        label: t('plt1.details.documents.packingList.position.commercialInvoiceNumber'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".numberOfPackages\"),\n                                        label: t('plt1.details.documents.packingList.position.numberOfPackages'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageUnit\"),\n                                        label: t('plt1.details.documents.packingList.position.packageUnit'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageSize\"),\n                                        label: t('plt1.details.documents.packingList.position.packageSize'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageVolume\"),\n                                        label: t('plt1.details.documents.packingList.position.packageVolume'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.position.packageNetWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.position.packageGrossWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packagesNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.position.packagesNetWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packagesGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.position.packagesGrossWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, this),\n                renderPackedItemsTable()\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 377,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the positions table\n    const renderPositionsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            component: _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"packing list positions table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.positionNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.commercialInvoiceNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.numberOfPackages')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.packageNetWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.packageGrossWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        children: fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                colSpan: 6,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.packingList.position.noPositions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 13\n                        }, this) : fields.map((field, index)=>{\n                            const position = getValues(\"\".concat(positionsFieldName, \".\").concat(index)) || {};\n                            var _position_numberOfPackages, _position_packageNetWeight, _position_packageGrossWeight;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: position.positionNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: position.commercialInvoiceNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: (_position_numberOfPackages = position.numberOfPackages) !== null && _position_numberOfPackages !== void 0 ? _position_numberOfPackages : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: [\n                                            (_position_packageNetWeight = position.packageNetWeight) !== null && _position_packageNetWeight !== void 0 ? _position_packageNetWeight : '-',\n                                            \" \",\n                                            position.packageNetWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: [\n                                            (_position_packageGrossWeight = position.packageGrossWeight) !== null && _position_packageGrossWeight !== void 0 ? _position_packageGrossWeight : '-',\n                                            \" \",\n                                            position.packageGrossWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditPosition(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deletePositionIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 488,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 487,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        spacing: 3,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        title: t('plt1.details.documents.packingList.form.positionsTitle'),\n                        action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"contained\",\n                            size: \"small\",\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                icon: \"eva:plus-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 566,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: handleAddPosition,\n                            sx: {\n                                mb: 2\n                            },\n                            disabled: readOnly,\n                            children: t('plt1.details.documents.packingList.position.addNew')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 563,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 560,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: [\n                            renderPositionsTable(),\n                            fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    mt: 2,\n                                    display: 'flex',\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    variant: \"soft\",\n                                    color: \"primary\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                        icon: \"eva:plus-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    onClick: handleAddPosition,\n                                    disabled: readOnly,\n                                    children: t('plt1.details.documents.packingList.position.addNew')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 559,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        title: t('plt1.details.documents.packingList.form.totalsTitle')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 597,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 598,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalQuantity\"),\n                                        label: t('plt1.details.documents.packingList.total.totalQuantity'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPackages\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPackages'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalPackagesUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalPackagesUnit'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolume\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolume'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 600,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 599,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 596,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                open: openPositionDialog,\n                onClose: handleClosePositionDialog,\n                fullWidth: true,\n                maxWidth: \"lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        children: currentPositionIndex === null ? t('plt1.details.documents.packingList.position.addNew') : t('plt1.details.documents.packingList.position.edit')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 654,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            sx: {\n                                pt: 3\n                            },\n                            children: renderPositionForm()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 660,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                onClick: handleClosePositionDialog,\n                                color: \"inherit\",\n                                children: t('common.cancel')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 663,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                onClick: handleSavePosition,\n                                variant: \"contained\",\n                                children: t('common.save')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 662,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 653,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__.ConfirmDialog, {\n                open: openDeleteConfirm,\n                onClose: handleCloseDeleteConfirm,\n                title: t('plt1.details.documents.packingList.position.confirmDeleteDialog.title'),\n                content: t('plt1.details.documents.packingList.position.confirmDeleteDialog.content'),\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    variant: \"contained\",\n                    color: \"error\",\n                    onClick: handleDeletePosition,\n                    children: t('common.delete')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 679,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 673,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                open: openPartyDialog,\n                onClose: handleClosePartyDialog,\n                onSave: handleUpdateParty,\n                formPath: fieldPrefix,\n                currentPartyType: currentPartyType,\n                readOnly: readOnly,\n                titlePrefix: \"plt1.details.documents.packingList.partyAddress\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 686,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n        lineNumber: 557,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1PackingListForm, \"jbyTJ/5RFz4AqmM1furakeA1SSM=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext,\n        src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray\n    ];\n});\n_c = PLT1PackingListForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1PackingListForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx\n"));

/***/ })

});