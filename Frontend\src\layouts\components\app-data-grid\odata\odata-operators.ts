const ODATA_OPERATORS = {
    // Date operators
    after: 'gt',
    before: 'lt',
    
    // Standard operators
    contains: 'contains',
    equals: 'eq',
    doesNotEqual: 'ne',
    not: 'ne',
    doesNotContain: 'notcontains',
    startsWith: 'startswith',
    endsWith: 'endswith',
    isEmpty: 'isEmpty',
    isNotEmpty: 'isNotEmpty',
    isAnyOf: 'isAnyOf',
    '>': 'gt',
    '>=': 'ge',
    '<': 'lt',
    '<=': 'le',
    '!=': 'ne'
  } as const;
  
  export const mapOperator = (operator?: string): string => {
    return ODATA_OPERATORS[operator as keyof typeof ODATA_OPERATORS] || 'eq';
  };