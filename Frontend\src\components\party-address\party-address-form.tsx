import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Divider from '@mui/material/Divider';
import CardHeader from '@mui/material/CardHeader';
import Typography from '@mui/material/Typography';

import { Field } from 'src/components/hook-form';
import { useTranslate } from 'src/locales';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

export interface PartyAddressFormProps {
  formPath: string;
  title: string;
  icon?: string;
  readOnly?: boolean;
}

export interface PartyAddressData {
  name: string;
  taxNumber: string;
  eoriNumber: string;
  address: AddressData;
}

export interface AddressData {
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  postCode?: string;
  country?: string;
  countryCode?: string;
}

export default function PartyAddressForm({
  formPath,
  title,
  icon = 'eva:person-outline',
  readOnly = false
}: PartyAddressFormProps) {
  const { t } = useTranslate();

  // Render the form card
  const renderFormCard = () => (
    <Card>
      <CardHeader
        title={
          <Stack direction="row" alignItems="center" spacing={1}>
            <Iconify icon={icon} />
            <Typography variant="subtitle1">{title}</Typography>
          </Stack>
        }
        sx={{ mb: 2 }}
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Field.Text
          name={`${formPath}.name`}
          label={t('components.partyAddress.form.name')}
          disabled={readOnly}
        />

        <Box
          sx={{
            rowGap: 3,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(1, 1fr)' },
          }}
        >
          <Field.Text
            name={`${formPath}.taxNumber`}
            label={t('components.partyAddress.form.taxNumber')}
            disabled={readOnly}
          />

          <Field.Text
            name={`${formPath}.eoriNumber`}
            label={t('components.partyAddress.form.eoriNumber')}
            disabled={readOnly}
          />
        </Box>

        <Box
          sx={{
            rowGap: 3,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(1, 1fr)' },
          }}
        >
          <Field.Text
            name={`${formPath}.address.addressLine1`}
            label={t('components.address.addressLine1')}
            disabled={readOnly}
          />

          <Field.Text
            name={`${formPath}.address.addressLine2`}
            label={t('components.address.addressLine2')}
            disabled={readOnly}
          />
        </Box>

        <Box
          sx={{
            rowGap: 3,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' },
          }}
        >
          <Field.Text
            name={`${formPath}.address.city`}
            label={t('components.address.city')}
            disabled={readOnly}
          />

          <Field.Text
            name={`${formPath}.address.state`}
            label={t('components.address.state')}
            disabled={readOnly}
          />
        </Box>

        <Box
          sx={{
            rowGap: 3,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' },
          }}
        >
          <Field.Text
            name={`${formPath}.address.postCode`}
            label={t('components.address.postalCode')}
            disabled={readOnly}
          />

          <Field.CountrySelect 
            name={`${formPath}.address.country`}
            label={t('components.address.country')}
            disabled={readOnly}
           />
        </Box>
      </Stack>
    </Card>
  );

  return renderFormCard();
}
