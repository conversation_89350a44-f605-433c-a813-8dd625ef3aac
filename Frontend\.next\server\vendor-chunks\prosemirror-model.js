"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-model";
exports.ids = ["vendor-chunks/prosemirror-model"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-model/dist/index.js":
/*!******************************************************!*\
  !*** ./node_modules/prosemirror-model/dist/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentMatch: () => (/* binding */ ContentMatch),\n/* harmony export */   DOMParser: () => (/* binding */ DOMParser),\n/* harmony export */   DOMSerializer: () => (/* binding */ DOMSerializer),\n/* harmony export */   Fragment: () => (/* binding */ Fragment),\n/* harmony export */   Mark: () => (/* binding */ Mark),\n/* harmony export */   MarkType: () => (/* binding */ MarkType),\n/* harmony export */   Node: () => (/* binding */ Node),\n/* harmony export */   NodeRange: () => (/* binding */ NodeRange),\n/* harmony export */   NodeType: () => (/* binding */ NodeType),\n/* harmony export */   ReplaceError: () => (/* binding */ ReplaceError),\n/* harmony export */   ResolvedPos: () => (/* binding */ ResolvedPos),\n/* harmony export */   Schema: () => (/* binding */ Schema),\n/* harmony export */   Slice: () => (/* binding */ Slice)\n/* harmony export */ });\n/* harmony import */ var orderedmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! orderedmap */ \"(ssr)/./node_modules/orderedmap/dist/index.js\");\n\n\nfunction findDiffStart(a, b, pos) {\n    for (let i = 0;; i++) {\n        if (i == a.childCount || i == b.childCount)\n            return a.childCount == b.childCount ? null : pos;\n        let childA = a.child(i), childB = b.child(i);\n        if (childA == childB) {\n            pos += childA.nodeSize;\n            continue;\n        }\n        if (!childA.sameMarkup(childB))\n            return pos;\n        if (childA.isText && childA.text != childB.text) {\n            for (let j = 0; childA.text[j] == childB.text[j]; j++)\n                pos++;\n            return pos;\n        }\n        if (childA.content.size || childB.content.size) {\n            let inner = findDiffStart(childA.content, childB.content, pos + 1);\n            if (inner != null)\n                return inner;\n        }\n        pos += childA.nodeSize;\n    }\n}\nfunction findDiffEnd(a, b, posA, posB) {\n    for (let iA = a.childCount, iB = b.childCount;;) {\n        if (iA == 0 || iB == 0)\n            return iA == iB ? null : { a: posA, b: posB };\n        let childA = a.child(--iA), childB = b.child(--iB), size = childA.nodeSize;\n        if (childA == childB) {\n            posA -= size;\n            posB -= size;\n            continue;\n        }\n        if (!childA.sameMarkup(childB))\n            return { a: posA, b: posB };\n        if (childA.isText && childA.text != childB.text) {\n            let same = 0, minSize = Math.min(childA.text.length, childB.text.length);\n            while (same < minSize && childA.text[childA.text.length - same - 1] == childB.text[childB.text.length - same - 1]) {\n                same++;\n                posA--;\n                posB--;\n            }\n            return { a: posA, b: posB };\n        }\n        if (childA.content.size || childB.content.size) {\n            let inner = findDiffEnd(childA.content, childB.content, posA - 1, posB - 1);\n            if (inner)\n                return inner;\n        }\n        posA -= size;\n        posB -= size;\n    }\n}\n\n/**\nA fragment represents a node's collection of child nodes.\n\nLike nodes, fragments are persistent data structures, and you\nshould not mutate them or their content. Rather, you create new\ninstances whenever needed. The API tries to make this easy.\n*/\nclass Fragment {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The child nodes in this fragment.\n    */\n    content, size) {\n        this.content = content;\n        this.size = size || 0;\n        if (size == null)\n            for (let i = 0; i < content.length; i++)\n                this.size += content[i].nodeSize;\n    }\n    /**\n    Invoke a callback for all descendant nodes between the given two\n    positions (relative to start of this fragment). Doesn't descend\n    into a node when the callback returns `false`.\n    */\n    nodesBetween(from, to, f, nodeStart = 0, parent) {\n        for (let i = 0, pos = 0; pos < to; i++) {\n            let child = this.content[i], end = pos + child.nodeSize;\n            if (end > from && f(child, nodeStart + pos, parent || null, i) !== false && child.content.size) {\n                let start = pos + 1;\n                child.nodesBetween(Math.max(0, from - start), Math.min(child.content.size, to - start), f, nodeStart + start);\n            }\n            pos = end;\n        }\n    }\n    /**\n    Call the given callback for every descendant node. `pos` will be\n    relative to the start of the fragment. The callback may return\n    `false` to prevent traversal of a given node's children.\n    */\n    descendants(f) {\n        this.nodesBetween(0, this.size, f);\n    }\n    /**\n    Extract the text between `from` and `to`. See the same method on\n    [`Node`](https://prosemirror.net/docs/ref/#model.Node.textBetween).\n    */\n    textBetween(from, to, blockSeparator, leafText) {\n        let text = \"\", first = true;\n        this.nodesBetween(from, to, (node, pos) => {\n            let nodeText = node.isText ? node.text.slice(Math.max(from, pos) - pos, to - pos)\n                : !node.isLeaf ? \"\"\n                    : leafText ? (typeof leafText === \"function\" ? leafText(node) : leafText)\n                        : node.type.spec.leafText ? node.type.spec.leafText(node)\n                            : \"\";\n            if (node.isBlock && (node.isLeaf && nodeText || node.isTextblock) && blockSeparator) {\n                if (first)\n                    first = false;\n                else\n                    text += blockSeparator;\n            }\n            text += nodeText;\n        }, 0);\n        return text;\n    }\n    /**\n    Create a new fragment containing the combined content of this\n    fragment and the other.\n    */\n    append(other) {\n        if (!other.size)\n            return this;\n        if (!this.size)\n            return other;\n        let last = this.lastChild, first = other.firstChild, content = this.content.slice(), i = 0;\n        if (last.isText && last.sameMarkup(first)) {\n            content[content.length - 1] = last.withText(last.text + first.text);\n            i = 1;\n        }\n        for (; i < other.content.length; i++)\n            content.push(other.content[i]);\n        return new Fragment(content, this.size + other.size);\n    }\n    /**\n    Cut out the sub-fragment between the two given positions.\n    */\n    cut(from, to = this.size) {\n        if (from == 0 && to == this.size)\n            return this;\n        let result = [], size = 0;\n        if (to > from)\n            for (let i = 0, pos = 0; pos < to; i++) {\n                let child = this.content[i], end = pos + child.nodeSize;\n                if (end > from) {\n                    if (pos < from || end > to) {\n                        if (child.isText)\n                            child = child.cut(Math.max(0, from - pos), Math.min(child.text.length, to - pos));\n                        else\n                            child = child.cut(Math.max(0, from - pos - 1), Math.min(child.content.size, to - pos - 1));\n                    }\n                    result.push(child);\n                    size += child.nodeSize;\n                }\n                pos = end;\n            }\n        return new Fragment(result, size);\n    }\n    /**\n    @internal\n    */\n    cutByIndex(from, to) {\n        if (from == to)\n            return Fragment.empty;\n        if (from == 0 && to == this.content.length)\n            return this;\n        return new Fragment(this.content.slice(from, to));\n    }\n    /**\n    Create a new fragment in which the node at the given index is\n    replaced by the given node.\n    */\n    replaceChild(index, node) {\n        let current = this.content[index];\n        if (current == node)\n            return this;\n        let copy = this.content.slice();\n        let size = this.size + node.nodeSize - current.nodeSize;\n        copy[index] = node;\n        return new Fragment(copy, size);\n    }\n    /**\n    Create a new fragment by prepending the given node to this\n    fragment.\n    */\n    addToStart(node) {\n        return new Fragment([node].concat(this.content), this.size + node.nodeSize);\n    }\n    /**\n    Create a new fragment by appending the given node to this\n    fragment.\n    */\n    addToEnd(node) {\n        return new Fragment(this.content.concat(node), this.size + node.nodeSize);\n    }\n    /**\n    Compare this fragment to another one.\n    */\n    eq(other) {\n        if (this.content.length != other.content.length)\n            return false;\n        for (let i = 0; i < this.content.length; i++)\n            if (!this.content[i].eq(other.content[i]))\n                return false;\n        return true;\n    }\n    /**\n    The first child of the fragment, or `null` if it is empty.\n    */\n    get firstChild() { return this.content.length ? this.content[0] : null; }\n    /**\n    The last child of the fragment, or `null` if it is empty.\n    */\n    get lastChild() { return this.content.length ? this.content[this.content.length - 1] : null; }\n    /**\n    The number of child nodes in this fragment.\n    */\n    get childCount() { return this.content.length; }\n    /**\n    Get the child node at the given index. Raise an error when the\n    index is out of range.\n    */\n    child(index) {\n        let found = this.content[index];\n        if (!found)\n            throw new RangeError(\"Index \" + index + \" out of range for \" + this);\n        return found;\n    }\n    /**\n    Get the child node at the given index, if it exists.\n    */\n    maybeChild(index) {\n        return this.content[index] || null;\n    }\n    /**\n    Call `f` for every child node, passing the node, its offset\n    into this parent node, and its index.\n    */\n    forEach(f) {\n        for (let i = 0, p = 0; i < this.content.length; i++) {\n            let child = this.content[i];\n            f(child, p, i);\n            p += child.nodeSize;\n        }\n    }\n    /**\n    Find the first position at which this fragment and another\n    fragment differ, or `null` if they are the same.\n    */\n    findDiffStart(other, pos = 0) {\n        return findDiffStart(this, other, pos);\n    }\n    /**\n    Find the first position, searching from the end, at which this\n    fragment and the given fragment differ, or `null` if they are\n    the same. Since this position will not be the same in both\n    nodes, an object with two separate positions is returned.\n    */\n    findDiffEnd(other, pos = this.size, otherPos = other.size) {\n        return findDiffEnd(this, other, pos, otherPos);\n    }\n    /**\n    Find the index and inner offset corresponding to a given relative\n    position in this fragment. The result object will be reused\n    (overwritten) the next time the function is called. @internal\n    */\n    findIndex(pos, round = -1) {\n        if (pos == 0)\n            return retIndex(0, pos);\n        if (pos == this.size)\n            return retIndex(this.content.length, pos);\n        if (pos > this.size || pos < 0)\n            throw new RangeError(`Position ${pos} outside of fragment (${this})`);\n        for (let i = 0, curPos = 0;; i++) {\n            let cur = this.child(i), end = curPos + cur.nodeSize;\n            if (end >= pos) {\n                if (end == pos || round > 0)\n                    return retIndex(i + 1, end);\n                return retIndex(i, curPos);\n            }\n            curPos = end;\n        }\n    }\n    /**\n    Return a debugging string that describes this fragment.\n    */\n    toString() { return \"<\" + this.toStringInner() + \">\"; }\n    /**\n    @internal\n    */\n    toStringInner() { return this.content.join(\", \"); }\n    /**\n    Create a JSON-serializeable representation of this fragment.\n    */\n    toJSON() {\n        return this.content.length ? this.content.map(n => n.toJSON()) : null;\n    }\n    /**\n    Deserialize a fragment from its JSON representation.\n    */\n    static fromJSON(schema, value) {\n        if (!value)\n            return Fragment.empty;\n        if (!Array.isArray(value))\n            throw new RangeError(\"Invalid input for Fragment.fromJSON\");\n        return new Fragment(value.map(schema.nodeFromJSON));\n    }\n    /**\n    Build a fragment from an array of nodes. Ensures that adjacent\n    text nodes with the same marks are joined together.\n    */\n    static fromArray(array) {\n        if (!array.length)\n            return Fragment.empty;\n        let joined, size = 0;\n        for (let i = 0; i < array.length; i++) {\n            let node = array[i];\n            size += node.nodeSize;\n            if (i && node.isText && array[i - 1].sameMarkup(node)) {\n                if (!joined)\n                    joined = array.slice(0, i);\n                joined[joined.length - 1] = node\n                    .withText(joined[joined.length - 1].text + node.text);\n            }\n            else if (joined) {\n                joined.push(node);\n            }\n        }\n        return new Fragment(joined || array, size);\n    }\n    /**\n    Create a fragment from something that can be interpreted as a\n    set of nodes. For `null`, it returns the empty fragment. For a\n    fragment, the fragment itself. For a node or array of nodes, a\n    fragment containing those nodes.\n    */\n    static from(nodes) {\n        if (!nodes)\n            return Fragment.empty;\n        if (nodes instanceof Fragment)\n            return nodes;\n        if (Array.isArray(nodes))\n            return this.fromArray(nodes);\n        if (nodes.attrs)\n            return new Fragment([nodes], nodes.nodeSize);\n        throw new RangeError(\"Can not convert \" + nodes + \" to a Fragment\" +\n            (nodes.nodesBetween ? \" (looks like multiple versions of prosemirror-model were loaded)\" : \"\"));\n    }\n}\n/**\nAn empty fragment. Intended to be reused whenever a node doesn't\ncontain anything (rather than allocating a new empty fragment for\neach leaf node).\n*/\nFragment.empty = new Fragment([], 0);\nconst found = { index: 0, offset: 0 };\nfunction retIndex(index, offset) {\n    found.index = index;\n    found.offset = offset;\n    return found;\n}\n\nfunction compareDeep(a, b) {\n    if (a === b)\n        return true;\n    if (!(a && typeof a == \"object\") ||\n        !(b && typeof b == \"object\"))\n        return false;\n    let array = Array.isArray(a);\n    if (Array.isArray(b) != array)\n        return false;\n    if (array) {\n        if (a.length != b.length)\n            return false;\n        for (let i = 0; i < a.length; i++)\n            if (!compareDeep(a[i], b[i]))\n                return false;\n    }\n    else {\n        for (let p in a)\n            if (!(p in b) || !compareDeep(a[p], b[p]))\n                return false;\n        for (let p in b)\n            if (!(p in a))\n                return false;\n    }\n    return true;\n}\n\n/**\nA mark is a piece of information that can be attached to a node,\nsuch as it being emphasized, in code font, or a link. It has a\ntype and optionally a set of attributes that provide further\ninformation (such as the target of the link). Marks are created\nthrough a `Schema`, which controls which types exist and which\nattributes they have.\n*/\nclass Mark {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The type of this mark.\n    */\n    type, \n    /**\n    The attributes associated with this mark.\n    */\n    attrs) {\n        this.type = type;\n        this.attrs = attrs;\n    }\n    /**\n    Given a set of marks, create a new set which contains this one as\n    well, in the right position. If this mark is already in the set,\n    the set itself is returned. If any marks that are set to be\n    [exclusive](https://prosemirror.net/docs/ref/#model.MarkSpec.excludes) with this mark are present,\n    those are replaced by this one.\n    */\n    addToSet(set) {\n        let copy, placed = false;\n        for (let i = 0; i < set.length; i++) {\n            let other = set[i];\n            if (this.eq(other))\n                return set;\n            if (this.type.excludes(other.type)) {\n                if (!copy)\n                    copy = set.slice(0, i);\n            }\n            else if (other.type.excludes(this.type)) {\n                return set;\n            }\n            else {\n                if (!placed && other.type.rank > this.type.rank) {\n                    if (!copy)\n                        copy = set.slice(0, i);\n                    copy.push(this);\n                    placed = true;\n                }\n                if (copy)\n                    copy.push(other);\n            }\n        }\n        if (!copy)\n            copy = set.slice();\n        if (!placed)\n            copy.push(this);\n        return copy;\n    }\n    /**\n    Remove this mark from the given set, returning a new set. If this\n    mark is not in the set, the set itself is returned.\n    */\n    removeFromSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (this.eq(set[i]))\n                return set.slice(0, i).concat(set.slice(i + 1));\n        return set;\n    }\n    /**\n    Test whether this mark is in the given set of marks.\n    */\n    isInSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (this.eq(set[i]))\n                return true;\n        return false;\n    }\n    /**\n    Test whether this mark has the same type and attributes as\n    another mark.\n    */\n    eq(other) {\n        return this == other ||\n            (this.type == other.type && compareDeep(this.attrs, other.attrs));\n    }\n    /**\n    Convert this mark to a JSON-serializeable representation.\n    */\n    toJSON() {\n        let obj = { type: this.type.name };\n        for (let _ in this.attrs) {\n            obj.attrs = this.attrs;\n            break;\n        }\n        return obj;\n    }\n    /**\n    Deserialize a mark from JSON.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            throw new RangeError(\"Invalid input for Mark.fromJSON\");\n        let type = schema.marks[json.type];\n        if (!type)\n            throw new RangeError(`There is no mark type ${json.type} in this schema`);\n        let mark = type.create(json.attrs);\n        type.checkAttrs(mark.attrs);\n        return mark;\n    }\n    /**\n    Test whether two sets of marks are identical.\n    */\n    static sameSet(a, b) {\n        if (a == b)\n            return true;\n        if (a.length != b.length)\n            return false;\n        for (let i = 0; i < a.length; i++)\n            if (!a[i].eq(b[i]))\n                return false;\n        return true;\n    }\n    /**\n    Create a properly sorted mark set from null, a single mark, or an\n    unsorted array of marks.\n    */\n    static setFrom(marks) {\n        if (!marks || Array.isArray(marks) && marks.length == 0)\n            return Mark.none;\n        if (marks instanceof Mark)\n            return [marks];\n        let copy = marks.slice();\n        copy.sort((a, b) => a.type.rank - b.type.rank);\n        return copy;\n    }\n}\n/**\nThe empty set of marks.\n*/\nMark.none = [];\n\n/**\nError type raised by [`Node.replace`](https://prosemirror.net/docs/ref/#model.Node.replace) when\ngiven an invalid replacement.\n*/\nclass ReplaceError extends Error {\n}\n/*\nReplaceError = function(this: any, message: string) {\n  let err = Error.call(this, message)\n  ;(err as any).__proto__ = ReplaceError.prototype\n  return err\n} as any\n\nReplaceError.prototype = Object.create(Error.prototype)\nReplaceError.prototype.constructor = ReplaceError\nReplaceError.prototype.name = \"ReplaceError\"\n*/\n/**\nA slice represents a piece cut out of a larger document. It\nstores not only a fragment, but also the depth up to which nodes on\nboth side are ‘open’ (cut through).\n*/\nclass Slice {\n    /**\n    Create a slice. When specifying a non-zero open depth, you must\n    make sure that there are nodes of at least that depth at the\n    appropriate side of the fragment—i.e. if the fragment is an\n    empty paragraph node, `openStart` and `openEnd` can't be greater\n    than 1.\n    \n    It is not necessary for the content of open nodes to conform to\n    the schema's content constraints, though it should be a valid\n    start/end/middle for such a node, depending on which sides are\n    open.\n    */\n    constructor(\n    /**\n    The slice's content.\n    */\n    content, \n    /**\n    The open depth at the start of the fragment.\n    */\n    openStart, \n    /**\n    The open depth at the end.\n    */\n    openEnd) {\n        this.content = content;\n        this.openStart = openStart;\n        this.openEnd = openEnd;\n    }\n    /**\n    The size this slice would add when inserted into a document.\n    */\n    get size() {\n        return this.content.size - this.openStart - this.openEnd;\n    }\n    /**\n    @internal\n    */\n    insertAt(pos, fragment) {\n        let content = insertInto(this.content, pos + this.openStart, fragment);\n        return content && new Slice(content, this.openStart, this.openEnd);\n    }\n    /**\n    @internal\n    */\n    removeBetween(from, to) {\n        return new Slice(removeRange(this.content, from + this.openStart, to + this.openStart), this.openStart, this.openEnd);\n    }\n    /**\n    Tests whether this slice is equal to another slice.\n    */\n    eq(other) {\n        return this.content.eq(other.content) && this.openStart == other.openStart && this.openEnd == other.openEnd;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return this.content + \"(\" + this.openStart + \",\" + this.openEnd + \")\";\n    }\n    /**\n    Convert a slice to a JSON-serializable representation.\n    */\n    toJSON() {\n        if (!this.content.size)\n            return null;\n        let json = { content: this.content.toJSON() };\n        if (this.openStart > 0)\n            json.openStart = this.openStart;\n        if (this.openEnd > 0)\n            json.openEnd = this.openEnd;\n        return json;\n    }\n    /**\n    Deserialize a slice from its JSON representation.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            return Slice.empty;\n        let openStart = json.openStart || 0, openEnd = json.openEnd || 0;\n        if (typeof openStart != \"number\" || typeof openEnd != \"number\")\n            throw new RangeError(\"Invalid input for Slice.fromJSON\");\n        return new Slice(Fragment.fromJSON(schema, json.content), openStart, openEnd);\n    }\n    /**\n    Create a slice from a fragment by taking the maximum possible\n    open value on both side of the fragment.\n    */\n    static maxOpen(fragment, openIsolating = true) {\n        let openStart = 0, openEnd = 0;\n        for (let n = fragment.firstChild; n && !n.isLeaf && (openIsolating || !n.type.spec.isolating); n = n.firstChild)\n            openStart++;\n        for (let n = fragment.lastChild; n && !n.isLeaf && (openIsolating || !n.type.spec.isolating); n = n.lastChild)\n            openEnd++;\n        return new Slice(fragment, openStart, openEnd);\n    }\n}\n/**\nThe empty slice.\n*/\nSlice.empty = new Slice(Fragment.empty, 0, 0);\nfunction removeRange(content, from, to) {\n    let { index, offset } = content.findIndex(from), child = content.maybeChild(index);\n    let { index: indexTo, offset: offsetTo } = content.findIndex(to);\n    if (offset == from || child.isText) {\n        if (offsetTo != to && !content.child(indexTo).isText)\n            throw new RangeError(\"Removing non-flat range\");\n        return content.cut(0, from).append(content.cut(to));\n    }\n    if (index != indexTo)\n        throw new RangeError(\"Removing non-flat range\");\n    return content.replaceChild(index, child.copy(removeRange(child.content, from - offset - 1, to - offset - 1)));\n}\nfunction insertInto(content, dist, insert, parent) {\n    let { index, offset } = content.findIndex(dist), child = content.maybeChild(index);\n    if (offset == dist || child.isText) {\n        if (parent && !parent.canReplace(index, index, insert))\n            return null;\n        return content.cut(0, dist).append(insert).append(content.cut(dist));\n    }\n    let inner = insertInto(child.content, dist - offset - 1, insert);\n    return inner && content.replaceChild(index, child.copy(inner));\n}\nfunction replace($from, $to, slice) {\n    if (slice.openStart > $from.depth)\n        throw new ReplaceError(\"Inserted content deeper than insertion position\");\n    if ($from.depth - slice.openStart != $to.depth - slice.openEnd)\n        throw new ReplaceError(\"Inconsistent open depths\");\n    return replaceOuter($from, $to, slice, 0);\n}\nfunction replaceOuter($from, $to, slice, depth) {\n    let index = $from.index(depth), node = $from.node(depth);\n    if (index == $to.index(depth) && depth < $from.depth - slice.openStart) {\n        let inner = replaceOuter($from, $to, slice, depth + 1);\n        return node.copy(node.content.replaceChild(index, inner));\n    }\n    else if (!slice.content.size) {\n        return close(node, replaceTwoWay($from, $to, depth));\n    }\n    else if (!slice.openStart && !slice.openEnd && $from.depth == depth && $to.depth == depth) { // Simple, flat case\n        let parent = $from.parent, content = parent.content;\n        return close(parent, content.cut(0, $from.parentOffset).append(slice.content).append(content.cut($to.parentOffset)));\n    }\n    else {\n        let { start, end } = prepareSliceForReplace(slice, $from);\n        return close(node, replaceThreeWay($from, start, end, $to, depth));\n    }\n}\nfunction checkJoin(main, sub) {\n    if (!sub.type.compatibleContent(main.type))\n        throw new ReplaceError(\"Cannot join \" + sub.type.name + \" onto \" + main.type.name);\n}\nfunction joinable($before, $after, depth) {\n    let node = $before.node(depth);\n    checkJoin(node, $after.node(depth));\n    return node;\n}\nfunction addNode(child, target) {\n    let last = target.length - 1;\n    if (last >= 0 && child.isText && child.sameMarkup(target[last]))\n        target[last] = child.withText(target[last].text + child.text);\n    else\n        target.push(child);\n}\nfunction addRange($start, $end, depth, target) {\n    let node = ($end || $start).node(depth);\n    let startIndex = 0, endIndex = $end ? $end.index(depth) : node.childCount;\n    if ($start) {\n        startIndex = $start.index(depth);\n        if ($start.depth > depth) {\n            startIndex++;\n        }\n        else if ($start.textOffset) {\n            addNode($start.nodeAfter, target);\n            startIndex++;\n        }\n    }\n    for (let i = startIndex; i < endIndex; i++)\n        addNode(node.child(i), target);\n    if ($end && $end.depth == depth && $end.textOffset)\n        addNode($end.nodeBefore, target);\n}\nfunction close(node, content) {\n    node.type.checkContent(content);\n    return node.copy(content);\n}\nfunction replaceThreeWay($from, $start, $end, $to, depth) {\n    let openStart = $from.depth > depth && joinable($from, $start, depth + 1);\n    let openEnd = $to.depth > depth && joinable($end, $to, depth + 1);\n    let content = [];\n    addRange(null, $from, depth, content);\n    if (openStart && openEnd && $start.index(depth) == $end.index(depth)) {\n        checkJoin(openStart, openEnd);\n        addNode(close(openStart, replaceThreeWay($from, $start, $end, $to, depth + 1)), content);\n    }\n    else {\n        if (openStart)\n            addNode(close(openStart, replaceTwoWay($from, $start, depth + 1)), content);\n        addRange($start, $end, depth, content);\n        if (openEnd)\n            addNode(close(openEnd, replaceTwoWay($end, $to, depth + 1)), content);\n    }\n    addRange($to, null, depth, content);\n    return new Fragment(content);\n}\nfunction replaceTwoWay($from, $to, depth) {\n    let content = [];\n    addRange(null, $from, depth, content);\n    if ($from.depth > depth) {\n        let type = joinable($from, $to, depth + 1);\n        addNode(close(type, replaceTwoWay($from, $to, depth + 1)), content);\n    }\n    addRange($to, null, depth, content);\n    return new Fragment(content);\n}\nfunction prepareSliceForReplace(slice, $along) {\n    let extra = $along.depth - slice.openStart, parent = $along.node(extra);\n    let node = parent.copy(slice.content);\n    for (let i = extra - 1; i >= 0; i--)\n        node = $along.node(i).copy(Fragment.from(node));\n    return { start: node.resolveNoCache(slice.openStart + extra),\n        end: node.resolveNoCache(node.content.size - slice.openEnd - extra) };\n}\n\n/**\nYou can [_resolve_](https://prosemirror.net/docs/ref/#model.Node.resolve) a position to get more\ninformation about it. Objects of this class represent such a\nresolved position, providing various pieces of context\ninformation, and some helper methods.\n\nThroughout this interface, methods that take an optional `depth`\nparameter will interpret undefined as `this.depth` and negative\nnumbers as `this.depth + value`.\n*/\nclass ResolvedPos {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The position that was resolved.\n    */\n    pos, \n    /**\n    @internal\n    */\n    path, \n    /**\n    The offset this position has into its parent node.\n    */\n    parentOffset) {\n        this.pos = pos;\n        this.path = path;\n        this.parentOffset = parentOffset;\n        this.depth = path.length / 3 - 1;\n    }\n    /**\n    @internal\n    */\n    resolveDepth(val) {\n        if (val == null)\n            return this.depth;\n        if (val < 0)\n            return this.depth + val;\n        return val;\n    }\n    /**\n    The parent node that the position points into. Note that even if\n    a position points into a text node, that node is not considered\n    the parent—text nodes are ‘flat’ in this model, and have no content.\n    */\n    get parent() { return this.node(this.depth); }\n    /**\n    The root node in which the position was resolved.\n    */\n    get doc() { return this.node(0); }\n    /**\n    The ancestor node at the given level. `p.node(p.depth)` is the\n    same as `p.parent`.\n    */\n    node(depth) { return this.path[this.resolveDepth(depth) * 3]; }\n    /**\n    The index into the ancestor at the given level. If this points\n    at the 3rd node in the 2nd paragraph on the top level, for\n    example, `p.index(0)` is 1 and `p.index(1)` is 2.\n    */\n    index(depth) { return this.path[this.resolveDepth(depth) * 3 + 1]; }\n    /**\n    The index pointing after this position into the ancestor at the\n    given level.\n    */\n    indexAfter(depth) {\n        depth = this.resolveDepth(depth);\n        return this.index(depth) + (depth == this.depth && !this.textOffset ? 0 : 1);\n    }\n    /**\n    The (absolute) position at the start of the node at the given\n    level.\n    */\n    start(depth) {\n        depth = this.resolveDepth(depth);\n        return depth == 0 ? 0 : this.path[depth * 3 - 1] + 1;\n    }\n    /**\n    The (absolute) position at the end of the node at the given\n    level.\n    */\n    end(depth) {\n        depth = this.resolveDepth(depth);\n        return this.start(depth) + this.node(depth).content.size;\n    }\n    /**\n    The (absolute) position directly before the wrapping node at the\n    given level, or, when `depth` is `this.depth + 1`, the original\n    position.\n    */\n    before(depth) {\n        depth = this.resolveDepth(depth);\n        if (!depth)\n            throw new RangeError(\"There is no position before the top-level node\");\n        return depth == this.depth + 1 ? this.pos : this.path[depth * 3 - 1];\n    }\n    /**\n    The (absolute) position directly after the wrapping node at the\n    given level, or the original position when `depth` is `this.depth + 1`.\n    */\n    after(depth) {\n        depth = this.resolveDepth(depth);\n        if (!depth)\n            throw new RangeError(\"There is no position after the top-level node\");\n        return depth == this.depth + 1 ? this.pos : this.path[depth * 3 - 1] + this.path[depth * 3].nodeSize;\n    }\n    /**\n    When this position points into a text node, this returns the\n    distance between the position and the start of the text node.\n    Will be zero for positions that point between nodes.\n    */\n    get textOffset() { return this.pos - this.path[this.path.length - 1]; }\n    /**\n    Get the node directly after the position, if any. If the position\n    points into a text node, only the part of that node after the\n    position is returned.\n    */\n    get nodeAfter() {\n        let parent = this.parent, index = this.index(this.depth);\n        if (index == parent.childCount)\n            return null;\n        let dOff = this.pos - this.path[this.path.length - 1], child = parent.child(index);\n        return dOff ? parent.child(index).cut(dOff) : child;\n    }\n    /**\n    Get the node directly before the position, if any. If the\n    position points into a text node, only the part of that node\n    before the position is returned.\n    */\n    get nodeBefore() {\n        let index = this.index(this.depth);\n        let dOff = this.pos - this.path[this.path.length - 1];\n        if (dOff)\n            return this.parent.child(index).cut(0, dOff);\n        return index == 0 ? null : this.parent.child(index - 1);\n    }\n    /**\n    Get the position at the given index in the parent node at the\n    given depth (which defaults to `this.depth`).\n    */\n    posAtIndex(index, depth) {\n        depth = this.resolveDepth(depth);\n        let node = this.path[depth * 3], pos = depth == 0 ? 0 : this.path[depth * 3 - 1] + 1;\n        for (let i = 0; i < index; i++)\n            pos += node.child(i).nodeSize;\n        return pos;\n    }\n    /**\n    Get the marks at this position, factoring in the surrounding\n    marks' [`inclusive`](https://prosemirror.net/docs/ref/#model.MarkSpec.inclusive) property. If the\n    position is at the start of a non-empty node, the marks of the\n    node after it (if any) are returned.\n    */\n    marks() {\n        let parent = this.parent, index = this.index();\n        // In an empty parent, return the empty array\n        if (parent.content.size == 0)\n            return Mark.none;\n        // When inside a text node, just return the text node's marks\n        if (this.textOffset)\n            return parent.child(index).marks;\n        let main = parent.maybeChild(index - 1), other = parent.maybeChild(index);\n        // If the `after` flag is true of there is no node before, make\n        // the node after this position the main reference.\n        if (!main) {\n            let tmp = main;\n            main = other;\n            other = tmp;\n        }\n        // Use all marks in the main node, except those that have\n        // `inclusive` set to false and are not present in the other node.\n        let marks = main.marks;\n        for (var i = 0; i < marks.length; i++)\n            if (marks[i].type.spec.inclusive === false && (!other || !marks[i].isInSet(other.marks)))\n                marks = marks[i--].removeFromSet(marks);\n        return marks;\n    }\n    /**\n    Get the marks after the current position, if any, except those\n    that are non-inclusive and not present at position `$end`. This\n    is mostly useful for getting the set of marks to preserve after a\n    deletion. Will return `null` if this position is at the end of\n    its parent node or its parent node isn't a textblock (in which\n    case no marks should be preserved).\n    */\n    marksAcross($end) {\n        let after = this.parent.maybeChild(this.index());\n        if (!after || !after.isInline)\n            return null;\n        let marks = after.marks, next = $end.parent.maybeChild($end.index());\n        for (var i = 0; i < marks.length; i++)\n            if (marks[i].type.spec.inclusive === false && (!next || !marks[i].isInSet(next.marks)))\n                marks = marks[i--].removeFromSet(marks);\n        return marks;\n    }\n    /**\n    The depth up to which this position and the given (non-resolved)\n    position share the same parent nodes.\n    */\n    sharedDepth(pos) {\n        for (let depth = this.depth; depth > 0; depth--)\n            if (this.start(depth) <= pos && this.end(depth) >= pos)\n                return depth;\n        return 0;\n    }\n    /**\n    Returns a range based on the place where this position and the\n    given position diverge around block content. If both point into\n    the same textblock, for example, a range around that textblock\n    will be returned. If they point into different blocks, the range\n    around those blocks in their shared ancestor is returned. You can\n    pass in an optional predicate that will be called with a parent\n    node to see if a range into that parent is acceptable.\n    */\n    blockRange(other = this, pred) {\n        if (other.pos < this.pos)\n            return other.blockRange(this);\n        for (let d = this.depth - (this.parent.inlineContent || this.pos == other.pos ? 1 : 0); d >= 0; d--)\n            if (other.pos <= this.end(d) && (!pred || pred(this.node(d))))\n                return new NodeRange(this, other, d);\n        return null;\n    }\n    /**\n    Query whether the given position shares the same parent node.\n    */\n    sameParent(other) {\n        return this.pos - this.parentOffset == other.pos - other.parentOffset;\n    }\n    /**\n    Return the greater of this and the given position.\n    */\n    max(other) {\n        return other.pos > this.pos ? other : this;\n    }\n    /**\n    Return the smaller of this and the given position.\n    */\n    min(other) {\n        return other.pos < this.pos ? other : this;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let str = \"\";\n        for (let i = 1; i <= this.depth; i++)\n            str += (str ? \"/\" : \"\") + this.node(i).type.name + \"_\" + this.index(i - 1);\n        return str + \":\" + this.parentOffset;\n    }\n    /**\n    @internal\n    */\n    static resolve(doc, pos) {\n        if (!(pos >= 0 && pos <= doc.content.size))\n            throw new RangeError(\"Position \" + pos + \" out of range\");\n        let path = [];\n        let start = 0, parentOffset = pos;\n        for (let node = doc;;) {\n            let { index, offset } = node.content.findIndex(parentOffset);\n            let rem = parentOffset - offset;\n            path.push(node, index, start + offset);\n            if (!rem)\n                break;\n            node = node.child(index);\n            if (node.isText)\n                break;\n            parentOffset = rem - 1;\n            start += offset + 1;\n        }\n        return new ResolvedPos(pos, path, parentOffset);\n    }\n    /**\n    @internal\n    */\n    static resolveCached(doc, pos) {\n        let cache = resolveCache.get(doc);\n        if (cache) {\n            for (let i = 0; i < cache.elts.length; i++) {\n                let elt = cache.elts[i];\n                if (elt.pos == pos)\n                    return elt;\n            }\n        }\n        else {\n            resolveCache.set(doc, cache = new ResolveCache);\n        }\n        let result = cache.elts[cache.i] = ResolvedPos.resolve(doc, pos);\n        cache.i = (cache.i + 1) % resolveCacheSize;\n        return result;\n    }\n}\nclass ResolveCache {\n    constructor() {\n        this.elts = [];\n        this.i = 0;\n    }\n}\nconst resolveCacheSize = 12, resolveCache = new WeakMap();\n/**\nRepresents a flat range of content, i.e. one that starts and\nends in the same node.\n*/\nclass NodeRange {\n    /**\n    Construct a node range. `$from` and `$to` should point into the\n    same node until at least the given `depth`, since a node range\n    denotes an adjacent set of nodes in a single parent node.\n    */\n    constructor(\n    /**\n    A resolved position along the start of the content. May have a\n    `depth` greater than this object's `depth` property, since\n    these are the positions that were used to compute the range,\n    not re-resolved positions directly at its boundaries.\n    */\n    $from, \n    /**\n    A position along the end of the content. See\n    caveat for [`$from`](https://prosemirror.net/docs/ref/#model.NodeRange.$from).\n    */\n    $to, \n    /**\n    The depth of the node that this range points into.\n    */\n    depth) {\n        this.$from = $from;\n        this.$to = $to;\n        this.depth = depth;\n    }\n    /**\n    The position at the start of the range.\n    */\n    get start() { return this.$from.before(this.depth + 1); }\n    /**\n    The position at the end of the range.\n    */\n    get end() { return this.$to.after(this.depth + 1); }\n    /**\n    The parent node that the range points into.\n    */\n    get parent() { return this.$from.node(this.depth); }\n    /**\n    The start index of the range in the parent node.\n    */\n    get startIndex() { return this.$from.index(this.depth); }\n    /**\n    The end index of the range in the parent node.\n    */\n    get endIndex() { return this.$to.indexAfter(this.depth); }\n}\n\nconst emptyAttrs = Object.create(null);\n/**\nThis class represents a node in the tree that makes up a\nProseMirror document. So a document is an instance of `Node`, with\nchildren that are also instances of `Node`.\n\nNodes are persistent data structures. Instead of changing them, you\ncreate new ones with the content you want. Old ones keep pointing\nat the old document shape. This is made cheaper by sharing\nstructure between the old and new data as much as possible, which a\ntree shape like this (without back pointers) makes easy.\n\n**Do not** directly mutate the properties of a `Node` object. See\n[the guide](/docs/guide/#doc) for more information.\n*/\nclass Node {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The type of node that this is.\n    */\n    type, \n    /**\n    An object mapping attribute names to values. The kind of\n    attributes allowed and required are\n    [determined](https://prosemirror.net/docs/ref/#model.NodeSpec.attrs) by the node type.\n    */\n    attrs, \n    // A fragment holding the node's children.\n    content, \n    /**\n    The marks (things like whether it is emphasized or part of a\n    link) applied to this node.\n    */\n    marks = Mark.none) {\n        this.type = type;\n        this.attrs = attrs;\n        this.marks = marks;\n        this.content = content || Fragment.empty;\n    }\n    /**\n    The array of this node's child nodes.\n    */\n    get children() { return this.content.content; }\n    /**\n    The size of this node, as defined by the integer-based [indexing\n    scheme](/docs/guide/#doc.indexing). For text nodes, this is the\n    amount of characters. For other leaf nodes, it is one. For\n    non-leaf nodes, it is the size of the content plus two (the\n    start and end token).\n    */\n    get nodeSize() { return this.isLeaf ? 1 : 2 + this.content.size; }\n    /**\n    The number of children that the node has.\n    */\n    get childCount() { return this.content.childCount; }\n    /**\n    Get the child node at the given index. Raises an error when the\n    index is out of range.\n    */\n    child(index) { return this.content.child(index); }\n    /**\n    Get the child node at the given index, if it exists.\n    */\n    maybeChild(index) { return this.content.maybeChild(index); }\n    /**\n    Call `f` for every child node, passing the node, its offset\n    into this parent node, and its index.\n    */\n    forEach(f) { this.content.forEach(f); }\n    /**\n    Invoke a callback for all descendant nodes recursively between\n    the given two positions that are relative to start of this\n    node's content. The callback is invoked with the node, its\n    position relative to the original node (method receiver),\n    its parent node, and its child index. When the callback returns\n    false for a given node, that node's children will not be\n    recursed over. The last parameter can be used to specify a\n    starting position to count from.\n    */\n    nodesBetween(from, to, f, startPos = 0) {\n        this.content.nodesBetween(from, to, f, startPos, this);\n    }\n    /**\n    Call the given callback for every descendant node. Doesn't\n    descend into a node when the callback returns `false`.\n    */\n    descendants(f) {\n        this.nodesBetween(0, this.content.size, f);\n    }\n    /**\n    Concatenates all the text nodes found in this fragment and its\n    children.\n    */\n    get textContent() {\n        return (this.isLeaf && this.type.spec.leafText)\n            ? this.type.spec.leafText(this)\n            : this.textBetween(0, this.content.size, \"\");\n    }\n    /**\n    Get all text between positions `from` and `to`. When\n    `blockSeparator` is given, it will be inserted to separate text\n    from different block nodes. If `leafText` is given, it'll be\n    inserted for every non-text leaf node encountered, otherwise\n    [`leafText`](https://prosemirror.net/docs/ref/#model.NodeSpec^leafText) will be used.\n    */\n    textBetween(from, to, blockSeparator, leafText) {\n        return this.content.textBetween(from, to, blockSeparator, leafText);\n    }\n    /**\n    Returns this node's first child, or `null` if there are no\n    children.\n    */\n    get firstChild() { return this.content.firstChild; }\n    /**\n    Returns this node's last child, or `null` if there are no\n    children.\n    */\n    get lastChild() { return this.content.lastChild; }\n    /**\n    Test whether two nodes represent the same piece of document.\n    */\n    eq(other) {\n        return this == other || (this.sameMarkup(other) && this.content.eq(other.content));\n    }\n    /**\n    Compare the markup (type, attributes, and marks) of this node to\n    those of another. Returns `true` if both have the same markup.\n    */\n    sameMarkup(other) {\n        return this.hasMarkup(other.type, other.attrs, other.marks);\n    }\n    /**\n    Check whether this node's markup correspond to the given type,\n    attributes, and marks.\n    */\n    hasMarkup(type, attrs, marks) {\n        return this.type == type &&\n            compareDeep(this.attrs, attrs || type.defaultAttrs || emptyAttrs) &&\n            Mark.sameSet(this.marks, marks || Mark.none);\n    }\n    /**\n    Create a new node with the same markup as this node, containing\n    the given content (or empty, if no content is given).\n    */\n    copy(content = null) {\n        if (content == this.content)\n            return this;\n        return new Node(this.type, this.attrs, content, this.marks);\n    }\n    /**\n    Create a copy of this node, with the given set of marks instead\n    of the node's own marks.\n    */\n    mark(marks) {\n        return marks == this.marks ? this : new Node(this.type, this.attrs, this.content, marks);\n    }\n    /**\n    Create a copy of this node with only the content between the\n    given positions. If `to` is not given, it defaults to the end of\n    the node.\n    */\n    cut(from, to = this.content.size) {\n        if (from == 0 && to == this.content.size)\n            return this;\n        return this.copy(this.content.cut(from, to));\n    }\n    /**\n    Cut out the part of the document between the given positions, and\n    return it as a `Slice` object.\n    */\n    slice(from, to = this.content.size, includeParents = false) {\n        if (from == to)\n            return Slice.empty;\n        let $from = this.resolve(from), $to = this.resolve(to);\n        let depth = includeParents ? 0 : $from.sharedDepth(to);\n        let start = $from.start(depth), node = $from.node(depth);\n        let content = node.content.cut($from.pos - start, $to.pos - start);\n        return new Slice(content, $from.depth - depth, $to.depth - depth);\n    }\n    /**\n    Replace the part of the document between the given positions with\n    the given slice. The slice must 'fit', meaning its open sides\n    must be able to connect to the surrounding content, and its\n    content nodes must be valid children for the node they are placed\n    into. If any of this is violated, an error of type\n    [`ReplaceError`](https://prosemirror.net/docs/ref/#model.ReplaceError) is thrown.\n    */\n    replace(from, to, slice) {\n        return replace(this.resolve(from), this.resolve(to), slice);\n    }\n    /**\n    Find the node directly after the given position.\n    */\n    nodeAt(pos) {\n        for (let node = this;;) {\n            let { index, offset } = node.content.findIndex(pos);\n            node = node.maybeChild(index);\n            if (!node)\n                return null;\n            if (offset == pos || node.isText)\n                return node;\n            pos -= offset + 1;\n        }\n    }\n    /**\n    Find the (direct) child node after the given offset, if any,\n    and return it along with its index and offset relative to this\n    node.\n    */\n    childAfter(pos) {\n        let { index, offset } = this.content.findIndex(pos);\n        return { node: this.content.maybeChild(index), index, offset };\n    }\n    /**\n    Find the (direct) child node before the given offset, if any,\n    and return it along with its index and offset relative to this\n    node.\n    */\n    childBefore(pos) {\n        if (pos == 0)\n            return { node: null, index: 0, offset: 0 };\n        let { index, offset } = this.content.findIndex(pos);\n        if (offset < pos)\n            return { node: this.content.child(index), index, offset };\n        let node = this.content.child(index - 1);\n        return { node, index: index - 1, offset: offset - node.nodeSize };\n    }\n    /**\n    Resolve the given position in the document, returning an\n    [object](https://prosemirror.net/docs/ref/#model.ResolvedPos) with information about its context.\n    */\n    resolve(pos) { return ResolvedPos.resolveCached(this, pos); }\n    /**\n    @internal\n    */\n    resolveNoCache(pos) { return ResolvedPos.resolve(this, pos); }\n    /**\n    Test whether a given mark or mark type occurs in this document\n    between the two given positions.\n    */\n    rangeHasMark(from, to, type) {\n        let found = false;\n        if (to > from)\n            this.nodesBetween(from, to, node => {\n                if (type.isInSet(node.marks))\n                    found = true;\n                return !found;\n            });\n        return found;\n    }\n    /**\n    True when this is a block (non-inline node)\n    */\n    get isBlock() { return this.type.isBlock; }\n    /**\n    True when this is a textblock node, a block node with inline\n    content.\n    */\n    get isTextblock() { return this.type.isTextblock; }\n    /**\n    True when this node allows inline content.\n    */\n    get inlineContent() { return this.type.inlineContent; }\n    /**\n    True when this is an inline node (a text node or a node that can\n    appear among text).\n    */\n    get isInline() { return this.type.isInline; }\n    /**\n    True when this is a text node.\n    */\n    get isText() { return this.type.isText; }\n    /**\n    True when this is a leaf node.\n    */\n    get isLeaf() { return this.type.isLeaf; }\n    /**\n    True when this is an atom, i.e. when it does not have directly\n    editable content. This is usually the same as `isLeaf`, but can\n    be configured with the [`atom` property](https://prosemirror.net/docs/ref/#model.NodeSpec.atom)\n    on a node's spec (typically used when the node is displayed as\n    an uneditable [node view](https://prosemirror.net/docs/ref/#view.NodeView)).\n    */\n    get isAtom() { return this.type.isAtom; }\n    /**\n    Return a string representation of this node for debugging\n    purposes.\n    */\n    toString() {\n        if (this.type.spec.toDebugString)\n            return this.type.spec.toDebugString(this);\n        let name = this.type.name;\n        if (this.content.size)\n            name += \"(\" + this.content.toStringInner() + \")\";\n        return wrapMarks(this.marks, name);\n    }\n    /**\n    Get the content match in this node at the given index.\n    */\n    contentMatchAt(index) {\n        let match = this.type.contentMatch.matchFragment(this.content, 0, index);\n        if (!match)\n            throw new Error(\"Called contentMatchAt on a node with invalid content\");\n        return match;\n    }\n    /**\n    Test whether replacing the range between `from` and `to` (by\n    child index) with the given replacement fragment (which defaults\n    to the empty fragment) would leave the node's content valid. You\n    can optionally pass `start` and `end` indices into the\n    replacement fragment.\n    */\n    canReplace(from, to, replacement = Fragment.empty, start = 0, end = replacement.childCount) {\n        let one = this.contentMatchAt(from).matchFragment(replacement, start, end);\n        let two = one && one.matchFragment(this.content, to);\n        if (!two || !two.validEnd)\n            return false;\n        for (let i = start; i < end; i++)\n            if (!this.type.allowsMarks(replacement.child(i).marks))\n                return false;\n        return true;\n    }\n    /**\n    Test whether replacing the range `from` to `to` (by index) with\n    a node of the given type would leave the node's content valid.\n    */\n    canReplaceWith(from, to, type, marks) {\n        if (marks && !this.type.allowsMarks(marks))\n            return false;\n        let start = this.contentMatchAt(from).matchType(type);\n        let end = start && start.matchFragment(this.content, to);\n        return end ? end.validEnd : false;\n    }\n    /**\n    Test whether the given node's content could be appended to this\n    node. If that node is empty, this will only return true if there\n    is at least one node type that can appear in both nodes (to avoid\n    merging completely incompatible nodes).\n    */\n    canAppend(other) {\n        if (other.content.size)\n            return this.canReplace(this.childCount, this.childCount, other.content);\n        else\n            return this.type.compatibleContent(other.type);\n    }\n    /**\n    Check whether this node and its descendants conform to the\n    schema, and raise an exception when they do not.\n    */\n    check() {\n        this.type.checkContent(this.content);\n        this.type.checkAttrs(this.attrs);\n        let copy = Mark.none;\n        for (let i = 0; i < this.marks.length; i++) {\n            let mark = this.marks[i];\n            mark.type.checkAttrs(mark.attrs);\n            copy = mark.addToSet(copy);\n        }\n        if (!Mark.sameSet(copy, this.marks))\n            throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(m => m.type.name)}`);\n        this.content.forEach(node => node.check());\n    }\n    /**\n    Return a JSON-serializeable representation of this node.\n    */\n    toJSON() {\n        let obj = { type: this.type.name };\n        for (let _ in this.attrs) {\n            obj.attrs = this.attrs;\n            break;\n        }\n        if (this.content.size)\n            obj.content = this.content.toJSON();\n        if (this.marks.length)\n            obj.marks = this.marks.map(n => n.toJSON());\n        return obj;\n    }\n    /**\n    Deserialize a node from its JSON representation.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            throw new RangeError(\"Invalid input for Node.fromJSON\");\n        let marks = undefined;\n        if (json.marks) {\n            if (!Array.isArray(json.marks))\n                throw new RangeError(\"Invalid mark data for Node.fromJSON\");\n            marks = json.marks.map(schema.markFromJSON);\n        }\n        if (json.type == \"text\") {\n            if (typeof json.text != \"string\")\n                throw new RangeError(\"Invalid text node in JSON\");\n            return schema.text(json.text, marks);\n        }\n        let content = Fragment.fromJSON(schema, json.content);\n        let node = schema.nodeType(json.type).create(json.attrs, content, marks);\n        node.type.checkAttrs(node.attrs);\n        return node;\n    }\n}\nNode.prototype.text = undefined;\nclass TextNode extends Node {\n    /**\n    @internal\n    */\n    constructor(type, attrs, content, marks) {\n        super(type, attrs, null, marks);\n        if (!content)\n            throw new RangeError(\"Empty text nodes are not allowed\");\n        this.text = content;\n    }\n    toString() {\n        if (this.type.spec.toDebugString)\n            return this.type.spec.toDebugString(this);\n        return wrapMarks(this.marks, JSON.stringify(this.text));\n    }\n    get textContent() { return this.text; }\n    textBetween(from, to) { return this.text.slice(from, to); }\n    get nodeSize() { return this.text.length; }\n    mark(marks) {\n        return marks == this.marks ? this : new TextNode(this.type, this.attrs, this.text, marks);\n    }\n    withText(text) {\n        if (text == this.text)\n            return this;\n        return new TextNode(this.type, this.attrs, text, this.marks);\n    }\n    cut(from = 0, to = this.text.length) {\n        if (from == 0 && to == this.text.length)\n            return this;\n        return this.withText(this.text.slice(from, to));\n    }\n    eq(other) {\n        return this.sameMarkup(other) && this.text == other.text;\n    }\n    toJSON() {\n        let base = super.toJSON();\n        base.text = this.text;\n        return base;\n    }\n}\nfunction wrapMarks(marks, str) {\n    for (let i = marks.length - 1; i >= 0; i--)\n        str = marks[i].type.name + \"(\" + str + \")\";\n    return str;\n}\n\n/**\nInstances of this class represent a match state of a node type's\n[content expression](https://prosemirror.net/docs/ref/#model.NodeSpec.content), and can be used to\nfind out whether further content matches here, and whether a given\nposition is a valid end of the node.\n*/\nclass ContentMatch {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    True when this match state represents a valid end of the node.\n    */\n    validEnd) {\n        this.validEnd = validEnd;\n        /**\n        @internal\n        */\n        this.next = [];\n        /**\n        @internal\n        */\n        this.wrapCache = [];\n    }\n    /**\n    @internal\n    */\n    static parse(string, nodeTypes) {\n        let stream = new TokenStream(string, nodeTypes);\n        if (stream.next == null)\n            return ContentMatch.empty;\n        let expr = parseExpr(stream);\n        if (stream.next)\n            stream.err(\"Unexpected trailing text\");\n        let match = dfa(nfa(expr));\n        checkForDeadEnds(match, stream);\n        return match;\n    }\n    /**\n    Match a node type, returning a match after that node if\n    successful.\n    */\n    matchType(type) {\n        for (let i = 0; i < this.next.length; i++)\n            if (this.next[i].type == type)\n                return this.next[i].next;\n        return null;\n    }\n    /**\n    Try to match a fragment. Returns the resulting match when\n    successful.\n    */\n    matchFragment(frag, start = 0, end = frag.childCount) {\n        let cur = this;\n        for (let i = start; cur && i < end; i++)\n            cur = cur.matchType(frag.child(i).type);\n        return cur;\n    }\n    /**\n    @internal\n    */\n    get inlineContent() {\n        return this.next.length != 0 && this.next[0].type.isInline;\n    }\n    /**\n    Get the first matching node type at this match position that can\n    be generated.\n    */\n    get defaultType() {\n        for (let i = 0; i < this.next.length; i++) {\n            let { type } = this.next[i];\n            if (!(type.isText || type.hasRequiredAttrs()))\n                return type;\n        }\n        return null;\n    }\n    /**\n    @internal\n    */\n    compatible(other) {\n        for (let i = 0; i < this.next.length; i++)\n            for (let j = 0; j < other.next.length; j++)\n                if (this.next[i].type == other.next[j].type)\n                    return true;\n        return false;\n    }\n    /**\n    Try to match the given fragment, and if that fails, see if it can\n    be made to match by inserting nodes in front of it. When\n    successful, return a fragment of inserted nodes (which may be\n    empty if nothing had to be inserted). When `toEnd` is true, only\n    return a fragment if the resulting match goes to the end of the\n    content expression.\n    */\n    fillBefore(after, toEnd = false, startIndex = 0) {\n        let seen = [this];\n        function search(match, types) {\n            let finished = match.matchFragment(after, startIndex);\n            if (finished && (!toEnd || finished.validEnd))\n                return Fragment.from(types.map(tp => tp.createAndFill()));\n            for (let i = 0; i < match.next.length; i++) {\n                let { type, next } = match.next[i];\n                if (!(type.isText || type.hasRequiredAttrs()) && seen.indexOf(next) == -1) {\n                    seen.push(next);\n                    let found = search(next, types.concat(type));\n                    if (found)\n                        return found;\n                }\n            }\n            return null;\n        }\n        return search(this, []);\n    }\n    /**\n    Find a set of wrapping node types that would allow a node of the\n    given type to appear at this position. The result may be empty\n    (when it fits directly) and will be null when no such wrapping\n    exists.\n    */\n    findWrapping(target) {\n        for (let i = 0; i < this.wrapCache.length; i += 2)\n            if (this.wrapCache[i] == target)\n                return this.wrapCache[i + 1];\n        let computed = this.computeWrapping(target);\n        this.wrapCache.push(target, computed);\n        return computed;\n    }\n    /**\n    @internal\n    */\n    computeWrapping(target) {\n        let seen = Object.create(null), active = [{ match: this, type: null, via: null }];\n        while (active.length) {\n            let current = active.shift(), match = current.match;\n            if (match.matchType(target)) {\n                let result = [];\n                for (let obj = current; obj.type; obj = obj.via)\n                    result.push(obj.type);\n                return result.reverse();\n            }\n            for (let i = 0; i < match.next.length; i++) {\n                let { type, next } = match.next[i];\n                if (!type.isLeaf && !type.hasRequiredAttrs() && !(type.name in seen) && (!current.type || next.validEnd)) {\n                    active.push({ match: type.contentMatch, type, via: current });\n                    seen[type.name] = true;\n                }\n            }\n        }\n        return null;\n    }\n    /**\n    The number of outgoing edges this node has in the finite\n    automaton that describes the content expression.\n    */\n    get edgeCount() {\n        return this.next.length;\n    }\n    /**\n    Get the _n_​th outgoing edge from this node in the finite\n    automaton that describes the content expression.\n    */\n    edge(n) {\n        if (n >= this.next.length)\n            throw new RangeError(`There's no ${n}th edge in this content match`);\n        return this.next[n];\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let seen = [];\n        function scan(m) {\n            seen.push(m);\n            for (let i = 0; i < m.next.length; i++)\n                if (seen.indexOf(m.next[i].next) == -1)\n                    scan(m.next[i].next);\n        }\n        scan(this);\n        return seen.map((m, i) => {\n            let out = i + (m.validEnd ? \"*\" : \" \") + \" \";\n            for (let i = 0; i < m.next.length; i++)\n                out += (i ? \", \" : \"\") + m.next[i].type.name + \"->\" + seen.indexOf(m.next[i].next);\n            return out;\n        }).join(\"\\n\");\n    }\n}\n/**\n@internal\n*/\nContentMatch.empty = new ContentMatch(true);\nclass TokenStream {\n    constructor(string, nodeTypes) {\n        this.string = string;\n        this.nodeTypes = nodeTypes;\n        this.inline = null;\n        this.pos = 0;\n        this.tokens = string.split(/\\s*(?=\\b|\\W|$)/);\n        if (this.tokens[this.tokens.length - 1] == \"\")\n            this.tokens.pop();\n        if (this.tokens[0] == \"\")\n            this.tokens.shift();\n    }\n    get next() { return this.tokens[this.pos]; }\n    eat(tok) { return this.next == tok && (this.pos++ || true); }\n    err(str) { throw new SyntaxError(str + \" (in content expression '\" + this.string + \"')\"); }\n}\nfunction parseExpr(stream) {\n    let exprs = [];\n    do {\n        exprs.push(parseExprSeq(stream));\n    } while (stream.eat(\"|\"));\n    return exprs.length == 1 ? exprs[0] : { type: \"choice\", exprs };\n}\nfunction parseExprSeq(stream) {\n    let exprs = [];\n    do {\n        exprs.push(parseExprSubscript(stream));\n    } while (stream.next && stream.next != \")\" && stream.next != \"|\");\n    return exprs.length == 1 ? exprs[0] : { type: \"seq\", exprs };\n}\nfunction parseExprSubscript(stream) {\n    let expr = parseExprAtom(stream);\n    for (;;) {\n        if (stream.eat(\"+\"))\n            expr = { type: \"plus\", expr };\n        else if (stream.eat(\"*\"))\n            expr = { type: \"star\", expr };\n        else if (stream.eat(\"?\"))\n            expr = { type: \"opt\", expr };\n        else if (stream.eat(\"{\"))\n            expr = parseExprRange(stream, expr);\n        else\n            break;\n    }\n    return expr;\n}\nfunction parseNum(stream) {\n    if (/\\D/.test(stream.next))\n        stream.err(\"Expected number, got '\" + stream.next + \"'\");\n    let result = Number(stream.next);\n    stream.pos++;\n    return result;\n}\nfunction parseExprRange(stream, expr) {\n    let min = parseNum(stream), max = min;\n    if (stream.eat(\",\")) {\n        if (stream.next != \"}\")\n            max = parseNum(stream);\n        else\n            max = -1;\n    }\n    if (!stream.eat(\"}\"))\n        stream.err(\"Unclosed braced range\");\n    return { type: \"range\", min, max, expr };\n}\nfunction resolveName(stream, name) {\n    let types = stream.nodeTypes, type = types[name];\n    if (type)\n        return [type];\n    let result = [];\n    for (let typeName in types) {\n        let type = types[typeName];\n        if (type.isInGroup(name))\n            result.push(type);\n    }\n    if (result.length == 0)\n        stream.err(\"No node type or group '\" + name + \"' found\");\n    return result;\n}\nfunction parseExprAtom(stream) {\n    if (stream.eat(\"(\")) {\n        let expr = parseExpr(stream);\n        if (!stream.eat(\")\"))\n            stream.err(\"Missing closing paren\");\n        return expr;\n    }\n    else if (!/\\W/.test(stream.next)) {\n        let exprs = resolveName(stream, stream.next).map(type => {\n            if (stream.inline == null)\n                stream.inline = type.isInline;\n            else if (stream.inline != type.isInline)\n                stream.err(\"Mixing inline and block content\");\n            return { type: \"name\", value: type };\n        });\n        stream.pos++;\n        return exprs.length == 1 ? exprs[0] : { type: \"choice\", exprs };\n    }\n    else {\n        stream.err(\"Unexpected token '\" + stream.next + \"'\");\n    }\n}\n// Construct an NFA from an expression as returned by the parser. The\n// NFA is represented as an array of states, which are themselves\n// arrays of edges, which are `{term, to}` objects. The first state is\n// the entry state and the last node is the success state.\n//\n// Note that unlike typical NFAs, the edge ordering in this one is\n// significant, in that it is used to contruct filler content when\n// necessary.\nfunction nfa(expr) {\n    let nfa = [[]];\n    connect(compile(expr, 0), node());\n    return nfa;\n    function node() { return nfa.push([]) - 1; }\n    function edge(from, to, term) {\n        let edge = { term, to };\n        nfa[from].push(edge);\n        return edge;\n    }\n    function connect(edges, to) {\n        edges.forEach(edge => edge.to = to);\n    }\n    function compile(expr, from) {\n        if (expr.type == \"choice\") {\n            return expr.exprs.reduce((out, expr) => out.concat(compile(expr, from)), []);\n        }\n        else if (expr.type == \"seq\") {\n            for (let i = 0;; i++) {\n                let next = compile(expr.exprs[i], from);\n                if (i == expr.exprs.length - 1)\n                    return next;\n                connect(next, from = node());\n            }\n        }\n        else if (expr.type == \"star\") {\n            let loop = node();\n            edge(from, loop);\n            connect(compile(expr.expr, loop), loop);\n            return [edge(loop)];\n        }\n        else if (expr.type == \"plus\") {\n            let loop = node();\n            connect(compile(expr.expr, from), loop);\n            connect(compile(expr.expr, loop), loop);\n            return [edge(loop)];\n        }\n        else if (expr.type == \"opt\") {\n            return [edge(from)].concat(compile(expr.expr, from));\n        }\n        else if (expr.type == \"range\") {\n            let cur = from;\n            for (let i = 0; i < expr.min; i++) {\n                let next = node();\n                connect(compile(expr.expr, cur), next);\n                cur = next;\n            }\n            if (expr.max == -1) {\n                connect(compile(expr.expr, cur), cur);\n            }\n            else {\n                for (let i = expr.min; i < expr.max; i++) {\n                    let next = node();\n                    edge(cur, next);\n                    connect(compile(expr.expr, cur), next);\n                    cur = next;\n                }\n            }\n            return [edge(cur)];\n        }\n        else if (expr.type == \"name\") {\n            return [edge(from, undefined, expr.value)];\n        }\n        else {\n            throw new Error(\"Unknown expr type\");\n        }\n    }\n}\nfunction cmp(a, b) { return b - a; }\n// Get the set of nodes reachable by null edges from `node`. Omit\n// nodes with only a single null-out-edge, since they may lead to\n// needless duplicated nodes.\nfunction nullFrom(nfa, node) {\n    let result = [];\n    scan(node);\n    return result.sort(cmp);\n    function scan(node) {\n        let edges = nfa[node];\n        if (edges.length == 1 && !edges[0].term)\n            return scan(edges[0].to);\n        result.push(node);\n        for (let i = 0; i < edges.length; i++) {\n            let { term, to } = edges[i];\n            if (!term && result.indexOf(to) == -1)\n                scan(to);\n        }\n    }\n}\n// Compiles an NFA as produced by `nfa` into a DFA, modeled as a set\n// of state objects (`ContentMatch` instances) with transitions\n// between them.\nfunction dfa(nfa) {\n    let labeled = Object.create(null);\n    return explore(nullFrom(nfa, 0));\n    function explore(states) {\n        let out = [];\n        states.forEach(node => {\n            nfa[node].forEach(({ term, to }) => {\n                if (!term)\n                    return;\n                let set;\n                for (let i = 0; i < out.length; i++)\n                    if (out[i][0] == term)\n                        set = out[i][1];\n                nullFrom(nfa, to).forEach(node => {\n                    if (!set)\n                        out.push([term, set = []]);\n                    if (set.indexOf(node) == -1)\n                        set.push(node);\n                });\n            });\n        });\n        let state = labeled[states.join(\",\")] = new ContentMatch(states.indexOf(nfa.length - 1) > -1);\n        for (let i = 0; i < out.length; i++) {\n            let states = out[i][1].sort(cmp);\n            state.next.push({ type: out[i][0], next: labeled[states.join(\",\")] || explore(states) });\n        }\n        return state;\n    }\n}\nfunction checkForDeadEnds(match, stream) {\n    for (let i = 0, work = [match]; i < work.length; i++) {\n        let state = work[i], dead = !state.validEnd, nodes = [];\n        for (let j = 0; j < state.next.length; j++) {\n            let { type, next } = state.next[j];\n            nodes.push(type.name);\n            if (dead && !(type.isText || type.hasRequiredAttrs()))\n                dead = false;\n            if (work.indexOf(next) == -1)\n                work.push(next);\n        }\n        if (dead)\n            stream.err(\"Only non-generatable nodes (\" + nodes.join(\", \") + \") in a required position (see https://prosemirror.net/docs/guide/#generatable)\");\n    }\n}\n\n// For node types where all attrs have a default value (or which don't\n// have any attributes), build up a single reusable default attribute\n// object, and use it for all nodes that don't specify specific\n// attributes.\nfunction defaultAttrs(attrs) {\n    let defaults = Object.create(null);\n    for (let attrName in attrs) {\n        let attr = attrs[attrName];\n        if (!attr.hasDefault)\n            return null;\n        defaults[attrName] = attr.default;\n    }\n    return defaults;\n}\nfunction computeAttrs(attrs, value) {\n    let built = Object.create(null);\n    for (let name in attrs) {\n        let given = value && value[name];\n        if (given === undefined) {\n            let attr = attrs[name];\n            if (attr.hasDefault)\n                given = attr.default;\n            else\n                throw new RangeError(\"No value supplied for attribute \" + name);\n        }\n        built[name] = given;\n    }\n    return built;\n}\nfunction checkAttrs(attrs, values, type, name) {\n    for (let name in values)\n        if (!(name in attrs))\n            throw new RangeError(`Unsupported attribute ${name} for ${type} of type ${name}`);\n    for (let name in attrs) {\n        let attr = attrs[name];\n        if (attr.validate)\n            attr.validate(values[name]);\n    }\n}\nfunction initAttrs(typeName, attrs) {\n    let result = Object.create(null);\n    if (attrs)\n        for (let name in attrs)\n            result[name] = new Attribute(typeName, name, attrs[name]);\n    return result;\n}\n/**\nNode types are objects allocated once per `Schema` and used to\n[tag](https://prosemirror.net/docs/ref/#model.Node.type) `Node` instances. They contain information\nabout the node type, such as its name and what kind of node it\nrepresents.\n*/\nclass NodeType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name the node type has in this schema.\n    */\n    name, \n    /**\n    A link back to the `Schema` the node type belongs to.\n    */\n    schema, \n    /**\n    The spec that this type is based on\n    */\n    spec) {\n        this.name = name;\n        this.schema = schema;\n        this.spec = spec;\n        /**\n        The set of marks allowed in this node. `null` means all marks\n        are allowed.\n        */\n        this.markSet = null;\n        this.groups = spec.group ? spec.group.split(\" \") : [];\n        this.attrs = initAttrs(name, spec.attrs);\n        this.defaultAttrs = defaultAttrs(this.attrs);\n        this.contentMatch = null;\n        this.inlineContent = null;\n        this.isBlock = !(spec.inline || name == \"text\");\n        this.isText = name == \"text\";\n    }\n    /**\n    True if this is an inline type.\n    */\n    get isInline() { return !this.isBlock; }\n    /**\n    True if this is a textblock type, a block that contains inline\n    content.\n    */\n    get isTextblock() { return this.isBlock && this.inlineContent; }\n    /**\n    True for node types that allow no content.\n    */\n    get isLeaf() { return this.contentMatch == ContentMatch.empty; }\n    /**\n    True when this node is an atom, i.e. when it does not have\n    directly editable content.\n    */\n    get isAtom() { return this.isLeaf || !!this.spec.atom; }\n    /**\n    Return true when this node type is part of the given\n    [group](https://prosemirror.net/docs/ref/#model.NodeSpec.group).\n    */\n    isInGroup(group) {\n        return this.groups.indexOf(group) > -1;\n    }\n    /**\n    The node type's [whitespace](https://prosemirror.net/docs/ref/#model.NodeSpec.whitespace) option.\n    */\n    get whitespace() {\n        return this.spec.whitespace || (this.spec.code ? \"pre\" : \"normal\");\n    }\n    /**\n    Tells you whether this node type has any required attributes.\n    */\n    hasRequiredAttrs() {\n        for (let n in this.attrs)\n            if (this.attrs[n].isRequired)\n                return true;\n        return false;\n    }\n    /**\n    Indicates whether this node allows some of the same content as\n    the given node type.\n    */\n    compatibleContent(other) {\n        return this == other || this.contentMatch.compatible(other.contentMatch);\n    }\n    /**\n    @internal\n    */\n    computeAttrs(attrs) {\n        if (!attrs && this.defaultAttrs)\n            return this.defaultAttrs;\n        else\n            return computeAttrs(this.attrs, attrs);\n    }\n    /**\n    Create a `Node` of this type. The given attributes are\n    checked and defaulted (you can pass `null` to use the type's\n    defaults entirely, if no required attributes exist). `content`\n    may be a `Fragment`, a node, an array of nodes, or\n    `null`. Similarly `marks` may be `null` to default to the empty\n    set of marks.\n    */\n    create(attrs = null, content, marks) {\n        if (this.isText)\n            throw new Error(\"NodeType.create can't construct text nodes\");\n        return new Node(this, this.computeAttrs(attrs), Fragment.from(content), Mark.setFrom(marks));\n    }\n    /**\n    Like [`create`](https://prosemirror.net/docs/ref/#model.NodeType.create), but check the given content\n    against the node type's content restrictions, and throw an error\n    if it doesn't match.\n    */\n    createChecked(attrs = null, content, marks) {\n        content = Fragment.from(content);\n        this.checkContent(content);\n        return new Node(this, this.computeAttrs(attrs), content, Mark.setFrom(marks));\n    }\n    /**\n    Like [`create`](https://prosemirror.net/docs/ref/#model.NodeType.create), but see if it is\n    necessary to add nodes to the start or end of the given fragment\n    to make it fit the node. If no fitting wrapping can be found,\n    return null. Note that, due to the fact that required nodes can\n    always be created, this will always succeed if you pass null or\n    `Fragment.empty` as content.\n    */\n    createAndFill(attrs = null, content, marks) {\n        attrs = this.computeAttrs(attrs);\n        content = Fragment.from(content);\n        if (content.size) {\n            let before = this.contentMatch.fillBefore(content);\n            if (!before)\n                return null;\n            content = before.append(content);\n        }\n        let matched = this.contentMatch.matchFragment(content);\n        let after = matched && matched.fillBefore(Fragment.empty, true);\n        if (!after)\n            return null;\n        return new Node(this, attrs, content.append(after), Mark.setFrom(marks));\n    }\n    /**\n    Returns true if the given fragment is valid content for this node\n    type.\n    */\n    validContent(content) {\n        let result = this.contentMatch.matchFragment(content);\n        if (!result || !result.validEnd)\n            return false;\n        for (let i = 0; i < content.childCount; i++)\n            if (!this.allowsMarks(content.child(i).marks))\n                return false;\n        return true;\n    }\n    /**\n    Throws a RangeError if the given fragment is not valid content for this\n    node type.\n    @internal\n    */\n    checkContent(content) {\n        if (!this.validContent(content))\n            throw new RangeError(`Invalid content for node ${this.name}: ${content.toString().slice(0, 50)}`);\n    }\n    /**\n    @internal\n    */\n    checkAttrs(attrs) {\n        checkAttrs(this.attrs, attrs, \"node\", this.name);\n    }\n    /**\n    Check whether the given mark type is allowed in this node.\n    */\n    allowsMarkType(markType) {\n        return this.markSet == null || this.markSet.indexOf(markType) > -1;\n    }\n    /**\n    Test whether the given set of marks are allowed in this node.\n    */\n    allowsMarks(marks) {\n        if (this.markSet == null)\n            return true;\n        for (let i = 0; i < marks.length; i++)\n            if (!this.allowsMarkType(marks[i].type))\n                return false;\n        return true;\n    }\n    /**\n    Removes the marks that are not allowed in this node from the given set.\n    */\n    allowedMarks(marks) {\n        if (this.markSet == null)\n            return marks;\n        let copy;\n        for (let i = 0; i < marks.length; i++) {\n            if (!this.allowsMarkType(marks[i].type)) {\n                if (!copy)\n                    copy = marks.slice(0, i);\n            }\n            else if (copy) {\n                copy.push(marks[i]);\n            }\n        }\n        return !copy ? marks : copy.length ? copy : Mark.none;\n    }\n    /**\n    @internal\n    */\n    static compile(nodes, schema) {\n        let result = Object.create(null);\n        nodes.forEach((name, spec) => result[name] = new NodeType(name, schema, spec));\n        let topType = schema.spec.topNode || \"doc\";\n        if (!result[topType])\n            throw new RangeError(\"Schema is missing its top node type ('\" + topType + \"')\");\n        if (!result.text)\n            throw new RangeError(\"Every schema needs a 'text' type\");\n        for (let _ in result.text.attrs)\n            throw new RangeError(\"The text node type should not have attributes\");\n        return result;\n    }\n}\nfunction validateType(typeName, attrName, type) {\n    let types = type.split(\"|\");\n    return (value) => {\n        let name = value === null ? \"null\" : typeof value;\n        if (types.indexOf(name) < 0)\n            throw new RangeError(`Expected value of type ${types} for attribute ${attrName} on type ${typeName}, got ${name}`);\n    };\n}\n// Attribute descriptors\nclass Attribute {\n    constructor(typeName, attrName, options) {\n        this.hasDefault = Object.prototype.hasOwnProperty.call(options, \"default\");\n        this.default = options.default;\n        this.validate = typeof options.validate == \"string\" ? validateType(typeName, attrName, options.validate) : options.validate;\n    }\n    get isRequired() {\n        return !this.hasDefault;\n    }\n}\n// Marks\n/**\nLike nodes, marks (which are associated with nodes to signify\nthings like emphasis or being part of a link) are\n[tagged](https://prosemirror.net/docs/ref/#model.Mark.type) with type objects, which are\ninstantiated once per `Schema`.\n*/\nclass MarkType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name of the mark type.\n    */\n    name, \n    /**\n    @internal\n    */\n    rank, \n    /**\n    The schema that this mark type instance is part of.\n    */\n    schema, \n    /**\n    The spec on which the type is based.\n    */\n    spec) {\n        this.name = name;\n        this.rank = rank;\n        this.schema = schema;\n        this.spec = spec;\n        this.attrs = initAttrs(name, spec.attrs);\n        this.excluded = null;\n        let defaults = defaultAttrs(this.attrs);\n        this.instance = defaults ? new Mark(this, defaults) : null;\n    }\n    /**\n    Create a mark of this type. `attrs` may be `null` or an object\n    containing only some of the mark's attributes. The others, if\n    they have defaults, will be added.\n    */\n    create(attrs = null) {\n        if (!attrs && this.instance)\n            return this.instance;\n        return new Mark(this, computeAttrs(this.attrs, attrs));\n    }\n    /**\n    @internal\n    */\n    static compile(marks, schema) {\n        let result = Object.create(null), rank = 0;\n        marks.forEach((name, spec) => result[name] = new MarkType(name, rank++, schema, spec));\n        return result;\n    }\n    /**\n    When there is a mark of this type in the given set, a new set\n    without it is returned. Otherwise, the input set is returned.\n    */\n    removeFromSet(set) {\n        for (var i = 0; i < set.length; i++)\n            if (set[i].type == this) {\n                set = set.slice(0, i).concat(set.slice(i + 1));\n                i--;\n            }\n        return set;\n    }\n    /**\n    Tests whether there is a mark of this type in the given set.\n    */\n    isInSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (set[i].type == this)\n                return set[i];\n    }\n    /**\n    @internal\n    */\n    checkAttrs(attrs) {\n        checkAttrs(this.attrs, attrs, \"mark\", this.name);\n    }\n    /**\n    Queries whether a given mark type is\n    [excluded](https://prosemirror.net/docs/ref/#model.MarkSpec.excludes) by this one.\n    */\n    excludes(other) {\n        return this.excluded.indexOf(other) > -1;\n    }\n}\n/**\nA document schema. Holds [node](https://prosemirror.net/docs/ref/#model.NodeType) and [mark\ntype](https://prosemirror.net/docs/ref/#model.MarkType) objects for the nodes and marks that may\noccur in conforming documents, and provides functionality for\ncreating and deserializing such documents.\n\nWhen given, the type parameters provide the names of the nodes and\nmarks in this schema.\n*/\nclass Schema {\n    /**\n    Construct a schema from a schema [specification](https://prosemirror.net/docs/ref/#model.SchemaSpec).\n    */\n    constructor(spec) {\n        /**\n        The [linebreak\n        replacement](https://prosemirror.net/docs/ref/#model.NodeSpec.linebreakReplacement) node defined\n        in this schema, if any.\n        */\n        this.linebreakReplacement = null;\n        /**\n        An object for storing whatever values modules may want to\n        compute and cache per schema. (If you want to store something\n        in it, try to use property names unlikely to clash.)\n        */\n        this.cached = Object.create(null);\n        let instanceSpec = this.spec = {};\n        for (let prop in spec)\n            instanceSpec[prop] = spec[prop];\n        instanceSpec.nodes = orderedmap__WEBPACK_IMPORTED_MODULE_0__[\"default\"].from(spec.nodes),\n            instanceSpec.marks = orderedmap__WEBPACK_IMPORTED_MODULE_0__[\"default\"].from(spec.marks || {}),\n            this.nodes = NodeType.compile(this.spec.nodes, this);\n        this.marks = MarkType.compile(this.spec.marks, this);\n        let contentExprCache = Object.create(null);\n        for (let prop in this.nodes) {\n            if (prop in this.marks)\n                throw new RangeError(prop + \" can not be both a node and a mark\");\n            let type = this.nodes[prop], contentExpr = type.spec.content || \"\", markExpr = type.spec.marks;\n            type.contentMatch = contentExprCache[contentExpr] ||\n                (contentExprCache[contentExpr] = ContentMatch.parse(contentExpr, this.nodes));\n            type.inlineContent = type.contentMatch.inlineContent;\n            if (type.spec.linebreakReplacement) {\n                if (this.linebreakReplacement)\n                    throw new RangeError(\"Multiple linebreak nodes defined\");\n                if (!type.isInline || !type.isLeaf)\n                    throw new RangeError(\"Linebreak replacement nodes must be inline leaf nodes\");\n                this.linebreakReplacement = type;\n            }\n            type.markSet = markExpr == \"_\" ? null :\n                markExpr ? gatherMarks(this, markExpr.split(\" \")) :\n                    markExpr == \"\" || !type.inlineContent ? [] : null;\n        }\n        for (let prop in this.marks) {\n            let type = this.marks[prop], excl = type.spec.excludes;\n            type.excluded = excl == null ? [type] : excl == \"\" ? [] : gatherMarks(this, excl.split(\" \"));\n        }\n        this.nodeFromJSON = this.nodeFromJSON.bind(this);\n        this.markFromJSON = this.markFromJSON.bind(this);\n        this.topNodeType = this.nodes[this.spec.topNode || \"doc\"];\n        this.cached.wrappings = Object.create(null);\n    }\n    /**\n    Create a node in this schema. The `type` may be a string or a\n    `NodeType` instance. Attributes will be extended with defaults,\n    `content` may be a `Fragment`, `null`, a `Node`, or an array of\n    nodes.\n    */\n    node(type, attrs = null, content, marks) {\n        if (typeof type == \"string\")\n            type = this.nodeType(type);\n        else if (!(type instanceof NodeType))\n            throw new RangeError(\"Invalid node type: \" + type);\n        else if (type.schema != this)\n            throw new RangeError(\"Node type from different schema used (\" + type.name + \")\");\n        return type.createChecked(attrs, content, marks);\n    }\n    /**\n    Create a text node in the schema. Empty text nodes are not\n    allowed.\n    */\n    text(text, marks) {\n        let type = this.nodes.text;\n        return new TextNode(type, type.defaultAttrs, text, Mark.setFrom(marks));\n    }\n    /**\n    Create a mark with the given type and attributes.\n    */\n    mark(type, attrs) {\n        if (typeof type == \"string\")\n            type = this.marks[type];\n        return type.create(attrs);\n    }\n    /**\n    Deserialize a node from its JSON representation. This method is\n    bound.\n    */\n    nodeFromJSON(json) {\n        return Node.fromJSON(this, json);\n    }\n    /**\n    Deserialize a mark from its JSON representation. This method is\n    bound.\n    */\n    markFromJSON(json) {\n        return Mark.fromJSON(this, json);\n    }\n    /**\n    @internal\n    */\n    nodeType(name) {\n        let found = this.nodes[name];\n        if (!found)\n            throw new RangeError(\"Unknown node type: \" + name);\n        return found;\n    }\n}\nfunction gatherMarks(schema, marks) {\n    let found = [];\n    for (let i = 0; i < marks.length; i++) {\n        let name = marks[i], mark = schema.marks[name], ok = mark;\n        if (mark) {\n            found.push(mark);\n        }\n        else {\n            for (let prop in schema.marks) {\n                let mark = schema.marks[prop];\n                if (name == \"_\" || (mark.spec.group && mark.spec.group.split(\" \").indexOf(name) > -1))\n                    found.push(ok = mark);\n            }\n        }\n        if (!ok)\n            throw new SyntaxError(\"Unknown mark type: '\" + marks[i] + \"'\");\n    }\n    return found;\n}\n\nfunction isTagRule(rule) { return rule.tag != null; }\nfunction isStyleRule(rule) { return rule.style != null; }\n/**\nA DOM parser represents a strategy for parsing DOM content into a\nProseMirror document conforming to a given schema. Its behavior is\ndefined by an array of [rules](https://prosemirror.net/docs/ref/#model.ParseRule).\n*/\nclass DOMParser {\n    /**\n    Create a parser that targets the given schema, using the given\n    parsing rules.\n    */\n    constructor(\n    /**\n    The schema into which the parser parses.\n    */\n    schema, \n    /**\n    The set of [parse rules](https://prosemirror.net/docs/ref/#model.ParseRule) that the parser\n    uses, in order of precedence.\n    */\n    rules) {\n        this.schema = schema;\n        this.rules = rules;\n        /**\n        @internal\n        */\n        this.tags = [];\n        /**\n        @internal\n        */\n        this.styles = [];\n        let matchedStyles = this.matchedStyles = [];\n        rules.forEach(rule => {\n            if (isTagRule(rule)) {\n                this.tags.push(rule);\n            }\n            else if (isStyleRule(rule)) {\n                let prop = /[^=]*/.exec(rule.style)[0];\n                if (matchedStyles.indexOf(prop) < 0)\n                    matchedStyles.push(prop);\n                this.styles.push(rule);\n            }\n        });\n        // Only normalize list elements when lists in the schema can't directly contain themselves\n        this.normalizeLists = !this.tags.some(r => {\n            if (!/^(ul|ol)\\b/.test(r.tag) || !r.node)\n                return false;\n            let node = schema.nodes[r.node];\n            return node.contentMatch.matchType(node);\n        });\n    }\n    /**\n    Parse a document from the content of a DOM node.\n    */\n    parse(dom, options = {}) {\n        let context = new ParseContext(this, options, false);\n        context.addAll(dom, Mark.none, options.from, options.to);\n        return context.finish();\n    }\n    /**\n    Parses the content of the given DOM node, like\n    [`parse`](https://prosemirror.net/docs/ref/#model.DOMParser.parse), and takes the same set of\n    options. But unlike that method, which produces a whole node,\n    this one returns a slice that is open at the sides, meaning that\n    the schema constraints aren't applied to the start of nodes to\n    the left of the input and the end of nodes at the end.\n    */\n    parseSlice(dom, options = {}) {\n        let context = new ParseContext(this, options, true);\n        context.addAll(dom, Mark.none, options.from, options.to);\n        return Slice.maxOpen(context.finish());\n    }\n    /**\n    @internal\n    */\n    matchTag(dom, context, after) {\n        for (let i = after ? this.tags.indexOf(after) + 1 : 0; i < this.tags.length; i++) {\n            let rule = this.tags[i];\n            if (matches(dom, rule.tag) &&\n                (rule.namespace === undefined || dom.namespaceURI == rule.namespace) &&\n                (!rule.context || context.matchesContext(rule.context))) {\n                if (rule.getAttrs) {\n                    let result = rule.getAttrs(dom);\n                    if (result === false)\n                        continue;\n                    rule.attrs = result || undefined;\n                }\n                return rule;\n            }\n        }\n    }\n    /**\n    @internal\n    */\n    matchStyle(prop, value, context, after) {\n        for (let i = after ? this.styles.indexOf(after) + 1 : 0; i < this.styles.length; i++) {\n            let rule = this.styles[i], style = rule.style;\n            if (style.indexOf(prop) != 0 ||\n                rule.context && !context.matchesContext(rule.context) ||\n                // Test that the style string either precisely matches the prop,\n                // or has an '=' sign after the prop, followed by the given\n                // value.\n                style.length > prop.length &&\n                    (style.charCodeAt(prop.length) != 61 || style.slice(prop.length + 1) != value))\n                continue;\n            if (rule.getAttrs) {\n                let result = rule.getAttrs(value);\n                if (result === false)\n                    continue;\n                rule.attrs = result || undefined;\n            }\n            return rule;\n        }\n    }\n    /**\n    @internal\n    */\n    static schemaRules(schema) {\n        let result = [];\n        function insert(rule) {\n            let priority = rule.priority == null ? 50 : rule.priority, i = 0;\n            for (; i < result.length; i++) {\n                let next = result[i], nextPriority = next.priority == null ? 50 : next.priority;\n                if (nextPriority < priority)\n                    break;\n            }\n            result.splice(i, 0, rule);\n        }\n        for (let name in schema.marks) {\n            let rules = schema.marks[name].spec.parseDOM;\n            if (rules)\n                rules.forEach(rule => {\n                    insert(rule = copy(rule));\n                    if (!(rule.mark || rule.ignore || rule.clearMark))\n                        rule.mark = name;\n                });\n        }\n        for (let name in schema.nodes) {\n            let rules = schema.nodes[name].spec.parseDOM;\n            if (rules)\n                rules.forEach(rule => {\n                    insert(rule = copy(rule));\n                    if (!(rule.node || rule.ignore || rule.mark))\n                        rule.node = name;\n                });\n        }\n        return result;\n    }\n    /**\n    Construct a DOM parser using the parsing rules listed in a\n    schema's [node specs](https://prosemirror.net/docs/ref/#model.NodeSpec.parseDOM), reordered by\n    [priority](https://prosemirror.net/docs/ref/#model.ParseRule.priority).\n    */\n    static fromSchema(schema) {\n        return schema.cached.domParser ||\n            (schema.cached.domParser = new DOMParser(schema, DOMParser.schemaRules(schema)));\n    }\n}\nconst blockTags = {\n    address: true, article: true, aside: true, blockquote: true, canvas: true,\n    dd: true, div: true, dl: true, fieldset: true, figcaption: true, figure: true,\n    footer: true, form: true, h1: true, h2: true, h3: true, h4: true, h5: true,\n    h6: true, header: true, hgroup: true, hr: true, li: true, noscript: true, ol: true,\n    output: true, p: true, pre: true, section: true, table: true, tfoot: true, ul: true\n};\nconst ignoreTags = {\n    head: true, noscript: true, object: true, script: true, style: true, title: true\n};\nconst listTags = { ol: true, ul: true };\n// Using a bitfield for node context options\nconst OPT_PRESERVE_WS = 1, OPT_PRESERVE_WS_FULL = 2, OPT_OPEN_LEFT = 4;\nfunction wsOptionsFor(type, preserveWhitespace, base) {\n    if (preserveWhitespace != null)\n        return (preserveWhitespace ? OPT_PRESERVE_WS : 0) |\n            (preserveWhitespace === \"full\" ? OPT_PRESERVE_WS_FULL : 0);\n    return type && type.whitespace == \"pre\" ? OPT_PRESERVE_WS | OPT_PRESERVE_WS_FULL : base & ~OPT_OPEN_LEFT;\n}\nclass NodeContext {\n    constructor(type, attrs, marks, solid, match, options) {\n        this.type = type;\n        this.attrs = attrs;\n        this.marks = marks;\n        this.solid = solid;\n        this.options = options;\n        this.content = [];\n        // Marks applied to the node's children\n        this.activeMarks = Mark.none;\n        this.match = match || (options & OPT_OPEN_LEFT ? null : type.contentMatch);\n    }\n    findWrapping(node) {\n        if (!this.match) {\n            if (!this.type)\n                return [];\n            let fill = this.type.contentMatch.fillBefore(Fragment.from(node));\n            if (fill) {\n                this.match = this.type.contentMatch.matchFragment(fill);\n            }\n            else {\n                let start = this.type.contentMatch, wrap;\n                if (wrap = start.findWrapping(node.type)) {\n                    this.match = start;\n                    return wrap;\n                }\n                else {\n                    return null;\n                }\n            }\n        }\n        return this.match.findWrapping(node.type);\n    }\n    finish(openEnd) {\n        if (!(this.options & OPT_PRESERVE_WS)) { // Strip trailing whitespace\n            let last = this.content[this.content.length - 1], m;\n            if (last && last.isText && (m = /[ \\t\\r\\n\\u000c]+$/.exec(last.text))) {\n                let text = last;\n                if (last.text.length == m[0].length)\n                    this.content.pop();\n                else\n                    this.content[this.content.length - 1] = text.withText(text.text.slice(0, text.text.length - m[0].length));\n            }\n        }\n        let content = Fragment.from(this.content);\n        if (!openEnd && this.match)\n            content = content.append(this.match.fillBefore(Fragment.empty, true));\n        return this.type ? this.type.create(this.attrs, content, this.marks) : content;\n    }\n    inlineContext(node) {\n        if (this.type)\n            return this.type.inlineContent;\n        if (this.content.length)\n            return this.content[0].isInline;\n        return node.parentNode && !blockTags.hasOwnProperty(node.parentNode.nodeName.toLowerCase());\n    }\n}\nclass ParseContext {\n    constructor(\n    // The parser we are using.\n    parser, \n    // The options passed to this parse.\n    options, isOpen) {\n        this.parser = parser;\n        this.options = options;\n        this.isOpen = isOpen;\n        this.open = 0;\n        this.localPreserveWS = false;\n        let topNode = options.topNode, topContext;\n        let topOptions = wsOptionsFor(null, options.preserveWhitespace, 0) | (isOpen ? OPT_OPEN_LEFT : 0);\n        if (topNode)\n            topContext = new NodeContext(topNode.type, topNode.attrs, Mark.none, true, options.topMatch || topNode.type.contentMatch, topOptions);\n        else if (isOpen)\n            topContext = new NodeContext(null, null, Mark.none, true, null, topOptions);\n        else\n            topContext = new NodeContext(parser.schema.topNodeType, null, Mark.none, true, null, topOptions);\n        this.nodes = [topContext];\n        this.find = options.findPositions;\n        this.needsBlock = false;\n    }\n    get top() {\n        return this.nodes[this.open];\n    }\n    // Add a DOM node to the content. Text is inserted as text node,\n    // otherwise, the node is passed to `addElement` or, if it has a\n    // `style` attribute, `addElementWithStyles`.\n    addDOM(dom, marks) {\n        if (dom.nodeType == 3)\n            this.addTextNode(dom, marks);\n        else if (dom.nodeType == 1)\n            this.addElement(dom, marks);\n    }\n    addTextNode(dom, marks) {\n        let value = dom.nodeValue;\n        let top = this.top, preserveWS = (top.options & OPT_PRESERVE_WS_FULL) ? \"full\"\n            : this.localPreserveWS || (top.options & OPT_PRESERVE_WS) > 0;\n        if (preserveWS === \"full\" ||\n            top.inlineContext(dom) ||\n            /[^ \\t\\r\\n\\u000c]/.test(value)) {\n            if (!preserveWS) {\n                value = value.replace(/[ \\t\\r\\n\\u000c]+/g, \" \");\n                // If this starts with whitespace, and there is no node before it, or\n                // a hard break, or a text node that ends with whitespace, strip the\n                // leading space.\n                if (/^[ \\t\\r\\n\\u000c]/.test(value) && this.open == this.nodes.length - 1) {\n                    let nodeBefore = top.content[top.content.length - 1];\n                    let domNodeBefore = dom.previousSibling;\n                    if (!nodeBefore ||\n                        (domNodeBefore && domNodeBefore.nodeName == 'BR') ||\n                        (nodeBefore.isText && /[ \\t\\r\\n\\u000c]$/.test(nodeBefore.text)))\n                        value = value.slice(1);\n                }\n            }\n            else if (preserveWS !== \"full\") {\n                value = value.replace(/\\r?\\n|\\r/g, \" \");\n            }\n            else {\n                value = value.replace(/\\r\\n?/g, \"\\n\");\n            }\n            if (value)\n                this.insertNode(this.parser.schema.text(value), marks);\n            this.findInText(dom);\n        }\n        else {\n            this.findInside(dom);\n        }\n    }\n    // Try to find a handler for the given tag and use that to parse. If\n    // none is found, the element's content nodes are added directly.\n    addElement(dom, marks, matchAfter) {\n        let outerWS = this.localPreserveWS, top = this.top;\n        if (dom.tagName == \"PRE\" || /pre/.test(dom.style && dom.style.whiteSpace))\n            this.localPreserveWS = true;\n        let name = dom.nodeName.toLowerCase(), ruleID;\n        if (listTags.hasOwnProperty(name) && this.parser.normalizeLists)\n            normalizeList(dom);\n        let rule = (this.options.ruleFromNode && this.options.ruleFromNode(dom)) ||\n            (ruleID = this.parser.matchTag(dom, this, matchAfter));\n        out: if (rule ? rule.ignore : ignoreTags.hasOwnProperty(name)) {\n            this.findInside(dom);\n            this.ignoreFallback(dom, marks);\n        }\n        else if (!rule || rule.skip || rule.closeParent) {\n            if (rule && rule.closeParent)\n                this.open = Math.max(0, this.open - 1);\n            else if (rule && rule.skip.nodeType)\n                dom = rule.skip;\n            let sync, oldNeedsBlock = this.needsBlock;\n            if (blockTags.hasOwnProperty(name)) {\n                if (top.content.length && top.content[0].isInline && this.open) {\n                    this.open--;\n                    top = this.top;\n                }\n                sync = true;\n                if (!top.type)\n                    this.needsBlock = true;\n            }\n            else if (!dom.firstChild) {\n                this.leafFallback(dom, marks);\n                break out;\n            }\n            let innerMarks = rule && rule.skip ? marks : this.readStyles(dom, marks);\n            if (innerMarks)\n                this.addAll(dom, innerMarks);\n            if (sync)\n                this.sync(top);\n            this.needsBlock = oldNeedsBlock;\n        }\n        else {\n            let innerMarks = this.readStyles(dom, marks);\n            if (innerMarks)\n                this.addElementByRule(dom, rule, innerMarks, rule.consuming === false ? ruleID : undefined);\n        }\n        this.localPreserveWS = outerWS;\n    }\n    // Called for leaf DOM nodes that would otherwise be ignored\n    leafFallback(dom, marks) {\n        if (dom.nodeName == \"BR\" && this.top.type && this.top.type.inlineContent)\n            this.addTextNode(dom.ownerDocument.createTextNode(\"\\n\"), marks);\n    }\n    // Called for ignored nodes\n    ignoreFallback(dom, marks) {\n        // Ignored BR nodes should at least create an inline context\n        if (dom.nodeName == \"BR\" && (!this.top.type || !this.top.type.inlineContent))\n            this.findPlace(this.parser.schema.text(\"-\"), marks);\n    }\n    // Run any style parser associated with the node's styles. Either\n    // return an updated array of marks, or null to indicate some of the\n    // styles had a rule with `ignore` set.\n    readStyles(dom, marks) {\n        let styles = dom.style;\n        // Because many properties will only show up in 'normalized' form\n        // in `style.item` (i.e. text-decoration becomes\n        // text-decoration-line, text-decoration-color, etc), we directly\n        // query the styles mentioned in our rules instead of iterating\n        // over the items.\n        if (styles && styles.length)\n            for (let i = 0; i < this.parser.matchedStyles.length; i++) {\n                let name = this.parser.matchedStyles[i], value = styles.getPropertyValue(name);\n                if (value)\n                    for (let after = undefined;;) {\n                        let rule = this.parser.matchStyle(name, value, this, after);\n                        if (!rule)\n                            break;\n                        if (rule.ignore)\n                            return null;\n                        if (rule.clearMark)\n                            marks = marks.filter(m => !rule.clearMark(m));\n                        else\n                            marks = marks.concat(this.parser.schema.marks[rule.mark].create(rule.attrs));\n                        if (rule.consuming === false)\n                            after = rule;\n                        else\n                            break;\n                    }\n            }\n        return marks;\n    }\n    // Look up a handler for the given node. If none are found, return\n    // false. Otherwise, apply it, use its return value to drive the way\n    // the node's content is wrapped, and return true.\n    addElementByRule(dom, rule, marks, continueAfter) {\n        let sync, nodeType;\n        if (rule.node) {\n            nodeType = this.parser.schema.nodes[rule.node];\n            if (!nodeType.isLeaf) {\n                let inner = this.enter(nodeType, rule.attrs || null, marks, rule.preserveWhitespace);\n                if (inner) {\n                    sync = true;\n                    marks = inner;\n                }\n            }\n            else if (!this.insertNode(nodeType.create(rule.attrs), marks)) {\n                this.leafFallback(dom, marks);\n            }\n        }\n        else {\n            let markType = this.parser.schema.marks[rule.mark];\n            marks = marks.concat(markType.create(rule.attrs));\n        }\n        let startIn = this.top;\n        if (nodeType && nodeType.isLeaf) {\n            this.findInside(dom);\n        }\n        else if (continueAfter) {\n            this.addElement(dom, marks, continueAfter);\n        }\n        else if (rule.getContent) {\n            this.findInside(dom);\n            rule.getContent(dom, this.parser.schema).forEach(node => this.insertNode(node, marks));\n        }\n        else {\n            let contentDOM = dom;\n            if (typeof rule.contentElement == \"string\")\n                contentDOM = dom.querySelector(rule.contentElement);\n            else if (typeof rule.contentElement == \"function\")\n                contentDOM = rule.contentElement(dom);\n            else if (rule.contentElement)\n                contentDOM = rule.contentElement;\n            this.findAround(dom, contentDOM, true);\n            this.addAll(contentDOM, marks);\n            this.findAround(dom, contentDOM, false);\n        }\n        if (sync && this.sync(startIn))\n            this.open--;\n    }\n    // Add all child nodes between `startIndex` and `endIndex` (or the\n    // whole node, if not given). If `sync` is passed, use it to\n    // synchronize after every block element.\n    addAll(parent, marks, startIndex, endIndex) {\n        let index = startIndex || 0;\n        for (let dom = startIndex ? parent.childNodes[startIndex] : parent.firstChild, end = endIndex == null ? null : parent.childNodes[endIndex]; dom != end; dom = dom.nextSibling, ++index) {\n            this.findAtPoint(parent, index);\n            this.addDOM(dom, marks);\n        }\n        this.findAtPoint(parent, index);\n    }\n    // Try to find a way to fit the given node type into the current\n    // context. May add intermediate wrappers and/or leave non-solid\n    // nodes that we're in.\n    findPlace(node, marks) {\n        let route, sync;\n        for (let depth = this.open; depth >= 0; depth--) {\n            let cx = this.nodes[depth];\n            let found = cx.findWrapping(node);\n            if (found && (!route || route.length > found.length)) {\n                route = found;\n                sync = cx;\n                if (!found.length)\n                    break;\n            }\n            if (cx.solid)\n                break;\n        }\n        if (!route)\n            return null;\n        this.sync(sync);\n        for (let i = 0; i < route.length; i++)\n            marks = this.enterInner(route[i], null, marks, false);\n        return marks;\n    }\n    // Try to insert the given node, adjusting the context when needed.\n    insertNode(node, marks) {\n        if (node.isInline && this.needsBlock && !this.top.type) {\n            let block = this.textblockFromContext();\n            if (block)\n                marks = this.enterInner(block, null, marks);\n        }\n        let innerMarks = this.findPlace(node, marks);\n        if (innerMarks) {\n            this.closeExtra();\n            let top = this.top;\n            if (top.match)\n                top.match = top.match.matchType(node.type);\n            let nodeMarks = Mark.none;\n            for (let m of innerMarks.concat(node.marks))\n                if (top.type ? top.type.allowsMarkType(m.type) : markMayApply(m.type, node.type))\n                    nodeMarks = m.addToSet(nodeMarks);\n            top.content.push(node.mark(nodeMarks));\n            return true;\n        }\n        return false;\n    }\n    // Try to start a node of the given type, adjusting the context when\n    // necessary.\n    enter(type, attrs, marks, preserveWS) {\n        let innerMarks = this.findPlace(type.create(attrs), marks);\n        if (innerMarks)\n            innerMarks = this.enterInner(type, attrs, marks, true, preserveWS);\n        return innerMarks;\n    }\n    // Open a node of the given type\n    enterInner(type, attrs, marks, solid = false, preserveWS) {\n        this.closeExtra();\n        let top = this.top;\n        top.match = top.match && top.match.matchType(type);\n        let options = wsOptionsFor(type, preserveWS, top.options);\n        if ((top.options & OPT_OPEN_LEFT) && top.content.length == 0)\n            options |= OPT_OPEN_LEFT;\n        let applyMarks = Mark.none;\n        marks = marks.filter(m => {\n            if (top.type ? top.type.allowsMarkType(m.type) : markMayApply(m.type, type)) {\n                applyMarks = m.addToSet(applyMarks);\n                return false;\n            }\n            return true;\n        });\n        this.nodes.push(new NodeContext(type, attrs, applyMarks, solid, null, options));\n        this.open++;\n        return marks;\n    }\n    // Make sure all nodes above this.open are finished and added to\n    // their parents\n    closeExtra(openEnd = false) {\n        let i = this.nodes.length - 1;\n        if (i > this.open) {\n            for (; i > this.open; i--)\n                this.nodes[i - 1].content.push(this.nodes[i].finish(openEnd));\n            this.nodes.length = this.open + 1;\n        }\n    }\n    finish() {\n        this.open = 0;\n        this.closeExtra(this.isOpen);\n        return this.nodes[0].finish(!!(this.isOpen || this.options.topOpen));\n    }\n    sync(to) {\n        for (let i = this.open; i >= 0; i--) {\n            if (this.nodes[i] == to) {\n                this.open = i;\n                return true;\n            }\n            else if (this.localPreserveWS) {\n                this.nodes[i].options |= OPT_PRESERVE_WS;\n            }\n        }\n        return false;\n    }\n    get currentPos() {\n        this.closeExtra();\n        let pos = 0;\n        for (let i = this.open; i >= 0; i--) {\n            let content = this.nodes[i].content;\n            for (let j = content.length - 1; j >= 0; j--)\n                pos += content[j].nodeSize;\n            if (i)\n                pos++;\n        }\n        return pos;\n    }\n    findAtPoint(parent, offset) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].node == parent && this.find[i].offset == offset)\n                    this.find[i].pos = this.currentPos;\n            }\n    }\n    findInside(parent) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].pos == null && parent.nodeType == 1 && parent.contains(this.find[i].node))\n                    this.find[i].pos = this.currentPos;\n            }\n    }\n    findAround(parent, content, before) {\n        if (parent != content && this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].pos == null && parent.nodeType == 1 && parent.contains(this.find[i].node)) {\n                    let pos = content.compareDocumentPosition(this.find[i].node);\n                    if (pos & (before ? 2 : 4))\n                        this.find[i].pos = this.currentPos;\n                }\n            }\n    }\n    findInText(textNode) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].node == textNode)\n                    this.find[i].pos = this.currentPos - (textNode.nodeValue.length - this.find[i].offset);\n            }\n    }\n    // Determines whether the given context string matches this context.\n    matchesContext(context) {\n        if (context.indexOf(\"|\") > -1)\n            return context.split(/\\s*\\|\\s*/).some(this.matchesContext, this);\n        let parts = context.split(\"/\");\n        let option = this.options.context;\n        let useRoot = !this.isOpen && (!option || option.parent.type == this.nodes[0].type);\n        let minDepth = -(option ? option.depth + 1 : 0) + (useRoot ? 0 : 1);\n        let match = (i, depth) => {\n            for (; i >= 0; i--) {\n                let part = parts[i];\n                if (part == \"\") {\n                    if (i == parts.length - 1 || i == 0)\n                        continue;\n                    for (; depth >= minDepth; depth--)\n                        if (match(i - 1, depth))\n                            return true;\n                    return false;\n                }\n                else {\n                    let next = depth > 0 || (depth == 0 && useRoot) ? this.nodes[depth].type\n                        : option && depth >= minDepth ? option.node(depth - minDepth).type\n                            : null;\n                    if (!next || (next.name != part && !next.isInGroup(part)))\n                        return false;\n                    depth--;\n                }\n            }\n            return true;\n        };\n        return match(parts.length - 1, this.open);\n    }\n    textblockFromContext() {\n        let $context = this.options.context;\n        if ($context)\n            for (let d = $context.depth; d >= 0; d--) {\n                let deflt = $context.node(d).contentMatchAt($context.indexAfter(d)).defaultType;\n                if (deflt && deflt.isTextblock && deflt.defaultAttrs)\n                    return deflt;\n            }\n        for (let name in this.parser.schema.nodes) {\n            let type = this.parser.schema.nodes[name];\n            if (type.isTextblock && type.defaultAttrs)\n                return type;\n        }\n    }\n}\n// Kludge to work around directly nested list nodes produced by some\n// tools and allowed by browsers to mean that the nested list is\n// actually part of the list item above it.\nfunction normalizeList(dom) {\n    for (let child = dom.firstChild, prevItem = null; child; child = child.nextSibling) {\n        let name = child.nodeType == 1 ? child.nodeName.toLowerCase() : null;\n        if (name && listTags.hasOwnProperty(name) && prevItem) {\n            prevItem.appendChild(child);\n            child = prevItem;\n        }\n        else if (name == \"li\") {\n            prevItem = child;\n        }\n        else if (name) {\n            prevItem = null;\n        }\n    }\n}\n// Apply a CSS selector.\nfunction matches(dom, selector) {\n    return (dom.matches || dom.msMatchesSelector || dom.webkitMatchesSelector || dom.mozMatchesSelector).call(dom, selector);\n}\nfunction copy(obj) {\n    let copy = {};\n    for (let prop in obj)\n        copy[prop] = obj[prop];\n    return copy;\n}\n// Used when finding a mark at the top level of a fragment parse.\n// Checks whether it would be reasonable to apply a given mark type to\n// a given node, by looking at the way the mark occurs in the schema.\nfunction markMayApply(markType, nodeType) {\n    let nodes = nodeType.schema.nodes;\n    for (let name in nodes) {\n        let parent = nodes[name];\n        if (!parent.allowsMarkType(markType))\n            continue;\n        let seen = [], scan = (match) => {\n            seen.push(match);\n            for (let i = 0; i < match.edgeCount; i++) {\n                let { type, next } = match.edge(i);\n                if (type == nodeType)\n                    return true;\n                if (seen.indexOf(next) < 0 && scan(next))\n                    return true;\n            }\n        };\n        if (scan(parent.contentMatch))\n            return true;\n    }\n}\n\n/**\nA DOM serializer knows how to convert ProseMirror nodes and\nmarks of various types to DOM nodes.\n*/\nclass DOMSerializer {\n    /**\n    Create a serializer. `nodes` should map node names to functions\n    that take a node and return a description of the corresponding\n    DOM. `marks` does the same for mark names, but also gets an\n    argument that tells it whether the mark's content is block or\n    inline content (for typical use, it'll always be inline). A mark\n    serializer may be `null` to indicate that marks of that type\n    should not be serialized.\n    */\n    constructor(\n    /**\n    The node serialization functions.\n    */\n    nodes, \n    /**\n    The mark serialization functions.\n    */\n    marks) {\n        this.nodes = nodes;\n        this.marks = marks;\n    }\n    /**\n    Serialize the content of this fragment to a DOM fragment. When\n    not in the browser, the `document` option, containing a DOM\n    document, should be passed so that the serializer can create\n    nodes.\n    */\n    serializeFragment(fragment, options = {}, target) {\n        if (!target)\n            target = doc(options).createDocumentFragment();\n        let top = target, active = [];\n        fragment.forEach(node => {\n            if (active.length || node.marks.length) {\n                let keep = 0, rendered = 0;\n                while (keep < active.length && rendered < node.marks.length) {\n                    let next = node.marks[rendered];\n                    if (!this.marks[next.type.name]) {\n                        rendered++;\n                        continue;\n                    }\n                    if (!next.eq(active[keep][0]) || next.type.spec.spanning === false)\n                        break;\n                    keep++;\n                    rendered++;\n                }\n                while (keep < active.length)\n                    top = active.pop()[1];\n                while (rendered < node.marks.length) {\n                    let add = node.marks[rendered++];\n                    let markDOM = this.serializeMark(add, node.isInline, options);\n                    if (markDOM) {\n                        active.push([add, top]);\n                        top.appendChild(markDOM.dom);\n                        top = markDOM.contentDOM || markDOM.dom;\n                    }\n                }\n            }\n            top.appendChild(this.serializeNodeInner(node, options));\n        });\n        return target;\n    }\n    /**\n    @internal\n    */\n    serializeNodeInner(node, options) {\n        let { dom, contentDOM } = renderSpec(doc(options), this.nodes[node.type.name](node), null, node.attrs);\n        if (contentDOM) {\n            if (node.isLeaf)\n                throw new RangeError(\"Content hole not allowed in a leaf node spec\");\n            this.serializeFragment(node.content, options, contentDOM);\n        }\n        return dom;\n    }\n    /**\n    Serialize this node to a DOM node. This can be useful when you\n    need to serialize a part of a document, as opposed to the whole\n    document. To serialize a whole document, use\n    [`serializeFragment`](https://prosemirror.net/docs/ref/#model.DOMSerializer.serializeFragment) on\n    its [content](https://prosemirror.net/docs/ref/#model.Node.content).\n    */\n    serializeNode(node, options = {}) {\n        let dom = this.serializeNodeInner(node, options);\n        for (let i = node.marks.length - 1; i >= 0; i--) {\n            let wrap = this.serializeMark(node.marks[i], node.isInline, options);\n            if (wrap) {\n                (wrap.contentDOM || wrap.dom).appendChild(dom);\n                dom = wrap.dom;\n            }\n        }\n        return dom;\n    }\n    /**\n    @internal\n    */\n    serializeMark(mark, inline, options = {}) {\n        let toDOM = this.marks[mark.type.name];\n        return toDOM && renderSpec(doc(options), toDOM(mark, inline), null, mark.attrs);\n    }\n    static renderSpec(doc, structure, xmlNS = null, blockArraysIn) {\n        return renderSpec(doc, structure, xmlNS, blockArraysIn);\n    }\n    /**\n    Build a serializer using the [`toDOM`](https://prosemirror.net/docs/ref/#model.NodeSpec.toDOM)\n    properties in a schema's node and mark specs.\n    */\n    static fromSchema(schema) {\n        return schema.cached.domSerializer ||\n            (schema.cached.domSerializer = new DOMSerializer(this.nodesFromSchema(schema), this.marksFromSchema(schema)));\n    }\n    /**\n    Gather the serializers in a schema's node specs into an object.\n    This can be useful as a base to build a custom serializer from.\n    */\n    static nodesFromSchema(schema) {\n        let result = gatherToDOM(schema.nodes);\n        if (!result.text)\n            result.text = node => node.text;\n        return result;\n    }\n    /**\n    Gather the serializers in a schema's mark specs into an object.\n    */\n    static marksFromSchema(schema) {\n        return gatherToDOM(schema.marks);\n    }\n}\nfunction gatherToDOM(obj) {\n    let result = {};\n    for (let name in obj) {\n        let toDOM = obj[name].spec.toDOM;\n        if (toDOM)\n            result[name] = toDOM;\n    }\n    return result;\n}\nfunction doc(options) {\n    return options.document || window.document;\n}\nconst suspiciousAttributeCache = new WeakMap();\nfunction suspiciousAttributes(attrs) {\n    let value = suspiciousAttributeCache.get(attrs);\n    if (value === undefined)\n        suspiciousAttributeCache.set(attrs, value = suspiciousAttributesInner(attrs));\n    return value;\n}\nfunction suspiciousAttributesInner(attrs) {\n    let result = null;\n    function scan(value) {\n        if (value && typeof value == \"object\") {\n            if (Array.isArray(value)) {\n                if (typeof value[0] == \"string\") {\n                    if (!result)\n                        result = [];\n                    result.push(value);\n                }\n                else {\n                    for (let i = 0; i < value.length; i++)\n                        scan(value[i]);\n                }\n            }\n            else {\n                for (let prop in value)\n                    scan(value[prop]);\n            }\n        }\n    }\n    scan(attrs);\n    return result;\n}\nfunction renderSpec(doc, structure, xmlNS, blockArraysIn) {\n    if (typeof structure == \"string\")\n        return { dom: doc.createTextNode(structure) };\n    if (structure.nodeType != null)\n        return { dom: structure };\n    if (structure.dom && structure.dom.nodeType != null)\n        return structure;\n    let tagName = structure[0], suspicious;\n    if (typeof tagName != \"string\")\n        throw new RangeError(\"Invalid array passed to renderSpec\");\n    if (blockArraysIn && (suspicious = suspiciousAttributes(blockArraysIn)) &&\n        suspicious.indexOf(structure) > -1)\n        throw new RangeError(\"Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.\");\n    let space = tagName.indexOf(\" \");\n    if (space > 0) {\n        xmlNS = tagName.slice(0, space);\n        tagName = tagName.slice(space + 1);\n    }\n    let contentDOM;\n    let dom = (xmlNS ? doc.createElementNS(xmlNS, tagName) : doc.createElement(tagName));\n    let attrs = structure[1], start = 1;\n    if (attrs && typeof attrs == \"object\" && attrs.nodeType == null && !Array.isArray(attrs)) {\n        start = 2;\n        for (let name in attrs)\n            if (attrs[name] != null) {\n                let space = name.indexOf(\" \");\n                if (space > 0)\n                    dom.setAttributeNS(name.slice(0, space), name.slice(space + 1), attrs[name]);\n                else\n                    dom.setAttribute(name, attrs[name]);\n            }\n    }\n    for (let i = start; i < structure.length; i++) {\n        let child = structure[i];\n        if (child === 0) {\n            if (i < structure.length - 1 || i > start)\n                throw new RangeError(\"Content hole must be the only child of its parent node\");\n            return { dom, contentDOM: dom };\n        }\n        else {\n            let { dom: inner, contentDOM: innerContent } = renderSpec(doc, child, xmlNS, blockArraysIn);\n            dom.appendChild(inner);\n            if (innerContent) {\n                if (contentDOM)\n                    throw new RangeError(\"Multiple content holes\");\n                contentDOM = innerContent;\n            }\n        }\n    }\n    return { dom, contentDOM };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-model/dist/index.js\n");

/***/ })

};
;