import type { DialogProps } from '@mui/material/Dialog';

import { useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import TextField from '@mui/material/TextField';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { Upload } from 'src/components/upload';
import { Iconify } from 'src/components/iconify';
import { useTranslate } from 'src/locales';
import { axiosInstance, endpoints } from 'src/lib/axios';
import { toast } from 'src/components/snackbar';
import { PLT1OrderType } from 'src/sections/orders/plt1/plt1-order-type';

// ----------------------------------------------------------------------

type Props = DialogProps & {
  open: boolean;
  title?: string;
  folderName?: string;
  onClose: () => void;
  onCreate?: () => void;
  onUpdate?: () => void;
  onChangeFolderName?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onUploadFiles?: (files: File[]) => void;
  orderId?: string;
  orderType?: PLT1OrderType; // Add this prop to receive order type
};

export function FileManagerNewFolderDialog({
  open,
  onClose,
  onCreate,
  onUpdate,
  folderName,
  onChangeFolderName,
  title = 'Upload files',
  onUploadFiles,
  orderId,
  orderType,
  ...other
}: Props) {
  const { t } = useTranslate();
  const [isUploading, setIsUploading] = useState(false);
  const [files, setFiles] = useState<(File | string)[]>([]);

  useEffect(() => {
    if (!open) {
      setFiles([]);
    }
  }, [open]);

  const handleDrop = useCallback(
    (acceptedFiles: File[]) => {
      setFiles([...files, ...acceptedFiles]);
    },
    [files]
  );

  const getUploadEndpoint = useCallback(() => {
    if (!orderId) return null;
    
    if (orderType === 'Air') {
      return `${endpoints.plt1.airUploadFiles}/${orderId}`;
    } else if (orderType === 'Road') {
      return `${endpoints.plt1.roadUploadFiles}/${orderId}`;
    } else {
      return `${endpoints.plt1.airUploadFiles}/${orderId}`;
    }
  }, [orderId, orderType]);

  const handleUpload = async () => {
    if (!files.length) {
      toast.error(t('fileManager.dialog.noFilesSelected'));
      return;
    }

    setIsUploading(true);

    try {
      if (onUploadFiles) {
        const fileObjects = files.filter((file): file is File => file instanceof File);
        await onUploadFiles(fileObjects);
      } else {
        toast.info(t('fileManager.dialog.uploading'));

        const formData = new FormData();

        if (orderId) {
          formData.append('OrderId', orderId);
        }

        files.forEach((file) => {
          if (file instanceof File) {
            formData.append('Files', file);
          }
        });

        // Get the correct upload endpoint
        const uploadEndpoint = getUploadEndpoint();
        
        if (!uploadEndpoint) {
          throw new Error('Invalid order ID or type');
        }

        // Make API request with the correct endpoint
        await axiosInstance.post(
          uploadEndpoint,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }
        );

        toast.success(t('fileManager.dialog.uploadSuccess'));
      }

      // Close dialog and reset state
      onClose();
    } catch (error) {
      console.error('Error uploading files:', error);
      toast.error(t('fileManager.dialog.uploadError'));
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveFile = (inputFile: File | string) => {
    const filtered = files.filter((file) => file !== inputFile);
    setFiles(filtered);
  };

  const handleRemoveAllFiles = () => {
    setFiles([]);
  };

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose} {...other}>
      <DialogTitle sx={[(theme) => ({ p: theme.spacing(3, 3, 2, 3) })]}>{title}</DialogTitle>

      <DialogContent dividers sx={{ pt: 1, pb: 0, border: 'none' }}>
        {(onCreate || onUpdate) && (
          <TextField
            fullWidth
            label="Folder name"
            value={folderName}
            onChange={onChangeFolderName}
            sx={{ mb: 3 }}
          />
        )}

        <Upload multiple value={files} onDrop={handleDrop} onRemove={handleRemoveFile} />
      </DialogContent>

      <DialogActions>
        <Button
          variant="contained"
          startIcon={isUploading ? null : <Iconify icon="eva:cloud-upload-fill" />}
          onClick={handleUpload}
          disabled={isUploading || !files.length}
        >
          {isUploading ? t('fileManager.dialog.uploading') : t('fileManager.dialog.upload')}
        </Button>

        {!!files.length && !isUploading && (
          <Button variant="outlined" color="inherit" onClick={handleRemoveAllFiles}>
            {t('fileManager.dialog.removeAll')}
          </Button>
        )}

        {(onCreate || onUpdate) && (
          <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: 'flex-end' }}>
            <Button variant="soft" onClick={onCreate || onUpdate} disabled={isUploading}>
              {onUpdate ? t('fileManager.dialog.save') : t('fileManager.dialog.create')}
            </Button>
          </Box>
        )}
      </DialogActions>
    </Dialog>
  );
}