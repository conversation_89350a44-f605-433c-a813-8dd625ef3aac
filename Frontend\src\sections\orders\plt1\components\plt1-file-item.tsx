'use client';

import { useCallback } from 'react';
import { useBoolean, usePopover } from 'minimal-shared/hooks';
import { useTranslate } from 'src/locales';
import { endpoints, axiosInstance } from 'src/lib/axios';
import { toast } from 'src/components/snackbar';


import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Divider from '@mui/material/Divider';
import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import Checkbox from '@mui/material/Checkbox';
import IconButton from '@mui/material/IconButton';
import ListItemText from '@mui/material/ListItemText';
import type { PaperProps } from '@mui/material/Paper';

import { fData } from 'src/utils/format-number';
import { fDateTime } from 'src/utils/format-time';


import { Iconify } from 'src/components/iconify';
import { FileThumbnail } from 'src/components/file-thumbnail';
import { CustomPopover } from 'src/components/custom-popover';

import { PLT1OrderFile } from './plt1-file-management';

// ----------------------------------------------------------------------

type Props = PaperProps & {
  file: PLT1OrderFile;
  onDelete: () => void;
  displayFavorite?: boolean;
};

export function PLT1OrderFileItem({ file, onDelete, displayFavorite, sx, ...other }: Props) {
  const { t } = useTranslate();

  const menuActions = usePopover();

  const favorite = useBoolean(file.isFavorited);

  const renderActions = () => (
    <Box
      sx={{
        top: 8,
        right: 8,
        flexShrink: { sm: 0 },
        position: { xs: 'absolute', sm: 'unset' },
      }}
    >

      {displayFavorite && (
        <Checkbox
          color="warning"
          icon={<Iconify icon="eva:star-outline" />}
          checkedIcon={<Iconify icon="eva:star-fill" />}
          checked={favorite.value}
          onChange={favorite.onToggle}
          inputProps={{
            id: `favorite-${file.id}-checkbox`,
            'aria-label': `Favorite ${file.id} checkbox`,
          }}
        />)}

      <IconButton color={menuActions.open ? 'inherit' : 'default'} onClick={menuActions.onOpen}>
        <Iconify icon="eva:more-vertical-fill" />
      </IconButton>
    </Box>
  );

  const renderText = () => (
    <ListItemText
      primary={file.name}
      secondary={
        <>
          {fData(file.size)}
          <Box
            sx={{
              mx: 0.75,
              width: 2,
              height: 2,
              borderRadius: '50%',
              bgcolor: 'currentColor',
            }}
          />
          {fDateTime(file.dateModified)}
        </>
      }
      primaryTypographyProps={{ noWrap: true, typography: 'subtitle2' }}
      secondaryTypographyProps={{
        mt: 0.5,
        component: 'span',
        alignItems: 'center',
        typography: 'caption',
        color: 'text.disabled',
        display: 'inline-flex',
      }}
    />
  );

  const handleDownload = useCallback(async () => {
    try {
      // Create the download URL
      const downloadUrl = `${endpoints.plt1.downloadFile}/${file.id}`;

      // Use axios to get the file with proper authentication headers
      const response = await axiosInstance.get(downloadUrl, {
        responseType: 'blob',
      });

      // Create a blob URL from the response data
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);

      // Create a temporary anchor element
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', file.name);

      // Append to the document
      document.body.appendChild(link);

      // Trigger the download
      link.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);

      toast.success(t('fileManager.fileItem.downloadSuccess'));
    } catch (error) {
      console.error('Error downloading file:', error);
      toast.error(t('fileManager.fileItem.downloadError'));
    }
  }, [file.id, file.name, t]);

  const renderMenuActions = () => (
    <CustomPopover
      open={menuActions.open}
      anchorEl={menuActions.anchorEl}
      onClose={menuActions.onClose}
      slotProps={{ arrow: { placement: 'right-top' } }}
    >
      <MenuList>
        <MenuItem
          onClick={() => {
            menuActions.onClose();
            handleDownload();
          }}
        >
          <Iconify icon="eva:download-fill" />
          {t('fileManager.fileItem.download')}
        </MenuItem>

        <Divider sx={{ borderStyle: 'dashed' }} />

        <MenuItem
          onClick={() => {
            menuActions.onClose();
            onDelete();
          }}
          sx={{ color: 'error.main' }}
        >
          <Iconify icon="solar:trash-bin-trash-bold" />
          {t('fileManager.fileItem.delete')}
        </MenuItem>
      </MenuList>
    </CustomPopover>
  );

  return (
    <>
      <Paper
        variant="outlined"
        sx={[
          (theme) => ({
            gap: 2,
            borderRadius: 2,
            display: 'flex',
            cursor: 'pointer',
            position: 'relative',
            bgcolor: 'transparent',
            p: { xs: 2.5, sm: 2 },
            alignItems: { xs: 'unset', sm: 'center' },
            flexDirection: { xs: 'column', sm: 'row' },
            '&:hover': { bgcolor: 'background.paper', boxShadow: theme.vars.customShadows.z20 },
          }),
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
        {...other}
      >
        <FileThumbnail file={file.type} />

        {renderText()}
        {renderActions()}
      </Paper>

      {renderMenuActions()}
    </>
  );
}
