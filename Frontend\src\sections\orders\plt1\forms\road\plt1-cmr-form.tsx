// src/sections/plt1-road-order-details/forms/plt1-cmr-form.tsx
import { useFormContext } from 'react-hook-form';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Divider from '@mui/material/Divider';
import CardHeader from '@mui/material/CardHeader';

import { Field } from 'src/components/hook-form';
import { useTranslate } from 'src/locales';
import { PartyAddressData } from 'src/components/party-address/party-address-form';
import PartyAddressDialog from 'src/components/party-address/party-address-dialog';
import { usePartyAddressForm } from 'src/components/party-address/hooks/usePartyAddressForm';
import { PartyType } from 'src/types/parties';
import PartyFormSection from 'src/components/party-address/party-form-section';

// ----------------------------------------------------------------------

export interface PLT1CMRData {
  id?: string;
  cmrNumber: string;
  vehicleNumber: string;
  grossWeight: number;
  grossWeightUnit: string;
  numberOfPieces: number;
  volume: number;
  shipper: PartyAddressData;
  consignee: PartyAddressData;
  t1OrderId: string;
}

// ----------------------------------------------------------------------

interface PLT1CMRFormProps {
  formPath: string;
  index?: number;
  readOnly?: boolean;
}

export default function PLT1CMRForm({
  formPath,
  index,
  readOnly = false,
}: PLT1CMRFormProps) {
  const { t } = useTranslate();
  const { watch } = useFormContext();

  // If index is provided, this form is part of a field array
  const fieldPrefix = index !== undefined ? `${formPath}[${index}]` : formPath;

  const {
    openPartyDialog,
    currentPartyType,
    handleOpenPartyDialog,
    handleClosePartyDialog,
    handleUpdateParty,
  } = usePartyAddressForm({ fieldPrefix });

  // Watch shipper and consignee to display their info
  const shipper = watch(`${fieldPrefix}.shipper`);
  const consignee = watch(`${fieldPrefix}.consignee`);

  const renderMainInfo = () => (
    <Card sx={{ boxShadow: 'none' }}>
      <CardHeader
        title={t('plt1.details.documents.cmr.form.mainInfoTitle')}
        sx={{ mb: 2 }}
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Field.Text
          name={`${fieldPrefix}.cmrNumber`}
          label={t('plt1.details.documents.cmr.form.cmrNumber')}
          disabled={readOnly}
        />

        <Field.Text
          name={`${fieldPrefix}.vehicleNumber`}
          label={t('plt1.details.documents.cmr.form.vehicleNumber')}
          disabled={readOnly}
        />
      </Stack>
    </Card>
  );

  const renderShipmentInfo = () => (
    <Card sx={{ mt: 3, boxShadow: 'none' }}>
      <CardHeader
        title={t('plt1.details.documents.cmr.form.shipmentInfoTitle')}
        sx={{ mb: 2 }}
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Box
          sx={{
            rowGap: 3,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(3, 1fr)' },
          }}
        >
          <Field.Text
            name={`${fieldPrefix}.numberOfPieces`}
            label={t('plt1.details.documents.cmr.form.numberOfPieces')}
            type="number"
            disabled={readOnly}
          />
          <Field.Text
            name={`${fieldPrefix}.grossWeight`}
            label={t('plt1.details.documents.cmr.form.grossWeight')}
            type="number"
            disabled={readOnly}
          />

          <Field.Text
            name={`${fieldPrefix}.grossWeightUnit`}
            label={t('plt1.details.documents.cmr.form.grossWeightUnit')}
            disabled={readOnly}
          />
        </Box>

        <Field.Text
          name={`${fieldPrefix}.volume`}
          label={t('plt1.details.documents.cmr.form.volume')}
          type="number"
          disabled={readOnly}
        />
      </Stack>
    </Card>
  );

  const renderParties = () => (
    <Card sx={{ mt: 3, boxShadow: 'none' }}>
      <CardHeader
        title={t('plt1.details.documents.cmr.form.partiesTitle')}
        sx={{ mb: 2 }}
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Box
          sx={{
            rowGap: 3,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' },
          }}
        >
          <PartyFormSection
            partyType={PartyType.SHIPPER}
            labelKey="plt1.details.documents.cmr.form.shipper"
            party={shipper}
            onOpenPartyDialog={handleOpenPartyDialog}
            readOnly={readOnly}
          />
          <PartyFormSection
            partyType={PartyType.CONSIGNEE}
            labelKey="plt1.details.documents.cmr.form.consignee"
            party={consignee}
            onOpenPartyDialog={handleOpenPartyDialog}
            readOnly={readOnly}
          />
        </Box>
      </Stack>
    </Card>
  );

  const renderPartyDialog = () => (
    <PartyAddressDialog
      open={openPartyDialog}
      onClose={handleClosePartyDialog}
      onSave={handleUpdateParty}
      formPath={fieldPrefix}
      currentPartyType={currentPartyType}
      readOnly={readOnly}
      titlePrefix="plt1.details.documents.cmr.partyAddress"
    />
  );

  return (
    <>
      <Stack spacing={3}>
        {renderMainInfo()}
        {renderShipmentInfo()}
        {renderParties()}
      </Stack>

      {renderPartyDialog()}
    </>
  );
}