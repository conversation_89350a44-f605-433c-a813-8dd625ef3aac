"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx":
/*!*******************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1PackingListForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/Grid2 */ \"(app-pages-browser)/./node_modules/@mui/material/Grid2/Grid2.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/custom-dialog */ \"(app-pages-browser)/./src/components/custom-dialog/index.ts\");\n/* harmony import */ var src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/components/party-address/hooks/usePartyAddressForm */ \"(app-pages-browser)/./src/components/party-address/hooks/usePartyAddressForm.tsx\");\n/* harmony import */ var src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/components/party-address/party-address-dialog */ \"(app-pages-browser)/./src/components/party-address/party-address-dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst DEFAULT_PACKED_ITEM = {\n    id: '',\n    name: '',\n    hsCode: '',\n    modelNumber: '',\n    quantity: 0,\n    itemNetWeight: 0,\n    itemNetWeightUnit: 'KGM',\n    itemGrossWeight: 0,\n    hsCodeHints: '',\n    packingListPositionId: ''\n};\nconst DEFAULT_POSITION = {\n    id: '',\n    positionNumber: 1,\n    commercialInvoiceNumber: '',\n    packageNetWeight: 0,\n    packageNetWeightUnit: 'KGM',\n    packageGrossWeight: 0,\n    packageGrossWeightUnit: 'KGM',\n    packagesNetWeight: 0,\n    packagesNetWeightUnit: 'KGM',\n    packagesGrossWeight: 0,\n    packagesGrossWeightUnit: 'KGM',\n    packageUnit: 'CTNS',\n    packageSize: '',\n    packageVolume: 0,\n    packageVolumeUnit: 'CBM',\n    numberOfPackages: 0,\n    packedItems: [],\n    packingListId: ''\n};\nconst DEFAULT_TOTAL = {\n    id: '',\n    shipmentOrPackingId: '',\n    totalQuantity: 0,\n    totalPackagesUnit: 'CTNS',\n    totalNumberOfPackages: 0,\n    totalNumberOfPallets: 0,\n    totalNetWeight: 0,\n    totalNetWeightUnit: 'KGM',\n    totalGrossWeight: 0,\n    totalGrossWeightUnit: 'KGM',\n    totalVolume: 0,\n    totalVolumeMeasurementUnit: 'CBM',\n    packingListId: ''\n};\nfunction PLT1PackingListForm(param) {\n    let { formPath, index, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    const { control, getValues, watch, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext)();\n    const fieldPrefix = index !== undefined ? \"\".concat(formPath, \".\").concat(index) : formPath;\n    const positionsFieldName = \"\".concat(fieldPrefix, \".packingListPositions\");\n    const totalFieldName = \"\".concat(fieldPrefix, \".listTotal\");\n    // Use the party address form hook\n    const { openPartyDialog, currentPartyType, handleClosePartyDialog, handleUpdateParty } = (0,src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm)({\n        fieldPrefix\n    });\n    // State for position dialog\n    const [openPositionDialog, setOpenPositionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPositionIndex, setCurrentPositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletePositionIndex, setDeletePositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize listTotal if it doesn't exist\n    const listTotal = watch(totalFieldName);\n    if (!listTotal) {\n        setValue(totalFieldName, DEFAULT_TOTAL);\n    }\n    // UseFieldArray hook to manage the positions array\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray)({\n        control,\n        name: positionsFieldName\n    });\n    // Handle opening the position dialog for adding a new position\n    const handleAddPosition = ()=>{\n        setCurrentPositionIndex(null);\n        // Add a new position with default values\n        const newPosition = {\n            ...DEFAULT_POSITION,\n            positionNumber: fields.length + 1\n        };\n        append(newPosition);\n        setCurrentPositionIndex(fields.length); // Set to the new index\n        setOpenPositionDialog(true);\n    };\n    // Handle opening the position dialog for editing an existing position\n    const handleEditPosition = (index)=>{\n        setCurrentPositionIndex(index);\n        setOpenPositionDialog(true);\n    };\n    // Handle closing the position dialog\n    const handleClosePositionDialog = ()=>{\n        setOpenPositionDialog(false);\n        // If we were adding a new position and user cancels, remove the empty position\n        if (currentPositionIndex !== null && currentPositionIndex === fields.length - 1) {\n            const position = getValues(\"\".concat(positionsFieldName, \".\").concat(currentPositionIndex));\n            // Check if it's an empty position (all fields are default values)\n            const isEmpty = !position.commercialInvoiceNumber && position.numberOfPackages === 0 && position.packageNetWeight === 0 && position.packageGrossWeight === 0 && position.packageVolume === 0 && (!position.packedItems || position.packedItems.length === 0);\n            if (isEmpty) {\n                remove(currentPositionIndex);\n            }\n        }\n        setCurrentPositionIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeletePositionIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deletePositionIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting a position\n    const handleDeletePosition = ()=>{\n        if (deletePositionIndex !== null) {\n            remove(deletePositionIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving a position (just close the dialog since form is already updated)\n    const handleSavePosition = ()=>{\n        handleClosePositionDialog();\n    };\n    // Render the position form in the dialog\n    const renderPositionForm = ()=>{\n        if (currentPositionIndex === null) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    container: true,\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: {\n                                xs: 12,\n                                sm: 6\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".positionNumber\"),\n                                label: t('plt1.details.documents.packingList.position.positionNumber'),\n                                type: \"number\",\n                                disabled: readOnly\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: {\n                                xs: 12,\n                                sm: 6\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".commercialInvoiceNumber\"),\n                                label: t('plt1.details.documents.packingList.position.commercialInvoiceNumber'),\n                                disabled: readOnly\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    container: true,\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: {\n                                xs: 12,\n                                sm: 6\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".numberOfPackages\"),\n                                label: t('plt1.details.documents.packingList.position.numberOfPackages'),\n                                type: \"number\",\n                                disabled: readOnly\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: {\n                                xs: 12,\n                                sm: 6\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageUnit\"),\n                                label: t('plt1.details.documents.packingList.position.packageUnit'),\n                                disabled: readOnly\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    container: true,\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: {\n                                xs: 12,\n                                sm: 6\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageSize\"),\n                                label: t('plt1.details.documents.packingList.position.packageSize'),\n                                disabled: readOnly\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: {\n                                xs: 12,\n                                sm: 6\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageVolume\"),\n                                label: t('plt1.details.documents.packingList.position.packageVolume'),\n                                type: \"number\",\n                                disabled: readOnly\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    container: true,\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: {\n                                xs: 12,\n                                sm: 6\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageNetWeight\"),\n                                label: t('plt1.details.documents.packingList.position.packageNetWeight'),\n                                type: \"number\",\n                                disabled: readOnly\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: {\n                                xs: 12,\n                                sm: 6\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageGrossWeight\"),\n                                label: t('plt1.details.documents.packingList.position.packageGrossWeight'),\n                                type: \"number\",\n                                disabled: readOnly\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    container: true,\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: {\n                                xs: 12,\n                                sm: 6\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packagesNetWeight\"),\n                                label: t('plt1.details.documents.packingList.position.packagesNetWeight'),\n                                type: \"number\",\n                                disabled: readOnly\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: {\n                                xs: 12,\n                                sm: 6\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packagesGrossWeight\"),\n                                label: t('plt1.details.documents.packingList.position.packagesGrossWeight'),\n                                type: \"number\",\n                                disabled: readOnly\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 247,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the positions table\n    const renderPositionsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            component: _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"packing list positions table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.positionNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.commercialInvoiceNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.numberOfPackages')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.packageNetWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.packageGrossWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        children: fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                colSpan: 6,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.packingList.position.noPositions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 13\n                        }, this) : fields.map((field, index)=>{\n                            const position = getValues(\"\".concat(positionsFieldName, \".\").concat(index)) || {};\n                            var _position_numberOfPackages, _position_packageNetWeight, _position_packageGrossWeight;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: position.positionNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: position.commercialInvoiceNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: (_position_numberOfPackages = position.numberOfPackages) !== null && _position_numberOfPackages !== void 0 ? _position_numberOfPackages : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: [\n                                            (_position_packageNetWeight = position.packageNetWeight) !== null && _position_packageNetWeight !== void 0 ? _position_packageNetWeight : '-',\n                                            \" \",\n                                            position.packageNetWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: [\n                                            (_position_packageGrossWeight = position.packageGrossWeight) !== null && _position_packageGrossWeight !== void 0 ? _position_packageGrossWeight : '-',\n                                            \" \",\n                                            position.packageGrossWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditPosition(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deletePositionIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 346,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 345,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        spacing: 3,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        title: t('plt1.details.documents.packingList.form.positionsTitle'),\n                        action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            variant: \"contained\",\n                            size: \"small\",\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                icon: \"eva:plus-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: handleAddPosition,\n                            sx: {\n                                mb: 2\n                            },\n                            disabled: readOnly,\n                            children: t('plt1.details.documents.packingList.position.addNew')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: [\n                            renderPositionsTable(),\n                            fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                sx: {\n                                    mt: 2,\n                                    display: 'flex',\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    variant: \"soft\",\n                                    color: \"primary\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                        icon: \"eva:plus-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    onClick: handleAddPosition,\n                                    disabled: readOnly,\n                                    children: t('plt1.details.documents.packingList.position.addNew')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 434,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 417,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        title: t('plt1.details.documents.packingList.form.totalsTitle')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalQuantity\"),\n                                        label: t('plt1.details.documents.packingList.total.totalQuantity'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPackages\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPackages'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalPackagesUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalPackagesUnit'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolume\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolume'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 454,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                open: openPositionDialog,\n                onClose: handleClosePositionDialog,\n                fullWidth: true,\n                maxWidth: \"md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        children: currentPositionIndex === null ? t('plt1.details.documents.packingList.position.addNew') : t('plt1.details.documents.packingList.position.edit')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            sx: {\n                                pt: 3\n                            },\n                            children: renderPositionForm()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                onClick: handleClosePositionDialog,\n                                color: \"inherit\",\n                                children: t('common.cancel')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                onClick: handleSavePosition,\n                                variant: \"contained\",\n                                children: t('common.save')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 511,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__.ConfirmDialog, {\n                open: openDeleteConfirm,\n                onClose: handleCloseDeleteConfirm,\n                title: t('plt1.details.documents.packingList.position.confirmDeleteDialog.title'),\n                content: t('plt1.details.documents.packingList.position.confirmDeleteDialog.content'),\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                    variant: \"contained\",\n                    color: \"error\",\n                    onClick: handleDeletePosition,\n                    children: t('common.delete')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 537,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                open: openPartyDialog,\n                onClose: handleClosePartyDialog,\n                onSave: handleUpdateParty,\n                formPath: fieldPrefix,\n                currentPartyType: currentPartyType,\n                readOnly: readOnly,\n                titlePrefix: \"plt1.details.documents.packingList.partyAddress\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 544,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n        lineNumber: 415,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1PackingListForm, \"/ZYd5ePfnMXCacahZCRj+uzPUOE=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext,\n        src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray\n    ];\n});\n_c = PLT1PackingListForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1PackingListForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx\n"));

/***/ })

});