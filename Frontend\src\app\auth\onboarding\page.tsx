import type { Metadata } from 'next';

import { CONFIG } from 'src/global-config';

import { getServerTranslations } from 'src/locales/server';
import OnboardingView from 'src/onboarding/views/onboarding-view';

// ----------------------------------------------------------------------

export async function generateMetadata(): Promise<Metadata> {
  const { t } = await getServerTranslations();
  return {
    title: `${t('onboarding.pageTitle')} - ${CONFIG.appName}`
  };
}

export default function Page() {
  return <OnboardingView />;
}
