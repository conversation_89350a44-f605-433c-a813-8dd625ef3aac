"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx":
/*!*******************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1PackingListForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/material/Grid2 */ \"(app-pages-browser)/./node_modules/@mui/material/Grid2/Grid2.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/custom-dialog */ \"(app-pages-browser)/./src/components/custom-dialog/index.ts\");\n/* harmony import */ var src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/components/party-address/hooks/usePartyAddressForm */ \"(app-pages-browser)/./src/components/party-address/hooks/usePartyAddressForm.tsx\");\n/* harmony import */ var src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/components/party-address/party-address-dialog */ \"(app-pages-browser)/./src/components/party-address/party-address-dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst DEFAULT_PACKED_ITEM = {\n    id: '',\n    name: '',\n    hsCode: '',\n    modelNumber: '',\n    quantity: 0,\n    itemNetWeight: 0,\n    itemNetWeightUnit: 'KGM',\n    itemGrossWeight: 0,\n    hsCodeHints: '',\n    packingListPositionId: ''\n};\nconst DEFAULT_POSITION = {\n    id: '',\n    positionNumber: 1,\n    commercialInvoiceNumber: '',\n    packageNetWeight: 0,\n    packageNetWeightUnit: 'KGM',\n    packageGrossWeight: 0,\n    packageGrossWeightUnit: 'KGM',\n    packagesNetWeight: 0,\n    packagesNetWeightUnit: 'KGM',\n    packagesGrossWeight: 0,\n    packagesGrossWeightUnit: 'KGM',\n    packageUnit: 'CTNS',\n    packageSize: '',\n    packageVolume: 0,\n    packageVolumeUnit: 'CBM',\n    numberOfPackages: 0,\n    packedItems: [],\n    packingListId: ''\n};\nconst DEFAULT_TOTAL = {\n    id: '',\n    shipmentOrPackingId: '',\n    totalQuantity: 0,\n    totalPackagesUnit: 'CTNS',\n    totalNumberOfPackages: 0,\n    totalNumberOfPallets: 0,\n    totalNetWeight: 0,\n    totalNetWeightUnit: 'KGM',\n    totalGrossWeight: 0,\n    totalGrossWeightUnit: 'KGM',\n    totalVolume: 0,\n    totalVolumeMeasurementUnit: 'CBM',\n    packingListId: ''\n};\nfunction PLT1PackingListForm(param) {\n    let { formPath, index, readOnly = false } = param;\n    _s();\n    var _s1 = $RefreshSig$();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    const { control, getValues, watch, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext)();\n    const fieldPrefix = index !== undefined ? \"\".concat(formPath, \".\").concat(index) : formPath;\n    const positionsFieldName = \"\".concat(fieldPrefix, \".packingListPositions\");\n    const totalFieldName = \"\".concat(fieldPrefix, \".listTotal\");\n    // Use the party address form hook\n    const { openPartyDialog, currentPartyType, handleClosePartyDialog, handleUpdateParty } = (0,src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm)({\n        fieldPrefix\n    });\n    // State for position dialog\n    const [openPositionDialog, setOpenPositionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPositionIndex, setCurrentPositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletePositionIndex, setDeletePositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize listTotal if it doesn't exist\n    const listTotal = watch(totalFieldName);\n    if (!listTotal) {\n        setValue(totalFieldName, DEFAULT_TOTAL);\n    }\n    // UseFieldArray hook to manage the positions array\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray)({\n        control,\n        name: positionsFieldName\n    });\n    // Handle opening the position dialog for adding a new position\n    const handleAddPosition = ()=>{\n        setCurrentPositionIndex(null);\n        // Add a new position with default values\n        const newPosition = {\n            ...DEFAULT_POSITION,\n            positionNumber: fields.length + 1\n        };\n        append(newPosition);\n        setCurrentPositionIndex(fields.length); // Set to the new index\n        setOpenPositionDialog(true);\n    };\n    // Handle opening the position dialog for editing an existing position\n    const handleEditPosition = (index)=>{\n        setCurrentPositionIndex(index);\n        setOpenPositionDialog(true);\n    };\n    // Handle closing the position dialog\n    const handleClosePositionDialog = ()=>{\n        setOpenPositionDialog(false);\n        // If we were adding a new position and user cancels, remove the empty position\n        if (currentPositionIndex !== null && currentPositionIndex === fields.length - 1) {\n            const position = getValues(\"\".concat(positionsFieldName, \".\").concat(currentPositionIndex));\n            // Check if it's an empty position (all fields are default values)\n            const isEmpty = !position.commercialInvoiceNumber && position.numberOfPackages === 0 && position.packageNetWeight === 0 && position.packageGrossWeight === 0 && position.packageVolume === 0 && (!position.packedItems || position.packedItems.length === 0);\n            if (isEmpty) {\n                remove(currentPositionIndex);\n            }\n        }\n        setCurrentPositionIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeletePositionIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deletePositionIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting a position\n    const handleDeletePosition = ()=>{\n        if (deletePositionIndex !== null) {\n            remove(deletePositionIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving a position (just close the dialog since form is already updated)\n    const handleSavePosition = ()=>{\n        handleClosePositionDialog();\n    };\n    // Render the position form in the dialog\n    const renderPositionForm = ()=>{\n        _s1();\n        if (currentPositionIndex === null) return null;\n        const packedItemsFieldName = \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packedItems\");\n        // UseFieldArray for packed items within this position\n        const packedItemsFieldArray = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray)({\n            control,\n            name: packedItemsFieldName\n        });\n        // Handle adding a new packed item\n        const handleAddPackedItem = ()=>{\n            const newPackedItem = {\n                ...DEFAULT_PACKED_ITEM\n            };\n            packedItemsFieldArray.append(newPackedItem);\n        };\n        // Handle removing a packed item\n        const handleRemovePackedItem = (itemIndex)=>{\n            packedItemsFieldArray.remove(itemIndex);\n        };\n        // Render inline editable table for packed items\n        const renderPackedItemsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        direction: \"row\",\n                        justifyContent: \"space-between\",\n                        alignItems: \"center\",\n                        sx: {\n                            mb: 2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                variant: \"h6\",\n                                children: t('plt1.details.documents.packingList.packedItem.title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                variant: \"outlined\",\n                                size: \"small\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                    icon: \"eva:plus-fill\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 24\n                                }, void 0),\n                                onClick: handleAddPackedItem,\n                                disabled: readOnly,\n                                children: t('plt1.details.documents.packingList.packedItem.addNew')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        component: _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                        sx: {\n                            maxHeight: 400\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            size: \"small\",\n                            stickyHeader: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.name')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.hsCode')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.modelNumber')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.quantity')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                children: t('plt1.details.documents.packingList.packedItem.itemNetWeight')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                align: \"right\",\n                                                children: t('common.actions')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    children: packedItemsFieldArray.fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            colSpan: 6,\n                                            align: \"center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"body2\",\n                                                color: \"text.secondary\",\n                                                children: t('plt1.details.documents.packingList.packedItem.noItems')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this) : packedItemsFieldArray.fields.map((field, itemIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 150\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".name\"),\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 120\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".hsCode\"),\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 120\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".modelNumber\"),\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 100\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".quantity\"),\n                                                        type: \"number\",\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        minWidth: 120\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                                        name: \"\".concat(packedItemsFieldName, \".\").concat(itemIndex, \".itemNetWeight\"),\n                                                        type: \"number\",\n                                                        size: \"small\",\n                                                        disabled: readOnly,\n                                                        sx: {\n                                                            '& .MuiInputBase-root': {\n                                                                fontSize: '0.875rem'\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    align: \"right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        title: t('common.delete'),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            size: \"small\",\n                                                            color: \"error\",\n                                                            onClick: ()=>handleRemovePackedItem(itemIndex),\n                                                            disabled: readOnly,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                                icon: \"eva:trash-2-outline\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, field.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            variant: \"h6\",\n                            sx: {\n                                mb: 2\n                            },\n                            children: t('plt1.details.documents.packingList.position.details')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".positionNumber\"),\n                                        label: t('plt1.details.documents.packingList.position.positionNumber'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".commercialInvoiceNumber\"),\n                                        label: t('plt1.details.documents.packingList.position.commercialInvoiceNumber'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".numberOfPackages\"),\n                                        label: t('plt1.details.documents.packingList.position.numberOfPackages'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageUnit\"),\n                                        label: t('plt1.details.documents.packingList.position.packageUnit'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageSize\"),\n                                        label: t('plt1.details.documents.packingList.position.packageSize'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageVolume\"),\n                                        label: t('plt1.details.documents.packingList.position.packageVolume'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.position.packageNetWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packageGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.position.packageGrossWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                mt: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packagesNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.position.packagesNetWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(positionsFieldName, \".\").concat(currentPositionIndex, \".packagesGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.position.packagesGrossWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 471,\n                    columnNumber: 9\n                }, this),\n                renderPackedItemsTable()\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 371,\n            columnNumber: 7\n        }, this);\n    };\n    _s1(renderPositionForm, \"8WfwmBuHpVjZ/CssRyW/285YVws=\", false, function() {\n        return [\n            react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray\n        ];\n    });\n    // Render the positions table\n    const renderPositionsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            component: _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"packing list positions table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.positionNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.commercialInvoiceNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.numberOfPackages')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.packageNetWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.position.packageGrossWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        children: fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                colSpan: 6,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.packingList.position.noPositions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 13\n                        }, this) : fields.map((field, index)=>{\n                            const position = getValues(\"\".concat(positionsFieldName, \".\").concat(index)) || {};\n                            var _position_numberOfPackages, _position_packageNetWeight, _position_packageGrossWeight;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: position.positionNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: position.commercialInvoiceNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: (_position_numberOfPackages = position.numberOfPackages) !== null && _position_numberOfPackages !== void 0 ? _position_numberOfPackages : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: [\n                                            (_position_packageNetWeight = position.packageNetWeight) !== null && _position_packageNetWeight !== void 0 ? _position_packageNetWeight : '-',\n                                            \" \",\n                                            position.packageNetWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: [\n                                            (_position_packageGrossWeight = position.packageGrossWeight) !== null && _position_packageGrossWeight !== void 0 ? _position_packageGrossWeight : '-',\n                                            \" \",\n                                            position.packageGrossWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditPosition(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deletePositionIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 482,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 481,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        spacing: 3,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        title: t('plt1.details.documents.packingList.form.positionsTitle'),\n                        action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            variant: \"contained\",\n                            size: \"small\",\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                icon: \"eva:plus-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 560,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: handleAddPosition,\n                            sx: {\n                                mb: 2\n                            },\n                            disabled: readOnly,\n                            children: t('plt1.details.documents.packingList.position.addNew')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 554,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 569,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: [\n                            renderPositionsTable(),\n                            fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    mt: 2,\n                                    display: 'flex',\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    variant: \"soft\",\n                                    color: \"primary\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                        icon: \"eva:plus-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    onClick: handleAddPosition,\n                                    disabled: readOnly,\n                                    children: t('plt1.details.documents.packingList.position.addNew')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 553,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        title: t('plt1.details.documents.packingList.form.totalsTitle')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 592,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalQuantity\"),\n                                        label: t('plt1.details.documents.packingList.total.totalQuantity'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPackages\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPackages'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalPackagesUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalPackagesUnit'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeight'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolume\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolume'),\n                                        type: \"number\",\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 590,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                open: openPositionDialog,\n                onClose: handleClosePositionDialog,\n                fullWidth: true,\n                maxWidth: \"lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        children: currentPositionIndex === null ? t('plt1.details.documents.packingList.position.addNew') : t('plt1.details.documents.packingList.position.edit')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 648,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            sx: {\n                                pt: 3\n                            },\n                            children: renderPositionForm()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 654,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 653,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                onClick: handleClosePositionDialog,\n                                color: \"inherit\",\n                                children: t('common.cancel')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 657,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                onClick: handleSavePosition,\n                                variant: \"contained\",\n                                children: t('common.save')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 656,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 647,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__.ConfirmDialog, {\n                open: openDeleteConfirm,\n                onClose: handleCloseDeleteConfirm,\n                title: t('plt1.details.documents.packingList.position.confirmDeleteDialog.title'),\n                content: t('plt1.details.documents.packingList.position.confirmDeleteDialog.content'),\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    variant: \"contained\",\n                    color: \"error\",\n                    onClick: handleDeletePosition,\n                    children: t('common.delete')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 673,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 667,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                open: openPartyDialog,\n                onClose: handleClosePartyDialog,\n                onSave: handleUpdateParty,\n                formPath: fieldPrefix,\n                currentPartyType: currentPartyType,\n                readOnly: readOnly,\n                titlePrefix: \"plt1.details.documents.packingList.partyAddress\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 680,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n        lineNumber: 551,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1PackingListForm, \"/ZYd5ePfnMXCacahZCRj+uzPUOE=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext,\n        src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray\n    ];\n});\n_c = PLT1PackingListForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1PackingListForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx\n"));

/***/ })

});