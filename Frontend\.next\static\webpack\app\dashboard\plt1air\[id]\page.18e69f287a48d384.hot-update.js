"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/details/plt1-order-details-base.tsx":
/*!**********************************************************************!*\
  !*** ./src/sections/orders/plt1/details/plt1-order-details-base.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLT1OrderDetailsBase: () => (/* binding */ PLT1OrderDetailsBase)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/Box */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/material/Card */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/material/Stack */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material_Button__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/material/Button */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/material/Divider */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _mui_material_Container__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/Container */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/material/Typography */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _mui_material_CircularProgress__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/material/CircularProgress */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _mui_material_Grid2__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/material/Grid2 */ \"(app-pages-browser)/./node_modules/@mui/material/Grid2/Grid2.js\");\n/* harmony import */ var _mui_lab_LoadingButton__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/lab/LoadingButton */ \"(app-pages-browser)/./node_modules/@mui/lab/LoadingButton/LoadingButton.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/material/IconButton */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/material/Tooltip */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var src_routes_paths__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/routes/paths */ \"(app-pages-browser)/./src/routes/paths.ts\");\n/* harmony import */ var src_routes_hooks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/routes/hooks */ \"(app-pages-browser)/./src/routes/hooks/index.ts\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_layouts_dashboard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/layouts/dashboard */ \"(app-pages-browser)/./src/layouts/dashboard/index.ts\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_layouts_components_app_data_grid_components_enum_tag_EnumTag__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/layouts/components/app-data-grid/components/enum-tag/EnumTag */ \"(app-pages-browser)/./src/layouts/components/app-data-grid/components/enum-tag/EnumTag.tsx\");\n/* harmony import */ var src_lib_formatters__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! src/lib/formatters */ \"(app-pages-browser)/./src/lib/formatters.ts\");\n/* harmony import */ var _components_plt1_file_management__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/plt1-file-management */ \"(app-pages-browser)/./src/sections/orders/plt1/components/plt1-file-management.tsx\");\n/* harmony import */ var _components_plt1_file_content_preview__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/plt1-file-content-preview */ \"(app-pages-browser)/./src/sections/orders/plt1/components/plt1-file-content-preview.tsx\");\n/* harmony import */ var _components_plt1_file_list_sidebar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/plt1-file-list-sidebar */ \"(app-pages-browser)/./src/sections/orders/plt1/components/plt1-file-list-sidebar.tsx\");\n/* harmony import */ var _components_plt1_file_cache__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../components/plt1-file-cache */ \"(app-pages-browser)/./src/sections/orders/plt1/components/plt1-file-cache.ts\");\n/* harmony import */ var _plt1_status__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../plt1-status */ \"(app-pages-browser)/./src/sections/orders/plt1/plt1-status.tsx\");\n/* harmony import */ var src_sections_plt1_other_data_tabs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! src/sections/plt1-other-data-tabs */ \"(app-pages-browser)/./src/sections/plt1-other-data-tabs.tsx\");\n/* harmony import */ var src_lib_axios__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! src/lib/axios */ \"(app-pages-browser)/./src/lib/axios.ts\");\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! swr */ \"(app-pages-browser)/./node_modules/swr/dist/index/index.mjs\");\n/* harmony import */ var _plt1_order_type__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../plt1-order-type */ \"(app-pages-browser)/./src/sections/orders/plt1/plt1-order-type.tsx\");\n// src/sections/plt1-order-details-base/PLT1OrderDetailsBase.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction PLT1OrderDetailsBase(param) {\n    let { orderId, readOnly: propReadOnly = false, isLoading, error, order, orderStatus, orderNumber, statusError, onSaveOrder, documentTabs, formChanged, isSaving, onCancel, isValid, downloadEndpoint, downloadFileName, showDownloadButton = false, sadecTxtEndpoint, sadecTxtFileName, showSadecTxtButton = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_4__.useTranslate)();\n    const router = (0,src_routes_hooks__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleBack = ()=>{\n        router.push(src_routes_paths__WEBPACK_IMPORTED_MODULE_2__.paths.dashboard.plt1.list);\n    };\n    // State management\n    const [splitViewActive, setSplitViewActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orderFiles, setOrderFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileListRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const methods = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useFormContext)();\n    // Determine if the form should be read-only\n    // Form is read-only if explicitly set to read-only or if the order status is \"Scanning\" or \"MerchandisePositionGeneration\"\n    const isScanning = orderStatus === _plt1_status__WEBPACK_IMPORTED_MODULE_13__.PLT1_ORDER_STATUS.Scanning;\n    const isMerchandisePositionGeneration = orderStatus === _plt1_status__WEBPACK_IMPORTED_MODULE_13__.PLT1_ORDER_STATUS.MerchandisePositionGeneration;\n    const readOnly = propReadOnly || isScanning || isMerchandisePositionGeneration;\n    // Handle save order\n    const handleSaveOrderSubmit = async (formData)=>{\n        await onSaveOrder(formData);\n    };\n    // Handle download XLSX\n    const handleDownloadXlsx = async ()=>{\n        if (!downloadEndpoint || !orderId || isDownloading) return;\n        setIsDownloading(true);\n        try {\n            const response = await src_lib_axios__WEBPACK_IMPORTED_MODULE_15__.axiosInstance.get(\"\".concat(downloadEndpoint, \"/\").concat(orderId, \"/download/xlsx\"), {\n                responseType: 'blob'\n            });\n            // Create blob URL and trigger download\n            const blob = new Blob([\n                response.data\n            ], {\n                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = downloadFileName || \"plt1-order-\".concat(orderId, \".xlsx\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n        } catch (error) {\n            console.error('Error downloading XLSX file:', error);\n        // You might want to show a toast error here\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    // State to track if files are being prefetched\n    const [isPrefetching, setIsPrefetching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for download functionality\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // SWR fetcher function\n    const filesFetcher = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PLT1OrderDetailsBase.useCallback[filesFetcher]\": async (url)=>{\n            const response = await src_lib_axios__WEBPACK_IMPORTED_MODULE_15__.axiosInstance.get(url);\n            return response.data.data || [];\n        }\n    }[\"PLT1OrderDetailsBase.useCallback[filesFetcher]\"], []);\n    // Use SWR to fetch and refresh order files every 10 seconds\n    const { data: files, mutate: refreshFiles, isValidating } = (0,swr__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(\"\".concat(src_lib_axios__WEBPACK_IMPORTED_MODULE_15__.endpoints.plt1.filePreviews, \"/\").concat(orderId), filesFetcher, {\n        refreshInterval: 10000,\n        revalidateOnFocus: true,\n        dedupingInterval: 5000,\n        onSuccess: {\n            \"PLT1OrderDetailsBase.useSWR\": (data)=>{\n                setOrderFiles(data);\n                // Only prefetch if there are files\n                if (data && data.length > 0) {\n                    prefetchFiles(data);\n                } else {\n                    // Reset prefetching state if there are no files\n                    setIsPrefetching(false);\n                }\n            }\n        }[\"PLT1OrderDetailsBase.useSWR\"]\n    });\n    // Update isPrefetching state based on SWR validation state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            if (isValidating && !isPrefetching) {\n                setIsPrefetching(true);\n            } else if (!isValidating && isPrefetching && (!files || files.length === 0)) {\n                // Reset prefetching state when validation completes and there are no files\n                setIsPrefetching(false);\n            }\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], [\n        isValidating,\n        isPrefetching,\n        files\n    ]);\n    // Prefetch files with different priorities\n    const prefetchFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PLT1OrderDetailsBase.useCallback[prefetchFiles]\": async (files)=>{\n            // Early return with state reset if no files\n            if (!files || files.length === 0) {\n                setIsPrefetching(false);\n                return;\n            }\n            setIsPrefetching(true);\n            try {\n                // Try to get the last viewed file ID from localStorage\n                const lastViewedFileId = localStorage.getItem(\"plt1-last-viewed-file-\".concat(orderId));\n                if (lastViewedFileId) {\n                    // Find the last viewed file and prefetch it with highest priority\n                    const lastViewedFile = files.find({\n                        \"PLT1OrderDetailsBase.useCallback[prefetchFiles].lastViewedFile\": (file)=>file.id === lastViewedFileId\n                    }[\"PLT1OrderDetailsBase.useCallback[prefetchFiles].lastViewedFile\"]);\n                    if (lastViewedFile) {\n                        // Prefetch the last viewed file with highest priority (10)\n                        await _components_plt1_file_cache__WEBPACK_IMPORTED_MODULE_12__.fileCache.prefetchFile(lastViewedFile, 10);\n                    }\n                }\n                // Prefetch the first few files with high priority (5)\n                const highPriorityFiles = files.slice(0, Math.min(5, files.length));\n                if (highPriorityFiles.length > 0) {\n                    await _components_plt1_file_cache__WEBPACK_IMPORTED_MODULE_12__.fileCache.prefetchFiles(highPriorityFiles, 5);\n                }\n                // Prefetch all remaining files with normal priority (1)\n                const remainingFiles = files.slice(Math.min(5, files.length));\n                if (remainingFiles.length > 0) {\n                    await _components_plt1_file_cache__WEBPACK_IMPORTED_MODULE_12__.fileCache.prefetchFiles(remainingFiles, 1);\n                }\n            } catch (error) {\n                console.error('Error prefetching files:', error);\n            } finally{\n                setIsPrefetching(false);\n            }\n        }\n    }[\"PLT1OrderDetailsBase.useCallback[prefetchFiles]\"], [\n        orderId\n    ]);\n    // Set initial files when SWR loads them\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            if (files) {\n                setOrderFiles(files);\n            }\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], [\n        files\n    ]);\n    // Function to manually refresh files\n    const handleRefreshFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PLT1OrderDetailsBase.useCallback[handleRefreshFiles]\": ()=>{\n            refreshFiles();\n        }\n    }[\"PLT1OrderDetailsBase.useCallback[handleRefreshFiles]\"], [\n        refreshFiles\n    ]);\n    // Clean up cache when component unmounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            return ({\n                \"PLT1OrderDetailsBase.useEffect\": ()=>{\n                    _components_plt1_file_cache__WEBPACK_IMPORTED_MODULE_12__.fileCache.clearCache();\n                }\n            })[\"PLT1OrderDetailsBase.useEffect\"];\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], []);\n    // Select the first file or the last viewed file\n    const selectFirstOrLastViewedFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PLT1OrderDetailsBase.useCallback[selectFirstOrLastViewedFile]\": ()=>{\n            if (orderFiles.length === 0) return;\n            // Try to get the last viewed file ID from localStorage\n            const lastViewedFileId = localStorage.getItem(\"plt1-last-viewed-file-\".concat(orderId));\n            if (lastViewedFileId) {\n                // Find the file with the saved ID\n                const lastFile = orderFiles.find({\n                    \"PLT1OrderDetailsBase.useCallback[selectFirstOrLastViewedFile].lastFile\": (file)=>file.id === lastViewedFileId\n                }[\"PLT1OrderDetailsBase.useCallback[selectFirstOrLastViewedFile].lastFile\"]);\n                if (lastFile) {\n                    setSelectedFile(lastFile);\n                    return;\n                }\n            }\n            // If no last viewed file or it's not found, select the first file\n            setSelectedFile(orderFiles[0]);\n        }\n    }[\"PLT1OrderDetailsBase.useCallback[selectFirstOrLastViewedFile]\"], [\n        orderId,\n        orderFiles\n    ]);\n    // Select first file or last viewed file when files are loaded and no file is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            if (orderFiles.length > 0 && !selectedFile && splitViewActive) {\n                selectFirstOrLastViewedFile();\n            }\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], [\n        orderFiles,\n        selectedFile,\n        splitViewActive,\n        selectFirstOrLastViewedFile\n    ]);\n    // Focus the file list and prefetch files when the split view is activated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            if (splitViewActive) {\n                // If there are no files, close the split view\n                if (orderFiles.length === 0) {\n                    setSplitViewActive(false);\n                    setIsPrefetching(false);\n                    return;\n                }\n                // Focus the file list\n                if (fileListRef.current) {\n                    // Use a small timeout to ensure the DOM is fully rendered\n                    const timeoutId = setTimeout({\n                        \"PLT1OrderDetailsBase.useEffect.timeoutId\": ()=>{\n                            var _fileListRef_current;\n                            (_fileListRef_current = fileListRef.current) === null || _fileListRef_current === void 0 ? void 0 : _fileListRef_current.focus();\n                        }\n                    }[\"PLT1OrderDetailsBase.useEffect.timeoutId\"], 100);\n                    return ({\n                        \"PLT1OrderDetailsBase.useEffect\": ()=>clearTimeout(timeoutId)\n                    })[\"PLT1OrderDetailsBase.useEffect\"];\n                }\n                // Prefetch all files in the background\n                prefetchFiles(orderFiles);\n            }\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], [\n        splitViewActive,\n        orderFiles,\n        prefetchFiles\n    ]);\n    // Handle closing file preview split view\n    const handleCloseFilePreview = ()=>{\n        setSplitViewActive(false);\n        setSelectedFile(null);\n    };\n    // Handle keyboard shortcut (Ctrl+Q) to toggle file preview split view\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"PLT1OrderDetailsBase.useEffect.handleKeyDown\": (event)=>{\n                    // Check if Ctrl+Q is pressed\n                    if ((event.ctrlKey || event.metaKey) && event.key === 'q') {\n                        event.preventDefault(); // Prevent any default browser action\n                        // If split view is active, always allow closing it\n                        if (splitViewActive) {\n                            setSplitViewActive(false);\n                            setSelectedFile(null);\n                        } else if (orderFiles.length > 0) {\n                            setSplitViewActive(true);\n                            // If no file is selected, select the first file or last viewed file\n                            if (!selectedFile) {\n                                selectFirstOrLastViewedFile();\n                            }\n                        }\n                    }\n                }\n            }[\"PLT1OrderDetailsBase.useEffect.handleKeyDown\"];\n            // Add event listener\n            window.addEventListener('keydown', handleKeyDown);\n            // Remove event listener on cleanup\n            return ({\n                \"PLT1OrderDetailsBase.useEffect\": ()=>{\n                    window.removeEventListener('keydown', handleKeyDown);\n                }\n            })[\"PLT1OrderDetailsBase.useEffect\"];\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], [\n        splitViewActive,\n        orderFiles,\n        orderId,\n        selectedFile,\n        selectFirstOrLastViewedFile\n    ]);\n    // Handle selecting a file to preview\n    const handleSelectFile = (file)=>{\n        setSelectedFile(file);\n        setSplitViewActive(true);\n        // Save the selected file ID to localStorage for future reference\n        localStorage.setItem(\"plt1-last-viewed-file-\".concat(orderId), file.id);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_layouts_dashboard__WEBPACK_IMPORTED_MODULE_5__.DashboardContent, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Container__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    sx: {\n                        display: 'flex',\n                        justifyContent: 'center',\n                        alignItems: 'center',\n                        height: '60vh'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CircularProgress__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                lineNumber: 351,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n            lineNumber: 350,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !order) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_layouts_dashboard__WEBPACK_IMPORTED_MODULE_5__.DashboardContent, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Container__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    sx: {\n                        textAlign: 'center',\n                        py: 5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"error\",\n                            sx: {\n                                mb: 2\n                            },\n                            children: t('plt1.details.notFound')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            variant: \"contained\",\n                            onClick: handleBack,\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                icon: \"eva:arrow-back-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 26\n                            }, void 0),\n                            children: t('common.back')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                lineNumber: 365,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n            lineNumber: 364,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_layouts_dashboard__WEBPACK_IMPORTED_MODULE_5__.DashboardContent, {\n        fullWidth: splitViewActive,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Container__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            maxWidth: splitViewActive ? false : 'lg',\n            disableGutters: splitViewActive,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    sx: {\n                        mb: 3,\n                        mt: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            direction: \"row\",\n                            alignItems: \"center\",\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                    onClick: handleBack,\n                                    sx: {\n                                        border: '1px solid',\n                                        borderColor: 'divider',\n                                        borderRadius: 1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                        icon: \"eva:arrow-back-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_layouts_components_app_data_grid_components_enum_tag_EnumTag__WEBPACK_IMPORTED_MODULE_7__.EnumTag, {\n                                    value: order === null || order === void 0 ? void 0 : order.orderType,\n                                    config: _plt1_order_type__WEBPACK_IMPORTED_MODULE_16__.PLT1_ORDER_TYPE_CONFIG,\n                                    translationPrefix: \"plt1.type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    variant: \"h4\",\n                                    children: orderNumber || (order === null || order === void 0 ? void 0 : order.number) || t('plt1.details.orderDetails')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            direction: \"row\",\n                            spacing: 2,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                sx: {\n                                    display: {\n                                        xs: 'none',\n                                        sm: 'flex'\n                                    },\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    title: orderFiles.length > 0 ? isPrefetching ? t('plt1.details.filePreview.prefetchingFiles', {\n                                        defaultValue: 'Prefetching files...'\n                                    }) : t('plt1.details.filePreview.shortcutHint', {\n                                        defaultValue: 'Press Ctrl+Q to toggle preview'\n                                    }) : t('plt1.details.filePreview.noFiles', {\n                                        defaultValue: 'No files available to preview'\n                                    }),\n                                    arrow: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: isPrefetching ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            variant: \"outlined\",\n                                            color: \"primary\",\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CircularProgress__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 34\n                                            }, void 0),\n                                            disabled: true,\n                                            children: t('plt1.details.filePreview.prefetchingFiles', {\n                                                defaultValue: 'Prefetching files...'\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            variant: \"outlined\",\n                                            color: \"primary\",\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                                icon: \"eva:eye-fill\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 34\n                                            }, void 0),\n                                            onClick: ()=>{\n                                                if (splitViewActive) {\n                                                    setSplitViewActive(false);\n                                                    setSelectedFile(null);\n                                                } else if (orderFiles.length > 0) {\n                                                    setSplitViewActive(true);\n                                                    if (!selectedFile) {\n                                                        selectFirstOrLastViewedFile();\n                                                    }\n                                                }\n                                            },\n                                            disabled: !splitViewActive && orderFiles.length === 0,\n                                            children: splitViewActive ? t('plt1.details.filePreview.closePreview') : t('plt1.details.filePreview.openPreview')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 9\n                }, this),\n                (isScanning || isMerchandisePositionGeneration) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    sx: {\n                        mb: 3,\n                        p: 2,\n                        bgcolor: 'warning.lighter',\n                        borderRadius: 1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        variant: \"subtitle1\",\n                        color: \"warning.darker\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                icon: \"eva:alert-triangle-fill\",\n                                sx: {\n                                    mr: 1,\n                                    verticalAlign: 'middle'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 15\n                            }, this),\n                            isScanning ? t('plt1.details.scanningInProgress') : t('plt1.details.merchandisePositionGenerationInProgress')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                    lineNumber: 473,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: methods.handleSubmit(handleSaveOrderSubmit),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            children: [\n                                !splitViewActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            size: {\n                                                xs: 12,\n                                                md: 5\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                sx: {\n                                                    mb: 3\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    sx: {\n                                                        p: 3,\n                                                        pb: 2\n                                                    },\n                                                    spacing: 2,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            direction: \"row\",\n                                                            justifyContent: \"space-between\",\n                                                            alignItems: \"center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    variant: \"h6\",\n                                                                    children: t('plt1.details.orderInfo.heading')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_layouts_components_app_data_grid_components_enum_tag_EnumTag__WEBPACK_IMPORTED_MODULE_7__.EnumTag, {\n                                                                    value: orderStatus || (order === null || order === void 0 ? void 0 : order.status),\n                                                                    config: _plt1_status__WEBPACK_IMPORTED_MODULE_13__.PLT1_ORDER_STATUS_CONFIG,\n                                                                    translationPrefix: \"plt1.status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                            sx: {\n                                                                borderStyle: 'dashed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            spacing: 1.5,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    direction: \"row\",\n                                                                    justifyContent: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                color: 'text.secondary'\n                                                                            },\n                                                                            children: t('plt1.details.orderInfo.orderingPartyEmail')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 505,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            children: order.orderingPartyEmail\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    direction: \"row\",\n                                                                    justifyContent: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                color: 'text.secondary'\n                                                                            },\n                                                                            children: t('plt1.details.orderInfo.orderDate')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 512,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            children: order.orderDate ? src_lib_formatters__WEBPACK_IMPORTED_MODULE_8__.formatters.dateTimeOffset(order.orderDate) : '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 515,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    direction: \"row\",\n                                                                    justifyContent: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                color: 'text.secondary'\n                                                                            },\n                                                                            children: t('plt1.details.orderInfo.confirmationDate')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 521,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            children: order.confirmationDate ? src_lib_formatters__WEBPACK_IMPORTED_MODULE_8__.formatters.dateTimeOffset(order.confirmationDate) : '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 524,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    direction: \"row\",\n                                                                    justifyContent: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                color: 'text.secondary'\n                                                                            },\n                                                                            children: t('plt1.details.orderInfo.forwardedDate')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            children: order.forwardedToTheCustomsOfficeDate ? src_lib_formatters__WEBPACK_IMPORTED_MODULE_8__.formatters.dateTimeOffset(order.forwardedToTheCustomsOfficeDate) : '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 535,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    direction: \"row\",\n                                                                    justifyContent: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                color: 'text.secondary'\n                                                                            },\n                                                                            children: t('plt1.details.orderInfo.completionDate')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 543,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            children: order.completionDate ? src_lib_formatters__WEBPACK_IMPORTED_MODULE_8__.formatters.dateTimeOffset(order.completionDate) : '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 546,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        showDownloadButton && orderStatus === _plt1_status__WEBPACK_IMPORTED_MODULE_13__.PLT1_ORDER_STATUS.Scanned && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                    sx: {\n                                                                        borderStyle: 'dashed'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    sx: {\n                                                                        display: 'flex',\n                                                                        justifyContent: 'center',\n                                                                        pt: 1\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_lab_LoadingButton__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                        variant: \"contained\",\n                                                                        color: \"success\",\n                                                                        size: \"medium\",\n                                                                        loading: isDownloading,\n                                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                                                            icon: \"eva:download-fill\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 564,\n                                                                            columnNumber: 42\n                                                                        }, void 0),\n                                                                        onClick: handleDownloadXlsx,\n                                                                        disabled: isDownloading,\n                                                                        sx: {\n                                                                            minWidth: 160\n                                                                        },\n                                                                        children: t('plt1.details.downloadXlsx', {\n                                                                            defaultValue: 'Download XLSX'\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            size: {\n                                                xs: 12,\n                                                md: 7\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                spacing: 2,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_plt1_file_management__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    orderId: orderId,\n                                                    orderType: order === null || order === void 0 ? void 0 : order.orderType,\n                                                    hasPendingChanges: formChanged,\n                                                    isScanning: isScanning || isMerchandisePositionGeneration\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        md: 12\n                                    },\n                                    children: splitViewActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        sx: {\n                                            display: 'flex',\n                                            flexDirection: 'row',\n                                            height: 'calc(100vh - 150px)',\n                                            mb: 3,\n                                            mt: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                sx: {\n                                                    width: '40%',\n                                                    pr: 2,\n                                                    overflow: 'auto'\n                                                },\n                                                children: documentTabs\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                sx: {\n                                                    width: '60%',\n                                                    display: 'flex',\n                                                    flexDirection: 'column',\n                                                    border: '1px solid',\n                                                    borderColor: 'divider',\n                                                    borderRadius: 1,\n                                                    overflow: 'hidden'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        sx: {\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'space-between',\n                                                            p: 2,\n                                                            borderBottom: '1px solid',\n                                                            borderColor: 'divider'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        variant: \"h6\",\n                                                                        component: \"div\",\n                                                                        children: t('plt1.details.filePreview.title')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                        lineNumber: 631,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        variant: \"caption\",\n                                                                        color: \"text.secondary\",\n                                                                        children: t('plt1.details.filePreview.shortcutHint', {\n                                                                            defaultValue: 'Press Ctrl+Q to toggle preview'\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                        lineNumber: 634,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                direction: \"row\",\n                                                                spacing: 1,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    edge: \"end\",\n                                                                    color: \"inherit\",\n                                                                    onClick: handleCloseFilePreview,\n                                                                    \"aria-label\": \"close\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                                                        icon: \"eva:close-fill\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                        lineNumber: 647,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 641,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        sx: {\n                                                            display: 'flex',\n                                                            flexDirection: {\n                                                                xs: 'column',\n                                                                md: 'row'\n                                                            },\n                                                            flexGrow: 1,\n                                                            overflow: 'hidden'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                sx: {\n                                                                    flexGrow: 1,\n                                                                    display: 'flex',\n                                                                    alignItems: 'stretch',\n                                                                    justifyContent: 'stretch',\n                                                                    p: 0,\n                                                                    overflow: 'hidden',\n                                                                    width: '100%'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_plt1_file_content_preview__WEBPACK_IMPORTED_MODULE_10__.PLT1FileContentPreview, {\n                                                                    file: selectedFile\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                lineNumber: 662,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_plt1_file_list_sidebar__WEBPACK_IMPORTED_MODULE_11__.PLT1FileListSidebar, {\n                                                                ref: fileListRef,\n                                                                files: orderFiles,\n                                                                selectedFile: selectedFile,\n                                                                onSelectFile: handleSelectFile,\n                                                                autoFocus: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                lineNumber: 676,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            documentTabs,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                sx: {\n                                                    mt: 3\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_sections_plt1_other_data_tabs__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    readOnly: readOnly\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 485,\n                            columnNumber: 11\n                        }, this),\n                        !readOnly && formChanged && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            sx: {\n                                position: 'fixed',\n                                bottom: 0,\n                                left: 0,\n                                right: 0,\n                                py: 2,\n                                px: 3,\n                                bgcolor: 'background.paper',\n                                borderTop: (theme)=>\"1px solid \".concat(theme.palette.divider),\n                                zIndex: (theme)=>theme.zIndex.appBar - 1,\n                                boxShadow: (theme)=>theme.shadows[3],\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'flex-end'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    sx: {\n                                        mr: 2\n                                    },\n                                    children: t('plt1.details.unsavedChanges')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    direction: \"row\",\n                                    spacing: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            color: \"inherit\",\n                                            variant: \"outlined\",\n                                            onClick: onCancel,\n                                            disabled: isSaving,\n                                            children: t('common.cancel')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 719,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_lab_LoadingButton__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                            variant: \"contained\",\n                                            color: \"primary\",\n                                            loading: isSaving,\n                                            type: \"submit\",\n                                            disabled: !isValid,\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                                icon: \"eva:save-fill\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            children: t('common.save')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 718,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 698,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                    lineNumber: 484,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n            lineNumber: 385,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n        lineNumber: 384,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1OrderDetailsBase, \"mhc9dLChJASftdQEC0EDcUDgqMc=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_4__.useTranslate,\n        src_routes_hooks__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useFormContext,\n        swr__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n    ];\n});\n_c = PLT1OrderDetailsBase;\nvar _c;\n$RefreshReg$(_c, \"PLT1OrderDetailsBase\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/details/plt1-order-details-base.tsx\n"));

/***/ })

});