const ROOTS = {
  AUTH: '/auth',
  DASHBOARD: '/dashboard',
};

// ----------------------------------------------------------------------

export const paths = {
  faqs: '/faqs',
  auth: {
    auth0: {
      signIn: `${ROOTS.AUTH}/sign-in`,
      onboarding: `${ROOTS.AUTH}/onboarding`
    }
  },
  dashboard: {
    root: ROOTS.DASHBOARD,
    user: {
      root: `${ROOTS.DASHBOARD}/user`,
      list: `${ROOTS.DASHBOARD}/user/list`
    },
    plt1: {
      list: `${ROOTS.DASHBOARD}`,
      details: (id: string, type: string) => {
        switch (type.toLowerCase()) {
          case 'air':
            return `${ROOTS.DASHBOARD}/plt1air/${id}`;
          case 'road':
            return `${ROOTS.DASHBOARD}/plt1road/${id}`;
          case 'sea':
            return `${ROOTS.DASHBOARD}/plt1sea/${id}`;
          default:
            return `${ROOTS.DASHBOARD}/plt1air/${id}`;
        }
      }
    },
  },
};