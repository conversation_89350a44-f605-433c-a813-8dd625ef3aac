/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cm.malik%5C%5Csource%5C%5Crepos%5C%5CRosseta%5C%5CFrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cm.malik%5C%5Csource%5C%5Crepos%5C%5CRosseta%5C%5CFrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbS5tYWxpayU1QyU1Q3NvdXJjZSU1QyU1Q3JlcG9zJTVDJTVDUm9zc2V0YSU1QyU1Q0Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw4SkFBNkciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG0ubWFsaWtcXFxcc291cmNlXFxcXHJlcG9zXFxcXFJvc3NldGFcXFxcRnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cm.malik%5C%5Csource%5C%5Crepos%5C%5CRosseta%5C%5CFrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxtLm1hbGlrXFxzb3VyY2VcXHJlcG9zXFxSb3NzZXRhXFxGcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxhcGlcXG5hdmlnYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vY2xpZW50L2NvbXBvbmVudHMvbmF2aWdhdGlvbic7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW5hdmlnYXRpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var src_routes_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/routes/hooks */ \"(app-pages-browser)/./src/routes/hooks/index.ts\");\n/* harmony import */ var src_global_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/global-config */ \"(app-pages-browser)/./src/global-config.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n\n// ----------------------------------------------------------------------\nfunction Page() {\n    _s();\n    const router = (0,src_routes_hooks__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"Page.useEffect\": ()=>{\n            router.push(src_global_config__WEBPACK_IMPORTED_MODULE_2__.CONFIG.auth.redirectPath);\n        }\n    }[\"Page.useEffect\"], [\n        router\n    ]);\n    return null;\n}\n_s(Page, \"vQduR7x+OPXj6PSmJyFnf+hU7bg=\", false, function() {\n    return [\n        src_routes_hooks__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRWtDO0FBRVc7QUFFRjtBQUUzQyx5RUFBeUU7QUFFMUQsU0FBU0c7O0lBQ3RCLE1BQU1DLFNBQVNILDJEQUFTQTtJQUV4QkQsZ0RBQVNBOzBCQUFDO1lBQ1JJLE9BQU9DLElBQUksQ0FBQ0gscURBQU1BLENBQUNJLElBQUksQ0FBQ0MsWUFBWTtRQUN0Qzt5QkFBRztRQUFDSDtLQUFPO0lBRVgsT0FBTztBQUNUO0dBUndCRDs7UUFDUEYsdURBQVNBOzs7S0FERkUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbS5tYWxpa1xcc291cmNlXFxyZXBvc1xcUm9zc2V0YVxcRnJvbnRlbmRcXHNyY1xcYXBwXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XHJcblxyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICdzcmMvcm91dGVzL2hvb2tzJztcclxuXHJcbmltcG9ydCB7IENPTkZJRyB9IGZyb20gJ3NyYy9nbG9iYWwtY29uZmlnJztcclxuXHJcbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2UoKSB7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICByb3V0ZXIucHVzaChDT05GSUcuYXV0aC5yZWRpcmVjdFBhdGgpO1xyXG4gIH0sIFtyb3V0ZXJdKTtcclxuXHJcbiAgcmV0dXJuIG51bGw7XHJcbn1cclxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVJvdXRlciIsIkNPTkZJRyIsIlBhZ2UiLCJyb3V0ZXIiLCJwdXNoIiwiYXV0aCIsInJlZGlyZWN0UGF0aCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/global-config.ts":
/*!******************************!*\
  !*** ./src/global-config.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONFIG: () => (/* binding */ CONFIG)\n/* harmony export */ });\n/* harmony import */ var src_routes_paths__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/routes/paths */ \"(app-pages-browser)/./src/routes/paths.ts\");\n/* harmony import */ var _package_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../package.json */ \"(app-pages-browser)/./package.json\");\n\n\nvar _process_env_NEXT_PUBLIC_SERVER_URL, _process_env_NEXT_PUBLIC_ASSETS_DIR, _process_env_NEXT_PUBLIC_AUTH0_CLIENT_ID, _process_env_NEXT_PUBLIC_AUTH0_DOMAIN, _process_env_NEXT_PUBLIC_AUTH0_CALLBACK_URL;\n// ----------------------------------------------------------------------\nconst CONFIG = {\n    appName: 'Rosseta',\n    appVersion: _package_json__WEBPACK_IMPORTED_MODULE_1__.version,\n    serverUrl: (_process_env_NEXT_PUBLIC_SERVER_URL = \"https://localhost:7120/\") !== null && _process_env_NEXT_PUBLIC_SERVER_URL !== void 0 ? _process_env_NEXT_PUBLIC_SERVER_URL : '',\n    assetsDir: (_process_env_NEXT_PUBLIC_ASSETS_DIR = \"\") !== null && _process_env_NEXT_PUBLIC_ASSETS_DIR !== void 0 ? _process_env_NEXT_PUBLIC_ASSETS_DIR : '',\n    isStaticExport: JSON.parse(\"\".concat(\"false\")),\n    /**\r\n   * Auth\r\n   * @method auth0\r\n   */ auth: {\n        method: 'auth0',\n        skip: false,\n        redirectPath: src_routes_paths__WEBPACK_IMPORTED_MODULE_0__.paths.dashboard.root\n    },\n    /**\r\n   * Auth0\r\n   */ auth0: {\n        clientId: (_process_env_NEXT_PUBLIC_AUTH0_CLIENT_ID = \"jepIC3pqXEy4FbWl29wdivW5OUV2pdgQ\") !== null && _process_env_NEXT_PUBLIC_AUTH0_CLIENT_ID !== void 0 ? _process_env_NEXT_PUBLIC_AUTH0_CLIENT_ID : '',\n        domain: (_process_env_NEXT_PUBLIC_AUTH0_DOMAIN = \"dev-qzi55nmxsr5dqp4x.eu.auth0.com\") !== null && _process_env_NEXT_PUBLIC_AUTH0_DOMAIN !== void 0 ? _process_env_NEXT_PUBLIC_AUTH0_DOMAIN : '',\n        callbackUrl: (_process_env_NEXT_PUBLIC_AUTH0_CALLBACK_URL = \"http://localhost:8083/auth/callback\") !== null && _process_env_NEXT_PUBLIC_AUTH0_CALLBACK_URL !== void 0 ? _process_env_NEXT_PUBLIC_AUTH0_CALLBACK_URL : ''\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/global-config.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/routes/hooks/index.ts":
/*!***********************************!*\
  !*** ./src/routes/hooks/index.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useParams: () => (/* reexport safe */ _use_params__WEBPACK_IMPORTED_MODULE_0__.useParams),\n/* harmony export */   usePathname: () => (/* reexport safe */ _use_pathname__WEBPACK_IMPORTED_MODULE_2__.usePathname),\n/* harmony export */   useRouter: () => (/* reexport safe */ _use_router__WEBPACK_IMPORTED_MODULE_1__.useRouter),\n/* harmony export */   useSearchParams: () => (/* reexport safe */ _use_search_params__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)\n/* harmony export */ });\n/* harmony import */ var _use_params__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-params */ \"(app-pages-browser)/./src/routes/hooks/use-params.ts\");\n/* harmony import */ var _use_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-router */ \"(app-pages-browser)/./src/routes/hooks/use-router.ts\");\n/* harmony import */ var _use_pathname__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-pathname */ \"(app-pages-browser)/./src/routes/hooks/use-pathname.ts\");\n/* harmony import */ var _use_search_params__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-search-params */ \"(app-pages-browser)/./src/routes/hooks/use-search-params.ts\");\n\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9yb3V0ZXMvaG9va3MvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBeUM7QUFDQTtBQUNJO0FBQ1MiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbS5tYWxpa1xcc291cmNlXFxyZXBvc1xcUm9zc2V0YVxcRnJvbnRlbmRcXHNyY1xccm91dGVzXFxob29rc1xcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgdXNlUGFyYW1zIH0gZnJvbSAnLi91c2UtcGFyYW1zJztcclxuZXhwb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnLi91c2Utcm91dGVyJztcclxuZXhwb3J0IHsgdXNlUGF0aG5hbWUgfSBmcm9tICcuL3VzZS1wYXRobmFtZSc7XHJcbmV4cG9ydCB7IHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gJy4vdXNlLXNlYXJjaC1wYXJhbXMnOyJdLCJuYW1lcyI6WyJ1c2VQYXJhbXMiLCJ1c2VSb3V0ZXIiLCJ1c2VQYXRobmFtZSIsInVzZVNlYXJjaFBhcmFtcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/routes/hooks/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/routes/hooks/use-params.ts":
/*!****************************************!*\
  !*** ./src/routes/hooks/use-params.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useParams: () => (/* reexport safe */ next_navigation__WEBPACK_IMPORTED_MODULE_0__.useParams)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9yb3V0ZXMvaG9va3MvdXNlLXBhcmFtcy50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxtLm1hbGlrXFxzb3VyY2VcXHJlcG9zXFxSb3NzZXRhXFxGcm9udGVuZFxcc3JjXFxyb3V0ZXNcXGhvb2tzXFx1c2UtcGFyYW1zLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IHVzZVBhcmFtcyB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XHJcbiJdLCJuYW1lcyI6WyJ1c2VQYXJhbXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/routes/hooks/use-params.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/routes/hooks/use-pathname.ts":
/*!******************************************!*\
  !*** ./src/routes/hooks/use-pathname.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePathname: () => (/* reexport safe */ next_navigation__WEBPACK_IMPORTED_MODULE_0__.usePathname)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9yb3V0ZXMvaG9va3MvdXNlLXBhdGhuYW1lLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQThDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG0ubWFsaWtcXHNvdXJjZVxccmVwb3NcXFJvc3NldGFcXEZyb250ZW5kXFxzcmNcXHJvdXRlc1xcaG9va3NcXHVzZS1wYXRobmFtZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XHJcbiJdLCJuYW1lcyI6WyJ1c2VQYXRobmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/routes/hooks/use-pathname.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/routes/hooks/use-router.ts":
/*!****************************************!*\
  !*** ./src/routes/hooks/use-router.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRouter: () => (/* reexport safe */ next_navigation__WEBPACK_IMPORTED_MODULE_0__.useRouter)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9yb3V0ZXMvaG9va3MvdXNlLXJvdXRlci50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxtLm1hbGlrXFxzb3VyY2VcXHJlcG9zXFxSb3NzZXRhXFxGcm9udGVuZFxcc3JjXFxyb3V0ZXNcXGhvb2tzXFx1c2Utcm91dGVyLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XHJcbiJdLCJuYW1lcyI6WyJ1c2VSb3V0ZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/routes/hooks/use-router.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/routes/hooks/use-search-params.ts":
/*!***********************************************!*\
  !*** ./src/routes/hooks/use-search-params.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSearchParams: () => (/* reexport safe */ next_navigation__WEBPACK_IMPORTED_MODULE_0__.useSearchParams)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9yb3V0ZXMvaG9va3MvdXNlLXNlYXJjaC1wYXJhbXMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbS5tYWxpa1xcc291cmNlXFxyZXBvc1xcUm9zc2V0YVxcRnJvbnRlbmRcXHNyY1xccm91dGVzXFxob29rc1xcdXNlLXNlYXJjaC1wYXJhbXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgdXNlU2VhcmNoUGFyYW1zIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcclxuIl0sIm5hbWVzIjpbInVzZVNlYXJjaFBhcmFtcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/routes/hooks/use-search-params.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/routes/paths.ts":
/*!*****************************!*\
  !*** ./src/routes/paths.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paths: () => (/* binding */ paths)\n/* harmony export */ });\nconst ROOTS = {\n    AUTH: '/auth',\n    DASHBOARD: '/dashboard'\n};\n// ----------------------------------------------------------------------\nconst paths = {\n    faqs: '/faqs',\n    auth: {\n        auth0: {\n            signIn: \"\".concat(ROOTS.AUTH, \"/sign-in\"),\n            onboarding: \"\".concat(ROOTS.AUTH, \"/onboarding\")\n        }\n    },\n    dashboard: {\n        root: ROOTS.DASHBOARD,\n        user: {\n            root: \"\".concat(ROOTS.DASHBOARD, \"/user\"),\n            list: \"\".concat(ROOTS.DASHBOARD, \"/user/list\")\n        },\n        plt1: {\n            list: \"\".concat(ROOTS.DASHBOARD),\n            details: (id, type)=>{\n                switch(type.toLowerCase()){\n                    case 'air':\n                        return \"\".concat(ROOTS.DASHBOARD, \"/plt1air/\").concat(id);\n                    case 'road':\n                        return \"\".concat(ROOTS.DASHBOARD, \"/plt1road/\").concat(id);\n                    case 'sea':\n                        return \"\".concat(ROOTS.DASHBOARD, \"/plt1sea/\").concat(id);\n                    default:\n                        return \"\".concat(ROOTS.DASHBOARD, \"/plt1air/\").concat(id);\n                }\n            }\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/routes/paths.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./package.json":
/*!**********************!*\
  !*** ./package.json ***!
  \**********************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"name":"@minimal-kit/starter-next-ts","author":"Minimals","version":"6.2.0","description":"Next Starter & TypeScript","private":true,"scripts":{"dev":"next dev -p 8083","start":"next start -p 8083","build":"next build","lint":"eslint \\"src/**/*.{js,jsx,ts,tsx}\\"","lint:fix":"eslint --fix \\"src/**/*.{js,jsx,ts,tsx}\\"","lint:print":"npx eslint --print-config eslint.config.mjs > eslint-show-config.json","fm:check":"prettier --check \\"src/**/*.{js,jsx,ts,tsx}\\"","fm:fix":"prettier --write \\"src/**/*.{js,jsx,ts,tsx}\\"","fix:all":"npm run lint:fix && npm run fm:fix","clean":"rm -rf node_modules .next out dist build","re:dev":"yarn clean && yarn install && yarn dev","re:build":"yarn clean && yarn install && yarn build","re:build-npm":"npm run clean && npm install && npm run build","tsc:dev":"yarn dev & yarn tsc:watch","tsc:watch":"tsc --noEmit --watch","tsc:print":"npx tsc --showConfig"},"engines":{"node":"20.x"},"packageManager":"yarn@1.22.22","dependencies":{"@auth0/auth0-react":"^2.2.4","@canvas-fonts/helvetica":"^1.0.4","@emotion/cache":"^11.13.5","@emotion/react":"^11.13.5","@emotion/styled":"^11.13.5","@fontsource-variable/dm-sans":"^5.1.0","@fontsource-variable/inter":"^5.1.0","@fontsource-variable/nunito-sans":"^5.1.0","@fontsource-variable/public-sans":"^5.1.1","@fontsource/barlow":"^5.1.0","@hookform/resolvers":"^3.9.1","@iconify/react":"^5.0.2","@mui/lab":"^6.0.0-beta.18","@mui/material":"^6.1.10","@mui/material-nextjs":"^6.1.9","@mui/x-data-grid":"^7.23.1","@mui/x-date-pickers":"^7.23.1","@mui/x-tree-view":"^7.23.0","@tiptap/extension-code-block-lowlight":"^2.11.2","@tiptap/extension-image":"^2.11.2","@tiptap/extension-link":"^2.11.2","@tiptap/extension-placeholder":"^2.11.2","@tiptap/extension-text-align":"^2.11.2","@tiptap/extension-underline":"^2.11.2","@tiptap/react":"^2.11.2","@tiptap/starter-kit":"^2.11.2","autosuggest-highlight":"^3.3.4","axios":"^1.7.9","dayjs":"^1.11.13","es-toolkit":"^1.29.0","framer-motion":"^11.13.1","i18next":"^24.0.5","i18next-browser-languagedetector":"^8.0.0","i18next-resources-to-backend":"^1.2.1","lowlight":"^3.3.0","minimal-shared":"^1.0.5","mui-one-time-password-input":"^3.0.2","next":"^15.1.2","nprogress":"^0.2.0","react":"^18.2.0","react-dom":"^18.3.0","react-dropzone":"^14.3.5","react-hook-form":"^7.53.2","react-i18next":"^15.1.3","react-phone-number-input":"^3.4.11","simplebar-react":"^3.2.6","sonner":"^1.7.1","stylis":"^4.3.4","stylis-plugin-rtl":"^2.1.1","swr":"^2.3.0","zod":"^3.23.8"},"devDependencies":{"@eslint/js":"^9.16.0","@svgr/webpack":"^8.1.0","@types/autosuggest-highlight":"^3.2.3","@types/node":"^22.10.1","@types/nprogress":"^0.2.3","@types/react":"^18.3.13","@types/react-dom":"^18.3.1","@types/react-phone-number-input":"^3.0.17","@types/stylis":"^4.2.7","@typescript-eslint/parser":"^8.17.0","eslint":"^9.16.0","eslint-import-resolver-typescript":"^3.7.0","eslint-plugin-import":"^2.31.0","eslint-plugin-perfectionist":"^4.2.0","eslint-plugin-react":"^7.37.2","eslint-plugin-react-hooks":"^5.0.0","eslint-plugin-unused-imports":"^4.1.4","globals":"^15.13.0","prettier":"^3.4.2","typescript":"^5.7.2","typescript-eslint":"^8.17.0"}}');

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cm.malik%5C%5Csource%5C%5Crepos%5C%5CRosseta%5C%5CFrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);