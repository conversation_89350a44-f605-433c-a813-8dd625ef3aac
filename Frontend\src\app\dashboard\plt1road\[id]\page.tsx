import type { Metadata } from 'next';

import { CONFIG } from 'src/global-config';
import { getServerTranslations } from 'src/locales/server';
import { PLT1RoadOrderDetailsView } from 'src/sections/orders/plt1/details/plt1road-order-details';
import { paramsType } from 'src/types/common';

// ----------------------------------------------------------------------
export async function generateMetadata(): Promise<Metadata> {
  const { t } = await getServerTranslations();
  return {
    title: `${t('plt1.details.pageTitle')} - ${CONFIG.appName}`
  };
}

export default async function Page({ params }: { params: paramsType }) {
  const { id } = await params;
  return <PLT1RoadOrderDetailsView orderId={id} />;
}