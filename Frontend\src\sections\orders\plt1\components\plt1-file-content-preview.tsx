'use client';
import { useState, useEffect } from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';
import { useTranslate } from 'src/locales';
import { Iconify } from 'src/components/iconify';
import { PLT1OrderFile } from './plt1-file-management';
import { fileCache } from './plt1-file-cache';
interface PLT1FileContentPreviewProps {
  file: PLT1OrderFile | null;
}
export function PLT1FileContentPreview({ file }: PLT1FileContentPreviewProps) {
  const { t } = useTranslate();
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // Get file extension
  const getFileExtension = (filename: string): string => {
    return filename.split('.').pop()?.toLowerCase() || '';
  };
  // Check if file is an image
  const isImage = (filename: string): boolean => {
    const ext = getFileExtension(filename);
    return ['jpg', 'jpeg', 'png'].includes(ext);
  };
  // Check if file is a PDF
  const isPdf = (filename: string): boolean => {
    return getFileExtension(filename) === 'pdf';
  };
  // Check if file is an Office document
  const isOfficeDocument = (filename: string): boolean => {
    const ext = getFileExtension(filename);
    return ['doc', 'docx', 'xls', 'xlsx'].includes(ext);
  };
  // Load file content when file changes
  useEffect(() => {
    if (file) {
      const loadFile = async () => {
        setIsLoading(true);
        setError(null);
        try {
          // Get the file from the cache or download it
          const { url, contentType } = await fileCache.getFile(file);
          setFileUrl(url);
        } catch (error) {
          console.error('Error loading file for preview:', error);
          setError(t('plt1.details.filePreview.loadError'));
        } finally {
          setIsLoading(false);
        }
      };
      loadFile();
    }
    // No need to revoke object URLs here as they're managed by the cache
    return () => {
      setFileUrl(null);
    };
  }, [file, t]);
  // Handle download
  const handleDownload = () => {
    if (file && fileUrl) {
      const link = document.createElement('a');
      link.href = fileUrl;
      link.setAttribute('download', file.name);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };
  if (!file) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          width: '100%',
        }}
      >
        <Typography color="text.secondary">{t('plt1.details.filePreview.selectFile')}</Typography>
      </Box>
    );
  }
  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          width: '100%',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }
  if (error) {
    return (
      <Box
        sx={{
          p: 3,
          textAlign: 'center',
          height: '100%',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography color="error" sx={{ mb: 2 }}>
          {error}
        </Typography>
        <Button
          variant="outlined"
          startIcon={<Iconify icon="eva:refresh-fill" />}
          onClick={() => {
            if (file) {
              setFileUrl(null);
              setError(null);
              setIsLoading(true);
              // Trigger a re-fetch by changing the key
              const loadFile = async () => {
                try {
                  // Get the file from the cache or download it
                  const { url } = await fileCache.getFile(file);
                  setFileUrl(url);
                  setError(null);
                } catch (error) {
                  console.error('Error loading file for preview:', error);
                  setError(t('plt1.details.filePreview.loadError'));
                } finally {
                  setIsLoading(false);
                }
              };
              loadFile();
            }
          }}
        >
          {t('common.retry')}
        </Button>
      </Box>
    );
  }
  if (!fileUrl) {
    return null;
  }
  if (isImage(file.name)) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          width: '100%', // Ensure full width
        }}
      >
        <Box
          sx={{
            flexGrow: 1,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'flex-start', // Align to top to allow scrolling
            p: 2,
            overflow: 'auto', // Enable scrolling
            width: '100%', // Ensure full width
          }}
        >
          <img
            src={fileUrl}
            alt={file.name}
            style={{
              maxWidth: '100%',
              objectFit: 'contain',
            }}
          />
        </Box>
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="outlined"
            startIcon={<Iconify icon="eva:download-fill" />}
            onClick={handleDownload}
          >
            {t('plt1.details.filePreview.download')}
          </Button>
        </Box>
      </Box>
    );
  }
  if (isPdf(file.name)) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          width: '100%', // Ensure full width
        }}
      >
        <Box
          sx={{
            flexGrow: 1,
            p: 0,
            width: '100%', // Ensure full width
            overflow: 'auto', // Allow scrolling
            display: 'flex', // Use flexbox
            flexDirection: 'column', // Stack children vertically
            alignItems: 'center', // Center horizontally
          }}
        >
          <iframe
            src={`${fileUrl}#toolbar=1&scrollbar=1&view=FitH`}
            title={file.name}
            style={{
              width: '100%',
              flexGrow: 1, // Take up all available space
              border: 'none',
              minHeight: '100%', // Ensure it takes at least full height
            }}
          />
        </Box>
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="outlined"
            startIcon={<Iconify icon="eva:download-fill" />}
            onClick={handleDownload}
          >
            {t('plt1.details.filePreview.download')}
          </Button>
        </Box>
      </Box>
    );
  }
  // For Office documents, use Google Docs Viewer
  if (isOfficeDocument(file.name) && fileUrl) {
    // For Office documents, we'll use Google Docs Viewer
    // This works with blob URLs directly in the iframe
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          width: '100%', // Ensure full width
        }}
      >
        <Box
          sx={{
            flexGrow: 1,
            p: 0,
            width: '100%', // Ensure full width
            overflow: 'auto', // Allow scrolling
            display: 'flex', // Use flexbox
            flexDirection: 'column', // Stack children vertically
            alignItems: 'center', // Center horizontally
          }}
        >
          <iframe
            src={fileUrl}
            title={file.name}
            style={{
              width: '100%',
              flexGrow: 1, // Take up all available space
              border: 'none',
              minHeight: '100%', // Ensure it takes at least full height
            }}
          />
        </Box>
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="outlined"
            startIcon={<Iconify icon="eva:download-fill" />}
            onClick={handleDownload}
          >
            {t('plt1.details.filePreview.download')}
          </Button>
        </Box>
      </Box>
    );
  }
  // For other file types, show a download prompt
  return (
    <Box
      sx={{
        p: 5,
        textAlign: 'center',
        height: '100%',
        width: '100%', // Ensure full width
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Typography variant="body1" sx={{ mb: 2 }}>
        {t('plt1.details.filePreview.cannotPreview')}
      </Typography>
      <Button
        variant="contained"
        startIcon={<Iconify icon="eva:download-fill" />}
        onClick={handleDownload}
      >
        {t('plt1.details.filePreview.download')}
      </Button>
    </Box>
  );
}
