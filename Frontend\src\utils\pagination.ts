import { IBaseFilters, IPaginatedFilters } from "src/types/pagination";

export function createPaginatedUrl(
  baseUrl: string, 
  filters: IPaginatedFilters<IBaseFilters>
): string {
  const params = new URLSearchParams();

  // Add pagination params
  params.append('page', String(filters.page));
  params.append('pageSize', String(filters.pageSize));
  params.append('orderBy', filters.orderBy);
  params.append('order', filters.order);

  // Add search if present
  if (filters.search) {
    params.append('search', filters.search);
  }

  // Handle additional filters
  Object.entries(filters).forEach(([key, value]) => {
    if (
      key !== 'page' && 
      key !== 'pageSize' && 
      key !== 'orderBy' && 
      key !== 'order' && 
      key !== 'search' &&
      value !== undefined &&
      value !== null
    ) {
      if (Array.isArray(value)) {
        value.forEach(v => params.append(key, String(v)));
      } else {
        params.append(key, String(value));
      }
    }
  });

  return `${baseUrl}?${params.toString()}`;
}