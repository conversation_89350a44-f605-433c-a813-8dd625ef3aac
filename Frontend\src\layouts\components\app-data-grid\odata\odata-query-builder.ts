import { ODataFieldType, ODataQueryParams } from "./odata";
import { ODataFilterBuilder } from "./odata-filter-builder";

export class ODataQueryBuilder {
  public buildQuery(params: ODataQueryParams): string {
    const queryParams: string[] = [];
    const { pagination, sortModel, filterModel, columns = [] } = params;

    // Pagination
    if (pagination) {
      queryParams.push(`$top=${pagination.pageSize}`);
      queryParams.push(`$skip=${pagination.page * pagination.pageSize}`);
    }
    queryParams.push(`$count=true`);

    // Sorting
    if (sortModel?.length) {
      const orderBy = sortModel
        .map((sort) => `${sort.field} ${sort.sort || 'asc'}`)
        .join(',');
      queryParams.push(`$orderby=${encodeURIComponent(orderBy)}`);
    }

    // Filtering
    if (filterModel?.items.length) {
      const validFilters = filterModel.items
        .filter(item => item.field && (
          item.value !== undefined ||
          ['isEmpty', 'isNotEmpty'].includes(item.operator || '')
        ))
        .map(item => {
          const column = columns.find(col => col.field === item.field);
          let fieldType: ODataFieldType = 'string';

          if (column?.type === 'dateTime') {
            fieldType = 'date';
          } else if (column?.type === 'singleSelect') {
            fieldType = 'enum';
          }

          return {
            ...item,
            fieldType,
            value: item.value ?? ''
          };
        });

      if (validFilters.length) {
        const filterBuilder = new ODataFilterBuilder();
        const filters = validFilters
          .map(filter => filterBuilder.buildFilter(filter))
          .filter(Boolean);

        if (filters.length > 0) {
          const filterString = filterModel.logicOperator === 'or'
            ? filters.join(' or ')
            : filters.join(' and ');

          if (filterString.trim()) {
            queryParams.push(`$filter=${encodeURIComponent(filterString)}`);
          }
        }
      }
    }

    return queryParams.join('&');
  }
}