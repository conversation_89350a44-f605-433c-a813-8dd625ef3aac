import type { ButtonProps } from '@mui/material/Button';

import Button from '@mui/material/Button';


import { useCallback } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { useSearchParams } from 'src/routes/hooks';
import { LANGUAGE_LOCAL_STORAGE_KEY, useTranslate } from 'src/locales';

// ----------------------------------------------------------------------

export type SignInButtonProps = ButtonProps & {
  forceReauthentication?: boolean;
}

export function SignInButton({ sx, forceReauthentication = false, ...other }: SignInButtonProps) {

  const { loginWithRedirect } = useAuth0();
  const searchParams = useSearchParams();
  const returnTo = searchParams.get('returnTo') ?? "";
  const { t } = useTranslate();

  const handleSignInWithRedirect = useCallback(async () => {
    try {
      await loginWithRedirect({
        appState: { returnTo: returnTo },
        authorizationParams:
        {
          ui_locales: localStorage.getItem(LANGUAGE_LOCAL_STORAGE_KEY) || 'en',
          ...(forceReauthentication && { max_age: 0 })
        }
      });
    } catch (error) {
      console.error(error);
    }
  }, [loginWithRedirect, returnTo]);


  return (
    <Button
      fullWidth
      size="large"
      color="primary"
      variant="contained"
      onClick={handleSignInWithRedirect}
      sx={{ mt: 3 }}
      {...other}
    >
      {t('components.signInButton.label')}
    </Button>
  )
}
