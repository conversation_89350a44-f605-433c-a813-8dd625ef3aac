import { useState } from 'react';
import { useFormContext } from 'react-hook-form';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';

import { useTranslate } from 'src/locales';
import { Iconify } from 'src/components/iconify';
import PLT1CustomsOfficeForm from '../forms/plt1-customs-office-form';

// ----------------------------------------------------------------------

interface PLT1CustomsOfficeTabProps {
  readOnly?: boolean;
}

export default function PLT1CustomsOfficeTab({
  readOnly = false,
}: PLT1CustomsOfficeTabProps) {
  const { t } = useTranslate();
  const { watch } = useFormContext();

  // Define the field path for customsOffice
  const fieldPath = 'customsOffice';

  // State for expanded/collapsed form
  const [isExpanded, setIsExpanded] = useState(false);

  // Watch the customsOffice object using the correct path
  const customsOffice = watch(fieldPath) || { customsOfficeCode: '' };

  const handleToggleExpand = () => {
    setIsExpanded((prev) => !prev);
  };

  return (
    <Box>
      <Card sx={{ mb: 3 }}>
        <Stack>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            sx={{ p: 3, pb: isExpanded ? 0 : 3 }}
          >
            <Typography variant="h6">{t('plt1.details.customsOffice.title')}</Typography>

            <Tooltip title={isExpanded ? t('common.collapse') : t('common.expand')}>
              <IconButton onClick={handleToggleExpand} disabled={readOnly && !isExpanded}>
                <Iconify icon={isExpanded ? 'eva:chevron-up-fill' : 'eva:chevron-down-fill'} />
              </IconButton>
            </Tooltip>
          </Stack>

          {!isExpanded && (
            <Box sx={{ p: 3, pt: 0 }}>
              <Stack spacing={1}>
                <Typography variant="subtitle2">{t('plt1.details.customsOffice.code')}</Typography>
                <Typography variant="body2">
                  {customsOffice?.customsOfficeCode || '-'}
                </Typography>
              </Stack>
            </Box>
          )}

          {isExpanded && (
            <Box sx={{ p: 3 }}>
              <PLT1CustomsOfficeForm
                formPath={fieldPath}
                readOnly={readOnly}
              />
            </Box>
          )}
        </Stack>
      </Card>
    </Box>
  );
}
