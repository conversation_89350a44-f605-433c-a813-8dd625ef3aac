"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/input-format";
exports.ids = ["vendor-chunks/input-format"];
exports.modules = {

/***/ "(ssr)/./node_modules/input-format/modules/closeBraces.js":
/*!**********************************************************!*\
  !*** ./node_modules/input-format/modules/closeBraces.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ closeBraces)\n/* harmony export */ });\n/* harmony import */ var _helpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers.js */ \"(ssr)/./node_modules/input-format/modules/helpers.js\");\n\nfunction closeBraces(retained_template, template) {\n  var placeholder = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'x';\n  var empty_placeholder = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : ' ';\n  var cut_before = retained_template.length;\n  var opening_braces = (0,_helpers_js__WEBPACK_IMPORTED_MODULE_0__.count_occurences)('(', retained_template);\n  var closing_braces = (0,_helpers_js__WEBPACK_IMPORTED_MODULE_0__.count_occurences)(')', retained_template);\n  var dangling_braces = opening_braces - closing_braces;\n\n  while (dangling_braces > 0 && cut_before < template.length) {\n    retained_template += template[cut_before].replace(placeholder, empty_placeholder);\n\n    if (template[cut_before] === ')') {\n      dangling_braces--;\n    }\n\n    cut_before++;\n  }\n\n  return retained_template;\n}\n//# sourceMappingURL=closeBraces.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaW5wdXQtZm9ybWF0L21vZHVsZXMvY2xvc2VCcmFjZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZ0Q7QUFDakM7QUFDZjtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsNkRBQWdCO0FBQ3ZDLHVCQUF1Qiw2REFBZ0I7QUFDdkM7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbS5tYWxpa1xcc291cmNlXFxyZXBvc1xcUm9zc2V0YVxcRnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcaW5wdXQtZm9ybWF0XFxtb2R1bGVzXFxjbG9zZUJyYWNlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjb3VudF9vY2N1cmVuY2VzIH0gZnJvbSAnLi9oZWxwZXJzLmpzJztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGNsb3NlQnJhY2VzKHJldGFpbmVkX3RlbXBsYXRlLCB0ZW1wbGF0ZSkge1xuICB2YXIgcGxhY2Vob2xkZXIgPSBhcmd1bWVudHMubGVuZ3RoID4gMiAmJiBhcmd1bWVudHNbMl0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1syXSA6ICd4JztcbiAgdmFyIGVtcHR5X3BsYWNlaG9sZGVyID0gYXJndW1lbnRzLmxlbmd0aCA+IDMgJiYgYXJndW1lbnRzWzNdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbM10gOiAnICc7XG4gIHZhciBjdXRfYmVmb3JlID0gcmV0YWluZWRfdGVtcGxhdGUubGVuZ3RoO1xuICB2YXIgb3BlbmluZ19icmFjZXMgPSBjb3VudF9vY2N1cmVuY2VzKCcoJywgcmV0YWluZWRfdGVtcGxhdGUpO1xuICB2YXIgY2xvc2luZ19icmFjZXMgPSBjb3VudF9vY2N1cmVuY2VzKCcpJywgcmV0YWluZWRfdGVtcGxhdGUpO1xuICB2YXIgZGFuZ2xpbmdfYnJhY2VzID0gb3BlbmluZ19icmFjZXMgLSBjbG9zaW5nX2JyYWNlcztcblxuICB3aGlsZSAoZGFuZ2xpbmdfYnJhY2VzID4gMCAmJiBjdXRfYmVmb3JlIDwgdGVtcGxhdGUubGVuZ3RoKSB7XG4gICAgcmV0YWluZWRfdGVtcGxhdGUgKz0gdGVtcGxhdGVbY3V0X2JlZm9yZV0ucmVwbGFjZShwbGFjZWhvbGRlciwgZW1wdHlfcGxhY2Vob2xkZXIpO1xuXG4gICAgaWYgKHRlbXBsYXRlW2N1dF9iZWZvcmVdID09PSAnKScpIHtcbiAgICAgIGRhbmdsaW5nX2JyYWNlcy0tO1xuICAgIH1cblxuICAgIGN1dF9iZWZvcmUrKztcbiAgfVxuXG4gIHJldHVybiByZXRhaW5lZF90ZW1wbGF0ZTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNsb3NlQnJhY2VzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/closeBraces.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/dom.js":
/*!**************************************************!*\
  !*** ./node_modules/input-format/modules/dom.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Keys: () => (/* binding */ Keys),\n/* harmony export */   getCaretPosition: () => (/* binding */ getCaretPosition),\n/* harmony export */   getOperation: () => (/* binding */ getOperation),\n/* harmony export */   getSelection: () => (/* binding */ getSelection),\n/* harmony export */   isReadOnly: () => (/* binding */ isReadOnly),\n/* harmony export */   setCaretPosition: () => (/* binding */ setCaretPosition)\n/* harmony export */ });\nfunction isReadOnly(element) {\n  return element.hasAttribute('readonly');\n} // Gets <input/> selection bounds\n\nfunction getSelection(element) {\n  // If no selection, return nothing\n  if (element.selectionStart === element.selectionEnd) {\n    return;\n  }\n\n  return {\n    start: element.selectionStart,\n    end: element.selectionEnd\n  };\n} // Key codes\n\nvar Keys = {\n  Backspace: 8,\n  Delete: 46\n}; // Finds out the operation to be intercepted and performed\n// based on the key down event `keyCode`.\n\nfunction getOperation(event) {\n  switch (event.keyCode) {\n    case Keys.Backspace:\n      return 'Backspace';\n\n    case Keys.Delete:\n      return 'Delete';\n  }\n} // Gets <input/> caret position\n\nfunction getCaretPosition(element) {\n  return element.selectionStart;\n} // Sets <input/> caret position\n\nfunction setCaretPosition(element, caret_position) {\n  // Sanity check\n  if (caret_position === undefined) {\n    return;\n  } // Set caret position.\n  // There has been an issue with caret positioning on Android devices.\n  // https://github.com/catamphetamine/input-format/issues/2\n  // I was revisiting this issue and looked for similar issues in other libraries.\n  // For example, there's [`text-mask`](https://github.com/text-mask/text-mask) library.\n  // They've had exactly the same issue when the caret seemingly refused to be repositioned programmatically.\n  // The symptoms were the same: whenever the caret passed through a non-digit character of a mask (a whitespace, a bracket, a dash, etc), it looked as if it placed itself one character before its correct position.\n  // https://github.com/text-mask/text-mask/issues/300\n  // They seem to have found a basic fix for it: calling `input.setSelectionRange()` in a timeout rather than instantly for Android devices.\n  // https://github.com/text-mask/text-mask/pull/400/files\n  // I've implemented the same workaround here.\n\n\n  if (isAndroid()) {\n    setTimeout(function () {\n      return element.setSelectionRange(caret_position, caret_position);\n    }, 0);\n  } else {\n    element.setSelectionRange(caret_position, caret_position);\n  }\n}\n\nfunction isAndroid() {\n  // `navigator` is not defined when running mocha tests.\n  if (typeof navigator !== 'undefined') {\n    return ANDROID_USER_AGENT_REG_EXP.test(navigator.userAgent);\n  }\n}\n\nvar ANDROID_USER_AGENT_REG_EXP = /Android/i;\n//# sourceMappingURL=dom.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/dom.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/edit.js":
/*!***************************************************!*\
  !*** ./node_modules/input-format/modules/edit.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ edit)\n/* harmony export */ });\n// Edits text `value` (if `operation` is passed) and repositions the `caret` if needed.\n//\n// Example:\n//\n// value - '88005553535'\n// caret - 2 // starting from 0; is positioned before the first zero\n// operation - 'Backspace'\n//\n// Returns\n// {\n// \tvalue: '8005553535'\n// \tcaret: 1\n// }\n//\n// Currently supports just 'Delete' and 'Backspace' operations\n//\nfunction edit(value, caret, operation) {\n  switch (operation) {\n    case 'Backspace':\n      // If there exists the previous character,\n      // then erase it and reposition the caret.\n      if (caret > 0) {\n        // Remove the previous character\n        value = value.slice(0, caret - 1) + value.slice(caret); // Position the caret where the previous (erased) character was\n\n        caret--;\n      }\n\n      break;\n\n    case 'Delete':\n      // Remove current digit (if any)\n      value = value.slice(0, caret) + value.slice(caret + 1);\n      break;\n  }\n\n  return {\n    value: value,\n    caret: caret\n  };\n}\n//# sourceMappingURL=edit.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/edit.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/format.js":
/*!*****************************************************!*\
  !*** ./node_modules/input-format/modules/format.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ format)\n/* harmony export */ });\n/* harmony import */ var _templateFormatter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./templateFormatter.js */ \"(ssr)/./node_modules/input-format/modules/templateFormatter.js\");\n // Formats `value` value preserving `caret` at the same character.\n//\n// `{ value, caret }` attribute is the result of `parse()` function call.\n//\n// Returns `{ text, caret }` where the new `caret` is the caret position\n// inside `text` text corresponding to the original `caret` position inside `value`.\n//\n// `formatter(value)` is a function returning `{ text, template }`.\n//\n// `text` is the `value` value formatted using `template`.\n// It may either cut off the non-filled right part of the `template`\n// or it may fill the non-filled character placeholders\n// in the right part of the `template` with `spacer`\n// which is a space (' ') character by default.\n//\n// `template` is the template used to format the `value`.\n// It can be either a full-length template or a partial template.\n//\n// `formatter` can also be a string — a `template`\n// where character placeholders are denoted by 'x'es.\n// In this case `formatter` function is automatically created.\n//\n// Example:\n//\n// `value` is '880',\n// `caret` is `2` (before the first `0`)\n//\n// `formatter` is `'880' =>\n//   { text: '8 (80 )', template: 'x (xxx) xxx-xx-xx' }`\n//\n// The result is `{ text: '8 (80 )', caret: 4 }`.\n//\n\nfunction format(value, caret, formatter) {\n  if (typeof formatter === 'string') {\n    formatter = (0,_templateFormatter_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(formatter);\n  }\n\n  var _ref = formatter(value) || {},\n      text = _ref.text,\n      template = _ref.template;\n\n  if (text === undefined) {\n    text = value;\n  }\n\n  if (template) {\n    if (caret === undefined) {\n      caret = text.length;\n    } else {\n      var index = 0;\n      var found = false;\n      var possibly_last_input_character_index = -1;\n\n      while (index < text.length && index < template.length) {\n        // Character placeholder found\n        if (text[index] !== template[index]) {\n          if (caret === 0) {\n            found = true;\n            caret = index;\n            break;\n          }\n\n          possibly_last_input_character_index = index;\n          caret--;\n        }\n\n        index++;\n      } // If the caret was positioned after last input character,\n      // then the text caret index is just after the last input character.\n\n\n      if (!found) {\n        caret = possibly_last_input_character_index + 1;\n      }\n    }\n  }\n\n  return {\n    text: text,\n    caret: caret\n  };\n}\n//# sourceMappingURL=format.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/format.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/helpers.js":
/*!******************************************************!*\
  !*** ./node_modules/input-format/modules/helpers.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   count_occurences: () => (/* binding */ count_occurences)\n/* harmony export */ });\nfunction _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\n// Counts all occurences of a symbol in a string\nfunction count_occurences(symbol, string) {\n  var count = 0; // Using `.split('')` here instead of normal `for ... of`\n  // because the importing application doesn't neccessarily include an ES6 polyfill.\n  // The `.split('')` approach discards \"exotic\" UTF-8 characters\n  // (the ones consisting of four bytes)\n  // but template placeholder characters don't fall into that range\n  // so skipping such miscellaneous \"exotic\" characters\n  // won't matter here for just counting placeholder character occurrences.\n\n  for (var _iterator = _createForOfIteratorHelperLoose(string.split('')), _step; !(_step = _iterator()).done;) {\n    var character = _step.value;\n\n    if (character === symbol) {\n      count++;\n    }\n  }\n\n  return count;\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/inputControl.js":
/*!***********************************************************!*\
  !*** ./node_modules/input-format/modules/inputControl.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   onChange: () => (/* binding */ onChange),\n/* harmony export */   onCut: () => (/* binding */ onCut),\n/* harmony export */   onKeyDown: () => (/* binding */ onKeyDown),\n/* harmony export */   onPaste: () => (/* binding */ onPaste)\n/* harmony export */ });\n/* harmony import */ var _edit_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./edit.js */ \"(ssr)/./node_modules/input-format/modules/edit.js\");\n/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parse.js */ \"(ssr)/./node_modules/input-format/modules/parse.js\");\n/* harmony import */ var _format_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./format.js */ \"(ssr)/./node_modules/input-format/modules/format.js\");\n/* harmony import */ var _dom_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dom.js */ \"(ssr)/./node_modules/input-format/modules/dom.js\");\n\n\n\n // Deprecated.\n// I don't know why this function exists.\n\nfunction onCut(event, input, _parse, _format, on_change) {\n  if ((0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.isReadOnly)(input)) {\n    return;\n  } // The actual cut hasn't happened just yet hence the timeout.\n\n\n  setTimeout(function () {\n    return formatInputText(input, _parse, _format, undefined, on_change);\n  }, 0);\n} // Deprecated.\n// I don't know why this function exists.\n\nfunction onPaste(event, input, _parse, _format, on_change) {\n  if ((0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.isReadOnly)(input)) {\n    return;\n  }\n\n  var selection = (0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.getSelection)(input); // If selection is made,\n  // just erase the selected text\n  // prior to pasting\n\n  if (selection) {\n    eraseSelection(input, selection);\n  }\n\n  formatInputText(input, _parse, _format, undefined, on_change);\n}\nfunction onChange(event, input, _parse, _format, on_change) {\n  formatInputText(input, _parse, _format, undefined, on_change);\n} // \"Delete\" and \"Backspace\" keys are special\n// in a way that they're not handled by the regular `onChange()` handler\n// and instead are intercepted and re-applied manually.\n// The reason is that normally hitting \"Backspace\" or \"Delete\"\n// results in erasing a character, but that character might be any character,\n// while it would be a better \"user experience\" if it erased not just any character\n// but the closest \"meaningful\" character.\n// For example, if a template is `(xxx) xxx-xxxx`,\n// and the `<input/>` value is `(111) 222-3333`,\n// then, if a user begins erasing the `3333` part via \"Backspace\"\n// and reaches the \"-\" character, then it would just erase the \"-\" character.\n// Nothing wrong with that, but it would be a better \"user experience\"\n// if hitting \"Backspace\" at that position would erase the closest \"meaningful\"\n// character, which would be the rightmost `2`.\n// So, what this `onKeyDown()` handler does is it intercepts\n// \"Backspace\" and \"Delete\" keys and re-applies those operations manually\n// following the logic described above.\n\nfunction onKeyDown(event, input, _parse, _format, on_change) {\n  if ((0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.isReadOnly)(input)) {\n    return;\n  }\n\n  var operation = (0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.getOperation)(event);\n\n  switch (operation) {\n    case 'Delete':\n    case 'Backspace':\n      // Intercept this operation and perform it manually.\n      event.preventDefault();\n      var selection = (0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.getSelection)(input); // If a selection is made, just erase the selected text.\n\n      if (selection) {\n        eraseSelection(input, selection);\n        return formatInputText(input, _parse, _format, undefined, on_change);\n      } // Else, perform the (character erasing) operation manually.\n\n\n      return formatInputText(input, _parse, _format, operation, on_change);\n\n    default: // Will be handled normally as part of the `onChange` handler.\n\n  }\n}\n/**\r\n * Erases the selected text inside an `<input/>`.\r\n * @param  {DOMElement} input\r\n * @param  {Selection} selection\r\n */\n\nfunction eraseSelection(input, selection) {\n  var text = input.value;\n  text = text.slice(0, selection.start) + text.slice(selection.end);\n  input.value = text;\n  (0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.setCaretPosition)(input, selection.start);\n}\n/**\r\n * Parses and re-formats `<input/>` textual value.\r\n * E.g. when a user enters something into the `<input/>`\r\n * that raw input must first be parsed and the re-formatted properly.\r\n * Is called either after some user input (e.g. entered a character, pasted something)\r\n * or after the user performed an `operation` (e.g. \"Backspace\", \"Delete\").\r\n * @param  {DOMElement} input\r\n * @param  {Function} parse\r\n * @param  {Function} format\r\n * @param  {string} [operation] - The operation that triggered `<input/>` textual value change. E.g. \"Backspace\", \"Delete\".\r\n * @param  {Function} onChange\r\n */\n\n\nfunction formatInputText(input, _parse, _format, operation, on_change) {\n  // Parse `<input/>` textual value.\n  // Get the `value` and `caret` position.\n  var _parse2 = (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input.value, (0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.getCaretPosition)(input), _parse),\n      value = _parse2.value,\n      caret = _parse2.caret; // If a user performed an operation (\"Backspace\", \"Delete\")\n  // then apply that operation and get the new `value` and `caret` position.\n\n\n  if (operation) {\n    var newValueAndCaret = (0,_edit_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(value, caret, operation);\n    value = newValueAndCaret.value;\n    caret = newValueAndCaret.caret;\n  } // Format the `value`.\n  // (and reposition the caret accordingly)\n\n\n  var formatted = (0,_format_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(value, caret, _format);\n  var text = formatted.text;\n  caret = formatted.caret; // Set `<input/>` textual value manually\n  // to prevent React from resetting the caret position\n  // later inside a subsequent `render()`.\n  // Doesn't work for custom `inputComponent`s for some reason.\n\n  input.value = text; // Position the caret properly.\n\n  (0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.setCaretPosition)(input, caret); // If the `<input/>` textual value did change,\n  // then the parsed `value` may have changed too.\n\n  on_change(value);\n}\n//# sourceMappingURL=inputControl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/inputControl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/parse.js":
/*!****************************************************!*\
  !*** ./node_modules/input-format/modules/parse.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ parse)\n/* harmony export */ });\n// Parses the `text`.\n//\n// Returns `{ value, caret }` where `caret` is\n// the caret position inside `value`\n// corresponding to the `caret_position` inside `text`.\n//\n// The `text` is parsed by feeding each character sequentially to\n// `parse_character(character, value, context)` function\n// and appending the result (if it's not `undefined`) to `value`.\n//\n// `context` argument is just a utility empty object that is shared within the bounds\n// of parsing a single input string. The `_parse()` function could use that object\n// to store any kind of \"flags\" in it in order to alter its behavior based when\n// parsing next characters within the same string. Or it could completely ignore it.\n//\n// Example:\n//\n// `text` is `8 (800) 555-35-35`,\n// `caret_position` is `4` (before the first `0`).\n// `parse_character` is `(character, value) =>\n//   if (character >= '0' && character <= '9') { return character }`.\n//\n// then `parse()` outputs `{ value: '88005553535', caret: 2 }`.\n//\nfunction parse(text, caret_position, parse_character) {\n  var context = {};\n  var value = '';\n  var focused_input_character_index = 0;\n  var index = 0;\n\n  while (index < text.length) {\n    var character = parse_character(text[index], value, context);\n\n    if (character !== undefined) {\n      value += character;\n\n      if (caret_position !== undefined) {\n        if (caret_position === index) {\n          focused_input_character_index = value.length - 1;\n        } else if (caret_position > index) {\n          focused_input_character_index = value.length;\n        }\n      }\n    }\n\n    index++;\n  } // If caret position wasn't specified\n\n\n  if (caret_position === undefined) {\n    // Then set caret position to \"after the last input character\"\n    focused_input_character_index = value.length;\n  }\n\n  var result = {\n    value: value,\n    caret: focused_input_character_index\n  };\n  return result;\n}\n//# sourceMappingURL=parse.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/react/Input.js":
/*!**********************************************************!*\
  !*** ./node_modules/input-format/modules/react/Input.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var _inputControl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../inputControl.js */ \"(ssr)/./node_modules/input-format/modules/inputControl.js\");\nvar _excluded = [\"value\", \"parse\", \"format\", \"inputComponent\", \"onChange\", \"onKeyDown\"];\n\nfunction _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n// This is just `./ReactInput.js` rewritten in Hooks.\n\n\n // Usage:\n//\n// <ReactInput\n// \tvalue={this.state.phone}\n// \tonChange={phone => this.setState({ phone })}\n// \tparse={character => character}\n// \tformat={value => ({ text: value, template: 'xxxxxxxx' })}/>\n//\n\nfunction Input(_ref, ref) {\n  var value = _ref.value,\n      parse = _ref.parse,\n      format = _ref.format,\n      InputComponent = _ref.inputComponent,\n      onChange = _ref.onChange,\n      onKeyDown = _ref.onKeyDown,\n      rest = _objectWithoutProperties(_ref, _excluded);\n\n  var internalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  var setRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (instance) {\n    internalRef.current = instance;\n\n    if (ref) {\n      if (typeof ref === 'function') {\n        ref(instance);\n      } else {\n        ref.current = instance;\n      }\n    }\n  }, [ref]);\n\n  var _onChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {\n    return (0,_inputControl_js__WEBPACK_IMPORTED_MODULE_1__.onChange)(event, internalRef.current, parse, format, onChange);\n  }, [internalRef, parse, format, onChange]);\n\n  var _onKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {\n    if (onKeyDown) {\n      onKeyDown(event);\n    } // If `onKeyDown()` handler above has called `event.preventDefault()`\n    // then ignore this `keydown` event.\n\n\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    return (0,_inputControl_js__WEBPACK_IMPORTED_MODULE_1__.onKeyDown)(event, internalRef.current, parse, format, onChange);\n  }, [internalRef, parse, format, onChange, onKeyDown]);\n\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(InputComponent, _extends({}, rest, {\n    ref: setRef,\n    value: format(isEmptyValue(value) ? '' : value).text,\n    onKeyDown: _onKeyDown,\n    onChange: _onChange\n  }));\n}\n\nInput = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(Input);\nInput.propTypes = {\n  // Parses a single characher of `<input/>` text.\n  parse: prop_types__WEBPACK_IMPORTED_MODULE_2__.func.isRequired,\n  // Formats `value` into `<input/>` text.\n  format: prop_types__WEBPACK_IMPORTED_MODULE_2__.func.isRequired,\n  // Renders `<input/>` by default.\n  inputComponent: prop_types__WEBPACK_IMPORTED_MODULE_2__.elementType.isRequired,\n  // `<input/>` `type` attribute.\n  type: prop_types__WEBPACK_IMPORTED_MODULE_2__.string.isRequired,\n  // Is parsed from <input/> text.\n  value: prop_types__WEBPACK_IMPORTED_MODULE_2__.string,\n  // This handler is called each time `<input/>` text is changed.\n  onChange: prop_types__WEBPACK_IMPORTED_MODULE_2__.func.isRequired,\n  // Passthrough\n  onKeyDown: prop_types__WEBPACK_IMPORTED_MODULE_2__.func,\n  onCut: prop_types__WEBPACK_IMPORTED_MODULE_2__.func,\n  onPaste: prop_types__WEBPACK_IMPORTED_MODULE_2__.func\n};\nInput.defaultProps = {\n  // Renders `<input/>` by default.\n  inputComponent: 'input',\n  // `<input/>` `type` attribute.\n  type: 'text'\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Input);\n\nfunction isEmptyValue(value) {\n  return value === undefined || value === null;\n}\n//# sourceMappingURL=Input.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/react/Input.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/templateFormatter.js":
/*!****************************************************************!*\
  !*** ./node_modules/input-format/modules/templateFormatter.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _helpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers.js */ \"(ssr)/./node_modules/input-format/modules/helpers.js\");\n/* harmony import */ var _closeBraces_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./closeBraces.js */ \"(ssr)/./node_modules/input-format/modules/closeBraces.js\");\nfunction _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\n\n // Takes a `template` where character placeholders\n// are denoted by 'x'es (e.g. 'x (xxx) xxx-xx-xx').\n//\n// Returns a function which takes `value` characters\n// and returns the `template` filled with those characters.\n// If the `template` can only be partially filled\n// then it is cut off.\n//\n// If `should_close_braces` is `true`,\n// then it will also make sure all dangling braces are closed,\n// e.g. \"8 (8\" -> \"8 (8  )\" (iPhone style phone number input).\n//\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(template) {\n  var placeholder = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'x';\n  var should_close_braces = arguments.length > 2 ? arguments[2] : undefined;\n\n  if (!template) {\n    return function (value) {\n      return {\n        text: value\n      };\n    };\n  }\n\n  var characters_in_template = (0,_helpers_js__WEBPACK_IMPORTED_MODULE_0__.count_occurences)(placeholder, template);\n  return function (value) {\n    if (!value) {\n      return {\n        text: '',\n        template: template\n      };\n    }\n\n    var value_character_index = 0;\n    var filled_in_template = ''; // Using `.split('')` here instead of normal `for ... of`\n    // because the importing application doesn't neccessarily include an ES6 polyfill.\n    // The `.split('')` approach discards \"exotic\" UTF-8 characters\n    // (the ones consisting of four bytes)\n    // but template placeholder characters don't fall into that range\n    // and appending UTF-8 characters to a string in parts still works.\n\n    for (var _iterator = _createForOfIteratorHelperLoose(template.split('')), _step; !(_step = _iterator()).done;) {\n      var character = _step.value;\n\n      if (character !== placeholder) {\n        filled_in_template += character;\n        continue;\n      }\n\n      filled_in_template += value[value_character_index];\n      value_character_index++; // If the last available value character has been filled in,\n      // then return the filled in template\n      // (either trim the right part or retain it,\n      //  if no more character placeholders in there)\n\n      if (value_character_index === value.length) {\n        // If there are more character placeholders\n        // in the right part of the template\n        // then simply trim it.\n        if (value.length < characters_in_template) {\n          break;\n        }\n      }\n    }\n\n    if (should_close_braces) {\n      filled_in_template = (0,_closeBraces_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(filled_in_template, template);\n    }\n\n    return {\n      text: filled_in_template,\n      template: template\n    };\n  };\n}\n//# sourceMappingURL=templateFormatter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/templateFormatter.js\n");

/***/ })

};
;