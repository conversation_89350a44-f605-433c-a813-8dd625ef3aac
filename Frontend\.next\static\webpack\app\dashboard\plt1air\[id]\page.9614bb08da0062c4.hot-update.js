"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/components/hook-form/rhf-text-field.tsx":
/*!*****************************************************!*\
  !*** ./src/components/hook-form/rhf-text-field.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RHFTextField: () => (/* binding */ RHFTextField)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _mui_material_TextField__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material/TextField */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n\nvar _s = $RefreshSig$();\n\n\nfunction RHFTextField(param) {\n    let { name, helperText, slotProps, type = 'text', ...other } = param;\n    _s();\n    const { control } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_1__.useFormContext)();\n    const isNumberType = type === 'number';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_1__.Controller, {\n        name: name,\n        control: control,\n        render: (param)=>{\n            let { field, fieldState: { error } } = param;\n            var _error_message;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TextField__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                ...field,\n                fullWidth: true,\n                value: isNumberType ? transformValue(field.value) : field.value,\n                onChange: (event)=>{\n                    const transformedValue = isNumberType ? transformValueOnChange(event.target.value) : event.target.value;\n                    field.onChange(transformedValue);\n                },\n                onBlur: (event)=>{\n                    const transformedValue = isNumberType ? transformValueOnBlur(event.target.value) : event.target.value;\n                    field.onChange(transformedValue);\n                },\n                type: isNumberType ? 'text' : type,\n                error: !!error,\n                helperText: (_error_message = error === null || error === void 0 ? void 0 : error.message) !== null && _error_message !== void 0 ? _error_message : helperText,\n                slotProps: {\n                    ...slotProps,\n                    htmlInput: {\n                        autoComplete: 'off',\n                        ...slotProps === null || slotProps === void 0 ? void 0 : slotProps.htmlInput,\n                        ...isNumberType && {\n                            inputMode: 'decimal',\n                            pattern: '[0-9]*\\\\.?[0-9]*'\n                        }\n                    }\n                },\n                ...other\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\components\\\\hook-form\\\\rhf-text-field.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, void 0);\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\components\\\\hook-form\\\\rhf-text-field.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n_s(RHFTextField, \"zyAxkz+Wq3InUdCKNlVVi99oElQ=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_1__.useFormContext\n    ];\n});\n_c = RHFTextField;\nvar _c;\n$RefreshReg$(_c, \"RHFTextField\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/hook-form/rhf-text-field.tsx\n"));

/***/ })

});