'use client';

import { useEffect } from 'react';
import { SplashScreen } from 'src/components/loading-screen';
import { useRouter } from 'src/routes/hooks';
import { JWT_STORAGE_KEY, requiresOnboarding } from 'src/auth/context/jwt';
import { paths } from 'src/routes/paths';
import { useAuthContext } from 'src/auth/hooks';

export default function CallbackPage() {
  const router = useRouter();

  const { loading } = useAuthContext();

  const redirect = async (): Promise<void> => {
    if (loading) {
      return;
    }

    const accessToken = sessionStorage.getItem(JWT_STORAGE_KEY);

    if (accessToken && requiresOnboarding(accessToken!)) {
      router.push(paths.auth.auth0.onboarding);
      return;
    }

    if (accessToken && requiresOnboarding(accessToken!) == false) {
      const returnUrl = localStorage.getItem('returnUrl') || paths.dashboard.root;
      localStorage.removeItem('returnUrl');
      router.push(returnUrl);
    }
  };

  useEffect(() => {
    redirect();
  }, [loading]);

  return <SplashScreen />;
} 