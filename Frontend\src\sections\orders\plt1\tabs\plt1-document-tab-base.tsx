'use client';

import { ReactNode } from 'react';
import { UseFieldArrayReturn, useFormContext } from 'react-hook-form';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import CardHeader from '@mui/material/CardHeader';
import Collapse from '@mui/material/Collapse';
import Tooltip from '@mui/material/Tooltip';

import { useTranslate } from 'src/locales';
import { Iconify } from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';
import { EmptyContent } from 'src/components/empty-content';
import { PLT1Order } from '../types/plt1-details.types';

// ----------------------------------------------------------------------

export interface PLT1DocumentTabBaseProps {
  t1OrderId: string;
  order?: PLT1Order;
  readOnly?: boolean;
  title: string;
  emptyTitle: string;
  emptyDescription: string;
  fieldArrayName: string;
  expandedIndex: number | null;
  deleteIndex: number | null;
  openConfirm: boolean;
  fieldArray: UseFieldArrayReturn<any, any, 'id'>;
  onToggleExpand: (index: number) => void;
  onOpenConfirm: (index: number) => void;
  onCloseConfirm: () => void;
  onDelete: () => void;
  onAdd: () => void;
  renderPreview: (item: any, index: number) => ReactNode;
  renderForm: (index: number) => ReactNode;
  getItemTitle: (item: any) => ReactNode;
}

export default function PLT1DocumentTabBase({
  readOnly = false,
  title,
  emptyTitle,
  emptyDescription,
  expandedIndex,
  deleteIndex,
  openConfirm,
  fieldArray,
  fieldArrayName,
  onToggleExpand,
  onOpenConfirm,
  onCloseConfirm,
  onDelete,
  onAdd,
  renderPreview,
  renderForm,
  getItemTitle,
}: PLT1DocumentTabBaseProps) {
  const { t } = useTranslate();
  const { getValues } = useFormContext();

  const { fields } = fieldArray;
  // Use the provided fieldArrayName prop instead of trying to access fieldArray.name

  return (
    <>
      <Stack spacing={3}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Typography variant="h6">{title}</Typography>

          {!readOnly && (
            <Button
              variant="contained"
              startIcon={<Iconify icon="mingcute:add-line" />}
              onClick={onAdd}
            >
              {t('common.addNew')}
            </Button>
          )}
        </Box>

        {fields.length === 0 ? (
          <EmptyContent
            title={emptyTitle}
            description={emptyDescription}
            sx={{ py: 5 }}
          />
        ) : (
          fields.map((field, index) => {
            const item = getValues(`${fieldArrayName}.${index}`);
            const isExpanded = expandedIndex === index;

            return (
              <Card key={field.id} sx={{ mb: 3 }}>
                <CardHeader
                  title={getItemTitle(item)}
                  action={
                    <Stack direction="row" spacing={1}>
                      <Tooltip title={isExpanded ? t('common.collapse') : t('common.expand')}>
                        <IconButton onClick={() => onToggleExpand(index)} disabled={readOnly && !isExpanded}>
                          <Iconify icon={isExpanded ? 'eva:chevron-up-fill' : 'eva:chevron-down-fill'} />
                        </IconButton>
                      </Tooltip>

                      {!readOnly && (
                        <Tooltip title={t('common.delete')}>
                          <IconButton
                            color="error"
                            onClick={() => onOpenConfirm(index)}
                          >
                            <Iconify icon="eva:trash-2-outline" />
                          </IconButton>
                        </Tooltip>
                      )}
                    </Stack>
                  }
                  sx={{
                    pb: isExpanded ? 0 : 2,
                    '& .MuiCardHeader-action': { alignSelf: 'center' }
                  }}
                />

                {!isExpanded && renderPreview(item, index)}

                <Collapse in={isExpanded}>
                  {isExpanded && (
                    <>
                      <Divider sx={{ borderStyle: 'dashed' }} />
                      <Box sx={{ p: 3 }}>
                        {renderForm(index)}
                      </Box>
                    </>
                  )}
                </Collapse>
              </Card>
            );
          })
        )}

        {fields.length > 0 && !readOnly && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}>
            <Button
              variant="soft"
              color="primary"
              startIcon={<Iconify icon="mingcute:add-line" />}
              onClick={onAdd}
            >
              {t('common.addNew')}
            </Button>
          </Box>
        )}
      </Stack>

      <ConfirmDialog
        open={openConfirm}
        onClose={onCloseConfirm}
        title={t('common.deleteConfirmation')}
        content={t('common.deleteConfirmationMessage')}
        action={
          <Button
            variant="contained"
            color="error"
            onClick={onDelete}
            autoFocus
          >
            {t('common.delete')}
          </Button>
        }
      />
    </>
  );
}

