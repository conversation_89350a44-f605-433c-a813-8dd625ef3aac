"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1sea/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-merchandise-positions-form.tsx":
/*!****************************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-merchandise-positions-form.tsx ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1MerchandisePositionsForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material/Box */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/Card */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material/Stack */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material_Button__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/material/Button */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/material/Divider */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/CardHeader */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/Typography */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/IconButton */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _mui_material_Table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material/Table */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _mui_material_TableBody__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mui/material/TableBody */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material/TableCell */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _mui_material_TableContainer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material/TableContainer */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _mui_material_TableHead__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/TableHead */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/TableRow */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _mui_material_Paper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/Paper */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _mui_material_Dialog__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/material/Dialog */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _mui_material_DialogTitle__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/material/DialogTitle */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _mui_material_DialogContent__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/material/DialogContent */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _mui_material_DialogActions__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/material/DialogActions */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/Tooltip */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/material/Grid2 */ \"(app-pages-browser)/./node_modules/@mui/material/Grid2/Grid2.js\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/custom-dialog */ \"(app-pages-browser)/./src/components/custom-dialog/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst DEFAULT_POSITION = {\n    id: null,\n    invoicePositionNumber: 0,\n    goodsDescription: '',\n    translatedGoodsDescription: '',\n    packageMarks: '',\n    numberOfPackages: 0,\n    packageType: '',\n    packageTypeDescription: '',\n    commodityCode: '',\n    fullTariffCode: '',\n    grossMass: 0,\n    grossMassUnit: 'kg',\n    netMass: 0,\n    netMassUnit: 'kg',\n    positionValue: 0,\n    positionValueCurrency: 'USD',\n    containerNumbers: [],\n    previousDocuments: [],\n    merchandisePositionsId: ''\n};\nfunction PLT1MerchandisePositionsForm(param) {\n    let { t1OrderId, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_4__.useTranslate)();\n    const { control, watch, getValues } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFormContext)();\n    const fieldArray = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFieldArray)({\n        control,\n        name: 'merchandisePositions.positions'\n    });\n    const merchandisePositions = watch('merchandisePositions');\n    // State for position dialog\n    const [openPositionDialog, setOpenPositionDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPositionIndex, setCurrentPositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletePositionIndex, setDeletePositionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Handle opening the position dialog for adding a new position\n    const handleAddPosition = ()=>{\n        setCurrentPositionIndex(null);\n        // Add a new position with default values\n        const newPosition = {\n            ...DEFAULT_POSITION,\n            merchandisePositionsId: (merchandisePositions === null || merchandisePositions === void 0 ? void 0 : merchandisePositions.id) || ''\n        };\n        fieldArray.append(newPosition);\n        setCurrentPositionIndex(fieldArray.fields.length); // Set to the new index\n        setOpenPositionDialog(true);\n    };\n    // Handle opening the position dialog for editing an existing position\n    const handleEditPosition = (index)=>{\n        setCurrentPositionIndex(index);\n        setOpenPositionDialog(true);\n    };\n    // Handle closing the position dialog\n    const handleClosePositionDialog = ()=>{\n        setOpenPositionDialog(false);\n        // If we were adding a new position and user cancels, remove the empty position\n        if (currentPositionIndex !== null && currentPositionIndex === fieldArray.fields.length - 1) {\n            const position = getValues(\"merchandisePositions.positions.\".concat(currentPositionIndex));\n            // Check if it's an empty position (all fields are default values)\n            const isEmpty = !position.goodsDescription && !position.translatedGoodsDescription && !position.packageMarks && !position.commodityCode && !position.fullTariffCode && position.numberOfPackages === 0 && position.grossMass === 0 && position.netMass === 0;\n            if (isEmpty) {\n                fieldArray.remove(currentPositionIndex);\n            }\n        }\n        setCurrentPositionIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeletePositionIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deletePositionIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting a position\n    const handleDeletePosition = ()=>{\n        if (deletePositionIndex !== null) {\n            fieldArray.remove(deletePositionIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving a position (just close the dialog since form is already updated)\n    const handleSavePosition = ()=>{\n        handleClosePositionDialog();\n    };\n    // Render the position form in the dialog - using React Hook Form fields\n    const renderPositionForm = ()=>{\n        if (currentPositionIndex === null) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".invoicePositionNumber\"),\n                    label: t('plt1.details.documents.merchandisePositions.form.invoicePositionNumber'),\n                    type: \"number\",\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 3,\n                        pt: 2,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(2, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".goodsDescription\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.goodsDescription'),\n                            multiline: true,\n                            rows: 2,\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".translatedGoodsDescription\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.translatedGoodsDescription'),\n                            multiline: true,\n                            rows: 2,\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 2.5,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(3, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".numberOfPackages\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.numberOfPackages'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".packageType\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.packageType'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".packageTypeDescription\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.packageTypeDescription'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".packageMarks\"),\n                    label: t('plt1.details.documents.merchandisePositions.form.packageMarks'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 3,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(2, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".commodityCode\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.commodityCode'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".fullTariffCode\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.fullTariffCode'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        rowGap: 3,\n                        columnGap: 2,\n                        display: 'grid',\n                        gridTemplateColumns: {\n                            xs: 'repeat(1, 1fr)',\n                            md: 'repeat(4, 1fr)'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".grossMass\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.grossMass'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".grossMassUnit\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.grossMassUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".netMass\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.netMass'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"merchandisePositions.positions.\".concat(currentPositionIndex, \".netMassUnit\"),\n                            label: t('plt1.details.documents.merchandisePositions.form.netMassUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the positions table\n    const renderPositionsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableContainer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            component: _mui_material_Paper__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Table__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"merchandise positions table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableHead__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.goodsDescription')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.numberOfPackages')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.packageType')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.grossMass')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.merchandisePositions.form.netMass')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableBody__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        children: fieldArray.fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                colSpan: 6,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.merchandisePositions.noPositions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 13\n                        }, this) : fieldArray.fields.map((field, index)=>{\n                            const position = getValues(\"merchandisePositions.positions.\".concat(index)) || {};\n                            var _position_numberOfPackages, _position_grossMass, _position_netMass;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableRow__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                variant: \"body2\",\n                                                sx: {\n                                                    maxWidth: 200\n                                                },\n                                                children: position.goodsDescription || '-'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 21\n                                            }, this),\n                                            position.translatedGoodsDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                variant: \"caption\",\n                                                color: \"text.secondary\",\n                                                sx: {\n                                                    display: 'block',\n                                                    mt: 0.5\n                                                },\n                                                children: position.translatedGoodsDescription\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: (_position_numberOfPackages = position.numberOfPackages) !== null && _position_numberOfPackages !== void 0 ? _position_numberOfPackages : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: position.packageTypeDescription || position.packageType || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_position_grossMass = position.grossMass) !== null && _position_grossMass !== void 0 ? _position_grossMass : '-',\n                                            \" \",\n                                            position.grossMassUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_position_netMass = position.netMass) !== null && _position_netMass !== void 0 ? _position_netMass : '-',\n                                            \" \",\n                                            position.netMassUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TableCell__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditPosition(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deletePositionIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n            lineNumber: 303,\n            columnNumber: 5\n        }, this);\n    // Render the totals summary\n    const renderTotalsSummary = ()=>{\n        const totals = merchandisePositions === null || merchandisePositions === void 0 ? void 0 : merchandisePositions.totals;\n        if (!totals) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            sx: {\n                boxShadow: 'none',\n                mb: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    title: t('plt1.details.documents.merchandisePositions.form.totalsTitle')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    sx: {\n                        borderStyle: 'dashed'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalNumberOfPositions')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: totals.totalNumberOfPositions || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalNumberOfPackages')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: totals.totalNumberOfPackages || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalGrossMass')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: [\n                                                totals.totalGrossMass || 0,\n                                                \" \",\n                                                totals.totalGrossMassUnit || ''\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                size: {\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            sx: {\n                                                mb: 0.5\n                                            },\n                                            children: t('plt1.details.documents.merchandisePositions.form.totalNetMass')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"h6\",\n                                            children: [\n                                                totals.totalNetMass || 0,\n                                                \" \",\n                                                totals.totalNetMassUnit || ''\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 399,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n            lineNumber: 394,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        spacing: 3,\n        children: [\n            renderTotalsSummary(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        title: t('plt1.details.documents.merchandisePositions.form.positionsTitle'),\n                        action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            variant: \"contained\",\n                            size: \"small\",\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                icon: \"eva:plus-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: handleAddPosition,\n                            sx: {\n                                mb: 2\n                            },\n                            disabled: readOnly,\n                            children: t('plt1.details.documents.merchandisePositions.addPosition')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: [\n                            renderPositionsTable(),\n                            fieldArray.fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                sx: {\n                                    mt: 2,\n                                    display: 'flex',\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    variant: \"soft\",\n                                    color: \"primary\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_2__.Iconify, {\n                                        icon: \"eva:plus-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    onClick: handleAddPosition,\n                                    disabled: readOnly,\n                                    children: t('plt1.details.documents.merchandisePositions.addPosition')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 453,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Dialog__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                open: openPositionDialog,\n                onClose: handleClosePositionDialog,\n                maxWidth: \"md\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_DialogTitle__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        children: currentPositionIndex === null ? t('plt1.details.documents.merchandisePositions.addPosition') : t('plt1.details.documents.merchandisePositions.editPosition')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 496,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_DialogContent__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        children: renderPositionForm()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 501,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_DialogActions__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                onClick: handleClosePositionDialog,\n                                children: t('common.cancel')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                variant: \"contained\",\n                                onClick: handleSavePosition,\n                                children: t('common.save')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 490,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__.ConfirmDialog, {\n                open: openDeleteConfirm,\n                onClose: handleCloseDeleteConfirm,\n                title: t('plt1.details.documents.merchandisePositions.confirmDeleteDialog.title'),\n                content: t('plt1.details.documents.merchandisePositions.confirmDeleteDialog.content'),\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                    variant: \"contained\",\n                    color: \"error\",\n                    onClick: handleDeletePosition,\n                    children: t('common.delete')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                    lineNumber: 521,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n                lineNumber: 515,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-merchandise-positions-form.tsx\",\n        lineNumber: 448,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1MerchandisePositionsForm, \"rzlgvldrAdFsKfES/DPx4X+02i8=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_4__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFormContext,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useFieldArray\n    ];\n});\n_c = PLT1MerchandisePositionsForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1MerchandisePositionsForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-merchandise-positions-form.tsx\n"));

/***/ })

});