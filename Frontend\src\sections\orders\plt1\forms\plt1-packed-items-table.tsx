import { useFormContext, useFieldArray } from 'react-hook-form';
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  IconButton,
  Tooltip,
  Stack,
} from '@mui/material';

import { useTranslate } from 'src/locales';
import { Field } from 'src/components/hook-form';
import { Iconify } from 'src/components/iconify';

// Default packed item structure
const DEFAULT_PACKED_ITEM = {
  id: null,
  name: '',
  hsCode: '',
  modelNumber: '',
  quantity: 0,
  itemNetWeight: 0,
  itemNetWeightUnit: 'KGM',
  itemGrossWeight: 0,
  hsCodeHints: '',
  packingListPositionId: null,
};

interface PLT1PackedItemsTableProps {
  fieldName: string;
  readOnly?: boolean;
}

export default function PLT1PackedItemsTable({ fieldName, readOnly = false }: PLT1PackedItemsTableProps) {
  const { t } = useTranslate();
  const { control } = useFormContext();

  // UseFieldArray for packed items
  const packedItemsFieldArray = useFieldArray({
    control,
    name: fieldName,
  });

  // Handle adding a new packed item
  const handleAddPackedItem = () => {
    const newPackedItem = { ...DEFAULT_PACKED_ITEM };
    packedItemsFieldArray.append(newPackedItem);
  };

  // Handle removing a packed item
  const handleRemovePackedItem = (itemIndex: number) => {
    packedItemsFieldArray.remove(itemIndex);
  };

  return (
    <Box>
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
        <Typography variant="h6">
          {t('plt1.details.documents.packingList.packedItem.title')}
        </Typography>
        <Button
          variant="outlined"
          size="small"
          startIcon={<Iconify icon="eva:plus-fill" />}
          onClick={handleAddPackedItem}
          disabled={readOnly}
        >
          {t('plt1.details.documents.packingList.packedItem.addNew')}
        </Button>
      </Stack>

      <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
        <Table size="small" stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>{t('plt1.details.documents.packingList.packedItem.name')}</TableCell>
              <TableCell>{t('plt1.details.documents.packingList.packedItem.modelNumber')}</TableCell>
              <TableCell>{t('plt1.details.documents.packingList.packedItem.quantity')}</TableCell>
              <TableCell>{t('plt1.details.documents.packingList.packedItem.itemNetWeight')}</TableCell>
              <TableCell>{t('plt1.details.documents.packingList.packedItem.itemGrossWeight')}</TableCell>
              <TableCell align="right">{t('common.actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {packedItemsFieldArray.fields.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  <Typography variant="body2" color="text.secondary">
                    {t('plt1.details.documents.packingList.packedItem.noItems')}
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              packedItemsFieldArray.fields.map((field, itemIndex) => (
                <TableRow key={field.id}>
                  <TableCell sx={{ minWidth: 150 }}>
                    <Field.Text
                      name={`${fieldName}.${itemIndex}.name`}
                      size="small"
                      disabled={readOnly}
                      sx={{ '& .MuiInputBase-root': { fontSize: '0.875rem' } }}
                    />
                  </TableCell>
                  <TableCell sx={{ minWidth: 120 }}>
                    <Field.Text
                      name={`${fieldName}.${itemIndex}.modelNumber`}
                      size="small"
                      disabled={readOnly}
                      sx={{ '& .MuiInputBase-root': { fontSize: '0.875rem' } }}
                    />
                  </TableCell>
                  <TableCell sx={{ minWidth: 100 }}>
                    <Field.Text
                      name={`${fieldName}.${itemIndex}.quantity`}
                      type="number"
                      size="small"
                      disabled={readOnly}
                      sx={{ '& .MuiInputBase-root': { fontSize: '0.875rem' } }}
                    />
                  </TableCell>
                  <TableCell sx={{ minWidth: 120 }}>
                    <Field.Text
                      name={`${fieldName}.${itemIndex}.itemNetWeight`}
                      type="number"
                      size="small"
                      disabled={readOnly}
                      sx={{ '& .MuiInputBase-root': { fontSize: '0.875rem' } }}
                    />
                  </TableCell>
                  <TableCell sx={{ minWidth: 120 }}>
                    <Field.Text
                      name={`${fieldName}.${itemIndex}.itemGrossWeight`}
                      type="number"
                      size="small"
                      disabled={readOnly}
                      sx={{ '& .MuiInputBase-root': { fontSize: '0.875rem' } }}
                    />
                  </TableCell>
                  <TableCell align="right">
                    <Tooltip title={t('common.delete')}>
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleRemovePackedItem(itemIndex)}
                        disabled={readOnly}
                      >
                        <Iconify icon="eva:trash-2-outline" />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}
