"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1road/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/plt1-road-document-tabs.tsx":
/*!**************************************************************!*\
  !*** ./src/sections/orders/plt1/plt1-road-document-tabs.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1RoadDocumentsTabs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material/Box */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _mui_material_Tab__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/Tab */ \"(app-pages-browser)/./node_modules/@mui/material/Tab/Tab.js\");\n/* harmony import */ var _mui_material_Tabs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/Tabs */ \"(app-pages-browser)/./node_modules/@mui/material/Tabs/Tabs.js\");\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material/Card */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material/Divider */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/CardHeader */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var _tabs_road_plt1_cmr_tab__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tabs/road/plt1-cmr-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/road/plt1-cmr-tab.tsx\");\n/* harmony import */ var _tabs_plt1_commercial_invoice_tab__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tabs/plt1-commercial-invoice-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-commercial-invoice-tab.tsx\");\n/* harmony import */ var _tabs_plt1_packing_list_tab__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tabs/plt1-packing-list-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-packing-list-tab.tsx\");\n/* harmony import */ var _tabs_plt1_notifications_arrival_tab__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./tabs/plt1-notifications-arrival-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-notifications-arrival-tab.tsx\");\n/* harmony import */ var _tabs_plt1_transit_tab__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./tabs/plt1-transit-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-transit-tab.tsx\");\n/* harmony import */ var _tabs_plt1_merchandise_positions_tab__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./tabs/plt1-merchandise-positions-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-merchandise-positions-tab.tsx\");\n// src/sections/plt1-road-order-details/plt1-road-document-tabs.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// ----------------------------------------------------------------------\nvar TabNames = /*#__PURE__*/ function(TabNames) {\n    TabNames[\"CMR\"] = \"cmr\";\n    TabNames[\"COMMERCIAL_INVOICES\"] = \"commercialInvoices\";\n    TabNames[\"PACKING_LISTS\"] = \"packingLists\";\n    TabNames[\"NOTIFICATIONS\"] = \"notifications\";\n    TabNames[\"TRANSIT_DOCUMENTS\"] = \"transitDocuments\";\n    TabNames[\"MERCHANDISE_POSITIONS\"] = \"merchandisePositions\";\n    return TabNames;\n}(TabNames || {});\nfunction PLT1RoadDocumentsTabs(param) {\n    let { t1OrderId, order, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    // Try to restore the expanded state from sessionStorage\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"PLT1RoadDocumentsTabs.useState\": ()=>{\n            const savedTab = sessionStorage.getItem(\"plt1-road-current-tab-\".concat(t1OrderId));\n            return savedTab && Object.values(TabNames).includes(savedTab) ? savedTab : \"cmr\";\n        }\n    }[\"PLT1RoadDocumentsTabs.useState\"]);\n    // Save tab selection to sessionStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1RoadDocumentsTabs.useEffect\": ()=>{\n            sessionStorage.setItem(\"plt1-road-current-tab-\".concat(t1OrderId), currentTab);\n        }\n    }[\"PLT1RoadDocumentsTabs.useEffect\"], [\n        currentTab,\n        t1OrderId\n    ]);\n    const handleChangeTab = (event, newValue)=>{\n        setCurrentTab(newValue);\n    };\n    const TABS = [\n        {\n            value: \"cmr\",\n            label: t('plt1.details.documents.tabs.cmr'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_road_plt1_cmr_tab__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 64,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"commercialInvoices\",\n            label: t('plt1.details.documents.tabs.commercialInvoices'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_commercial_invoice_tab__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 69,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"packingLists\",\n            label: t('plt1.details.documents.tabs.packingLists'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_packing_list_tab__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 74,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"notifications\",\n            label: t('plt1.details.documents.tabs.notificationsOfArrival'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_notifications_arrival_tab__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 79,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"transitDocuments\",\n            label: t('plt1.details.documents.tabs.transitDocuments'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_transit_tab__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 84,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"merchandisePositions\",\n            label: t('plt1.details.documents.tabs.merchandisePositions'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_merchandise_positions_tab__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 89,\n                columnNumber: 24\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                title: t('plt1.details.documents.heading'),\n                sx: {\n                    mb: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 95,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                sx: {\n                    borderStyle: 'dashed'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 99,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tabs__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                value: currentTab,\n                onChange: handleChangeTab,\n                sx: {\n                    px: 2.5,\n                    boxShadow: (theme)=>\"inset 0 -2px 0 0 \".concat(theme.palette.divider)\n                },\n                children: TABS.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tab__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        value: tab.value,\n                        label: tab.label\n                    }, tab.value, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 21\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                lineNumber: 100,\n                columnNumber: 13\n            }, this),\n            TABS.map((tab)=>tab.value === currentTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: tab.component\n                }, tab.value, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 25\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1-road-document-tabs.tsx\",\n        lineNumber: 94,\n        columnNumber: 9\n    }, this);\n}\n_s(PLT1RoadDocumentsTabs, \"xZNw/UDA+Vc50l0DEB7kcPEGJZo=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate\n    ];\n});\n_c = PLT1RoadDocumentsTabs;\nvar _c;\n$RefreshReg$(_c, \"PLT1RoadDocumentsTabs\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/plt1-road-document-tabs.tsx\n"));

/***/ })

});