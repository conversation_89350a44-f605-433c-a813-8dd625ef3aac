import { axiosInstance, endpoints } from 'src/lib/axios';
import { PLT1OrderFile } from './plt1-file-management';

interface CachedFile {
  url: string;
  blob: Blob;
  contentType: string;
  lastAccessed: number;
}

// File status for tracking prefetch status
type FileStatus = 'not-cached' | 'prefetching' | 'cached';

// Priority queue item
interface PriorityQueueItem {
  fileId: string;
  priority: number; // Higher number = higher priority
}

class PLT1FileCache {
  private cache: Map<string, CachedFile> = new Map();
  private prefetchQueue: PriorityQueueItem[] = [];
  private prefetchingFiles: Set<string> = new Set(); // Track files currently being prefetched
  private isPrefetching = false;
  private maxCacheSize = 50; // Maximum number of files to keep in cache
  private maxConcurrentPrefetches = 3; // Maximum number of concurrent prefetch operations

  // Get a file from the cache or download it if not cached
  async getFile(file: PLT1OrderFile): Promise<{ url: string; contentType: string }> {
    // Check if the file is already in the cache
    const cachedFile = this.cache.get(file.id);
    if (cachedFile) {
      // Update last accessed time
      cachedFile.lastAccessed = Date.now();
      return { url: cachedFile.url, contentType: cachedFile.contentType };
    }

    // If not in cache, download the file
    return this.downloadFile(file);
  }

  // Check if a file is in the cache or being prefetched
  getFileStatus(fileId: string): FileStatus {
    if (this.cache.has(fileId)) {
      return 'cached';
    }
    if (this.prefetchingFiles.has(fileId)) {
      return 'prefetching';
    }
    return 'not-cached';
  }

  // Download a file and add it to the cache
  private async downloadFile(file: PLT1OrderFile): Promise<{ url: string; contentType: string }> {
    try {
      // Use axios to get the file with proper authentication headers
      const response = await axiosInstance.get(`${endpoints.plt1.downloadFile}/${file.id}`, {
        responseType: 'blob',
      });

      // Create a blob URL from the response data
      const blob = new Blob([response.data], { type: response.headers['content-type'] });
      const url = window.URL.createObjectURL(blob);
      const contentType = response.headers['content-type'];

      // Add to cache
      this.cache.set(file.id, {
        url,
        blob,
        contentType,
        lastAccessed: Date.now(),
      });

      // Clean up cache if it's too large
      this.cleanupCache();

      return { url, contentType };
    } catch (error) {
      console.error('Error downloading file:', error);
      throw error;
    }
  }

  // Prefetch files in the background with priority
  async prefetchFiles(files: PLT1OrderFile[], priority = 1): Promise<void> {
    // Filter out files that are already cached or being prefetched
    const filesToPrefetch = files.filter(file =>
      !this.cache.has(file.id) && !this.prefetchingFiles.has(file.id)
    );

    // Add files to the prefetch queue with priority
    this.prefetchQueue = [
      ...this.prefetchQueue,
      ...filesToPrefetch.map(file => ({ fileId: file.id, priority })),
    ];

    // Sort the queue by priority (higher priority first)
    this.prefetchQueue.sort((a, b) => b.priority - a.priority);

    // Start prefetching if not already in progress
    if (!this.isPrefetching) {
      this.startPrefetching();
    }
  }

  // Prefetch a specific file with high priority
  async prefetchFile(file: PLT1OrderFile, priority = 10): Promise<void> {
    // If already cached or being prefetched, do nothing
    if (this.cache.has(file.id) || this.prefetchingFiles.has(file.id)) {
      return;
    }

    // Add to queue with high priority
    this.prefetchQueue.push({ fileId: file.id, priority });

    // Sort the queue by priority
    this.prefetchQueue.sort((a, b) => b.priority - a.priority);

    // Start prefetching if not already in progress
    if (!this.isPrefetching) {
      this.startPrefetching();
    }
  }

  // Start prefetching files from the queue
  private async startPrefetching(): Promise<void> {
    if (this.prefetchQueue.length === 0 || this.isPrefetching) {
      return;
    }

    this.isPrefetching = true;

    // Process the queue with concurrent prefetching
    const processBatch = async () => {
      // Get the next batch of files to prefetch
      const currentBatchSize = Math.min(
        this.maxConcurrentPrefetches - this.prefetchingFiles.size,
        this.prefetchQueue.length
      );

      if (currentBatchSize <= 0) {
        // If we can't process more files right now, wait a bit and check again
        setTimeout(processBatch, 100);
        return;
      }

      // Get the next batch of files from the queue
      const batch = this.prefetchQueue.splice(0, currentBatchSize);

      // Start prefetching each file in the batch
      const prefetchPromises = batch.map(async ({ fileId }) => {
        // Mark as being prefetched
        this.prefetchingFiles.add(fileId);

        try {
          // Skip if already cached (could have been added since we queued it)
          if (this.cache.has(fileId)) {
            this.prefetchingFiles.delete(fileId);
            return;
          }

          // Use axios to get the file with proper authentication headers
          const response = await axiosInstance.get(`${endpoints.plt1.downloadFile}/${fileId}`, {
            responseType: 'blob',
          });

          // Create a blob URL from the response data
          const blob = new Blob([response.data], { type: response.headers['content-type'] });
          const url = window.URL.createObjectURL(blob);
          const contentType = response.headers['content-type'];

          // Add to cache
          this.cache.set(fileId, {
            url,
            blob,
            contentType,
            lastAccessed: Date.now(),
          });

          // Clean up cache if it's too large
          this.cleanupCache();
        } catch (error) {
          console.error(`Error prefetching file ${fileId}:`, error);
        } finally {
          // Remove from prefetching set
          this.prefetchingFiles.delete(fileId);
        }
      });

      // Wait for all prefetch operations to complete
      await Promise.all(prefetchPromises);

      // Continue processing if there are more files in the queue
      if (this.prefetchQueue.length > 0) {
        processBatch();
      } else {
        this.isPrefetching = false;
      }
    };

    // Start processing the queue
    processBatch();
  }

  // Clean up the cache if it's too large
  private cleanupCache(): void {
    if (this.cache.size <= this.maxCacheSize) {
      return;
    }

    // Sort files by last accessed time
    const sortedFiles = Array.from(this.cache.entries()).sort(
      ([, a], [, b]) => a.lastAccessed - b.lastAccessed
    );

    // Remove oldest files until we're under the limit
    const filesToRemove = sortedFiles.slice(0, this.cache.size - this.maxCacheSize);
    for (const [fileId, cachedFile] of filesToRemove) {
      // Revoke the object URL to free up memory
      window.URL.revokeObjectURL(cachedFile.url);
      this.cache.delete(fileId);
    }
  }

  // Clear the entire cache
  clearCache(): void {
    for (const [, cachedFile] of this.cache.entries()) {
      window.URL.revokeObjectURL(cachedFile.url);
    }
    this.cache.clear();
    this.prefetchQueue = [];
    this.prefetchingFiles.clear();
    this.isPrefetching = false;
  }
}

// Create a singleton instance
export const fileCache = new PLT1FileCache();
