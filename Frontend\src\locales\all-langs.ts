'use client';

// core (MUI)
import {
  enUS as enUSCore,
  plPL as plPLNCore,
} from '@mui/material/locale';
// date pickers (MUI)
import {
  enUS as enUSDate,
  plPL as plPLNDate,
} from '@mui/x-date-pickers/locales';
// data grid (MUI)
import {
  enUS as enUSDataGrid,
  plPL as plPLNDataGrid,
} from '@mui/x-data-grid/locales';

// ----------------------------------------------------------------------

export const allLangs = [
  {
    value: 'pl',
    label: 'Polski',
    countryCode: 'PL',
    adapterLocale: 'pl',
    numberFormat: { code: 'pl-PL', currency: 'PLN' },
    systemValue: {
      components: { ...plPLNCore.components, ...plPLNDate.components, ...plPLNDataGrid.components },
    },
  },
  {
    value: 'en',
    label: 'English',
    countryCode: 'GB',
    adapterLocale: 'en',
    numberFormat: { code: 'en-US', currency: 'USD' },
    systemValue: {
      components: { ...enUSCore.components, ...enUSDate.components, ...enUSDataGrid.components },
    }
  }
];

/**
 * Country code:
 * https://flagcdn.com/en/codes.json
 *
 * Number format code:
 * https://gist.github.com/raushankrjha/d1c7e35cf87e69aa8b4208a8171a8416
 */
