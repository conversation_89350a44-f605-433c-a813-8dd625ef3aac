'use client';

// src/sections/plt1-road-order-details/PLT1RoadOrderDetailsView.tsx
import { useCallback, useState, useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { endpoints } from 'src/lib/axios';
import { useTranslate } from 'src/locales';
import { usePLT1OrderStatus } from '../hooks/use-plt1-status';
import { PLT1OrderDetailsBase } from './plt1-order-details-base';
import PLT1RoadDocumentsTabs from '../plt1-road-document-tabs';
import { 
  PLT1CommercialInvoice, 
  PLT1CustomsOffice, 
  PLT1MerchandisePositions, 
  PLT1NotificationOfArrival, 
  PLT1PackingList, 
  PLT1RoadCMR, 
  PLT1TransitDocument, 
  PLT1VehicleRegistration 
} from '../types/plt1-details.types';
import { usePLT1RoadOrderDetails } from '../hooks/use-plt1road-details';
import { handlePLT1OrderSave, hasFormChanges } from '../utils/order-details-utils';

// ----------------------------------------------------------------------

interface PLT1RoadOrderDetailsViewProps {
  orderId: string;
  readOnly?: boolean;
}

// Define specific form values type for Road orders
export interface RoadFormValues {
  cmrDocuments: PLT1RoadCMR[];
  commercialInvoices: PLT1CommercialInvoice[];
  packingLists: PLT1PackingList[];
  notificationsOfArrivals: PLT1NotificationOfArrival[];
  transitDocuments: PLT1TransitDocument[];
  merchandisePositions?: PLT1MerchandisePositions;
  customsOffice?: PLT1CustomsOffice;
  vehicleRegistration?: PLT1VehicleRegistration;
}

export function PLT1RoadOrderDetailsView({
  orderId,
  readOnly: propReadOnly = false,
}: PLT1RoadOrderDetailsViewProps) {
  const { t } = useTranslate();
  const [isSaving, setIsSaving] = useState(false);
  const [formChanged, setFormChanged] = useState(false);
  const [initialFormValues, setInitialFormValues] = useState<RoadFormValues | null>(null);

  // Create form methods with the specific RoadFormValues type
  const methods = useForm<RoadFormValues>({
    // Initialize default values in the derived component
    defaultValues: {
      cmrDocuments: [],
      commercialInvoices: [],
      packingLists: [],
      notificationsOfArrivals: [],
      transitDocuments: [],
      customsOffice: { customsOfficeCode: '' },
      merchandisePositions: {
        id: null,
        positions: [],
        t1OrderId: orderId,
      },
      vehicleRegistration: {
        vehicleRegistrationNumber: '',
        vehicleCountryCode: '',
        trailerRegistrationNumber: '',
        trailerCountryCode: '',
      },
    },
    mode: 'onChange',
  });
  
  const { formState, watch, reset } = methods;
  const { isValid } = formState;

  const {
    order,
    reload,
    error: orderError,
    isLoading: isOrderLoading,
  } = usePLT1RoadOrderDetails(orderId);

  // Update form values when order data is loaded
  useEffect(() => {
    if (order) {
      const formData: RoadFormValues = {
        cmrDocuments: order.cmrDocuments || [],
        commercialInvoices: order.commercialInvoices || [],
        packingLists: order.packingLists || [],
        notificationsOfArrivals: order.notificationsOfArrivals || [],
        transitDocuments: order.transitDocuments || [],
        merchandisePositions: order.merchandisePositions || {
          id: null,
          positions: [],
          t1OrderId: orderId,
        },
        customsOffice: order.customsOffice || { customsOfficeCode: '' },
        vehicleRegistration: order.vehicleRegistration || {
          vehicleRegistrationNumber: '',
          vehicleCountryCode: '',
          trailerRegistrationNumber: '',
          trailerCountryCode: '',
        },
      };
      reset(formData);
      setInitialFormValues(formData);
      setFormChanged(false);
    }
  }, [order, reset]);

  // Helper function to check if values are actually different
  const hasRealChanges = useCallback(() => {
    if (!initialFormValues) return false;
    const currentValues = methods.getValues();
    return hasFormChanges(currentValues, initialFormValues);
  }, [initialFormValues, methods]);

  // Watch for form changes
  useEffect(() => {
    const subscription = watch(() => {
      setFormChanged(hasRealChanges());
    });

    return () => subscription.unsubscribe();
  }, [watch, hasRealChanges]);

  // Handle status change callback
  const handleStatusChange = useCallback(() => {
    reload();
  }, [reload, t]);

  // Get order status
  const {
    status: orderStatus,
    orderNumber,
    error: statusError,
  } = usePLT1OrderStatus(orderId, 1000, handleStatusChange);

  // Save order data
  const handleSaveOrder = async (formData: RoadFormValues): Promise<void> => {
    await handlePLT1OrderSave(
      formData,
      {
        orderId,
        endpoint: endpoints.plt1.roadUpdate,
        idField: 'pLT1RoadId',
        t,
      },
      setIsSaving,
      setFormChanged,
      reload
    );
  };
  
  // Handle form cancel
  const handleCancel = () => {
    if (initialFormValues) {
      reset(initialFormValues);
      setFormChanged(false);
    }
  };

  return (
    <FormProvider {...methods}>
      <PLT1OrderDetailsBase<RoadFormValues>
        orderId={orderId}
        readOnly={propReadOnly}
        order={order}
        isLoading={isOrderLoading}
        error={orderError}
        orderStatus={orderStatus}
        orderNumber={orderNumber}
        statusError={statusError}
        onSaveOrder={handleSaveOrder}
        formChanged={formChanged}
        isSaving={isSaving}
        onCancel={handleCancel}
        isValid={isValid}
        documentTabs={
          <PLT1RoadDocumentsTabs
            t1OrderId={orderId}
            order={order}
            readOnly={propReadOnly || orderStatus === 'Scanning'}
          />
        }
      />
    </FormProvider>
  );
}