import { Chip } from '@mui/material';
import { useTranslate } from 'src/locales';
import { Iconify } from 'src/components/iconify';
import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { gridOperators } from '../../utils/operators';
import type { EnumConfigMap } from './types';

interface EnumTagProps {
  value: string;
  config: EnumConfigMap;
  translationPrefix: string;
}

export const EnumTag = ({ value, config, translationPrefix }: EnumTagProps) => {
  const { t } = useTranslate();
  const enumConfig = config[value] || {
    color: 'default',
    icon: 'eva:question-mark-circle-outline',
    translationKey: value && typeof value === 'string'
      ? value.charAt(0).toLowerCase() + value.slice(1)
      : 'unknown'
  };

  return (
    <Chip
      label={t(`${translationPrefix}.${enumConfig.translationKey}`)}
      color={enumConfig.color}
      icon={<Iconify icon={enumConfig.icon} />}
      size="small"
    />
  );
};

export const useEnumOptions = (
  config: EnumConfigMap,
  translationPrefix: string
) => {
  const { t } = useTranslate();

  return Object.keys(config).map((enumValue) => ({
    value: enumValue,
    label: t(`${translationPrefix}.${config[enumValue].translationKey}`)
  }));
};

export const renderEnumCell = (
  params: GridRenderCellParams<any, string>,
  config: EnumConfigMap,
  translationPrefix: string
) => {
  if (!params.value) return null;

  return (
    <EnumTag
      value={params.value}
      config={config}
      translationPrefix={translationPrefix}
    />
  );
};

export const useEnumColumn = <T extends string>(
  config: EnumConfigMap,
  translationPrefix: string,
  fieldConfig: {
    field: string;
    headerTranslationKey: string;
  }
): GridColDef => {
  const { t } = useTranslate();
  const options = useEnumOptions(config, translationPrefix);

  return {
    field: fieldConfig.field,
    headerName: t(fieldConfig.headerTranslationKey),
    flex: 1,
    type: 'singleSelect',
    valueOptions: options,
    renderCell: (params) => renderEnumCell(params, config, translationPrefix),
    filterOperators: gridOperators.getEnumOperators()
  };
};
