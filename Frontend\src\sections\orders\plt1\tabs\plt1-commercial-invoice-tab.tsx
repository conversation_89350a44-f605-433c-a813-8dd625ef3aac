'use client';

import { useRef, useState, useEffect } from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';

import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { useTranslate } from 'src/locales';
import PLT1DocumentTabBase from './plt1-document-tab-base';
import { PLT1Order as PLT1Order } from '../types/plt1-details.types';
import PLT1CommercialInvoiceForm, {
  PLT1CommercialInvoiceData,
} from '../forms/plt1-commercial-invoice-form';

// ----------------------------------------------------------------------

interface PLT1CommercialInvoicesTabProps {
  t1OrderId: string;
  order?: PLT1Order;
  readOnly?: boolean;
}

interface FormValues {
  commercialInvoices: PLT1CommercialInvoiceData[];
}

export default function PLT1CommercialInvoicesTab({
  t1OrderId,
  order,
  readOnly = false,
}: PLT1CommercialInvoicesTabProps) {
  const { t } = useTranslate();
  const { control } = useFormContext<FormValues>();

  // Try to restore the expanded state from sessionStorage
  const [expandedInvoice, setExpandedInvoice] = useState<number | null>(() => {
    const saved = sessionStorage.getItem(`plt1-expanded-invoice-${t1OrderId}`);
    return saved ? parseInt(saved, 10) : null;
  });

  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const deleteButtonRef = useRef<HTMLButtonElement>(null);

  const fieldArray = useFieldArray({
    control,
    name: 'commercialInvoices',
  });

  // Save expanded state to sessionStorage whenever it changes
  useEffect(() => {
    if (expandedInvoice !== null) {
      sessionStorage.setItem(`plt1-expanded-invoice-${t1OrderId}`, expandedInvoice.toString());
    } else {
      sessionStorage.removeItem(`plt1-expanded-invoice-${t1OrderId}`);
    }
  }, [expandedInvoice, t1OrderId]);

  const handleAddInvoice = (): void => {
    const newInvoice: PLT1CommercialInvoiceData = {
      id: undefined,
      invoiceNumber: '',
      invoiceDate: null,
      incoterms: '',
      countryOfOrigin: '',
      invoiceValue: 0,
      invoiceCurrency: 'USD',
      shipper: {
        name: '',
        taxNumber: '',
        eoriNumber: '',
        address: {
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          postCode: '',
          country: '',
          countryCode: '',
        },
      },
      consignee: {
        name: '',
        taxNumber: '',
        eoriNumber: '',
        address: {
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          postCode: '',
          country: '',
          countryCode: '',
        },
      },
      items: [],
      t1OrderId,
    };

    fieldArray.append(newInvoice);
    setExpandedInvoice(fieldArray.fields.length);
  };

  const handleToggleExpand = (index: number): void => {
    setExpandedInvoice(expandedInvoice === index ? null : index);
  };

  const handleOpenConfirm = (index: number): void => {
    setDeleteIndex(index);
    setOpenConfirm(true);
  };

  const handleCloseConfirm = (): void => {
    setOpenConfirm(false);
    if (deleteIndex !== null) {
      deleteButtonRef.current?.focus();
    }
  };

  const handleDeleteInvoice = (): void => {
    if (deleteIndex !== null) {
      fieldArray.remove(deleteIndex);
      if (expandedInvoice === deleteIndex) {
        setExpandedInvoice(null);
      }
    }
    handleCloseConfirm();
  };

  // Render preview of the invoice item when collapsed
  const renderPreview = (invoice: any, _index: number) => (
    <Stack
      direction="row"
      spacing={2}
      sx={{
        px: 3,
        pb: 2,
        display: 'flex',
        flexWrap: 'wrap',
        '& > *': { mr: 3, mb: 1 },
      }}
    >
      {invoice.invoiceValue !== undefined &&
        invoice.invoiceValue !== null &&
        invoice.invoiceValue > 0 && (
          <Typography variant="body2" color="text.secondary">
            {t('plt1.details.documents.commercialInvoice.preview.totalAmount')}:{' '}
            {invoice.invoiceValue} {invoice.invoiceCurrency}
          </Typography>
        )}

      {/* Display item count if available */}
      {invoice.items && invoice.items.length > 0 && (
        <Typography variant="body2" color="text.secondary">
          {t('plt1.details.documents.commercialInvoice.preview.itemCount')}: {invoice.items.length}
        </Typography>
      )}
    </Stack>
  );

  // Render the form when expanded
  const renderForm = (index: number) => (
    <PLT1CommercialInvoiceForm formPath="commercialInvoices" index={index} readOnly={readOnly} />
  );

  // Render the title of each item
  const getItemTitle = (invoice: any) => (
    <Typography variant="subtitle1">
      {invoice.invoiceNumber || t('plt1.details.documents.commercialInvoice.preview.newInvoice')}
    </Typography>
  );

  return (
    <PLT1DocumentTabBase
      t1OrderId={t1OrderId}
      order={order}
      readOnly={readOnly}
      title={t('plt1.details.documents.commercialInvoice.heading')}
      emptyTitle={t('plt1.details.documents.commercialInvoice.noData')}
      emptyDescription={t('plt1.details.documents.commercialInvoice.addYourFirst')}
      expandedIndex={expandedInvoice}
      deleteIndex={deleteIndex}
      openConfirm={openConfirm}
      fieldArray={fieldArray}
      fieldArrayName="commercialInvoices"
      onToggleExpand={handleToggleExpand}
      onOpenConfirm={handleOpenConfirm}
      onCloseConfirm={handleCloseConfirm}
      onDelete={handleDeleteInvoice}
      onAdd={handleAddInvoice}
      renderPreview={renderPreview}
      renderForm={renderForm}
      getItemTitle={getItemTitle}
    />
  );
}
