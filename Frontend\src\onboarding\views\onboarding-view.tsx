'use client'

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z as zod } from 'zod';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import LoadingButton from '@mui/lab/LoadingButton';
import Card from '@mui/material/Card';
import { toast } from 'src/components/snackbar';
import { Form, Field, schemaHelper } from 'src/components/hook-form';
import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';
import { CardHeader, Divider, Typography } from '@mui/material';
import { Welcome } from '../../components/welcome/welcome';
import { SeoIllustration } from 'src/assets/illustrations';
import { useAuthContext } from 'src/auth/hooks';
import { useTranslate } from 'src/locales';
import { TFunctionNonStrict } from 'i18next';
import { setSession } from 'src/auth/context/jwt';
import { axiosInstance, endpoints } from 'src/lib/axios';

const createOnboardingSchema = (t: TFunctionNonStrict<string, undefined>) => zod.object({
  companyName: zod.string().min(1, { message: t('onboarding.formValidation.requiredCompanyName') }),
  taxNumber: zod.string().min(1, { message: t('onboarding.formValidation.requiredTaxNumber') }),
  address: zod.object({
    street1: zod.string().min(1, { message: t('onboarding.formValidation.requiredStreet1') }),
    street2: zod.string().min(1, { message: t('onboarding.formValidation.requiredStreet2') }),
    country: schemaHelper.nullableInput(zod.string().min(1, { message: t('onboarding.formValidation.requiredCountry') }), {
      message: t('onboarding.formValidation.requiredCountry'),
    }),
    city: zod.string().min(1, { message: t('onboarding.formValidation.requiredCity') }),
    postalCode: zod.string().min(1, { message: t('onboarding.formValidation.requiredPostalCode') })
  }),
});

export default function OnboardingView() {
  const router = useRouter();
  const { user } = useAuthContext();
  const { t } = useTranslate();

  const defaultValues = {
    companyName: '',
    taxNumber: '',
    address: {
      street1: '',
      street2: '',
      city: '',
      country: '',
      postalCode: '',
    },
  };

  const methods = useForm({
    resolver: zodResolver(createOnboardingSchema(t)),
    defaultValues,
    mode: 'onChange',
  });

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      const requestData = {
        TenantName: data.companyName,
        TaxNumber: data.taxNumber,
        Address: {
          AddressFirstLine: data.address.street1,
          AddressSecondLine: data.address.street2,
          Country: data.address.country,
          City: data.address.city,
          PostalCode: data.address.postalCode,
        },
      };

      const response = await axiosInstance.post(endpoints.auth.onboard, requestData);

      const { token: newAccessToken, refreshToken: newRefreshToken } = response.data;
      setSession(newAccessToken, newRefreshToken);

      reset();
      toast.success(t('onboarding.success'), {
        position: "bottom-right"
      });
      router.push(paths.dashboard.root);
      console.info('DATA', data);
    } catch (error) {
      console.error(error);
    }
  });

  const renderformDetials = () => (
    <>
      <Card sx={{ m: 1 }}>
        <Welcome
          title={t('onboarding.welcomeTitle', { name: user?.displayName })}
          description={t('onboarding.welcomeDescription')}
          img={<SeoIllustration hideBackground />}
        />
      </Card>
      <Card sx={{ m: 1 }}>
        <CardHeader title={t('onboarding.form.formTitle')} subheader={t('onboarding.form.formSubheader')} sx={{ pb: 3 }} />
        <Divider sx={{ borderStyle: 'dashed' }} />
        <Stack spacing={3} sx={{ p: 3 }}>

          <Box
            sx={{
              rowGap: 3,
              columnGap: 2,
              display: 'grid',
              gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' },
            }}
          >
            <Field.Text name="companyName" label={t('components.partyAddress.name')} />
            <Field.Text name="taxNumber" label={t('components.partyAddress.taxNumber')} />
          </Box>
          <Stack spacing={1}>
            <Typography variant="subtitle2">{t('onboarding.form.addressSectionTitle')}</Typography>
          </Stack>
          <Divider sx={{ borderStyle: 'dashed' }} />
          <Box sx={{
            rowGap: 3,
            columnGap: 2,
            display: 'grid'
          }}>
            <Field.Text name="address.street1" label={t('components.address.addressLine1')} />
            <Field.Text name="address.street2" label={t('components.address.addressLine2')} />
          </Box>
          <Box
            sx={{
              rowGap: 3,
              columnGap: 2,
              display: 'grid',
              gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' },
            }}
          >
            <Field.CountrySelect name="address.country" placeholder={t('components.address.country')} />
            <Field.Text name="address.city" label={t('components.address.city')} />
            <Field.Text name="address.postalCode" label={t('components.address.postalCode')} />
          </Box>
        </Stack>
      </Card>
    </>
  )

  const renderActions = () => (
    <Box
      sx={{
        mb: 2,
        mr: 2,
        gap: 3,
        display: 'flex',
        flexWrap: 'wrap',
        alignItems: 'center',
        justifyContent: 'flex-end',
      }}
    >
      <LoadingButton type="submit" variant="contained" size="large" loading={isSubmitting}>
        {t('onboarding.form.submit')}
      </LoadingButton>
    </Box>
  );

  return (
    <Form methods={methods} onSubmit={onSubmit}>
      <Stack spacing={{ xs: 3, md: 5 }} sx={{ mx: 'auto', maxWidth: { xs: 720, xl: 880 } }}>
        {renderformDetials()}
        {renderActions()}
      </Stack>
    </Form>

  );
}
