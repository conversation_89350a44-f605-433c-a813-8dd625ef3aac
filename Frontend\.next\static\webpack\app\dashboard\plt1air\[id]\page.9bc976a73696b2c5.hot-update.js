"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/plt1air-document-tabs.tsx":
/*!************************************************************!*\
  !*** ./src/sections/orders/plt1/plt1air-document-tabs.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1AirDocumentsTabs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mui/material/Box */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _mui_material_Tab__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material/Tab */ \"(app-pages-browser)/./node_modules/@mui/material/Tab/Tab.js\");\n/* harmony import */ var _mui_material_Tabs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/Tabs */ \"(app-pages-browser)/./node_modules/@mui/material/Tabs/Tabs.js\");\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/Card */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/Divider */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material/CardHeader */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var _tabs_air_plt1air_house_airwaybill_tab__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tabs/air/plt1air-house-airwaybill-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/air/plt1air-house-airwaybill-tab.tsx\");\n/* harmony import */ var _tabs_air_plt1air_master_airwaybill_tab__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tabs/air/plt1air-master-airwaybill-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/air/plt1air-master-airwaybill-tab.tsx\");\n/* harmony import */ var _tabs_plt1_commercial_invoice_tab__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tabs/plt1-commercial-invoice-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-commercial-invoice-tab.tsx\");\n/* harmony import */ var _tabs_plt1_packing_list_tab__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./tabs/plt1-packing-list-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-packing-list-tab.tsx\");\n/* harmony import */ var _tabs_plt1_notifications_arrival_tab__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./tabs/plt1-notifications-arrival-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-notifications-arrival-tab.tsx\");\n/* harmony import */ var _tabs_plt1_transit_tab__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./tabs/plt1-transit-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-transit-tab.tsx\");\n/* harmony import */ var _tabs_plt1_merchandise_positions_tab__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./tabs/plt1-merchandise-positions-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-merchandise-positions-tab.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// ----------------------------------------------------------------------\nvar TabNames = /*#__PURE__*/ function(TabNames) {\n    TabNames[\"HAWB\"] = \"hawb\";\n    TabNames[\"MAWB\"] = \"mawb\";\n    TabNames[\"COMMERCIAL_INVOICES\"] = \"commercialInvoices\";\n    TabNames[\"PACKING_LISTS\"] = \"packingLists\";\n    TabNames[\"NOTIFICATIONS\"] = \"notifications\";\n    TabNames[\"TRANSIT_DOCUMENTS\"] = \"transitDocuments\";\n    TabNames[\"MERCHANDISE_POSITIONS\"] = \"merchandisePositions\";\n    return TabNames;\n}(TabNames || {});\nfunction PLT1AirDocumentsTabs(param) {\n    let { t1OrderId, order, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"PLT1AirDocumentsTabs.useState\": ()=>{\n            const savedTab = sessionStorage.getItem(\"plt1-current-tab-\".concat(t1OrderId));\n            return savedTab && Object.values(TabNames).includes(savedTab) ? savedTab : \"hawb\";\n        }\n    }[\"PLT1AirDocumentsTabs.useState\"]);\n    const handleChangeTab = (event, newValue)=>{\n        setCurrentTab(newValue);\n        sessionStorage.setItem(\"plt1-current-tab-\".concat(t1OrderId), newValue);\n    };\n    const TABS = [\n        {\n            value: \"hawb\",\n            label: t('plt1.details.documents.tabs.houseAirWaybills'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_air_plt1air_house_airwaybill_tab__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 59,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"mawb\",\n            label: t('plt1.details.documents.tabs.masterAirWaybills'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_air_plt1air_master_airwaybill_tab__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 64,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"commercialInvoices\",\n            label: t('plt1.details.documents.tabs.commercialInvoices'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_commercial_invoice_tab__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 69,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"packingLists\",\n            label: t('plt1.details.documents.tabs.packingLists'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_packing_list_tab__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 74,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"notifications\",\n            label: t('plt1.details.documents.tabs.notificationsOfArrival'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_notifications_arrival_tab__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 79,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"transitDocuments\",\n            label: t('plt1.details.documents.tabs.transitDocuments'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_transit_tab__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 84,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"merchandisePositions\",\n            label: t('plt1.details.documents.tabs.merchandisePositions'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_merchandise_positions_tab__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 89,\n                columnNumber: 24\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                title: t('plt1.details.documents.heading'),\n                sx: {\n                    mb: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 95,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                sx: {\n                    borderStyle: 'dashed'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 99,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tabs__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                value: currentTab,\n                onChange: handleChangeTab,\n                sx: {\n                    px: 2.5,\n                    boxShadow: (theme)=>\"inset 0 -2px 0 0 \".concat(theme.palette.divider)\n                },\n                children: TABS.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tab__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        value: tab.value,\n                        label: tab.label\n                    }, tab.value, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 21\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 100,\n                columnNumber: 13\n            }, this),\n            TABS.map((tab)=>tab.value === currentTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: tab.component\n                }, tab.value, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 25\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n        lineNumber: 94,\n        columnNumber: 9\n    }, this);\n}\n_s(PLT1AirDocumentsTabs, \"kaSynK7N1TfUw/LWa0d5ys3mM1E=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate\n    ];\n});\n_c = PLT1AirDocumentsTabs;\nvar _c;\n$RefreshReg$(_c, \"PLT1AirDocumentsTabs\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/plt1air-document-tabs.tsx\n"));

/***/ })

});