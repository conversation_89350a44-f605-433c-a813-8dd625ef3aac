import type { NavSectionProps } from 'src/components/nav-section';

import { paths } from 'src/routes/paths';

import { CONFIG } from 'src/global-config';

import { Label } from 'src/components/label';
import { SvgColor } from 'src/components/svg-color';

// ----------------------------------------------------------------------

const icon = (name: string) => (
  <SvgColor src={`${CONFIG.assetsDir}/assets/icons/navbar/${name}.svg`} />
);

const ICONS = {
  job: icon('ic-job'),
  blog: icon('ic-blog'),
  chat: icon('ic-chat'),
  mail: icon('ic-mail'),
  user: icon('ic-user'),
  file: icon('ic-file'),
  lock: icon('ic-lock'),
  tour: icon('ic-tour'),
  order: icon('ic-order'),
  label: icon('ic-label'),
  blank: icon('ic-blank'),
  kanban: icon('ic-kanban'),
  folder: icon('ic-folder'),
  course: icon('ic-course'),
  banking: icon('ic-banking'),
  booking: icon('ic-booking'),
  invoice: icon('ic-invoice'),
  product: icon('ic-product'),
  calendar: icon('ic-calendar'),
  disabled: icon('ic-disabled'),
  external: icon('ic-external'),
  menuItem: icon('ic-menu-item'),
  ecommerce: icon('ic-ecommerce'),
  analytics: icon('ic-analytics'),
  dashboard: icon('ic-dashboard'),
  parameter: icon('ic-parameter'),
  transform: icon('ic-transform'),
  import: icon('ic-import'),
  export: icon('ic-export'),
  transit: icon('ic-transit'),
  processes: icon('ic-processes'),
};

// ----------------------------------------------------------------------

export const navData: NavSectionProps['data'] = [
  /**
   * Processes
   */
  {
    subheaderTranslationKey: 'navbar.processes.subheader',
    items: [
      {
        translationKey: 'navbar.processes.group',
        path: paths.dashboard.root,
        icon: ICONS.processes,
        children: [
          {
            translationKey: 'navbar.processes.transits',
            path: paths.dashboard.root,
            icon: ICONS.transit
          }
        ],
      },
    ],
  },
  {
    subheaderTranslationKey: 'navbar.management.subheader',
    items: [
      {
        translationKey: 'navbar.management.usersGroup',
        path: paths.dashboard.user.root,
        icon: ICONS.user,
        children: [
          { translationKey: 'navbar.management.users.list', path: paths.dashboard.user.list }
        ],
      },
    ]
  }
];