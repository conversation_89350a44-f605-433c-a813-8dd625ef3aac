import { useState } from 'react';
import { useFormContext } from 'react-hook-form';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';

import { useTranslate } from 'src/locales';
import { Iconify } from 'src/components/iconify';
import PLT1VehicleRegistrationForm from '../forms/plt1-vehicle-registration-form';

// ----------------------------------------------------------------------

interface PLT1VehicleRegistrationTabProps {
  readOnly?: boolean;
}

export default function PLT1VehicleRegistrationTab({
  readOnly = false,
}: PLT1VehicleRegistrationTabProps) {
  const { t } = useTranslate();
  const { watch } = useFormContext();

  // Define the field path for vehicleRegistration
  const fieldPath = 'vehicleRegistration';

  // State for expanded/collapsed form
  const [isExpanded, setIsExpanded] = useState(false);

  // Watch the vehicleRegistration object using the correct path
  const vehicleRegistration = watch(fieldPath) || {
    vehicleRegistrationNumber: '',
    vehicleCountryCode: '',
    trailerRegistrationNumber: '',
    trailerCountryCode: '',
  };

  const handleToggleExpand = () => {
    setIsExpanded((prev) => !prev);
  };

  return (
    <Box>
      <Card sx={{ mb: 3 }}>
        <Stack>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            sx={{ p: 3, pb: isExpanded ? 0 : 3 }}
          >
            <Typography variant="h6">{t('plt1.details.vehicleRegistration.title')}</Typography>

            <Tooltip title={isExpanded ? t('common.collapse') : t('common.expand')}>
              <IconButton onClick={handleToggleExpand} disabled={readOnly && !isExpanded}>
                <Iconify icon={isExpanded ? 'eva:chevron-up-fill' : 'eva:chevron-down-fill'} />
              </IconButton>
            </Tooltip>
          </Stack>

          {!isExpanded && (
            <Box sx={{ p: 3, pt: 0 }}>
              <Stack spacing={2}>
                <Stack direction="row" spacing={2}>
                  <Stack spacing={1} sx={{ width: '50%' }}>
                    <Typography variant="subtitle2">{t('plt1.details.vehicleRegistration.vehicleRegistrationNumber')}</Typography>
                    <Typography variant="body2">
                      {vehicleRegistration?.vehicleRegistrationNumber || '-'}
                    </Typography>
                  </Stack>
                  <Stack spacing={1} sx={{ width: '50%' }}>
                    <Typography variant="subtitle2">{t('plt1.details.vehicleRegistration.vehicleCountryCode')}</Typography>
                    <Typography variant="body2">
                      {vehicleRegistration?.vehicleCountryCode || '-'}
                    </Typography>
                  </Stack>
                </Stack>
                <Stack direction="row" spacing={2}>
                  <Stack spacing={1} sx={{ width: '50%' }}>
                    <Typography variant="subtitle2">{t('plt1.details.vehicleRegistration.trailerRegistrationNumber')}</Typography>
                    <Typography variant="body2">
                      {vehicleRegistration?.trailerRegistrationNumber || '-'}
                    </Typography>
                  </Stack>
                  <Stack spacing={1} sx={{ width: '50%' }}>
                    <Typography variant="subtitle2">{t('plt1.details.vehicleRegistration.trailerCountryCode')}</Typography>
                    <Typography variant="body2">
                      {vehicleRegistration?.trailerCountryCode || '-'}
                    </Typography>
                  </Stack>
                </Stack>
              </Stack>
            </Box>
          )}

          {isExpanded && (
            <Box sx={{ p: 3 }}>
              <PLT1VehicleRegistrationForm
                formPath={fieldPath}
                readOnly={readOnly}
              />
            </Box>
          )}
        </Stack>
      </Card>
    </Box>
  );
}
