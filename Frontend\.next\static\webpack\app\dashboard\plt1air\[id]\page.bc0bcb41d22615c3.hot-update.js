"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-commercial-invoice-form.tsx":
/*!*************************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-commercial-invoice-form.tsx ***!
  \*************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1CommercialInvoiceForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,Stack!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,Stack!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,Stack!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,Stack!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,Stack!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,Stack!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/components/custom-dialog */ \"(app-pages-browser)/./src/components/custom-dialog/index.ts\");\n/* harmony import */ var _components_party_address_party_form_section__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/party-address/party-form-section */ \"(app-pages-browser)/./src/components/party-address/party-form-section.tsx\");\n/* harmony import */ var src_types_parties__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/types/parties */ \"(app-pages-browser)/./src/types/parties.ts\");\n/* harmony import */ var src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/components/party-address/hooks/usePartyAddressForm */ \"(app-pages-browser)/./src/components/party-address/hooks/usePartyAddressForm.tsx\");\n/* harmony import */ var src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! src/components/party-address/party-address-dialog */ \"(app-pages-browser)/./src/components/party-address/party-address-dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst DEFAULT_ITEM = {\n    id: null,\n    name: '',\n    translatedName: '',\n    modelNumber: '',\n    quantity: 0,\n    unit: '',\n    value: 0,\n    currency: 'USD',\n    hsCode: '',\n    commercialInvoiceId: null\n};\nfunction PLT1CommercialInvoiceForm(param) {\n    let { formPath, index, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    const { control, getValues, setValue, watch } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useFormContext)();\n    const fieldPrefix = index !== undefined ? \"\".concat(formPath, \".\").concat(index) : formPath;\n    const itemsFieldName = \"\".concat(fieldPrefix, \".items\");\n    // Use the party address form hook\n    const { openPartyDialog, currentPartyType, handleOpenPartyDialog, handleClosePartyDialog, handleUpdateParty } = (0,src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_7__.usePartyAddressForm)({\n        fieldPrefix\n    });\n    // State for item dialog\n    const [openItemDialog, setOpenItemDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteItemIndex, setDeleteItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Watch shipper and consignee to display their info\n    const shipper = watch(\"\".concat(fieldPrefix, \".shipper\"));\n    const consignee = watch(\"\".concat(fieldPrefix, \".consignee\"));\n    const invoiceCurrency = watch(\"\".concat(fieldPrefix, \".invoiceCurrency\"));\n    // UseFieldArray hook to manage the items array\n    const { fields, append, remove, update } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useFieldArray)({\n        control,\n        name: itemsFieldName\n    });\n    // Items Management Functions\n    // Handle opening the item dialog for adding a new item\n    const handleAddItem = ()=>{\n        setCurrentItemIndex(null);\n        // Add a new item with default values\n        const newItem = {\n            ...DEFAULT_ITEM,\n            currency: invoiceCurrency || 'USD'\n        };\n        append(newItem);\n        setCurrentItemIndex(fields.length); // Set to the new index\n        setOpenItemDialog(true);\n    };\n    // Handle opening the item dialog for editing an existing item\n    const handleEditItem = (index)=>{\n        setCurrentItemIndex(index);\n        setOpenItemDialog(true);\n    };\n    // Handle closing the item dialog\n    const handleCloseItemDialog = ()=>{\n        setOpenItemDialog(false);\n        // If we were adding a new item and user cancels, remove the empty item\n        if (currentItemIndex !== null && currentItemIndex === fields.length - 1) {\n            const item = getValues(\"\".concat(itemsFieldName, \".\").concat(currentItemIndex));\n            // Check if it's an empty item (all fields are default values)\n            const isEmpty = !item.name && !item.translatedName && !item.modelNumber && !item.hsCode && item.quantity === 0 && item.value === 0;\n            if (isEmpty) {\n                remove(currentItemIndex);\n            }\n        }\n        setCurrentItemIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeleteItemIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deleteItemIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting an item\n    const handleDeleteItem = ()=>{\n        if (deleteItemIndex !== null) {\n            remove(deleteItemIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving an item (just close the dialog since form is already updated)\n    const handleSaveItem = ()=>{\n        // Update total value\n        updateInvoiceTotalValue();\n        // Close the dialog\n        handleCloseItemDialog();\n    };\n    // Calculate and update total value based on items\n    const updateInvoiceTotalValue = ()=>{\n        // Get all items\n        const items = getValues(\"\".concat(itemsFieldName)) || [];\n        // Filter items that have the same currency as the invoice\n        const currentCurrency = getValues(\"\".concat(fieldPrefix, \".invoiceCurrency\")) || 'USD';\n        const itemsWithSameCurrency = items.filter((item)=>(item.currency === currentCurrency || !item.currency) && item.value);\n        // Calculate total\n        const totalValue = itemsWithSameCurrency.reduce((sum, item)=>sum + (parseFloat(item.value) || 0) * (parseFloat(item.quantity) || 1), 0);\n        // Update invoice total value if there are items with matching currency\n        if (itemsWithSameCurrency.length > 0) {\n            setValue(\"\".concat(fieldPrefix, \".invoiceValue\"), parseFloat(totalValue.toFixed(2)), {\n                shouldDirty: true\n            });\n        }\n    };\n    // Render the item form in the dialog - using React Hook Form fields\n    const renderItemForm = ()=>{\n        if (currentItemIndex === null) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            spacing: 2.5,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".name\"),\n                    label: t('plt1.details.documents.commercialInvoice.item.name'),\n                    size: \"small\",\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".translatedName\"),\n                    label: t('plt1.details.documents.commercialInvoice.item.translatedName'),\n                    multiline: true,\n                    rows: 2,\n                    size: \"small\",\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".modelNumber\"),\n                    label: t('plt1.details.documents.commercialInvoice.item.modelNumber'),\n                    multiline: true,\n                    rows: 2,\n                    size: \"small\",\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".hsCode\"),\n                    label: t('plt1.details.documents.commercialInvoice.item.hsCode'),\n                    size: \"small\",\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".quantity\"),\n                            label: t('plt1.details.documents.commercialInvoice.item.quantity'),\n                            type: \"number\",\n                            size: \"small\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".unit\"),\n                            label: t('plt1.details.documents.commercialInvoice.item.unit'),\n                            placeholder: \"pcs, kg, etc.\",\n                            size: \"small\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".value\"),\n                            label: t('plt1.details.documents.commercialInvoice.item.value'),\n                            type: \"number\",\n                            size: \"small\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".currency\"),\n                            label: t('plt1.details.documents.commercialInvoice.item.currency'),\n                            size: \"small\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n            lineNumber: 217,\n            columnNumber: 7\n        }, this);\n    };\n    const renderMainInfo = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            sx: {\n                boxShadow: \"none\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    title: t('plt1.details.documents.commercialInvoice.form.mainInfoTitle'),\n                    sx: {\n                        mb: 2\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    sx: {\n                        borderStyle: 'dashed'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    spacing: 3,\n                    sx: {\n                        p: 3\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(fieldPrefix, \".invoiceNumber\"),\n                            label: t('plt1.details.documents.commercialInvoice.form.invoiceNumber'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.DatePicker, {\n                            name: \"\".concat(fieldPrefix, \".invoiceDate\"),\n                            label: t('plt1.details.documents.commercialInvoice.form.invoiceDate'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            direction: \"row\",\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                    name: \"\".concat(fieldPrefix, \".countryOfOrigin\"),\n                                    label: t('plt1.details.documents.commercialInvoice.form.countryOfOrigin'),\n                                    disabled: readOnly\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                    name: \"\".concat(fieldPrefix, \".incoterms\"),\n                                    label: t('plt1.details.documents.commercialInvoice.form.incoterms'),\n                                    disabled: readOnly\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            direction: \"row\",\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                    name: \"\".concat(fieldPrefix, \".invoiceValue\"),\n                                    label: t('plt1.details.documents.commercialInvoice.form.totalAmount'),\n                                    type: \"number\",\n                                    disabled: readOnly\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                    name: \"\".concat(fieldPrefix, \".invoiceCurrency\"),\n                                    label: t('plt1.details.documents.commercialInvoice.form.currency'),\n                                    disabled: readOnly\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n            lineNumber: 288,\n            columnNumber: 5\n        }, this);\n    const renderParties = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            sx: {\n                mt: 3,\n                boxShadow: \"none\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    title: t('plt1.details.documents.commercialInvoice.form.partiesTitle'),\n                    sx: {\n                        mb: 2\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    sx: {\n                        borderStyle: 'dashed'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    spacing: 3,\n                    sx: {\n                        p: 3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        sx: {\n                            rowGap: 3,\n                            columnGap: 2,\n                            display: 'grid',\n                            gridTemplateColumns: {\n                                xs: 'repeat(1, 1fr)',\n                                md: 'repeat(2, 1fr)'\n                            }\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_party_address_party_form_section__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                partyType: src_types_parties__WEBPACK_IMPORTED_MODULE_6__.PartyType.SHIPPER,\n                                labelKey: \"plt1.details.documents.commercialInvoice.form.shipper\",\n                                party: shipper,\n                                onOpenPartyDialog: handleOpenPartyDialog,\n                                readOnly: readOnly\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_party_address_party_form_section__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                partyType: src_types_parties__WEBPACK_IMPORTED_MODULE_6__.PartyType.CONSIGNEE,\n                                labelKey: \"plt1.details.documents.commercialInvoice.form.consignee\",\n                                party: consignee,\n                                onOpenPartyDialog: handleOpenPartyDialog,\n                                readOnly: readOnly\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n            lineNumber: 335,\n            columnNumber: 5\n        }, this);\n    const renderPartyDialog = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            open: openPartyDialog,\n            onClose: handleClosePartyDialog,\n            onSave: handleUpdateParty,\n            formPath: fieldPrefix,\n            currentPartyType: currentPartyType,\n            readOnly: readOnly,\n            titlePrefix: \"plt1.details.documents.commercialInvoice.partyAddress\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n            lineNumber: 367,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                spacing: 3,\n                children: [\n                    renderMainInfo(),\n                    renderParties()\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                lineNumber: 380,\n                columnNumber: 7\n            }, this),\n            renderPartyDialog(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_4__.ConfirmDialog, {\n                open: openDeleteConfirm,\n                onClose: handleCloseDeleteConfirm,\n                title: t('plt1.details.documents.commercialInvoice.item.confirmDeleteDialog.title'),\n                content: t('plt1.details.documents.commercialInvoice.item.confirmDeleteDialog.content'),\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_Stack_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    variant: \"contained\",\n                    color: \"error\",\n                    onClick: handleDeleteItem,\n                    children: t('common.delete')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-commercial-invoice-form.tsx\",\n                lineNumber: 388,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(PLT1CommercialInvoiceForm, \"7K9R/vdXnhLfxOFImLZan34gLlU=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useFormContext,\n        src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_7__.usePartyAddressForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useFieldArray\n    ];\n});\n_c = PLT1CommercialInvoiceForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1CommercialInvoiceForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-commercial-invoice-form.tsx\n"));

/***/ })

});