"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mui-one-time-password-input";
exports.ids = ["vendor-chunks/mui-one-time-password-input"];
exports.modules = {

/***/ "(ssr)/./node_modules/mui-one-time-password-input/dist/mui-one-time-password-input.es.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/mui-one-time-password-input/dist/mui-one-time-password-input.es.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MuiOtpInput: () => (/* binding */ le)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/styled.js\");\n/* harmony import */ var _mui_material_TextField__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/TextField */ \"(ssr)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/Box */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n\n\n\n\n\nconst G = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_mui_material_TextField__WEBPACK_IMPORTED_MODULE_3__[\"default\"]))`\n  input {\n    text-align: center;\n  }\n`, J = {\n  TextFieldStyled: G\n}, Q = (n) => /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(J.TextFieldStyled, { ...n }), D = {\n  left: \"ArrowLeft\",\n  right: \"ArrowRight\",\n  backspace: \"Backspace\",\n  home: \"Home\",\n  end: \"End\"\n};\nfunction U(n, s) {\n  return n <= 0 ? [] : Array.from({ length: n }, s);\n}\nfunction X(n, s, l) {\n  return n.map((i, F) => s === F ? l : i);\n}\nfunction P(n) {\n  return n.join(\"\");\n}\nfunction M(n, s) {\n  return [...n, s];\n}\nfunction Z(n, s, l) {\n  return n.reduce(\n    (i, F, C) => {\n      const { characters: y, restArrayMerged: d } = i;\n      if (C < l)\n        return {\n          restArrayMerged: d,\n          characters: M(y, F)\n        };\n      const [V, ...E] = d;\n      return {\n        restArrayMerged: E,\n        characters: M(y, V || \"\")\n      };\n    },\n    {\n      restArrayMerged: s,\n      characters: []\n    }\n  ).characters;\n}\nfunction v(n) {\n  return (s) => {\n    n.forEach((l) => {\n      typeof l == \"function\" ? l(s) : l != null && (l.current = s);\n    });\n  };\n}\nfunction ee(n) {\n  return n.split(\"\");\n}\nfunction N(n) {\n  const s = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(() => {\n    throw new Error(\"Cannot call an event handler while rendering.\");\n  });\n  return react__WEBPACK_IMPORTED_MODULE_1___default().useInsertionEffect(() => {\n    s.current = n;\n  }), react__WEBPACK_IMPORTED_MODULE_1___default().useCallback((...l) => s.current?.(...l), []);\n}\nconst te = () => !0, le = react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(\n  (n, s) => {\n    const {\n      value: l = \"\",\n      length: i = 4,\n      autoFocus: F = !1,\n      onChange: C,\n      TextFieldsProps: y,\n      onComplete: d,\n      validateChar: V = te,\n      className: E,\n      onBlur: b,\n      ...K\n    } = n, j = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(l), w = N(d), I = N((e) => {\n      const t = e.slice(0, i);\n      return {\n        isCompleted: t.length === i,\n        finalValue: t\n      };\n    });\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(() => {\n      const { isCompleted: e, finalValue: t } = I(\n        j.current\n      );\n      e && w(t);\n    }, [i, w, I]);\n    const p = U(\n      i,\n      (e, t) => ({\n        character: l[t] || \"\",\n        inputRef: react__WEBPACK_IMPORTED_MODULE_1___default().createRef()\n      })\n    ), T = (e) => p.findIndex(({ inputRef: t }) => t.current === e), k = () => p.map(({ character: e }) => e), A = (e, t) => {\n      const r = X(\n        k(),\n        e,\n        t\n      );\n      return P(r);\n    }, $ = (e) => {\n      p[e]?.inputRef.current?.focus();\n    }, c = (e) => {\n      p[e]?.inputRef.current?.select();\n    }, O = (e) => {\n      e + 1 !== i && (p[e + 1].character ? c(e + 1) : $(e + 1));\n    }, S = (e, t) => typeof V != \"function\" ? !0 : V(e, t), Y = (e) => {\n      const t = T(e.target);\n      if (t === 0 && e.target.value.length > 1) {\n        const { finalValue: m, isCompleted: B } = I(\n          e.target.value\n        );\n        C?.(m), B && d?.(m), c(m.length - 1);\n        return;\n      }\n      const r = e.target.value[0] || \"\";\n      let u = r;\n      u && !S(u, t) && (u = \"\");\n      const a = A(t, u);\n      C?.(a);\n      const { isCompleted: h, finalValue: f } = I(a);\n      h && d?.(f), u !== \"\" ? a.length - 1 < t ? c(a.length) : O(t) : r === \"\" && a.length <= t && c(t - 1);\n    }, _ = (e) => {\n      const t = e.target, r = t.selectionStart, u = t.selectionEnd, a = T(t), h = r === 0 && u === 0;\n      if (t.value === e.key)\n        e.preventDefault(), O(a);\n      else if (D.backspace === e.key) {\n        if (!t.value)\n          e.preventDefault(), c(a - 1);\n        else if (h) {\n          e.preventDefault();\n          const f = A(a, \"\");\n          C?.(f), f.length <= a && c(a - 1);\n        }\n      } else D.left === e.key ? (e.preventDefault(), c(a - 1)) : D.right === e.key ? (e.preventDefault(), c(a + 1)) : D.home === e.key ? (e.preventDefault(), c(0)) : D.end === e.key && (e.preventDefault(), c(p.length - 1));\n    }, H = (e) => {\n      const t = e.clipboardData.getData(\"text/plain\"), r = e.target, u = p.findIndex(\n        ({ character: x, inputRef: o }) => x === \"\" || o.current === r\n      ), a = k(), h = Z(\n        a,\n        ee(t),\n        u\n      ).map((x, o) => S(x, o) ? x : \"\"), f = P(h);\n      C?.(f);\n      const { isCompleted: m, finalValue: B } = I(f);\n      m ? (d?.(B), c(i - 1)) : c(f.length);\n    }, L = (e) => {\n      if (!p.some(({ inputRef: r }) => r.current === e.relatedTarget)) {\n        const { isCompleted: r, finalValue: u } = I(l);\n        b?.(u, r);\n      }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n      _mui_material_Box__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n      {\n        display: \"flex\",\n        gap: \"20px\",\n        alignItems: \"center\",\n        ref: s,\n        className: `MuiOtpInput-Box ${E || \"\"}`,\n        ...K,\n        children: p.map(({ character: e, inputRef: t }, r) => {\n          const {\n            onPaste: u,\n            onFocus: a,\n            onKeyDown: h,\n            className: f,\n            onBlur: m,\n            inputRef: B,\n            ...x\n          } = typeof y == \"function\" ? y(r) || {} : y || {};\n          return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n            Q,\n            {\n              autoFocus: F ? r === 0 : !1,\n              autoComplete: \"one-time-code\",\n              value: e,\n              inputRef: v([t, B]),\n              className: `MuiOtpInput-TextField MuiOtpInput-TextField-${r + 1} ${f || \"\"}`,\n              onPaste: (o) => {\n                o.preventDefault(), H(o), u?.(o);\n              },\n              onFocus: (o) => {\n                o.preventDefault(), o.target.select(), a?.(o);\n              },\n              onChange: Y,\n              onKeyDown: (o) => {\n                _(o), h?.(o);\n              },\n              onBlur: (o) => {\n                m?.(o), L(o);\n              },\n              ...x\n            },\n            r\n          );\n        })\n      }\n    );\n  }\n);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mui-one-time-password-input/dist/mui-one-time-password-input.es.js\n");

/***/ })

};
;