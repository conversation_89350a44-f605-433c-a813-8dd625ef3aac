'use client';

import { useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import LoadingButton from '@mui/lab/LoadingButton';
import TextField from '@mui/material/TextField';
import Box from '@mui/material/Box';
import { useTranslate } from 'src/locales';
import { axiosInstance, endpoints } from 'src/lib/axios';

interface T1FileDeleteConfirmationDialogProps {
  open: boolean;
  onClose: () => void;
  fileId: string;
  fileName: string;
  onSuccess: () => void;
}

export function PLT1FileDeleteConfirmationDialog({
  open,
  onClose,
  fileId,
  fileName,
  onSuccess,
}: T1FileDeleteConfirmationDialogProps) {
  const { t } = useTranslate();

  // Define translation keys to avoid typos
  const translationKeys = {
    title: 'plt1.details.fileManagement.deleteConfirmation.title',
    confirmation: 'plt1.details.fileManagement.deleteConfirmation.confirmation',
    typeToConfirm: 'plt1.details.fileManagement.deleteConfirmation.typeToConfirm',
    confirmPlaceholder: 'plt1.details.fileManagement.deleteConfirmation.confirmPlaceholder',
    error: 'plt1.details.fileManagement.deleteConfirmation.error',
  };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [confirmText, setConfirmText] = useState('');

  const handleDelete = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      const response = await axiosInstance.delete<{ isSuccess: boolean; messages?: string[] }>(
        `${endpoints.plt1.deleteFile}/${fileId}`
      );

      if (response.data.isSuccess) {
        onSuccess();
        onClose();
        setConfirmText(''); // Reset the confirmation text on success
      } else {
        setError(response.data.messages?.[0] || t(translationKeys.error));
      }
    } catch (error) {
      console.error('Error deleting file:', error);
      setError(t(translationKeys.error));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    onClose();
    setConfirmText('');
    setError(null);
  };

  // Check if the confirmation text matches the required text (case insensitive)
  const isDeleteEnabled = confirmText.toLowerCase() === 'delete';

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>{t(translationKeys.title)}</DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 1, typography: 'body2' }}>
          {t(translationKeys.confirmation, { fileName })}
        </Box>
        <Box sx={{ mb: 1 }}>
          <Box sx={{ mb: 3, typography: 'body2' }}>{t(translationKeys.typeToConfirm)}</Box>

          <TextField
            fullWidth
            value={confirmText}
            onChange={(e) => setConfirmText(e.target.value)}
            placeholder={t(translationKeys.confirmPlaceholder)}
            variant="outlined"
            size="small"
            autoComplete="off"
            disabled={isSubmitting}
          />
        </Box>

        {error && (
          <DialogContentText color="error" sx={{ mt: 2 }}>
            {error}
          </DialogContentText>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={isSubmitting}>
          {t('common.cancel')}
        </Button>
        <LoadingButton
          onClick={handleDelete}
          loading={isSubmitting}
          color="error"
          variant="contained"
          disabled={!isDeleteEnabled || isSubmitting}
        >
          {t('common.delete')}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
}
