globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/auth/callback/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/@mui/lab/LoadingButton/LoadingButton.js":{"*":{"id":"(ssr)/./node_modules/@mui/lab/LoadingButton/LoadingButton.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js":{"*":{"id":"(ssr)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Accordion/Accordion.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Alert/Alert.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Autocomplete/Autocomplete.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/AvatarGroup/AvatarGroup.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/AvatarGroup/AvatarGroup.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Badge/Badge.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Badge/Badge.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Button/Button.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/ButtonGroup/ButtonGroup.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/ButtonGroup/ButtonGroup.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Checkbox/Checkbox.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Checkbox/Checkbox.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Chip/Chip.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/CircularProgress/CircularProgress.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/DialogActions/DialogActions.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Divider/Divider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Fab/Fab.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Fab/Fab.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/FilledInput/FilledInput.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/FilledInput/FilledInput.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/FormControlLabel/FormControlLabel.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/FormControlLabel/FormControlLabel.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/IconButton/IconButton.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/InputBase/InputBase.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/InputBase/InputBase.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/InputLabel/InputLabel.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/InputLabel/InputLabel.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/List/List.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/List/List.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/ListItemIcon/ListItemIcon.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/ListItemIcon/ListItemIcon.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/MenuItem/MenuItem.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/OutlinedInput/OutlinedInput.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/OutlinedInput/OutlinedInput.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/PaginationItem/PaginationItem.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/PaginationItem/PaginationItem.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Paper/Paper.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Radio/Radio.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Radio/Radio.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Rating/Rating.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Rating/Rating.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Slider/Slider.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Slider/Slider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/styles/styled.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/styles/styled.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/styles/ThemeProvider.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/styles/ThemeProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/styles/ThemeProviderWithVars.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/styles/ThemeProviderWithVars.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/styles/useTheme.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/styles/useThemeProps.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/styles/useThemeProps.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/SvgIcon/SvgIcon.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/SvgIcon/SvgIcon.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Switch/Switch.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Switch/Switch.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Tab/Tab.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Tab/Tab.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/TableCell/TableCell.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/TableRow/TableRow.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/TextField/TextField.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/ToggleButton/ToggleButton.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/ToggleButton/ToggleButton.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Tooltip/Tooltip.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/Typography/Typography.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/styled-engine/GlobalStyles/GlobalStyles.js":{"*":{"id":"(ssr)/./node_modules/@mui/styled-engine/GlobalStyles/GlobalStyles.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/styled-engine/StyledEngineProvider/StyledEngineProvider.js":{"*":{"id":"(ssr)/./node_modules/@mui/styled-engine/StyledEngineProvider/StyledEngineProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/Box/Box.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/Box/Box.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/Container/Container.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/Container/Container.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/createBox/createBox.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/createBox/createBox.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/cssVars/useCurrentColorScheme.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/cssVars/useCurrentColorScheme.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/GlobalStyles/GlobalStyles.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/GlobalStyles/GlobalStyles.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/Grid/Grid.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/Grid/Grid.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/Stack/Stack.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/Stack/Stack.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/ThemeProvider/ThemeProvider.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/ThemeProvider/ThemeProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/useTheme/useTheme.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/useTheme/useTheme.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/useThemeProps/useThemeProps.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/useThemeProps/useThemeProps.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/useThemeWithoutDefault/useThemeWithoutDefault.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/useThemeWithoutDefault/useThemeWithoutDefault.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useControlled/useControlled.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useControlled/useControlled.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useEnhancedEffect/useEnhancedEffect.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useEnhancedEffect/useEnhancedEffect.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useEventCallback/useEventCallback.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useEventCallback/useEventCallback.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useForkRef/useForkRef.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useForkRef/useForkRef.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useId/useId.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useId/useId.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useIsFocusVisible/useIsFocusVisible.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useIsFocusVisible/useIsFocusVisible.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useLazyRef/useLazyRef.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useLazyRef/useLazyRef.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useOnMount/useOnMount.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useOnMount/useOnMount.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/usePreviousProps/usePreviousProps.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/usePreviousProps/usePreviousProps.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useSlotProps/useSlotProps.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useSlotProps/useSlotProps.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useTimeout/useTimeout.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useTimeout/useTimeout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/sonner/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/auth/context/auth0/auth-provider.tsx":{"*":{"id":"(ssr)/./src/auth/context/auth0/auth-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/animate/motion-lazy.tsx":{"*":{"id":"(ssr)/./src/components/animate/motion-lazy.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/progress-bar/progress-bar.tsx":{"*":{"id":"(ssr)/./src/components/progress-bar/progress-bar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/settings/context/settings-context.ts":{"*":{"id":"(ssr)/./src/components/settings/context/settings-context.ts","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/settings/context/settings-provider.tsx":{"*":{"id":"(ssr)/./src/components/settings/context/settings-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/settings/context/use-settings-context.ts":{"*":{"id":"(ssr)/./src/components/settings/context/use-settings-context.ts","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/settings/drawer/settings-drawer.tsx":{"*":{"id":"(ssr)/./src/components/settings/drawer/settings-drawer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/snackbar/snackbar.tsx":{"*":{"id":"(ssr)/./src/components/snackbar/snackbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/locales/all-langs.ts":{"*":{"id":"(ssr)/./src/locales/all-langs.ts","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/locales/i18n-provider.tsx":{"*":{"id":"(ssr)/./src/locales/i18n-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/locales/localization-provider.tsx":{"*":{"id":"(ssr)/./src/locales/localization-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/locales/use-locales.ts":{"*":{"id":"(ssr)/./src/locales/use-locales.ts","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/theme/theme-provider.tsx":{"*":{"id":"(ssr)/./src/theme/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/loading-screen/loading-screen.tsx":{"*":{"id":"(ssr)/./src/components/loading-screen/loading-screen.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/loading-screen/splash-screen.tsx":{"*":{"id":"(ssr)/./src/components/loading-screen/splash-screen.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/sections/error/not-found-view.tsx":{"*":{"id":"(ssr)/./src/sections/error/not-found-view.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/auth/guard/auth-guard.tsx":{"*":{"id":"(ssr)/./src/auth/guard/auth-guard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/auth/guard/guest-guard.tsx":{"*":{"id":"(ssr)/./src/auth/guard/guest-guard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/auth/guard/role-based-guard.tsx":{"*":{"id":"(ssr)/./src/auth/guard/role-based-guard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/layouts/dashboard/content.tsx":{"*":{"id":"(ssr)/./src/layouts/dashboard/content.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/layouts/dashboard/layout.tsx":{"*":{"id":"(ssr)/./src/layouts/dashboard/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/sections/orders/plt1/plt1-list-view.tsx":{"*":{"id":"(ssr)/./src/sections/orders/plt1/plt1-list-view.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/layouts/auth-split/content.tsx":{"*":{"id":"(ssr)/./src/layouts/auth-split/content.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/layouts/auth-split/layout.tsx":{"*":{"id":"(ssr)/./src/layouts/auth-split/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/auth/view/auth0/auth0-sign-in-view.tsx":{"*":{"id":"(ssr)/./src/auth/view/auth0/auth0-sign-in-view.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/callback/page.tsx":{"*":{"id":"(ssr)/./src/app/auth/callback/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/sections/orders/plt1/details/plt1air-details.tsx":{"*":{"id":"(ssr)/./src/sections/orders/plt1/details/plt1air-details.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\lab\\LoadingButton\\LoadingButton.js":{"id":"(app-pages-browser)/./node_modules/@mui/lab/LoadingButton/LoadingButton.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material-nextjs\\v13-appRouter\\appRouterV13.js":{"id":"(app-pages-browser)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Accordion\\Accordion.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\AccordionSummary\\AccordionSummary.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Alert\\Alert.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Autocomplete\\Autocomplete.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\AvatarGroup\\AvatarGroup.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/AvatarGroup/AvatarGroup.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Badge\\Badge.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Badge/Badge.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Button\\Button.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\ButtonGroup\\ButtonGroup.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/ButtonGroup/ButtonGroup.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Checkbox\\Checkbox.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Checkbox/Checkbox.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Chip\\Chip.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\CircularProgress\\CircularProgress.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\DialogActions\\DialogActions.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Divider\\Divider.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Fab\\Fab.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Fab/Fab.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\FilledInput\\FilledInput.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/FilledInput/FilledInput.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\FormControlLabel\\FormControlLabel.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/FormControlLabel/FormControlLabel.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\IconButton\\IconButton.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\InputBase\\InputBase.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/InputBase/InputBase.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\InputLabel\\InputLabel.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/InputLabel/InputLabel.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\List\\List.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/List/List.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\ListItemIcon\\ListItemIcon.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/ListItemIcon/ListItemIcon.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\MenuItem\\MenuItem.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\OutlinedInput\\OutlinedInput.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/OutlinedInput/OutlinedInput.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\PaginationItem\\PaginationItem.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/PaginationItem/PaginationItem.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Paper\\Paper.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Radio\\Radio.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Radio/Radio.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Rating\\Rating.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Rating/Rating.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Slider\\Slider.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Slider/Slider.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\styles\\styled.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/styles/styled.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\styles\\ThemeProvider.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/styles/ThemeProvider.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\styles\\ThemeProviderWithVars.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/styles/ThemeProviderWithVars.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\styles\\useTheme.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\styles\\useThemeProps.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/styles/useThemeProps.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\SvgIcon\\SvgIcon.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/SvgIcon/SvgIcon.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Switch\\Switch.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Switch/Switch.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Tab\\Tab.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Tab/Tab.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\TableCell\\TableCell.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\TableRow\\TableRow.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\TextField\\TextField.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\ToggleButton\\ToggleButton.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/ToggleButton/ToggleButton.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Tooltip\\Tooltip.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\material\\Typography\\Typography.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\styled-engine\\GlobalStyles\\GlobalStyles.js":{"id":"(app-pages-browser)/./node_modules/@mui/styled-engine/GlobalStyles/GlobalStyles.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\styled-engine\\StyledEngineProvider\\StyledEngineProvider.js":{"id":"(app-pages-browser)/./node_modules/@mui/styled-engine/StyledEngineProvider/StyledEngineProvider.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\system\\esm\\Box\\Box.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/Box/Box.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\system\\esm\\Container\\Container.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/Container/Container.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\system\\esm\\createBox\\createBox.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/createBox/createBox.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\system\\esm\\cssVars\\useCurrentColorScheme.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/cssVars/useCurrentColorScheme.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\system\\esm\\GlobalStyles\\GlobalStyles.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/GlobalStyles/GlobalStyles.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\system\\esm\\Grid\\Grid.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/Grid/Grid.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\system\\esm\\Stack\\Stack.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/Stack/Stack.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\system\\esm\\ThemeProvider\\ThemeProvider.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/ThemeProvider/ThemeProvider.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\system\\esm\\useMediaQuery\\useMediaQuery.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\system\\esm\\useTheme\\useTheme.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/useTheme/useTheme.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\system\\esm\\useThemeProps\\useThemeProps.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/useThemeProps/useThemeProps.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\system\\esm\\useThemeWithoutDefault\\useThemeWithoutDefault.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/useThemeWithoutDefault/useThemeWithoutDefault.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\utils\\esm\\useControlled\\useControlled.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useControlled/useControlled.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\utils\\esm\\useEnhancedEffect\\useEnhancedEffect.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useEnhancedEffect/useEnhancedEffect.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\utils\\esm\\useEventCallback\\useEventCallback.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useEventCallback/useEventCallback.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\utils\\esm\\useForkRef\\useForkRef.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useForkRef/useForkRef.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\utils\\esm\\useId\\useId.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useId/useId.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\utils\\esm\\useIsFocusVisible\\useIsFocusVisible.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useIsFocusVisible/useIsFocusVisible.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\utils\\esm\\useLazyRef\\useLazyRef.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useLazyRef/useLazyRef.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\utils\\esm\\useOnMount\\useOnMount.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useOnMount/useOnMount.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\utils\\esm\\usePreviousProps\\usePreviousProps.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/usePreviousProps/usePreviousProps.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\utils\\esm\\useSlotProps\\useSlotProps.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useSlotProps/useSlotProps.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\@mui\\utils\\esm\\useTimeout\\useTimeout.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useTimeout/useTimeout.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\sonner\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\auth\\context\\auth0\\auth-provider.tsx":{"id":"(app-pages-browser)/./src/auth/context/auth0/auth-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\components\\animate\\motion-lazy.tsx":{"id":"(app-pages-browser)/./src/components/animate/motion-lazy.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\components\\progress-bar\\progress-bar.tsx":{"id":"(app-pages-browser)/./src/components/progress-bar/progress-bar.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\components\\settings\\context\\settings-context.ts":{"id":"(app-pages-browser)/./src/components/settings/context/settings-context.ts","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\components\\settings\\context\\settings-provider.tsx":{"id":"(app-pages-browser)/./src/components/settings/context/settings-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\components\\settings\\context\\use-settings-context.ts":{"id":"(app-pages-browser)/./src/components/settings/context/use-settings-context.ts","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\components\\settings\\drawer\\settings-drawer.tsx":{"id":"(app-pages-browser)/./src/components/settings/drawer/settings-drawer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\components\\snackbar\\snackbar.tsx":{"id":"(app-pages-browser)/./src/components/snackbar/snackbar.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\global.css":{"id":"(app-pages-browser)/./src/global.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\locales\\all-langs.ts":{"id":"(app-pages-browser)/./src/locales/all-langs.ts","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\locales\\i18n-provider.tsx":{"id":"(app-pages-browser)/./src/locales/i18n-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\locales\\localization-provider.tsx":{"id":"(app-pages-browser)/./src/locales/localization-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\locales\\use-locales.ts":{"id":"(app-pages-browser)/./src/locales/use-locales.ts","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\theme\\theme-provider.tsx":{"id":"(app-pages-browser)/./src/theme/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\components\\loading-screen\\loading-screen.tsx":{"id":"(app-pages-browser)/./src/components/loading-screen/loading-screen.tsx","name":"*","chunks":["app/loading","static/chunks/app/loading.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\components\\loading-screen\\splash-screen.tsx":{"id":"(app-pages-browser)/./src/components/loading-screen/splash-screen.tsx","name":"*","chunks":["app/loading","static/chunks/app/loading.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\sections\\error\\not-found-view.tsx":{"id":"(app-pages-browser)/./src/sections/error/not-found-view.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\next\\dist\\lib\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\node_modules\\next\\dist\\esm\\lib\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\auth\\guard\\auth-guard.tsx":{"id":"(app-pages-browser)/./src/auth/guard/auth-guard.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\auth\\guard\\guest-guard.tsx":{"id":"(app-pages-browser)/./src/auth/guard/guest-guard.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\auth\\guard\\role-based-guard.tsx":{"id":"(app-pages-browser)/./src/auth/guard/role-based-guard.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\layouts\\dashboard\\content.tsx":{"id":"(app-pages-browser)/./src/layouts/dashboard/content.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\layouts\\dashboard\\layout.tsx":{"id":"(app-pages-browser)/./src/layouts/dashboard/layout.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\sections\\orders\\plt1\\plt1-list-view.tsx":{"id":"(app-pages-browser)/./src/sections/orders/plt1/plt1-list-view.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\layouts\\auth-split\\content.tsx":{"id":"(app-pages-browser)/./src/layouts/auth-split/content.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\layouts\\auth-split\\layout.tsx":{"id":"(app-pages-browser)/./src/layouts/auth-split/layout.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\auth\\view\\auth0\\auth0-sign-in-view.tsx":{"id":"(app-pages-browser)/./src/auth/view/auth0/auth0-sign-in-view.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\app\\auth\\callback\\page.tsx":{"id":"(app-pages-browser)/./src/app/auth/callback/page.tsx","name":"*","chunks":["app/auth/callback/page","static/chunks/app/auth/callback/page.js"],"async":false},"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\sections\\orders\\plt1\\details\\plt1air-details.tsx":{"id":"(app-pages-browser)/./src/sections/orders/plt1/details/plt1air-details.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\":[],"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\app\\loading":[],"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\app\\not-found":[],"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\app\\page":[],"C:\\Users\\<USER>\\source\\repos\\Rosseta\\Frontend\\src\\app\\auth\\callback\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/@mui/lab/LoadingButton/LoadingButton.js":{"*":{"id":"(rsc)/./node_modules/@mui/lab/LoadingButton/LoadingButton.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js":{"*":{"id":"(rsc)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Accordion/Accordion.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Alert/Alert.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Autocomplete/Autocomplete.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/AvatarGroup/AvatarGroup.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/AvatarGroup/AvatarGroup.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Badge/Badge.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Badge/Badge.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Button/Button.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/ButtonGroup/ButtonGroup.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/ButtonGroup/ButtonGroup.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Checkbox/Checkbox.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Checkbox/Checkbox.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Chip/Chip.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/CircularProgress/CircularProgress.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/DialogActions/DialogActions.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Divider/Divider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Fab/Fab.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Fab/Fab.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/FilledInput/FilledInput.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/FilledInput/FilledInput.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/FormControlLabel/FormControlLabel.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/FormControlLabel/FormControlLabel.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/IconButton/IconButton.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/InputBase/InputBase.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/InputBase/InputBase.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/InputLabel/InputLabel.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/InputLabel/InputLabel.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/List/List.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/List/List.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/ListItemIcon/ListItemIcon.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/ListItemIcon/ListItemIcon.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/MenuItem/MenuItem.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/OutlinedInput/OutlinedInput.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/OutlinedInput/OutlinedInput.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/PaginationItem/PaginationItem.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/PaginationItem/PaginationItem.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Paper/Paper.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Radio/Radio.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Radio/Radio.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Rating/Rating.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Rating/Rating.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Slider/Slider.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Slider/Slider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/styles/styled.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/styles/styled.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/styles/ThemeProvider.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/styles/ThemeProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/styles/ThemeProviderWithVars.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/styles/ThemeProviderWithVars.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/styles/useTheme.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/styles/useThemeProps.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/styles/useThemeProps.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/SvgIcon/SvgIcon.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/SvgIcon/SvgIcon.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Switch/Switch.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Switch/Switch.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Tab/Tab.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Tab/Tab.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/TableCell/TableCell.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/TableRow/TableRow.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/TextField/TextField.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/ToggleButton/ToggleButton.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/ToggleButton/ToggleButton.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Tooltip/Tooltip.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js":{"*":{"id":"(rsc)/./node_modules/@mui/material/Typography/Typography.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/styled-engine/GlobalStyles/GlobalStyles.js":{"*":{"id":"(rsc)/./node_modules/@mui/styled-engine/GlobalStyles/GlobalStyles.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/styled-engine/StyledEngineProvider/StyledEngineProvider.js":{"*":{"id":"(rsc)/./node_modules/@mui/styled-engine/StyledEngineProvider/StyledEngineProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/Box/Box.js":{"*":{"id":"(rsc)/./node_modules/@mui/system/esm/Box/Box.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/Container/Container.js":{"*":{"id":"(rsc)/./node_modules/@mui/system/esm/Container/Container.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/createBox/createBox.js":{"*":{"id":"(rsc)/./node_modules/@mui/system/esm/createBox/createBox.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/cssVars/useCurrentColorScheme.js":{"*":{"id":"(rsc)/./node_modules/@mui/system/esm/cssVars/useCurrentColorScheme.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/GlobalStyles/GlobalStyles.js":{"*":{"id":"(rsc)/./node_modules/@mui/system/esm/GlobalStyles/GlobalStyles.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/Grid/Grid.js":{"*":{"id":"(rsc)/./node_modules/@mui/system/esm/Grid/Grid.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/Stack/Stack.js":{"*":{"id":"(rsc)/./node_modules/@mui/system/esm/Stack/Stack.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/ThemeProvider/ThemeProvider.js":{"*":{"id":"(rsc)/./node_modules/@mui/system/esm/ThemeProvider/ThemeProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js":{"*":{"id":"(rsc)/./node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/useTheme/useTheme.js":{"*":{"id":"(rsc)/./node_modules/@mui/system/esm/useTheme/useTheme.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/useThemeProps/useThemeProps.js":{"*":{"id":"(rsc)/./node_modules/@mui/system/esm/useThemeProps/useThemeProps.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/useThemeWithoutDefault/useThemeWithoutDefault.js":{"*":{"id":"(rsc)/./node_modules/@mui/system/esm/useThemeWithoutDefault/useThemeWithoutDefault.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useControlled/useControlled.js":{"*":{"id":"(rsc)/./node_modules/@mui/utils/esm/useControlled/useControlled.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useEnhancedEffect/useEnhancedEffect.js":{"*":{"id":"(rsc)/./node_modules/@mui/utils/esm/useEnhancedEffect/useEnhancedEffect.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useEventCallback/useEventCallback.js":{"*":{"id":"(rsc)/./node_modules/@mui/utils/esm/useEventCallback/useEventCallback.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useForkRef/useForkRef.js":{"*":{"id":"(rsc)/./node_modules/@mui/utils/esm/useForkRef/useForkRef.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useId/useId.js":{"*":{"id":"(rsc)/./node_modules/@mui/utils/esm/useId/useId.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useIsFocusVisible/useIsFocusVisible.js":{"*":{"id":"(rsc)/./node_modules/@mui/utils/esm/useIsFocusVisible/useIsFocusVisible.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useLazyRef/useLazyRef.js":{"*":{"id":"(rsc)/./node_modules/@mui/utils/esm/useLazyRef/useLazyRef.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useOnMount/useOnMount.js":{"*":{"id":"(rsc)/./node_modules/@mui/utils/esm/useOnMount/useOnMount.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/usePreviousProps/usePreviousProps.js":{"*":{"id":"(rsc)/./node_modules/@mui/utils/esm/usePreviousProps/usePreviousProps.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useSlotProps/useSlotProps.js":{"*":{"id":"(rsc)/./node_modules/@mui/utils/esm/useSlotProps/useSlotProps.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useTimeout/useTimeout.js":{"*":{"id":"(rsc)/./node_modules/@mui/utils/esm/useTimeout/useTimeout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/sonner/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/auth/context/auth0/auth-provider.tsx":{"*":{"id":"(rsc)/./src/auth/context/auth0/auth-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/animate/motion-lazy.tsx":{"*":{"id":"(rsc)/./src/components/animate/motion-lazy.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/progress-bar/progress-bar.tsx":{"*":{"id":"(rsc)/./src/components/progress-bar/progress-bar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/settings/context/settings-context.ts":{"*":{"id":"(rsc)/./src/components/settings/context/settings-context.ts","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/settings/context/settings-provider.tsx":{"*":{"id":"(rsc)/./src/components/settings/context/settings-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/settings/context/use-settings-context.ts":{"*":{"id":"(rsc)/./src/components/settings/context/use-settings-context.ts","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/settings/drawer/settings-drawer.tsx":{"*":{"id":"(rsc)/./src/components/settings/drawer/settings-drawer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/snackbar/snackbar.tsx":{"*":{"id":"(rsc)/./src/components/snackbar/snackbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/global.css":{"*":{"id":"(rsc)/./src/global.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/locales/all-langs.ts":{"*":{"id":"(rsc)/./src/locales/all-langs.ts","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/locales/i18n-provider.tsx":{"*":{"id":"(rsc)/./src/locales/i18n-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/locales/localization-provider.tsx":{"*":{"id":"(rsc)/./src/locales/localization-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/locales/use-locales.ts":{"*":{"id":"(rsc)/./src/locales/use-locales.ts","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/theme/theme-provider.tsx":{"*":{"id":"(rsc)/./src/theme/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/loading-screen/loading-screen.tsx":{"*":{"id":"(rsc)/./src/components/loading-screen/loading-screen.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/loading-screen/splash-screen.tsx":{"*":{"id":"(rsc)/./src/components/loading-screen/splash-screen.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/sections/error/not-found-view.tsx":{"*":{"id":"(rsc)/./src/sections/error/not-found-view.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/auth/guard/auth-guard.tsx":{"*":{"id":"(rsc)/./src/auth/guard/auth-guard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/auth/guard/guest-guard.tsx":{"*":{"id":"(rsc)/./src/auth/guard/guest-guard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/auth/guard/role-based-guard.tsx":{"*":{"id":"(rsc)/./src/auth/guard/role-based-guard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/layouts/dashboard/content.tsx":{"*":{"id":"(rsc)/./src/layouts/dashboard/content.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/layouts/dashboard/layout.tsx":{"*":{"id":"(rsc)/./src/layouts/dashboard/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/sections/orders/plt1/plt1-list-view.tsx":{"*":{"id":"(rsc)/./src/sections/orders/plt1/plt1-list-view.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/layouts/auth-split/content.tsx":{"*":{"id":"(rsc)/./src/layouts/auth-split/content.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/layouts/auth-split/layout.tsx":{"*":{"id":"(rsc)/./src/layouts/auth-split/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/auth/view/auth0/auth0-sign-in-view.tsx":{"*":{"id":"(rsc)/./src/auth/view/auth0/auth0-sign-in-view.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/callback/page.tsx":{"*":{"id":"(rsc)/./src/app/auth/callback/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/sections/orders/plt1/details/plt1air-details.tsx":{"*":{"id":"(rsc)/./src/sections/orders/plt1/details/plt1air-details.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}