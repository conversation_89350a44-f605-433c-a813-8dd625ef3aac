"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@iconify";
exports.ids = ["vendor-chunks/@iconify"];
exports.modules = {

/***/ "(ssr)/./node_modules/@iconify/react/dist/iconify.js":
/*!*****************************************************!*\
  !*** ./node_modules/@iconify/react/dist/iconify.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Icon: () => (/* binding */ Icon),\n/* harmony export */   InlineIcon: () => (/* binding */ InlineIcon),\n/* harmony export */   _api: () => (/* binding */ _api),\n/* harmony export */   addAPIProvider: () => (/* binding */ addAPIProvider),\n/* harmony export */   addCollection: () => (/* binding */ addCollection),\n/* harmony export */   addIcon: () => (/* binding */ addIcon),\n/* harmony export */   buildIcon: () => (/* binding */ iconToSVG),\n/* harmony export */   calculateSize: () => (/* binding */ calculateSize),\n/* harmony export */   disableCache: () => (/* binding */ disableCache),\n/* harmony export */   enableCache: () => (/* binding */ enableCache),\n/* harmony export */   getIcon: () => (/* binding */ getIcon),\n/* harmony export */   iconExists: () => (/* binding */ iconLoaded),\n/* harmony export */   iconLoaded: () => (/* binding */ iconLoaded),\n/* harmony export */   listIcons: () => (/* binding */ listIcons),\n/* harmony export */   loadIcon: () => (/* binding */ loadIcon),\n/* harmony export */   loadIcons: () => (/* binding */ loadIcons),\n/* harmony export */   replaceIDs: () => (/* binding */ replaceIDs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ Icon,InlineIcon,_api,addAPIProvider,addCollection,addIcon,buildIcon,calculateSize,disableCache,enableCache,getIcon,iconExists,iconLoaded,listIcons,loadIcon,loadIcons,replaceIDs auto */ \nconst defaultIconDimensions = Object.freeze({\n    left: 0,\n    top: 0,\n    width: 16,\n    height: 16\n});\nconst defaultIconTransformations = Object.freeze({\n    rotate: 0,\n    vFlip: false,\n    hFlip: false\n});\nconst defaultIconProps = Object.freeze({\n    ...defaultIconDimensions,\n    ...defaultIconTransformations\n});\nconst defaultExtendedIconProps = Object.freeze({\n    ...defaultIconProps,\n    body: \"\",\n    hidden: false\n});\nfunction mergeIconTransformations(obj1, obj2) {\n    const result = {};\n    if (!obj1.hFlip !== !obj2.hFlip) {\n        result.hFlip = true;\n    }\n    if (!obj1.vFlip !== !obj2.vFlip) {\n        result.vFlip = true;\n    }\n    const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;\n    if (rotate) {\n        result.rotate = rotate;\n    }\n    return result;\n}\nfunction mergeIconData(parent, child) {\n    const result = mergeIconTransformations(parent, child);\n    for(const key in defaultExtendedIconProps){\n        if (key in defaultIconTransformations) {\n            if (key in parent && !(key in result)) {\n                result[key] = defaultIconTransformations[key];\n            }\n        } else if (key in child) {\n            result[key] = child[key];\n        } else if (key in parent) {\n            result[key] = parent[key];\n        }\n    }\n    return result;\n}\nfunction getIconsTree(data, names) {\n    const icons = data.icons;\n    const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n    const resolved = /* @__PURE__ */ Object.create(null);\n    function resolve(name) {\n        if (icons[name]) {\n            return resolved[name] = [];\n        }\n        if (!(name in resolved)) {\n            resolved[name] = null;\n            const parent = aliases[name] && aliases[name].parent;\n            const value = parent && resolve(parent);\n            if (value) {\n                resolved[name] = [\n                    parent\n                ].concat(value);\n            }\n        }\n        return resolved[name];\n    }\n    (names || Object.keys(icons).concat(Object.keys(aliases))).forEach(resolve);\n    return resolved;\n}\nfunction internalGetIconData(data, name, tree) {\n    const icons = data.icons;\n    const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n    let currentProps = {};\n    function parse(name2) {\n        currentProps = mergeIconData(icons[name2] || aliases[name2], currentProps);\n    }\n    parse(name);\n    tree.forEach(parse);\n    return mergeIconData(data, currentProps);\n}\nfunction parseIconSet(data, callback) {\n    const names = [];\n    if (typeof data !== \"object\" || typeof data.icons !== \"object\") {\n        return names;\n    }\n    if (data.not_found instanceof Array) {\n        data.not_found.forEach((name)=>{\n            callback(name, null);\n            names.push(name);\n        });\n    }\n    const tree = getIconsTree(data);\n    for(const name in tree){\n        const item = tree[name];\n        if (item) {\n            callback(name, internalGetIconData(data, name, item));\n            names.push(name);\n        }\n    }\n    return names;\n}\nconst matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;\nconst stringToIcon = (value, validate, allowSimpleName, provider = \"\")=>{\n    const colonSeparated = value.split(\":\");\n    if (value.slice(0, 1) === \"@\") {\n        if (colonSeparated.length < 2 || colonSeparated.length > 3) {\n            return null;\n        }\n        provider = colonSeparated.shift().slice(1);\n    }\n    if (colonSeparated.length > 3 || !colonSeparated.length) {\n        return null;\n    }\n    if (colonSeparated.length > 1) {\n        const name2 = colonSeparated.pop();\n        const prefix = colonSeparated.pop();\n        const result = {\n            // Allow provider without '@': \"provider:prefix:name\"\n            provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,\n            prefix,\n            name: name2\n        };\n        return validate && !validateIconName(result) ? null : result;\n    }\n    const name = colonSeparated[0];\n    const dashSeparated = name.split(\"-\");\n    if (dashSeparated.length > 1) {\n        const result = {\n            provider,\n            prefix: dashSeparated.shift(),\n            name: dashSeparated.join(\"-\")\n        };\n        return validate && !validateIconName(result) ? null : result;\n    }\n    if (allowSimpleName && provider === \"\") {\n        const result = {\n            provider,\n            prefix: \"\",\n            name\n        };\n        return validate && !validateIconName(result, allowSimpleName) ? null : result;\n    }\n    return null;\n};\nconst validateIconName = (icon, allowSimpleName)=>{\n    if (!icon) {\n        return false;\n    }\n    return !!((icon.provider === \"\" || icon.provider.match(matchIconName)) && (allowSimpleName && icon.prefix === \"\" || icon.prefix.match(matchIconName)) && icon.name.match(matchIconName));\n};\nconst optionalPropertyDefaults = {\n    provider: \"\",\n    aliases: {},\n    not_found: {},\n    ...defaultIconDimensions\n};\nfunction checkOptionalProps(item, defaults) {\n    for(const prop in defaults){\n        if (prop in item && typeof item[prop] !== typeof defaults[prop]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction quicklyValidateIconSet(obj) {\n    if (typeof obj !== \"object\" || obj === null) {\n        return null;\n    }\n    const data = obj;\n    if (typeof data.prefix !== \"string\" || !obj.icons || typeof obj.icons !== \"object\") {\n        return null;\n    }\n    if (!checkOptionalProps(obj, optionalPropertyDefaults)) {\n        return null;\n    }\n    const icons = data.icons;\n    for(const name in icons){\n        const icon = icons[name];\n        if (!name.match(matchIconName) || typeof icon.body !== \"string\" || !checkOptionalProps(icon, defaultExtendedIconProps)) {\n            return null;\n        }\n    }\n    const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n    for(const name in aliases){\n        const icon = aliases[name];\n        const parent = icon.parent;\n        if (!name.match(matchIconName) || typeof parent !== \"string\" || !icons[parent] && !aliases[parent] || !checkOptionalProps(icon, defaultExtendedIconProps)) {\n            return null;\n        }\n    }\n    return data;\n}\nconst dataStorage = /* @__PURE__ */ Object.create(null);\nfunction newStorage(provider, prefix) {\n    return {\n        provider,\n        prefix,\n        icons: /* @__PURE__ */ Object.create(null),\n        missing: /* @__PURE__ */ new Set()\n    };\n}\nfunction getStorage(provider, prefix) {\n    const providerStorage = dataStorage[provider] || (dataStorage[provider] = /* @__PURE__ */ Object.create(null));\n    return providerStorage[prefix] || (providerStorage[prefix] = newStorage(provider, prefix));\n}\nfunction addIconSet(storage, data) {\n    if (!quicklyValidateIconSet(data)) {\n        return [];\n    }\n    return parseIconSet(data, (name, icon)=>{\n        if (icon) {\n            storage.icons[name] = icon;\n        } else {\n            storage.missing.add(name);\n        }\n    });\n}\nfunction addIconToStorage(storage, name, icon) {\n    try {\n        if (typeof icon.body === \"string\") {\n            storage.icons[name] = {\n                ...icon\n            };\n            return true;\n        }\n    } catch (err) {}\n    return false;\n}\nfunction listIcons(provider, prefix) {\n    let allIcons = [];\n    const providers = typeof provider === \"string\" ? [\n        provider\n    ] : Object.keys(dataStorage);\n    providers.forEach((provider2)=>{\n        const prefixes = typeof provider2 === \"string\" && typeof prefix === \"string\" ? [\n            prefix\n        ] : Object.keys(dataStorage[provider2] || {});\n        prefixes.forEach((prefix2)=>{\n            const storage = getStorage(provider2, prefix2);\n            allIcons = allIcons.concat(Object.keys(storage.icons).map((name)=>(provider2 !== \"\" ? \"@\" + provider2 + \":\" : \"\") + prefix2 + \":\" + name));\n        });\n    });\n    return allIcons;\n}\nlet simpleNames = false;\nfunction allowSimpleNames(allow) {\n    if (typeof allow === \"boolean\") {\n        simpleNames = allow;\n    }\n    return simpleNames;\n}\nfunction getIconData(name) {\n    const icon = typeof name === \"string\" ? stringToIcon(name, true, simpleNames) : name;\n    if (icon) {\n        const storage = getStorage(icon.provider, icon.prefix);\n        const iconName = icon.name;\n        return storage.icons[iconName] || (storage.missing.has(iconName) ? null : void 0);\n    }\n}\nfunction addIcon(name, data) {\n    const icon = stringToIcon(name, true, simpleNames);\n    if (!icon) {\n        return false;\n    }\n    const storage = getStorage(icon.provider, icon.prefix);\n    return addIconToStorage(storage, icon.name, data);\n}\nfunction addCollection(data, provider) {\n    if (typeof data !== \"object\") {\n        return false;\n    }\n    if (typeof provider !== \"string\") {\n        provider = data.provider || \"\";\n    }\n    if (simpleNames && !provider && !data.prefix) {\n        let added = false;\n        if (quicklyValidateIconSet(data)) {\n            data.prefix = \"\";\n            parseIconSet(data, (name, icon)=>{\n                if (icon && addIcon(name, icon)) {\n                    added = true;\n                }\n            });\n        }\n        return added;\n    }\n    const prefix = data.prefix;\n    if (!validateIconName({\n        provider,\n        prefix,\n        name: \"a\"\n    })) {\n        return false;\n    }\n    const storage = getStorage(provider, prefix);\n    return !!addIconSet(storage, data);\n}\nfunction iconLoaded(name) {\n    return !!getIconData(name);\n}\nfunction getIcon(name) {\n    const result = getIconData(name);\n    return result ? {\n        ...defaultIconProps,\n        ...result\n    } : null;\n}\nconst defaultIconSizeCustomisations = Object.freeze({\n    width: null,\n    height: null\n});\nconst defaultIconCustomisations = Object.freeze({\n    // Dimensions\n    ...defaultIconSizeCustomisations,\n    // Transformations\n    ...defaultIconTransformations\n});\nconst unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;\nconst unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;\nfunction calculateSize(size, ratio, precision) {\n    if (ratio === 1) {\n        return size;\n    }\n    precision = precision || 100;\n    if (typeof size === \"number\") {\n        return Math.ceil(size * ratio * precision) / precision;\n    }\n    if (typeof size !== \"string\") {\n        return size;\n    }\n    const oldParts = size.split(unitsSplit);\n    if (oldParts === null || !oldParts.length) {\n        return size;\n    }\n    const newParts = [];\n    let code = oldParts.shift();\n    let isNumber = unitsTest.test(code);\n    while(true){\n        if (isNumber) {\n            const num = parseFloat(code);\n            if (isNaN(num)) {\n                newParts.push(code);\n            } else {\n                newParts.push(Math.ceil(num * ratio * precision) / precision);\n            }\n        } else {\n            newParts.push(code);\n        }\n        code = oldParts.shift();\n        if (code === void 0) {\n            return newParts.join(\"\");\n        }\n        isNumber = !isNumber;\n    }\n}\nfunction splitSVGDefs(content, tag = \"defs\") {\n    let defs = \"\";\n    const index = content.indexOf(\"<\" + tag);\n    while(index >= 0){\n        const start = content.indexOf(\">\", index);\n        const end = content.indexOf(\"</\" + tag);\n        if (start === -1 || end === -1) {\n            break;\n        }\n        const endEnd = content.indexOf(\">\", end);\n        if (endEnd === -1) {\n            break;\n        }\n        defs += content.slice(start + 1, end).trim();\n        content = content.slice(0, index).trim() + content.slice(endEnd + 1);\n    }\n    return {\n        defs,\n        content\n    };\n}\nfunction mergeDefsAndContent(defs, content) {\n    return defs ? \"<defs>\" + defs + \"</defs>\" + content : content;\n}\nfunction wrapSVGContent(body, start, end) {\n    const split = splitSVGDefs(body);\n    return mergeDefsAndContent(split.defs, start + split.content + end);\n}\nconst isUnsetKeyword = (value)=>value === \"unset\" || value === \"undefined\" || value === \"none\";\nfunction iconToSVG(icon, customisations) {\n    const fullIcon = {\n        ...defaultIconProps,\n        ...icon\n    };\n    const fullCustomisations = {\n        ...defaultIconCustomisations,\n        ...customisations\n    };\n    const box = {\n        left: fullIcon.left,\n        top: fullIcon.top,\n        width: fullIcon.width,\n        height: fullIcon.height\n    };\n    let body = fullIcon.body;\n    [\n        fullIcon,\n        fullCustomisations\n    ].forEach((props)=>{\n        const transformations = [];\n        const hFlip = props.hFlip;\n        const vFlip = props.vFlip;\n        let rotation = props.rotate;\n        if (hFlip) {\n            if (vFlip) {\n                rotation += 2;\n            } else {\n                transformations.push(\"translate(\" + (box.width + box.left).toString() + \" \" + (0 - box.top).toString() + \")\");\n                transformations.push(\"scale(-1 1)\");\n                box.top = box.left = 0;\n            }\n        } else if (vFlip) {\n            transformations.push(\"translate(\" + (0 - box.left).toString() + \" \" + (box.height + box.top).toString() + \")\");\n            transformations.push(\"scale(1 -1)\");\n            box.top = box.left = 0;\n        }\n        let tempValue;\n        if (rotation < 0) {\n            rotation -= Math.floor(rotation / 4) * 4;\n        }\n        rotation = rotation % 4;\n        switch(rotation){\n            case 1:\n                tempValue = box.height / 2 + box.top;\n                transformations.unshift(\"rotate(90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\");\n                break;\n            case 2:\n                transformations.unshift(\"rotate(180 \" + (box.width / 2 + box.left).toString() + \" \" + (box.height / 2 + box.top).toString() + \")\");\n                break;\n            case 3:\n                tempValue = box.width / 2 + box.left;\n                transformations.unshift(\"rotate(-90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\");\n                break;\n        }\n        if (rotation % 2 === 1) {\n            if (box.left !== box.top) {\n                tempValue = box.left;\n                box.left = box.top;\n                box.top = tempValue;\n            }\n            if (box.width !== box.height) {\n                tempValue = box.width;\n                box.width = box.height;\n                box.height = tempValue;\n            }\n        }\n        if (transformations.length) {\n            body = wrapSVGContent(body, '<g transform=\"' + transformations.join(\" \") + '\">', \"</g>\");\n        }\n    });\n    const customisationsWidth = fullCustomisations.width;\n    const customisationsHeight = fullCustomisations.height;\n    const boxWidth = box.width;\n    const boxHeight = box.height;\n    let width;\n    let height;\n    if (customisationsWidth === null) {\n        height = customisationsHeight === null ? \"1em\" : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n        width = calculateSize(height, boxWidth / boxHeight);\n    } else {\n        width = customisationsWidth === \"auto\" ? boxWidth : customisationsWidth;\n        height = customisationsHeight === null ? calculateSize(width, boxHeight / boxWidth) : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n    }\n    const attributes = {};\n    const setAttr = (prop, value)=>{\n        if (!isUnsetKeyword(value)) {\n            attributes[prop] = value.toString();\n        }\n    };\n    setAttr(\"width\", width);\n    setAttr(\"height\", height);\n    const viewBox = [\n        box.left,\n        box.top,\n        boxWidth,\n        boxHeight\n    ];\n    attributes.viewBox = viewBox.join(\" \");\n    return {\n        attributes,\n        viewBox,\n        body\n    };\n}\nconst regex = /\\sid=\"(\\S+)\"/g;\nconst randomPrefix = \"IconifyId\" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);\nlet counter = 0;\nfunction replaceIDs(body, prefix = randomPrefix) {\n    const ids = [];\n    let match;\n    while(match = regex.exec(body)){\n        ids.push(match[1]);\n    }\n    if (!ids.length) {\n        return body;\n    }\n    const suffix = \"suffix\" + (Math.random() * 16777216 | Date.now()).toString(16);\n    ids.forEach((id)=>{\n        const newID = typeof prefix === \"function\" ? prefix(id) : prefix + (counter++).toString();\n        const escapedID = id.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n        body = body.replace(// Allowed characters before id: [#;\"]\n        // Allowed characters after id: [)\"], .[a-z]\n        new RegExp('([#;\"])(' + escapedID + ')([\")]|\\\\.[a-z])', \"g\"), \"$1\" + newID + suffix + \"$3\");\n    });\n    body = body.replace(new RegExp(suffix, \"g\"), \"\");\n    return body;\n}\nconst storage = /* @__PURE__ */ Object.create(null);\nfunction setAPIModule(provider, item) {\n    storage[provider] = item;\n}\nfunction getAPIModule(provider) {\n    return storage[provider] || storage[\"\"];\n}\nfunction createAPIConfig(source) {\n    let resources;\n    if (typeof source.resources === \"string\") {\n        resources = [\n            source.resources\n        ];\n    } else {\n        resources = source.resources;\n        if (!(resources instanceof Array) || !resources.length) {\n            return null;\n        }\n    }\n    const result = {\n        // API hosts\n        resources,\n        // Root path\n        path: source.path || \"/\",\n        // URL length limit\n        maxURL: source.maxURL || 500,\n        // Timeout before next host is used.\n        rotate: source.rotate || 750,\n        // Timeout before failing query.\n        timeout: source.timeout || 5e3,\n        // Randomise default API end point.\n        random: source.random === true,\n        // Start index\n        index: source.index || 0,\n        // Receive data after time out (used if time out kicks in first, then API module sends data anyway).\n        dataAfterTimeout: source.dataAfterTimeout !== false\n    };\n    return result;\n}\nconst configStorage = /* @__PURE__ */ Object.create(null);\nconst fallBackAPISources = [\n    \"https://api.simplesvg.com\",\n    \"https://api.unisvg.com\"\n];\nconst fallBackAPI = [];\nwhile(fallBackAPISources.length > 0){\n    if (fallBackAPISources.length === 1) {\n        fallBackAPI.push(fallBackAPISources.shift());\n    } else {\n        if (Math.random() > 0.5) {\n            fallBackAPI.push(fallBackAPISources.shift());\n        } else {\n            fallBackAPI.push(fallBackAPISources.pop());\n        }\n    }\n}\nconfigStorage[\"\"] = createAPIConfig({\n    resources: [\n        \"https://api.iconify.design\"\n    ].concat(fallBackAPI)\n});\nfunction addAPIProvider(provider, customConfig) {\n    const config = createAPIConfig(customConfig);\n    if (config === null) {\n        return false;\n    }\n    configStorage[provider] = config;\n    return true;\n}\nfunction getAPIConfig(provider) {\n    return configStorage[provider];\n}\nfunction listAPIProviders() {\n    return Object.keys(configStorage);\n}\nconst detectFetch = ()=>{\n    let callback;\n    try {\n        callback = fetch;\n        if (typeof callback === \"function\") {\n            return callback;\n        }\n    } catch (err) {}\n};\nlet fetchModule = detectFetch();\nfunction setFetch(fetch2) {\n    fetchModule = fetch2;\n}\nfunction getFetch() {\n    return fetchModule;\n}\nfunction calculateMaxLength(provider, prefix) {\n    const config = getAPIConfig(provider);\n    if (!config) {\n        return 0;\n    }\n    let result;\n    if (!config.maxURL) {\n        result = 0;\n    } else {\n        let maxHostLength = 0;\n        config.resources.forEach((item)=>{\n            const host = item;\n            maxHostLength = Math.max(maxHostLength, host.length);\n        });\n        const url = prefix + \".json?icons=\";\n        result = config.maxURL - maxHostLength - config.path.length - url.length;\n    }\n    return result;\n}\nfunction shouldAbort(status) {\n    return status === 404;\n}\nconst prepare = (provider, prefix, icons)=>{\n    const results = [];\n    const maxLength = calculateMaxLength(provider, prefix);\n    const type = \"icons\";\n    let item = {\n        type,\n        provider,\n        prefix,\n        icons: []\n    };\n    let length = 0;\n    icons.forEach((name, index)=>{\n        length += name.length + 1;\n        if (length >= maxLength && index > 0) {\n            results.push(item);\n            item = {\n                type,\n                provider,\n                prefix,\n                icons: []\n            };\n            length = name.length;\n        }\n        item.icons.push(name);\n    });\n    results.push(item);\n    return results;\n};\nfunction getPath(provider) {\n    if (typeof provider === \"string\") {\n        const config = getAPIConfig(provider);\n        if (config) {\n            return config.path;\n        }\n    }\n    return \"/\";\n}\nconst send = (host, params, callback)=>{\n    if (!fetchModule) {\n        callback(\"abort\", 424);\n        return;\n    }\n    let path = getPath(params.provider);\n    switch(params.type){\n        case \"icons\":\n            {\n                const prefix = params.prefix;\n                const icons = params.icons;\n                const iconsList = icons.join(\",\");\n                const urlParams = new URLSearchParams({\n                    icons: iconsList\n                });\n                path += prefix + \".json?\" + urlParams.toString();\n                break;\n            }\n        case \"custom\":\n            {\n                const uri = params.uri;\n                path += uri.slice(0, 1) === \"/\" ? uri.slice(1) : uri;\n                break;\n            }\n        default:\n            callback(\"abort\", 400);\n            return;\n    }\n    let defaultError = 503;\n    fetchModule(host + path).then((response)=>{\n        const status = response.status;\n        if (status !== 200) {\n            setTimeout(()=>{\n                callback(shouldAbort(status) ? \"abort\" : \"next\", status);\n            });\n            return;\n        }\n        defaultError = 501;\n        return response.json();\n    }).then((data)=>{\n        if (typeof data !== \"object\" || data === null) {\n            setTimeout(()=>{\n                if (data === 404) {\n                    callback(\"abort\", data);\n                } else {\n                    callback(\"next\", defaultError);\n                }\n            });\n            return;\n        }\n        setTimeout(()=>{\n            callback(\"success\", data);\n        });\n    }).catch(()=>{\n        callback(\"next\", defaultError);\n    });\n};\nconst fetchAPIModule = {\n    prepare,\n    send\n};\nfunction sortIcons(icons) {\n    const result = {\n        loaded: [],\n        missing: [],\n        pending: []\n    };\n    const storage = /* @__PURE__ */ Object.create(null);\n    icons.sort((a, b)=>{\n        if (a.provider !== b.provider) {\n            return a.provider.localeCompare(b.provider);\n        }\n        if (a.prefix !== b.prefix) {\n            return a.prefix.localeCompare(b.prefix);\n        }\n        return a.name.localeCompare(b.name);\n    });\n    let lastIcon = {\n        provider: \"\",\n        prefix: \"\",\n        name: \"\"\n    };\n    icons.forEach((icon)=>{\n        if (lastIcon.name === icon.name && lastIcon.prefix === icon.prefix && lastIcon.provider === icon.provider) {\n            return;\n        }\n        lastIcon = icon;\n        const provider = icon.provider;\n        const prefix = icon.prefix;\n        const name = icon.name;\n        const providerStorage = storage[provider] || (storage[provider] = /* @__PURE__ */ Object.create(null));\n        const localStorage = providerStorage[prefix] || (providerStorage[prefix] = getStorage(provider, prefix));\n        let list;\n        if (name in localStorage.icons) {\n            list = result.loaded;\n        } else if (prefix === \"\" || localStorage.missing.has(name)) {\n            list = result.missing;\n        } else {\n            list = result.pending;\n        }\n        const item = {\n            provider,\n            prefix,\n            name\n        };\n        list.push(item);\n    });\n    return result;\n}\nfunction removeCallback(storages, id) {\n    storages.forEach((storage)=>{\n        const items = storage.loaderCallbacks;\n        if (items) {\n            storage.loaderCallbacks = items.filter((row)=>row.id !== id);\n        }\n    });\n}\nfunction updateCallbacks(storage) {\n    if (!storage.pendingCallbacksFlag) {\n        storage.pendingCallbacksFlag = true;\n        setTimeout(()=>{\n            storage.pendingCallbacksFlag = false;\n            const items = storage.loaderCallbacks ? storage.loaderCallbacks.slice(0) : [];\n            if (!items.length) {\n                return;\n            }\n            let hasPending = false;\n            const provider = storage.provider;\n            const prefix = storage.prefix;\n            items.forEach((item)=>{\n                const icons = item.icons;\n                const oldLength = icons.pending.length;\n                icons.pending = icons.pending.filter((icon)=>{\n                    if (icon.prefix !== prefix) {\n                        return true;\n                    }\n                    const name = icon.name;\n                    if (storage.icons[name]) {\n                        icons.loaded.push({\n                            provider,\n                            prefix,\n                            name\n                        });\n                    } else if (storage.missing.has(name)) {\n                        icons.missing.push({\n                            provider,\n                            prefix,\n                            name\n                        });\n                    } else {\n                        hasPending = true;\n                        return true;\n                    }\n                    return false;\n                });\n                if (icons.pending.length !== oldLength) {\n                    if (!hasPending) {\n                        removeCallback([\n                            storage\n                        ], item.id);\n                    }\n                    item.callback(icons.loaded.slice(0), icons.missing.slice(0), icons.pending.slice(0), item.abort);\n                }\n            });\n        });\n    }\n}\nlet idCounter = 0;\nfunction storeCallback(callback, icons, pendingSources) {\n    const id = idCounter++;\n    const abort = removeCallback.bind(null, pendingSources, id);\n    if (!icons.pending.length) {\n        return abort;\n    }\n    const item = {\n        id,\n        icons,\n        callback,\n        abort\n    };\n    pendingSources.forEach((storage)=>{\n        (storage.loaderCallbacks || (storage.loaderCallbacks = [])).push(item);\n    });\n    return abort;\n}\nfunction listToIcons(list, validate = true, simpleNames = false) {\n    const result = [];\n    list.forEach((item)=>{\n        const icon = typeof item === \"string\" ? stringToIcon(item, validate, simpleNames) : item;\n        if (icon) {\n            result.push(icon);\n        }\n    });\n    return result;\n}\n// src/config.ts\nvar defaultConfig = {\n    resources: [],\n    index: 0,\n    timeout: 2e3,\n    rotate: 750,\n    random: false,\n    dataAfterTimeout: false\n};\n// src/query.ts\nfunction sendQuery(config, payload, query, done) {\n    const resourcesCount = config.resources.length;\n    const startIndex = config.random ? Math.floor(Math.random() * resourcesCount) : config.index;\n    let resources;\n    if (config.random) {\n        let list = config.resources.slice(0);\n        resources = [];\n        while(list.length > 1){\n            const nextIndex = Math.floor(Math.random() * list.length);\n            resources.push(list[nextIndex]);\n            list = list.slice(0, nextIndex).concat(list.slice(nextIndex + 1));\n        }\n        resources = resources.concat(list);\n    } else {\n        resources = config.resources.slice(startIndex).concat(config.resources.slice(0, startIndex));\n    }\n    const startTime = Date.now();\n    let status = \"pending\";\n    let queriesSent = 0;\n    let lastError;\n    let timer = null;\n    let queue = [];\n    let doneCallbacks = [];\n    if (typeof done === \"function\") {\n        doneCallbacks.push(done);\n    }\n    function resetTimer() {\n        if (timer) {\n            clearTimeout(timer);\n            timer = null;\n        }\n    }\n    function abort() {\n        if (status === \"pending\") {\n            status = \"aborted\";\n        }\n        resetTimer();\n        queue.forEach((item)=>{\n            if (item.status === \"pending\") {\n                item.status = \"aborted\";\n            }\n        });\n        queue = [];\n    }\n    function subscribe(callback, overwrite) {\n        if (overwrite) {\n            doneCallbacks = [];\n        }\n        if (typeof callback === \"function\") {\n            doneCallbacks.push(callback);\n        }\n    }\n    function getQueryStatus() {\n        return {\n            startTime,\n            payload,\n            status,\n            queriesSent,\n            queriesPending: queue.length,\n            subscribe,\n            abort\n        };\n    }\n    function failQuery() {\n        status = \"failed\";\n        doneCallbacks.forEach((callback)=>{\n            callback(void 0, lastError);\n        });\n    }\n    function clearQueue() {\n        queue.forEach((item)=>{\n            if (item.status === \"pending\") {\n                item.status = \"aborted\";\n            }\n        });\n        queue = [];\n    }\n    function moduleResponse(item, response, data) {\n        const isError = response !== \"success\";\n        queue = queue.filter((queued)=>queued !== item);\n        switch(status){\n            case \"pending\":\n                break;\n            case \"failed\":\n                if (isError || !config.dataAfterTimeout) {\n                    return;\n                }\n                break;\n            default:\n                return;\n        }\n        if (response === \"abort\") {\n            lastError = data;\n            failQuery();\n            return;\n        }\n        if (isError) {\n            lastError = data;\n            if (!queue.length) {\n                if (!resources.length) {\n                    failQuery();\n                } else {\n                    execNext();\n                }\n            }\n            return;\n        }\n        resetTimer();\n        clearQueue();\n        if (!config.random) {\n            const index = config.resources.indexOf(item.resource);\n            if (index !== -1 && index !== config.index) {\n                config.index = index;\n            }\n        }\n        status = \"completed\";\n        doneCallbacks.forEach((callback)=>{\n            callback(data);\n        });\n    }\n    function execNext() {\n        if (status !== \"pending\") {\n            return;\n        }\n        resetTimer();\n        const resource = resources.shift();\n        if (resource === void 0) {\n            if (queue.length) {\n                timer = setTimeout(()=>{\n                    resetTimer();\n                    if (status === \"pending\") {\n                        clearQueue();\n                        failQuery();\n                    }\n                }, config.timeout);\n                return;\n            }\n            failQuery();\n            return;\n        }\n        const item = {\n            status: \"pending\",\n            resource,\n            callback: (status2, data)=>{\n                moduleResponse(item, status2, data);\n            }\n        };\n        queue.push(item);\n        queriesSent++;\n        timer = setTimeout(execNext, config.rotate);\n        query(resource, payload, item.callback);\n    }\n    setTimeout(execNext);\n    return getQueryStatus;\n}\n// src/index.ts\nfunction initRedundancy(cfg) {\n    const config = {\n        ...defaultConfig,\n        ...cfg\n    };\n    let queries = [];\n    function cleanup() {\n        queries = queries.filter((item)=>item().status === \"pending\");\n    }\n    function query(payload, queryCallback, doneCallback) {\n        const query2 = sendQuery(config, payload, queryCallback, (data, error)=>{\n            cleanup();\n            if (doneCallback) {\n                doneCallback(data, error);\n            }\n        });\n        queries.push(query2);\n        return query2;\n    }\n    function find(callback) {\n        return queries.find((value)=>{\n            return callback(value);\n        }) || null;\n    }\n    const instance = {\n        query,\n        find,\n        setIndex: (index)=>{\n            config.index = index;\n        },\n        getIndex: ()=>config.index,\n        cleanup\n    };\n    return instance;\n}\nfunction emptyCallback$1() {}\nconst redundancyCache = /* @__PURE__ */ Object.create(null);\nfunction getRedundancyCache(provider) {\n    if (!redundancyCache[provider]) {\n        const config = getAPIConfig(provider);\n        if (!config) {\n            return;\n        }\n        const redundancy = initRedundancy(config);\n        const cachedReundancy = {\n            config,\n            redundancy\n        };\n        redundancyCache[provider] = cachedReundancy;\n    }\n    return redundancyCache[provider];\n}\nfunction sendAPIQuery(target, query, callback) {\n    let redundancy;\n    let send;\n    if (typeof target === \"string\") {\n        const api = getAPIModule(target);\n        if (!api) {\n            callback(void 0, 424);\n            return emptyCallback$1;\n        }\n        send = api.send;\n        const cached = getRedundancyCache(target);\n        if (cached) {\n            redundancy = cached.redundancy;\n        }\n    } else {\n        const config = createAPIConfig(target);\n        if (config) {\n            redundancy = initRedundancy(config);\n            const moduleKey = target.resources ? target.resources[0] : \"\";\n            const api = getAPIModule(moduleKey);\n            if (api) {\n                send = api.send;\n            }\n        }\n    }\n    if (!redundancy || !send) {\n        callback(void 0, 424);\n        return emptyCallback$1;\n    }\n    return redundancy.query(query, send, callback)().abort;\n}\nconst browserCacheVersion = \"iconify2\";\nconst browserCachePrefix = \"iconify\";\nconst browserCacheCountKey = browserCachePrefix + \"-count\";\nconst browserCacheVersionKey = browserCachePrefix + \"-version\";\nconst browserStorageHour = 36e5;\nconst browserStorageCacheExpiration = 168;\nconst browserStorageLimit = 50;\nfunction getStoredItem(func, key) {\n    try {\n        return func.getItem(key);\n    } catch (err) {}\n}\nfunction setStoredItem(func, key, value) {\n    try {\n        func.setItem(key, value);\n        return true;\n    } catch (err) {}\n}\nfunction removeStoredItem(func, key) {\n    try {\n        func.removeItem(key);\n    } catch (err) {}\n}\nfunction setBrowserStorageItemsCount(storage, value) {\n    return setStoredItem(storage, browserCacheCountKey, value.toString());\n}\nfunction getBrowserStorageItemsCount(storage) {\n    return parseInt(getStoredItem(storage, browserCacheCountKey)) || 0;\n}\nconst browserStorageConfig = {\n    local: true,\n    session: true\n};\nconst browserStorageEmptyItems = {\n    local: /* @__PURE__ */ new Set(),\n    session: /* @__PURE__ */ new Set()\n};\nlet browserStorageStatus = false;\nfunction setBrowserStorageStatus(status) {\n    browserStorageStatus = status;\n}\nlet _window =  true ? {} : 0;\nfunction getBrowserStorage(key) {\n    const attr = key + \"Storage\";\n    try {\n        if (_window && _window[attr] && typeof _window[attr].length === \"number\") {\n            return _window[attr];\n        }\n    } catch (err) {}\n    browserStorageConfig[key] = false;\n}\nfunction iterateBrowserStorage(key, callback) {\n    const func = getBrowserStorage(key);\n    if (!func) {\n        return;\n    }\n    const version = getStoredItem(func, browserCacheVersionKey);\n    if (version !== browserCacheVersion) {\n        if (version) {\n            const total2 = getBrowserStorageItemsCount(func);\n            for(let i = 0; i < total2; i++){\n                removeStoredItem(func, browserCachePrefix + i.toString());\n            }\n        }\n        setStoredItem(func, browserCacheVersionKey, browserCacheVersion);\n        setBrowserStorageItemsCount(func, 0);\n        return;\n    }\n    const minTime = Math.floor(Date.now() / browserStorageHour) - browserStorageCacheExpiration;\n    const parseItem = (index)=>{\n        const name = browserCachePrefix + index.toString();\n        const item = getStoredItem(func, name);\n        if (typeof item !== \"string\") {\n            return;\n        }\n        try {\n            const data = JSON.parse(item);\n            if (typeof data === \"object\" && typeof data.cached === \"number\" && data.cached > minTime && typeof data.provider === \"string\" && typeof data.data === \"object\" && typeof data.data.prefix === \"string\" && // Valid item: run callback\n            callback(data, index)) {\n                return true;\n            }\n        } catch (err) {}\n        removeStoredItem(func, name);\n    };\n    let total = getBrowserStorageItemsCount(func);\n    for(let i = total - 1; i >= 0; i--){\n        if (!parseItem(i)) {\n            if (i === total - 1) {\n                total--;\n                setBrowserStorageItemsCount(func, total);\n            } else {\n                browserStorageEmptyItems[key].add(i);\n            }\n        }\n    }\n}\nfunction initBrowserStorage() {\n    if (browserStorageStatus) {\n        return;\n    }\n    setBrowserStorageStatus(true);\n    for(const key in browserStorageConfig){\n        iterateBrowserStorage(key, (item)=>{\n            const iconSet = item.data;\n            const provider = item.provider;\n            const prefix = iconSet.prefix;\n            const storage = getStorage(provider, prefix);\n            if (!addIconSet(storage, iconSet).length) {\n                return false;\n            }\n            const lastModified = iconSet.lastModified || -1;\n            storage.lastModifiedCached = storage.lastModifiedCached ? Math.min(storage.lastModifiedCached, lastModified) : lastModified;\n            return true;\n        });\n    }\n}\nfunction updateLastModified(storage, lastModified) {\n    const lastValue = storage.lastModifiedCached;\n    if (// Matches or newer\n    lastValue && lastValue >= lastModified) {\n        return lastValue === lastModified;\n    }\n    storage.lastModifiedCached = lastModified;\n    if (lastValue) {\n        for(const key in browserStorageConfig){\n            iterateBrowserStorage(key, (item)=>{\n                const iconSet = item.data;\n                return item.provider !== storage.provider || iconSet.prefix !== storage.prefix || iconSet.lastModified === lastModified;\n            });\n        }\n    }\n    return true;\n}\nfunction storeInBrowserStorage(storage, data) {\n    if (!browserStorageStatus) {\n        initBrowserStorage();\n    }\n    function store(key) {\n        let func;\n        if (!browserStorageConfig[key] || !(func = getBrowserStorage(key))) {\n            return;\n        }\n        const set = browserStorageEmptyItems[key];\n        let index;\n        if (set.size) {\n            set.delete(index = Array.from(set).shift());\n        } else {\n            index = getBrowserStorageItemsCount(func);\n            if (index >= browserStorageLimit || !setBrowserStorageItemsCount(func, index + 1)) {\n                return;\n            }\n        }\n        const item = {\n            cached: Math.floor(Date.now() / browserStorageHour),\n            provider: storage.provider,\n            data\n        };\n        return setStoredItem(func, browserCachePrefix + index.toString(), JSON.stringify(item));\n    }\n    if (data.lastModified && !updateLastModified(storage, data.lastModified)) {\n        return;\n    }\n    if (!Object.keys(data.icons).length) {\n        return;\n    }\n    if (data.not_found) {\n        data = Object.assign({}, data);\n        delete data.not_found;\n    }\n    if (!store(\"local\")) {\n        store(\"session\");\n    }\n}\nfunction emptyCallback() {}\nfunction loadedNewIcons(storage) {\n    if (!storage.iconsLoaderFlag) {\n        storage.iconsLoaderFlag = true;\n        setTimeout(()=>{\n            storage.iconsLoaderFlag = false;\n            updateCallbacks(storage);\n        });\n    }\n}\nfunction loadNewIcons(storage, icons) {\n    if (!storage.iconsToLoad) {\n        storage.iconsToLoad = icons;\n    } else {\n        storage.iconsToLoad = storage.iconsToLoad.concat(icons).sort();\n    }\n    if (!storage.iconsQueueFlag) {\n        storage.iconsQueueFlag = true;\n        setTimeout(()=>{\n            storage.iconsQueueFlag = false;\n            const { provider, prefix } = storage;\n            const icons2 = storage.iconsToLoad;\n            delete storage.iconsToLoad;\n            let api;\n            if (!icons2 || !(api = getAPIModule(provider))) {\n                return;\n            }\n            const params = api.prepare(provider, prefix, icons2);\n            params.forEach((item)=>{\n                sendAPIQuery(provider, item, (data)=>{\n                    if (typeof data !== \"object\") {\n                        item.icons.forEach((name)=>{\n                            storage.missing.add(name);\n                        });\n                    } else {\n                        try {\n                            const parsed = addIconSet(storage, data);\n                            if (!parsed.length) {\n                                return;\n                            }\n                            const pending = storage.pendingIcons;\n                            if (pending) {\n                                parsed.forEach((name)=>{\n                                    pending.delete(name);\n                                });\n                            }\n                            storeInBrowserStorage(storage, data);\n                        } catch (err) {\n                            console.error(err);\n                        }\n                    }\n                    loadedNewIcons(storage);\n                });\n            });\n        });\n    }\n}\nconst loadIcons = (icons, callback)=>{\n    const cleanedIcons = listToIcons(icons, true, allowSimpleNames());\n    const sortedIcons = sortIcons(cleanedIcons);\n    if (!sortedIcons.pending.length) {\n        let callCallback = true;\n        if (callback) {\n            setTimeout(()=>{\n                if (callCallback) {\n                    callback(sortedIcons.loaded, sortedIcons.missing, sortedIcons.pending, emptyCallback);\n                }\n            });\n        }\n        return ()=>{\n            callCallback = false;\n        };\n    }\n    const newIcons = /* @__PURE__ */ Object.create(null);\n    const sources = [];\n    let lastProvider, lastPrefix;\n    sortedIcons.pending.forEach((icon)=>{\n        const { provider, prefix } = icon;\n        if (prefix === lastPrefix && provider === lastProvider) {\n            return;\n        }\n        lastProvider = provider;\n        lastPrefix = prefix;\n        sources.push(getStorage(provider, prefix));\n        const providerNewIcons = newIcons[provider] || (newIcons[provider] = /* @__PURE__ */ Object.create(null));\n        if (!providerNewIcons[prefix]) {\n            providerNewIcons[prefix] = [];\n        }\n    });\n    sortedIcons.pending.forEach((icon)=>{\n        const { provider, prefix, name } = icon;\n        const storage = getStorage(provider, prefix);\n        const pendingQueue = storage.pendingIcons || (storage.pendingIcons = /* @__PURE__ */ new Set());\n        if (!pendingQueue.has(name)) {\n            pendingQueue.add(name);\n            newIcons[provider][prefix].push(name);\n        }\n    });\n    sources.forEach((storage)=>{\n        const { provider, prefix } = storage;\n        if (newIcons[provider][prefix].length) {\n            loadNewIcons(storage, newIcons[provider][prefix]);\n        }\n    });\n    return callback ? storeCallback(callback, sortedIcons, sources) : emptyCallback;\n};\nconst loadIcon = (icon)=>{\n    return new Promise((fulfill, reject)=>{\n        const iconObj = typeof icon === \"string\" ? stringToIcon(icon, true) : icon;\n        if (!iconObj) {\n            reject(icon);\n            return;\n        }\n        loadIcons([\n            iconObj || icon\n        ], (loaded)=>{\n            if (loaded.length && iconObj) {\n                const data = getIconData(iconObj);\n                if (data) {\n                    fulfill({\n                        ...defaultIconProps,\n                        ...data\n                    });\n                    return;\n                }\n            }\n            reject(icon);\n        });\n    });\n};\nfunction toggleBrowserCache(storage, value) {\n    switch(storage){\n        case \"local\":\n        case \"session\":\n            browserStorageConfig[storage] = value;\n            break;\n        case \"all\":\n            for(const key in browserStorageConfig){\n                browserStorageConfig[key] = value;\n            }\n            break;\n    }\n}\nfunction mergeCustomisations(defaults, item) {\n    const result = {\n        ...defaults\n    };\n    for(const key in item){\n        const value = item[key];\n        const valueType = typeof value;\n        if (key in defaultIconSizeCustomisations) {\n            if (value === null || value && (valueType === \"string\" || valueType === \"number\")) {\n                result[key] = value;\n            }\n        } else if (valueType === typeof result[key]) {\n            result[key] = key === \"rotate\" ? value % 4 : value;\n        }\n    }\n    return result;\n}\nconst separator = /[\\s,]+/;\nfunction flipFromString(custom, flip) {\n    flip.split(separator).forEach((str)=>{\n        const value = str.trim();\n        switch(value){\n            case \"horizontal\":\n                custom.hFlip = true;\n                break;\n            case \"vertical\":\n                custom.vFlip = true;\n                break;\n        }\n    });\n}\nfunction rotateFromString(value, defaultValue = 0) {\n    const units = value.replace(/^-?[0-9.]*/, \"\");\n    function cleanup(value2) {\n        while(value2 < 0){\n            value2 += 4;\n        }\n        return value2 % 4;\n    }\n    if (units === \"\") {\n        const num = parseInt(value);\n        return isNaN(num) ? 0 : cleanup(num);\n    } else if (units !== value) {\n        let split = 0;\n        switch(units){\n            case \"%\":\n                split = 25;\n                break;\n            case \"deg\":\n                split = 90;\n        }\n        if (split) {\n            let num = parseFloat(value.slice(0, value.length - units.length));\n            if (isNaN(num)) {\n                return 0;\n            }\n            num = num / split;\n            return num % 1 === 0 ? cleanup(num) : 0;\n        }\n    }\n    return defaultValue;\n}\nfunction iconToHTML(body, attributes) {\n    let renderAttribsHTML = body.indexOf(\"xlink:\") === -1 ? \"\" : ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"';\n    for(const attr in attributes){\n        renderAttribsHTML += \" \" + attr + '=\"' + attributes[attr] + '\"';\n    }\n    return '<svg xmlns=\"http://www.w3.org/2000/svg\"' + renderAttribsHTML + \">\" + body + \"</svg>\";\n}\nfunction encodeSVGforURL(svg) {\n    return svg.replace(/\"/g, \"'\").replace(/%/g, \"%25\").replace(/#/g, \"%23\").replace(/</g, \"%3C\").replace(/>/g, \"%3E\").replace(/\\s+/g, \" \");\n}\nfunction svgToData(svg) {\n    return \"data:image/svg+xml,\" + encodeSVGforURL(svg);\n}\nfunction svgToURL(svg) {\n    return 'url(\"' + svgToData(svg) + '\")';\n}\nlet policy;\nfunction createPolicy() {\n    try {\n        policy = window.trustedTypes.createPolicy(\"iconify\", {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n            createHTML: (s)=>s\n        });\n    } catch (err) {\n        policy = null;\n    }\n}\nfunction cleanUpInnerHTML(html) {\n    if (policy === void 0) {\n        createPolicy();\n    }\n    return policy ? policy.createHTML(html) : html;\n}\nconst defaultExtendedIconCustomisations = {\n    ...defaultIconCustomisations,\n    inline: false\n};\n/**\n * Default SVG attributes\n */ const svgDefaults = {\n    'xmlns': 'http://www.w3.org/2000/svg',\n    'xmlnsXlink': 'http://www.w3.org/1999/xlink',\n    'aria-hidden': true,\n    'role': 'img'\n};\n/**\n * Style modes\n */ const commonProps = {\n    display: 'inline-block'\n};\nconst monotoneProps = {\n    backgroundColor: 'currentColor'\n};\nconst coloredProps = {\n    backgroundColor: 'transparent'\n};\n// Dynamically add common props to variables above\nconst propsToAdd = {\n    Image: 'var(--svg)',\n    Repeat: 'no-repeat',\n    Size: '100% 100%'\n};\nconst propsToAddTo = {\n    WebkitMask: monotoneProps,\n    mask: monotoneProps,\n    background: coloredProps\n};\nfor(const prefix in propsToAddTo){\n    const list = propsToAddTo[prefix];\n    for(const prop in propsToAdd){\n        list[prefix + prop] = propsToAdd[prop];\n    }\n}\n/**\n * Default values for customisations for inline icon\n */ const inlineDefaults = {\n    ...defaultExtendedIconCustomisations,\n    inline: true\n};\n/**\n * Fix size: add 'px' to numbers\n */ function fixSize(value) {\n    return value + (value.match(/^[-0-9.]+$/) ? 'px' : '');\n}\n/**\n * Render icon\n */ const render = (// Icon must be validated before calling this function\nicon, // Partial properties\nprops, // Icon name\nname)=>{\n    // Get default properties\n    const defaultProps = props.inline ? inlineDefaults : defaultExtendedIconCustomisations;\n    // Get all customisations\n    const customisations = mergeCustomisations(defaultProps, props);\n    // Check mode\n    const mode = props.mode || 'svg';\n    // Create style\n    const style = {};\n    const customStyle = props.style || {};\n    // Create SVG component properties\n    const componentProps = {\n        ...mode === 'svg' ? svgDefaults : {}\n    };\n    if (name) {\n        const iconName = stringToIcon(name, false, true);\n        if (iconName) {\n            const classNames = [\n                'iconify'\n            ];\n            const props = [\n                'provider',\n                'prefix'\n            ];\n            for (const prop of props){\n                if (iconName[prop]) {\n                    classNames.push('iconify--' + iconName[prop]);\n                }\n            }\n            componentProps.className = classNames.join(' ');\n        }\n    }\n    // Get element properties\n    for(let key in props){\n        const value = props[key];\n        if (value === void 0) {\n            continue;\n        }\n        switch(key){\n            // Properties to ignore\n            case 'icon':\n            case 'style':\n            case 'children':\n            case 'onLoad':\n            case 'mode':\n            case 'ssr':\n                break;\n            // Forward ref\n            case '_ref':\n                componentProps.ref = value;\n                break;\n            // Merge class names\n            case 'className':\n                componentProps[key] = (componentProps[key] ? componentProps[key] + ' ' : '') + value;\n                break;\n            // Boolean attributes\n            case 'inline':\n            case 'hFlip':\n            case 'vFlip':\n                customisations[key] = value === true || value === 'true' || value === 1;\n                break;\n            // Flip as string: 'horizontal,vertical'\n            case 'flip':\n                if (typeof value === 'string') {\n                    flipFromString(customisations, value);\n                }\n                break;\n            // Color: copy to style\n            case 'color':\n                style.color = value;\n                break;\n            // Rotation as string\n            case 'rotate':\n                if (typeof value === 'string') {\n                    customisations[key] = rotateFromString(value);\n                } else if (typeof value === 'number') {\n                    customisations[key] = value;\n                }\n                break;\n            // Remove aria-hidden\n            case 'ariaHidden':\n            case 'aria-hidden':\n                if (value !== true && value !== 'true') {\n                    delete componentProps['aria-hidden'];\n                }\n                break;\n            // Copy missing property if it does not exist in customisations\n            default:\n                if (defaultProps[key] === void 0) {\n                    componentProps[key] = value;\n                }\n        }\n    }\n    // Generate icon\n    const item = iconToSVG(icon, customisations);\n    const renderAttribs = item.attributes;\n    // Inline display\n    if (customisations.inline) {\n        style.verticalAlign = '-0.125em';\n    }\n    if (mode === 'svg') {\n        // Add style\n        componentProps.style = {\n            ...style,\n            ...customStyle\n        };\n        // Add icon stuff\n        Object.assign(componentProps, renderAttribs);\n        // Counter for ids based on \"id\" property to render icons consistently on server and client\n        let localCounter = 0;\n        let id = props.id;\n        if (typeof id === 'string') {\n            // Convert '-' to '_' to avoid errors in animations\n            id = id.replace(/-/g, '_');\n        }\n        // Add icon stuff\n        componentProps.dangerouslySetInnerHTML = {\n            __html: cleanUpInnerHTML(replaceIDs(item.body, id ? ()=>id + 'ID' + localCounter++ : 'iconifyReact'))\n        };\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)('svg', componentProps);\n    }\n    // Render <span> with style\n    const { body, width, height } = icon;\n    const useMask = mode === 'mask' || (mode === 'bg' ? false : body.indexOf('currentColor') !== -1);\n    // Generate SVG\n    const html = iconToHTML(body, {\n        ...renderAttribs,\n        width: width + '',\n        height: height + ''\n    });\n    // Generate style\n    componentProps.style = {\n        ...style,\n        '--svg': svgToURL(html),\n        'width': fixSize(renderAttribs.width),\n        'height': fixSize(renderAttribs.height),\n        ...commonProps,\n        ...useMask ? monotoneProps : coloredProps,\n        ...customStyle\n    };\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)('span', componentProps);\n};\n/**\n * Enable cache\n */ function enableCache(storage) {\n    toggleBrowserCache(storage, true);\n}\n/**\n * Disable cache\n */ function disableCache(storage) {\n    toggleBrowserCache(storage, false);\n}\n/**\n * Initialise stuff\n */ // Enable short names\nallowSimpleNames(true);\n// Set API module\nsetAPIModule('', fetchAPIModule);\n/**\n * Browser stuff\n */ if (typeof document !== 'undefined' && \"undefined\" !== 'undefined') {}\nfunction IconComponent(props) {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!!props.ssr);\n    const [abort, setAbort] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    // Get initial state\n    function getInitialState(mounted) {\n        if (mounted) {\n            const name = props.icon;\n            if (typeof name === 'object') {\n                // Icon as object\n                return {\n                    name: '',\n                    data: name\n                };\n            }\n            const data = getIconData(name);\n            if (data) {\n                return {\n                    name,\n                    data\n                };\n            }\n        }\n        return {\n            name: ''\n        };\n    }\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getInitialState(!!props.ssr));\n    // Cancel loading\n    function cleanup() {\n        const callback = abort.callback;\n        if (callback) {\n            callback();\n            setAbort({});\n        }\n    }\n    // Change state if it is different\n    function changeState(newState) {\n        if (JSON.stringify(state) !== JSON.stringify(newState)) {\n            cleanup();\n            setState(newState);\n            return true;\n        }\n    }\n    // Update state\n    function updateState() {\n        var _a;\n        const name = props.icon;\n        if (typeof name === 'object') {\n            // Icon as object\n            changeState({\n                name: '',\n                data: name\n            });\n            return;\n        }\n        // New icon or got icon data\n        const data = getIconData(name);\n        if (changeState({\n            name,\n            data\n        })) {\n            if (data === undefined) {\n                // Load icon, update state when done\n                const callback = loadIcons([\n                    name\n                ], updateState);\n                setAbort({\n                    callback\n                });\n            } else if (data) {\n                // Icon data is available: trigger onLoad callback if present\n                (_a = props.onLoad) === null || _a === void 0 ? void 0 : _a.call(props, name);\n            }\n        }\n    }\n    // Mounted state, cleanup for loader\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"IconComponent.useEffect\": ()=>{\n            setMounted(true);\n            return cleanup;\n        }\n    }[\"IconComponent.useEffect\"], []);\n    // Icon changed or component mounted\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"IconComponent.useEffect\": ()=>{\n            if (mounted) {\n                updateState();\n            }\n        }\n    }[\"IconComponent.useEffect\"], [\n        props.icon,\n        mounted\n    ]);\n    // Render icon\n    const { name, data } = state;\n    if (!data) {\n        return props.children ? props.children : /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)('span', {});\n    }\n    return render({\n        ...defaultIconProps,\n        ...data\n    }, props, name);\n}\n/**\n * Block icon\n *\n * @param props - Component properties\n */ const Icon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref)=>IconComponent({\n        ...props,\n        _ref: ref\n    }));\n/**\n * Inline icon (has negative verticalAlign that makes it behave like icon font)\n *\n * @param props - Component properties\n */ const InlineIcon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref)=>IconComponent({\n        inline: true,\n        ...props,\n        _ref: ref\n    }));\n/**\n * Internal API\n */ const _api = {\n    getAPIConfig,\n    setAPIModule,\n    sendAPIQuery,\n    setFetch,\n    getFetch,\n    listAPIProviders\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/react/dist/iconify.js\n");

/***/ })

};
;