import { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { PartyType } from 'src/types/parties';

interface UsePartyAddressFormProps {
  fieldPrefix: string;
}

interface UsePartyAddressFormReturn {
  openPartyDialog: boolean;
  currentPartyType: PartyType | null;
  handleOpenPartyDialog: (partyType: PartyType) => void;
  handleClosePartyDialog: () => void;
  handleUpdateParty: () => void;
}

export const usePartyAddressForm = ({ fieldPrefix }: UsePartyAddressFormProps): UsePartyAddressFormReturn => {
  const { setValue, getValues } = useFormContext();

  // State for party address edit dialog
  const [openPartyDialog, setOpenPartyDialog] = useState(false);
  const [currentPartyType, setCurrentPartyType] = useState<PartyType | null>(null);

  // Handle opening the party edit dialog
  const handleOpenPartyDialog = (partyType: PartyType) => {
    setCurrentPartyType(partyType);
    setOpenPartyDialog(true);

    // Initialize form data if needed
    const currentParty = getValues(`${fieldPrefix}.${partyType}`);
    if (!currentParty || Object.keys(currentParty).length === 0) {
      setValue(`${fieldPrefix}.${partyType}`, {
        id: null,
        name: '',
        address: {
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          postCode: '',
          country: '',
          countryCode: ''
        }
      });
    } else {
      // Ensure all fields are defined (not null)
      const fieldsToCheck = [
        'name',
        'address.addressLine1',
        'address.addressLine2',
        'address.city',
        'address.state',
        'address.postCode',
        'address.country',
        'address.countryCode'
      ];

      fieldsToCheck.forEach(field => {
        // Handle nested fields with dot notation
        const parts = field.split('.');
        let currentValue;

        if (parts.length > 1 && currentParty.address) {
          currentValue = currentParty.address[parts[1]];
          if (currentValue === null || currentValue === undefined) {
            setValue(`${fieldPrefix}.${partyType}.address.${parts[1]}`, '');
          }
        } else {
          currentValue = currentParty[field];
          if (currentValue === null || currentValue === undefined) {
            setValue(`${fieldPrefix}.${partyType}.${field}`, '');
          }
        }
      });
    }
  };

  // Handle closing the party edit dialog
  const handleClosePartyDialog = () => {
    setOpenPartyDialog(false);
    setCurrentPartyType(null);
  };

  // Handle updating party data in the form
  const handleUpdateParty = () => {
    if (!currentPartyType) return;
    handleClosePartyDialog();
  };

  return {
    openPartyDialog,
    currentPartyType,
    handleOpenPartyDialog,
    handleClosePartyDialog,
    handleUpdateParty
  };
};
