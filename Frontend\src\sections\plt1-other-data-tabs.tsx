import { useState, SyntheticEvent } from 'react';

import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Card from '@mui/material/Card';
import Divider from '@mui/material/Divider';
import CardHeader from '@mui/material/CardHeader';

import { useTranslate } from 'src/locales';
import PLT1CustomsOfficeTab from './orders/plt1/tabs/plt1-customs-office-tab';
import PLT1VehicleRegistrationTab from './orders/plt1/tabs/plt1-vehicle-registration-tab';

// ----------------------------------------------------------------------

enum TabNames {
    CUSTOMS_OFFICE = 'customsOffice',
    VEHICLE_REGISTRATION = 'vehicleRegistration',
}

// ----------------------------------------------------------------------

export interface PLT1OtherDataTabsProps {
    readOnly?: boolean;
}

export default function PLT1OtherDataTabs({ readOnly = false }: PLT1OtherDataTabsProps) {
    const { t } = useTranslate();
    const [currentTab, setCurrentTab] = useState(TabNames.CUSTOMS_OFFICE);

    const handleChangeTab = (_event: SyntheticEvent, newValue: TabNames) => {
        setCurrentTab(newValue);
    };

    const TABS = [
        {
            value: TabNames.CUSTOMS_OFFICE,
            label: t('plt1.details.documents.tabs.customsOffice'),
            component: <PLT1CustomsOfficeTab readOnly={readOnly} />
        },
        {
            value: TabNames.VEHICLE_REGISTRATION,
            label: t('plt1.details.documents.tabs.vehicleRegistration'),
            component: <PLT1VehicleRegistrationTab readOnly={readOnly} />
        },
    ];

    return (
        <Card>
            <CardHeader
                title={t('plt1.details.otherData.heading')}
                sx={{ mb: 2 }}
            />
            <Divider sx={{ borderStyle: 'dashed' }} />
            <Tabs
                value={currentTab}
                onChange={handleChangeTab}
                sx={{
                    px: 2.5,
                    boxShadow: (theme) => `inset 0 -2px 0 0 ${theme.palette.divider}`,
                }}
            >
                {TABS.map((tab) => (
                    <Tab
                        key={tab.value}
                        value={tab.value}
                        label={tab.label}
                    />
                ))}
            </Tabs>

            {TABS.map(
                (tab) =>
                    tab.value === currentTab && (
                        <Box key={tab.value} sx={{ p: 3 }}>
                            {tab.component}
                        </Box>
                    )
            )}
        </Card>
    );
}