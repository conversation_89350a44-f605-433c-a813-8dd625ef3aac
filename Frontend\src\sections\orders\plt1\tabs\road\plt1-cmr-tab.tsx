import { useRef, useState, useEffect } from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';

import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { useTranslate } from 'src/locales';
import PLT1CMRForm, { PLT1CMRData } from '../../forms/road/plt1-cmr-form';
import PLT1DocumentTabBase from '../plt1-document-tab-base';

// ----------------------------------------------------------------------

interface PLT1CMRTabProps {
  t1OrderId: string;
  order?: any;
  readOnly?: boolean;
}

interface FormValues {
  cmrDocuments: PLT1CMRData[];
}

export default function PLT1CMRTab({
  t1OrderId,
  order,
  readOnly = false,
}: PLT1CMRTabProps) {
  const { t } = useTranslate();
  const { control } = useFormContext<FormValues>();

  // Try to restore the expanded state from sessionStorage
  const [expandedCMR, setExpandedCMR] = useState<number | null>(() => {
    const saved = sessionStorage.getItem(`plt1-expanded-cmr-${t1OrderId}`);
    return saved ? parseInt(saved, 10) : null;
  });

  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const deleteButtonRef = useRef<HTMLButtonElement>(null);

  const fieldArray = useFieldArray({
    control,
    name: 'cmrDocuments',
  });

  // Save expanded state to sessionStorage whenever it changes
  useEffect(() => {
    if (expandedCMR !== null) {
      sessionStorage.setItem(`plt1-expanded-cmr-${t1OrderId}`, expandedCMR.toString());
    } else {
      sessionStorage.removeItem(`plt1-expanded-cmr-${t1OrderId}`);
    }
  }, [expandedCMR, t1OrderId]);

  const handleAddCMR = (): void => {
    const newCMR: PLT1CMRData = {
      id: undefined,
      cmrNumber: '',
      vehicleNumber: '',
      grossWeight: 0,
      grossWeightUnit: 'kg',
      numberOfPieces: 0,
      volume: 0,
      shipper: {
        name: '',
        taxNumber: '',
        eoriNumber: '',
        address: {
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          postCode: '',
          country: '',
          countryCode: '',
        },
      },
      consignee: {
        name: '',
        taxNumber: '',
        eoriNumber: '',
        address: {
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          postCode: '',
          country: '',
          countryCode: '',
        },
      },
      t1OrderId,
    };

    fieldArray.append(newCMR);
    setExpandedCMR(fieldArray.fields.length);
  };

  const handleToggleExpand = (index: number): void => {
    setExpandedCMR(expandedCMR === index ? null : index);
  };

  const handleOpenConfirm = (index: number): void => {
    setDeleteIndex(index);
    setOpenConfirm(true);
  };

  const handleCloseConfirm = (): void => {
    setOpenConfirm(false);
    if (deleteIndex !== null) {
      deleteButtonRef.current?.focus();
    }
  };

  const handleDeleteCMR = (): void => {
    if (deleteIndex !== null) {
      fieldArray.remove(deleteIndex);
      if (expandedCMR === deleteIndex) {
        setExpandedCMR(null);
      }
    }
    handleCloseConfirm();
  };

  // Render preview of the CMR document when collapsed
  const renderPreview = (cmr: any, _index: number) => (
    <Stack
      direction="row"
      spacing={2}
      sx={{
        px: 3,
        pb: 2,
        display: 'flex',
        flexWrap: 'wrap',
        '& > *': { mr: 3, mb: 1 },
      }}
    >
      {cmr.vehicleNumber && (
        <Typography variant="body2" color="text.secondary">
          {t('plt1.details.documents.cmr.preview.vehicleNumber')}: {cmr.vehicleNumber}
        </Typography>
      )}

      {cmr.numberOfPieces !== undefined &&
        cmr.numberOfPieces !== null &&
        cmr.numberOfPieces > 0 && (
          <Typography variant="body2" color="text.secondary">
            {t('plt1.details.documents.cmr.preview.numberOfPieces')}: {cmr.numberOfPieces}
          </Typography>
        )}

      {cmr.grossWeight !== undefined && cmr.grossWeight !== null && cmr.grossWeight > 0 && (
        <Typography variant="body2" color="text.secondary">
          {t('plt1.details.documents.cmr.preview.grossWeight')}: {cmr.grossWeight}{' '}
          {cmr.grossWeightUnit || 'kg'}
        </Typography>
      )}

      {cmr.volume !== undefined && cmr.volume !== null && cmr.volume > 0 && (
        <Typography variant="body2" color="text.secondary">
          {t('plt1.details.documents.cmr.preview.volume')}: {cmr.volume}
        </Typography>
      )}
    </Stack>
  );

  // Render the form when expanded
  const renderForm = (index: number) => (
    <PLT1CMRForm formPath="cmrDocuments" index={index} readOnly={readOnly} />
  );

  // Render the title of each item
  const getItemTitle = (cmr: any) => (
    <Stack direction="row" alignItems="center" spacing={1}>
      <Typography variant="subtitle1">
        {cmr.cmrNumber || t('plt1.details.documents.cmr.preview.newCMR')}
      </Typography>
    </Stack>
  );

  return (
    <PLT1DocumentTabBase
      t1OrderId={t1OrderId}
      order={order}
      readOnly={readOnly}
      title={t('plt1.details.documents.cmr.heading')}
      emptyTitle={t('plt1.details.documents.cmr.noData')}
      emptyDescription={t('plt1.details.documents.cmr.addYourFirst')}
      expandedIndex={expandedCMR}
      deleteIndex={deleteIndex}
      openConfirm={openConfirm}
      fieldArray={fieldArray}
      fieldArrayName="cmrDocuments"
      onToggleExpand={handleToggleExpand}
      onOpenConfirm={handleOpenConfirm}
      onCloseConfirm={handleCloseConfirm}
      onDelete={handleDeleteCMR}
      onAdd={handleAddCMR}
      renderPreview={renderPreview}
      renderForm={renderForm}
      getItemTitle={getItemTitle}
    />
  );
}