import React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import { useTranslate } from 'src/locales';
import { PartyType } from 'src/types/parties';
import PartyAddressForm from './party-address-form';

interface PartyAddressDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: () => void;
  formPath: string;
  currentPartyType: PartyType | null;
  readOnly?: boolean;
  titlePrefix?: string;
}

export default function PartyAddressDialog({
  open,
  onClose,
  onSave,
  formPath,
  currentPartyType,
  readOnly = false,
  titlePrefix = 'partyAddress'
}: PartyAddressDialogProps) {
  const { t } = useTranslate();

  if (!currentPartyType) {
    return null;
  }

  const getTitle = () => {
    if (currentPartyType === PartyType.SHIPPER) {
      return t(`${titlePrefix}.editShipper`);
    }
    return t(`${titlePrefix}.editConsignee`);
  };

  const getDetailsTitle = () => {
    if (currentPartyType === PartyType.SHIPPER) {
      return t(`${titlePrefix}.shipperDetails`);
    }
    return t(`${titlePrefix}.consigneeDetails`);
  };

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
      <DialogTitle>
        {getTitle()}
      </DialogTitle>

      <DialogContent>
        <PartyAddressForm
          formPath={`${formPath}.${currentPartyType}`}
          title={getDetailsTitle()}
          icon={currentPartyType === PartyType.SHIPPER ? 'eva:person-outline' : 'eva:people-outline'}
          readOnly={readOnly}
        />
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="inherit">
          {t('common.cancel')}
        </Button>
        <Button
          onClick={onSave}
          variant="contained"
          disabled={readOnly}
        >
          {t('common.save')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
