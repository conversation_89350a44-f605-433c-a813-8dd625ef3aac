"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/details/plt1-order-details-base.tsx":
/*!**********************************************************************!*\
  !*** ./src/sections/orders/plt1/details/plt1-order-details-base.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLT1OrderDetailsBase: () => (/* binding */ PLT1OrderDetailsBase)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/Box */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/material/Card */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/material/Stack */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material_Button__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/material/Button */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/material/Divider */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _mui_material_Container__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/Container */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/material/Typography */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _mui_material_CircularProgress__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/material/CircularProgress */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _mui_material_Grid2__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/material/Grid2 */ \"(app-pages-browser)/./node_modules/@mui/material/Grid2/Grid2.js\");\n/* harmony import */ var _mui_lab_LoadingButton__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/lab/LoadingButton */ \"(app-pages-browser)/./node_modules/@mui/lab/LoadingButton/LoadingButton.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/material/IconButton */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/material/Tooltip */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var src_routes_paths__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/routes/paths */ \"(app-pages-browser)/./src/routes/paths.ts\");\n/* harmony import */ var src_routes_hooks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/routes/hooks */ \"(app-pages-browser)/./src/routes/hooks/index.ts\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_layouts_dashboard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/layouts/dashboard */ \"(app-pages-browser)/./src/layouts/dashboard/index.ts\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_layouts_components_app_data_grid_components_enum_tag_EnumTag__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/layouts/components/app-data-grid/components/enum-tag/EnumTag */ \"(app-pages-browser)/./src/layouts/components/app-data-grid/components/enum-tag/EnumTag.tsx\");\n/* harmony import */ var src_lib_formatters__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! src/lib/formatters */ \"(app-pages-browser)/./src/lib/formatters.ts\");\n/* harmony import */ var _components_plt1_file_management__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/plt1-file-management */ \"(app-pages-browser)/./src/sections/orders/plt1/components/plt1-file-management.tsx\");\n/* harmony import */ var _components_plt1_file_content_preview__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/plt1-file-content-preview */ \"(app-pages-browser)/./src/sections/orders/plt1/components/plt1-file-content-preview.tsx\");\n/* harmony import */ var _components_plt1_file_list_sidebar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/plt1-file-list-sidebar */ \"(app-pages-browser)/./src/sections/orders/plt1/components/plt1-file-list-sidebar.tsx\");\n/* harmony import */ var _components_plt1_file_cache__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../components/plt1-file-cache */ \"(app-pages-browser)/./src/sections/orders/plt1/components/plt1-file-cache.ts\");\n/* harmony import */ var _plt1_status__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../plt1-status */ \"(app-pages-browser)/./src/sections/orders/plt1/plt1-status.tsx\");\n/* harmony import */ var src_sections_plt1_other_data_tabs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! src/sections/plt1-other-data-tabs */ \"(app-pages-browser)/./src/sections/plt1-other-data-tabs.tsx\");\n/* harmony import */ var src_lib_axios__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! src/lib/axios */ \"(app-pages-browser)/./src/lib/axios.ts\");\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! swr */ \"(app-pages-browser)/./node_modules/swr/dist/index/index.mjs\");\n/* harmony import */ var _plt1_order_type__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../plt1-order-type */ \"(app-pages-browser)/./src/sections/orders/plt1/plt1-order-type.tsx\");\n// src/sections/plt1-order-details-base/PLT1OrderDetailsBase.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction PLT1OrderDetailsBase(param) {\n    let { orderId, readOnly: propReadOnly = false, isLoading, error, order, orderStatus, orderNumber, statusError, onSaveOrder, documentTabs, formChanged, isSaving, onCancel, isValid, downloadEndpoint, downloadFileName, showDownloadButton = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_4__.useTranslate)();\n    const router = (0,src_routes_hooks__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleBack = ()=>{\n        router.push(src_routes_paths__WEBPACK_IMPORTED_MODULE_2__.paths.dashboard.plt1.list);\n    };\n    // State management\n    const [splitViewActive, setSplitViewActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orderFiles, setOrderFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileListRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const methods = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useFormContext)();\n    // Determine if the form should be read-only\n    // Form is read-only if explicitly set to read-only or if the order status is \"Scanning\" or \"MerchandisePositionGeneration\"\n    const isScanning = orderStatus === _plt1_status__WEBPACK_IMPORTED_MODULE_13__.PLT1_ORDER_STATUS.Scanning;\n    const isMerchandisePositionGeneration = orderStatus === _plt1_status__WEBPACK_IMPORTED_MODULE_13__.PLT1_ORDER_STATUS.MerchandisePositionGeneration;\n    const readOnly = propReadOnly || isScanning || isMerchandisePositionGeneration;\n    // Handle save order\n    const handleSaveOrderSubmit = async (formData)=>{\n        await onSaveOrder(formData);\n    };\n    // Handle download XLSX\n    const handleDownloadXlsx = async ()=>{\n        if (!downloadEndpoint || !orderId || isDownloading) return;\n        setIsDownloading(true);\n        try {\n            const response = await src_lib_axios__WEBPACK_IMPORTED_MODULE_15__.axiosInstance.get(\"\".concat(downloadEndpoint, \"/\").concat(orderId, \"/download/xlsx\"), {\n                responseType: 'blob'\n            });\n            // Create blob URL and trigger download\n            const blob = new Blob([\n                response.data\n            ], {\n                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = downloadFileName || \"plt1-order-\".concat(orderId, \".xlsx\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n        } catch (error) {\n            console.error('Error downloading XLSX file:', error);\n        // You might want to show a toast error here\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    // State to track if files are being prefetched\n    const [isPrefetching, setIsPrefetching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for download functionality\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // SWR fetcher function\n    const filesFetcher = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PLT1OrderDetailsBase.useCallback[filesFetcher]\": async (url)=>{\n            const response = await src_lib_axios__WEBPACK_IMPORTED_MODULE_15__.axiosInstance.get(url);\n            return response.data.data || [];\n        }\n    }[\"PLT1OrderDetailsBase.useCallback[filesFetcher]\"], []);\n    // Use SWR to fetch and refresh order files every 10 seconds\n    const { data: files, mutate: refreshFiles, isValidating } = (0,swr__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(\"\".concat(src_lib_axios__WEBPACK_IMPORTED_MODULE_15__.endpoints.plt1.filePreviews, \"/\").concat(orderId), filesFetcher, {\n        refreshInterval: 10000,\n        revalidateOnFocus: true,\n        dedupingInterval: 5000,\n        onSuccess: {\n            \"PLT1OrderDetailsBase.useSWR\": (data)=>{\n                setOrderFiles(data);\n                // Only prefetch if there are files\n                if (data && data.length > 0) {\n                    prefetchFiles(data);\n                } else {\n                    // Reset prefetching state if there are no files\n                    setIsPrefetching(false);\n                }\n            }\n        }[\"PLT1OrderDetailsBase.useSWR\"]\n    });\n    // Update isPrefetching state based on SWR validation state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            if (isValidating && !isPrefetching) {\n                setIsPrefetching(true);\n            } else if (!isValidating && isPrefetching && (!files || files.length === 0)) {\n                // Reset prefetching state when validation completes and there are no files\n                setIsPrefetching(false);\n            }\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], [\n        isValidating,\n        isPrefetching,\n        files\n    ]);\n    // Prefetch files with different priorities\n    const prefetchFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PLT1OrderDetailsBase.useCallback[prefetchFiles]\": async (files)=>{\n            // Early return with state reset if no files\n            if (!files || files.length === 0) {\n                setIsPrefetching(false);\n                return;\n            }\n            setIsPrefetching(true);\n            try {\n                // Try to get the last viewed file ID from localStorage\n                const lastViewedFileId = localStorage.getItem(\"plt1-last-viewed-file-\".concat(orderId));\n                if (lastViewedFileId) {\n                    // Find the last viewed file and prefetch it with highest priority\n                    const lastViewedFile = files.find({\n                        \"PLT1OrderDetailsBase.useCallback[prefetchFiles].lastViewedFile\": (file)=>file.id === lastViewedFileId\n                    }[\"PLT1OrderDetailsBase.useCallback[prefetchFiles].lastViewedFile\"]);\n                    if (lastViewedFile) {\n                        // Prefetch the last viewed file with highest priority (10)\n                        await _components_plt1_file_cache__WEBPACK_IMPORTED_MODULE_12__.fileCache.prefetchFile(lastViewedFile, 10);\n                    }\n                }\n                // Prefetch the first few files with high priority (5)\n                const highPriorityFiles = files.slice(0, Math.min(5, files.length));\n                if (highPriorityFiles.length > 0) {\n                    await _components_plt1_file_cache__WEBPACK_IMPORTED_MODULE_12__.fileCache.prefetchFiles(highPriorityFiles, 5);\n                }\n                // Prefetch all remaining files with normal priority (1)\n                const remainingFiles = files.slice(Math.min(5, files.length));\n                if (remainingFiles.length > 0) {\n                    await _components_plt1_file_cache__WEBPACK_IMPORTED_MODULE_12__.fileCache.prefetchFiles(remainingFiles, 1);\n                }\n            } catch (error) {\n                console.error('Error prefetching files:', error);\n            } finally{\n                setIsPrefetching(false);\n            }\n        }\n    }[\"PLT1OrderDetailsBase.useCallback[prefetchFiles]\"], [\n        orderId\n    ]);\n    // Set initial files when SWR loads them\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            if (files) {\n                setOrderFiles(files);\n            }\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], [\n        files\n    ]);\n    // Function to manually refresh files\n    const handleRefreshFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PLT1OrderDetailsBase.useCallback[handleRefreshFiles]\": ()=>{\n            refreshFiles();\n        }\n    }[\"PLT1OrderDetailsBase.useCallback[handleRefreshFiles]\"], [\n        refreshFiles\n    ]);\n    // Clean up cache when component unmounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            return ({\n                \"PLT1OrderDetailsBase.useEffect\": ()=>{\n                    _components_plt1_file_cache__WEBPACK_IMPORTED_MODULE_12__.fileCache.clearCache();\n                }\n            })[\"PLT1OrderDetailsBase.useEffect\"];\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], []);\n    // Select the first file or the last viewed file\n    const selectFirstOrLastViewedFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PLT1OrderDetailsBase.useCallback[selectFirstOrLastViewedFile]\": ()=>{\n            if (orderFiles.length === 0) return;\n            // Try to get the last viewed file ID from localStorage\n            const lastViewedFileId = localStorage.getItem(\"plt1-last-viewed-file-\".concat(orderId));\n            if (lastViewedFileId) {\n                // Find the file with the saved ID\n                const lastFile = orderFiles.find({\n                    \"PLT1OrderDetailsBase.useCallback[selectFirstOrLastViewedFile].lastFile\": (file)=>file.id === lastViewedFileId\n                }[\"PLT1OrderDetailsBase.useCallback[selectFirstOrLastViewedFile].lastFile\"]);\n                if (lastFile) {\n                    setSelectedFile(lastFile);\n                    return;\n                }\n            }\n            // If no last viewed file or it's not found, select the first file\n            setSelectedFile(orderFiles[0]);\n        }\n    }[\"PLT1OrderDetailsBase.useCallback[selectFirstOrLastViewedFile]\"], [\n        orderId,\n        orderFiles\n    ]);\n    // Select first file or last viewed file when files are loaded and no file is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            if (orderFiles.length > 0 && !selectedFile && splitViewActive) {\n                selectFirstOrLastViewedFile();\n            }\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], [\n        orderFiles,\n        selectedFile,\n        splitViewActive,\n        selectFirstOrLastViewedFile\n    ]);\n    // Focus the file list and prefetch files when the split view is activated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            if (splitViewActive) {\n                // If there are no files, close the split view\n                if (orderFiles.length === 0) {\n                    setSplitViewActive(false);\n                    setIsPrefetching(false);\n                    return;\n                }\n                // Focus the file list\n                if (fileListRef.current) {\n                    // Use a small timeout to ensure the DOM is fully rendered\n                    const timeoutId = setTimeout({\n                        \"PLT1OrderDetailsBase.useEffect.timeoutId\": ()=>{\n                            var _fileListRef_current;\n                            (_fileListRef_current = fileListRef.current) === null || _fileListRef_current === void 0 ? void 0 : _fileListRef_current.focus();\n                        }\n                    }[\"PLT1OrderDetailsBase.useEffect.timeoutId\"], 100);\n                    return ({\n                        \"PLT1OrderDetailsBase.useEffect\": ()=>clearTimeout(timeoutId)\n                    })[\"PLT1OrderDetailsBase.useEffect\"];\n                }\n                // Prefetch all files in the background\n                prefetchFiles(orderFiles);\n            }\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], [\n        splitViewActive,\n        orderFiles,\n        prefetchFiles\n    ]);\n    // Handle closing file preview split view\n    const handleCloseFilePreview = ()=>{\n        setSplitViewActive(false);\n        setSelectedFile(null);\n    };\n    // Handle keyboard shortcut (Ctrl+Q) to toggle file preview split view\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1OrderDetailsBase.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"PLT1OrderDetailsBase.useEffect.handleKeyDown\": (event)=>{\n                    // Check if Ctrl+Q is pressed\n                    if ((event.ctrlKey || event.metaKey) && event.key === 'q') {\n                        event.preventDefault(); // Prevent any default browser action\n                        // If split view is active, always allow closing it\n                        if (splitViewActive) {\n                            setSplitViewActive(false);\n                            setSelectedFile(null);\n                        } else if (orderFiles.length > 0) {\n                            setSplitViewActive(true);\n                            // If no file is selected, select the first file or last viewed file\n                            if (!selectedFile) {\n                                selectFirstOrLastViewedFile();\n                            }\n                        }\n                    }\n                }\n            }[\"PLT1OrderDetailsBase.useEffect.handleKeyDown\"];\n            // Add event listener\n            window.addEventListener('keydown', handleKeyDown);\n            // Remove event listener on cleanup\n            return ({\n                \"PLT1OrderDetailsBase.useEffect\": ()=>{\n                    window.removeEventListener('keydown', handleKeyDown);\n                }\n            })[\"PLT1OrderDetailsBase.useEffect\"];\n        }\n    }[\"PLT1OrderDetailsBase.useEffect\"], [\n        splitViewActive,\n        orderFiles,\n        orderId,\n        selectedFile,\n        selectFirstOrLastViewedFile\n    ]);\n    // Handle selecting a file to preview\n    const handleSelectFile = (file)=>{\n        setSelectedFile(file);\n        setSplitViewActive(true);\n        // Save the selected file ID to localStorage for future reference\n        localStorage.setItem(\"plt1-last-viewed-file-\".concat(orderId), file.id);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_layouts_dashboard__WEBPACK_IMPORTED_MODULE_5__.DashboardContent, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Container__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    sx: {\n                        display: 'flex',\n                        justifyContent: 'center',\n                        alignItems: 'center',\n                        height: '60vh'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CircularProgress__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                lineNumber: 344,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n            lineNumber: 343,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !order) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_layouts_dashboard__WEBPACK_IMPORTED_MODULE_5__.DashboardContent, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Container__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    sx: {\n                        textAlign: 'center',\n                        py: 5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            variant: \"h6\",\n                            color: \"error\",\n                            sx: {\n                                mb: 2\n                            },\n                            children: t('plt1.details.notFound')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            variant: \"contained\",\n                            onClick: handleBack,\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                icon: \"eva:arrow-back-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 26\n                            }, void 0),\n                            children: t('common.back')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                    lineNumber: 359,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                lineNumber: 358,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n            lineNumber: 357,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_layouts_dashboard__WEBPACK_IMPORTED_MODULE_5__.DashboardContent, {\n        fullWidth: splitViewActive,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Container__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            maxWidth: splitViewActive ? false : 'lg',\n            disableGutters: splitViewActive,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    sx: {\n                        mb: 3,\n                        mt: 2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            direction: \"row\",\n                            alignItems: \"center\",\n                            spacing: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                    onClick: handleBack,\n                                    sx: {\n                                        border: '1px solid',\n                                        borderColor: 'divider',\n                                        borderRadius: 1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                        icon: \"eva:arrow-back-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_layouts_components_app_data_grid_components_enum_tag_EnumTag__WEBPACK_IMPORTED_MODULE_7__.EnumTag, {\n                                    value: order === null || order === void 0 ? void 0 : order.orderType,\n                                    config: _plt1_order_type__WEBPACK_IMPORTED_MODULE_16__.PLT1_ORDER_TYPE_CONFIG,\n                                    translationPrefix: \"plt1.type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    variant: \"h4\",\n                                    children: orderNumber || (order === null || order === void 0 ? void 0 : order.number) || t('plt1.details.orderDetails')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            direction: \"row\",\n                            spacing: 2,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                sx: {\n                                    display: {\n                                        xs: 'none',\n                                        sm: 'flex'\n                                    },\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tooltip__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    title: orderFiles.length > 0 ? isPrefetching ? t('plt1.details.filePreview.prefetchingFiles', {\n                                        defaultValue: 'Prefetching files...'\n                                    }) : t('plt1.details.filePreview.shortcutHint', {\n                                        defaultValue: 'Press Ctrl+Q to toggle preview'\n                                    }) : t('plt1.details.filePreview.noFiles', {\n                                        defaultValue: 'No files available to preview'\n                                    }),\n                                    arrow: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: isPrefetching ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            variant: \"outlined\",\n                                            color: \"primary\",\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CircularProgress__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 34\n                                            }, void 0),\n                                            disabled: true,\n                                            children: t('plt1.details.filePreview.prefetchingFiles', {\n                                                defaultValue: 'Prefetching files...'\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            variant: \"outlined\",\n                                            color: \"primary\",\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                                icon: \"eva:eye-fill\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 34\n                                            }, void 0),\n                                            onClick: ()=>{\n                                                if (splitViewActive) {\n                                                    setSplitViewActive(false);\n                                                    setSelectedFile(null);\n                                                } else if (orderFiles.length > 0) {\n                                                    setSplitViewActive(true);\n                                                    if (!selectedFile) {\n                                                        selectFirstOrLastViewedFile();\n                                                    }\n                                                }\n                                            },\n                                            disabled: !splitViewActive && orderFiles.length === 0,\n                                            children: splitViewActive ? t('plt1.details.filePreview.closePreview') : t('plt1.details.filePreview.openPreview')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, this),\n                (isScanning || isMerchandisePositionGeneration) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    sx: {\n                        mb: 3,\n                        p: 2,\n                        bgcolor: 'warning.lighter',\n                        borderRadius: 1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        variant: \"subtitle1\",\n                        color: \"warning.darker\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                icon: \"eva:alert-triangle-fill\",\n                                sx: {\n                                    mr: 1,\n                                    verticalAlign: 'middle'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 15\n                            }, this),\n                            isScanning ? t('plt1.details.scanningInProgress') : t('plt1.details.merchandisePositionGenerationInProgress')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                    lineNumber: 466,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: methods.handleSubmit(handleSaveOrderSubmit),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            children: [\n                                !splitViewActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            size: {\n                                                xs: 12,\n                                                md: 5\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                sx: {\n                                                    mb: 3\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    sx: {\n                                                        p: 3,\n                                                        pb: 2\n                                                    },\n                                                    spacing: 2,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            direction: \"row\",\n                                                            justifyContent: \"space-between\",\n                                                            alignItems: \"center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    variant: \"h6\",\n                                                                    children: t('plt1.details.orderInfo.heading')\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_layouts_components_app_data_grid_components_enum_tag_EnumTag__WEBPACK_IMPORTED_MODULE_7__.EnumTag, {\n                                                                    value: orderStatus || (order === null || order === void 0 ? void 0 : order.status),\n                                                                    config: _plt1_status__WEBPACK_IMPORTED_MODULE_13__.PLT1_ORDER_STATUS_CONFIG,\n                                                                    translationPrefix: \"plt1.status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                            sx: {\n                                                                borderStyle: 'dashed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            spacing: 1.5,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    direction: \"row\",\n                                                                    justifyContent: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                color: 'text.secondary'\n                                                                            },\n                                                                            children: t('plt1.details.orderInfo.orderingPartyEmail')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 498,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            children: order.orderingPartyEmail\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 501,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    direction: \"row\",\n                                                                    justifyContent: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                color: 'text.secondary'\n                                                                            },\n                                                                            children: t('plt1.details.orderInfo.orderDate')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 505,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            children: order.orderDate ? src_lib_formatters__WEBPACK_IMPORTED_MODULE_8__.formatters.dateTimeOffset(order.orderDate) : '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    direction: \"row\",\n                                                                    justifyContent: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                color: 'text.secondary'\n                                                                            },\n                                                                            children: t('plt1.details.orderInfo.confirmationDate')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 514,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            children: order.confirmationDate ? src_lib_formatters__WEBPACK_IMPORTED_MODULE_8__.formatters.dateTimeOffset(order.confirmationDate) : '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 517,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    direction: \"row\",\n                                                                    justifyContent: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                color: 'text.secondary'\n                                                                            },\n                                                                            children: t('plt1.details.orderInfo.forwardedDate')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 525,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            children: order.forwardedToTheCustomsOfficeDate ? src_lib_formatters__WEBPACK_IMPORTED_MODULE_8__.formatters.dateTimeOffset(order.forwardedToTheCustomsOfficeDate) : '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 528,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    direction: \"row\",\n                                                                    justifyContent: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                color: 'text.secondary'\n                                                                            },\n                                                                            children: t('plt1.details.orderInfo.completionDate')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 536,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            children: order.completionDate ? src_lib_formatters__WEBPACK_IMPORTED_MODULE_8__.formatters.dateTimeOffset(order.completionDate) : '-'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                            lineNumber: 539,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            size: {\n                                                xs: 12,\n                                                md: 7\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                spacing: 2,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_plt1_file_management__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    orderId: orderId,\n                                                    orderType: order === null || order === void 0 ? void 0 : order.orderType,\n                                                    hasPendingChanges: formChanged,\n                                                    isScanning: isScanning || isMerchandisePositionGeneration\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Grid2__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                    size: {\n                                        xs: 12,\n                                        md: 12\n                                    },\n                                    children: splitViewActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        sx: {\n                                            display: 'flex',\n                                            flexDirection: 'row',\n                                            height: 'calc(100vh - 150px)',\n                                            mb: 3,\n                                            mt: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                sx: {\n                                                    width: '40%',\n                                                    pr: 2,\n                                                    overflow: 'auto'\n                                                },\n                                                children: documentTabs\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                sx: {\n                                                    width: '60%',\n                                                    display: 'flex',\n                                                    flexDirection: 'column',\n                                                    border: '1px solid',\n                                                    borderColor: 'divider',\n                                                    borderRadius: 1,\n                                                    overflow: 'hidden'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        sx: {\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'space-between',\n                                                            p: 2,\n                                                            borderBottom: '1px solid',\n                                                            borderColor: 'divider'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        variant: \"h6\",\n                                                                        component: \"div\",\n                                                                        children: t('plt1.details.filePreview.title')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        variant: \"caption\",\n                                                                        color: \"text.secondary\",\n                                                                        children: t('plt1.details.filePreview.shortcutHint', {\n                                                                            defaultValue: 'Press Ctrl+Q to toggle preview'\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                lineNumber: 602,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                direction: \"row\",\n                                                                spacing: 1,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    edge: \"end\",\n                                                                    color: \"inherit\",\n                                                                    onClick: handleCloseFilePreview,\n                                                                    \"aria-label\": \"close\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                                                        icon: \"eva:close-fill\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                        lineNumber: 619,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                lineNumber: 612,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        sx: {\n                                                            display: 'flex',\n                                                            flexDirection: {\n                                                                xs: 'column',\n                                                                md: 'row'\n                                                            },\n                                                            flexGrow: 1,\n                                                            overflow: 'hidden'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                sx: {\n                                                                    flexGrow: 1,\n                                                                    display: 'flex',\n                                                                    alignItems: 'stretch',\n                                                                    justifyContent: 'stretch',\n                                                                    p: 0,\n                                                                    overflow: 'hidden',\n                                                                    width: '100%'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_plt1_file_content_preview__WEBPACK_IMPORTED_MODULE_10__.PLT1FileContentPreview, {\n                                                                    file: selectedFile\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                    lineNumber: 645,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_plt1_file_list_sidebar__WEBPACK_IMPORTED_MODULE_11__.PLT1FileListSidebar, {\n                                                                ref: fileListRef,\n                                                                files: orderFiles,\n                                                                selectedFile: selectedFile,\n                                                                onSelectFile: handleSelectFile,\n                                                                autoFocus: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                                lineNumber: 648,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                        lineNumber: 567,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            documentTabs,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                sx: {\n                                                    mt: 3\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_sections_plt1_other_data_tabs__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    readOnly: readOnly\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 11\n                        }, this),\n                        !readOnly && formChanged && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            sx: {\n                                position: 'fixed',\n                                bottom: 0,\n                                left: 0,\n                                right: 0,\n                                py: 2,\n                                px: 3,\n                                bgcolor: 'background.paper',\n                                borderTop: (theme)=>\"1px solid \".concat(theme.palette.divider),\n                                zIndex: (theme)=>theme.zIndex.appBar - 1,\n                                boxShadow: (theme)=>theme.shadows[3],\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'flex-end'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    sx: {\n                                        mr: 2\n                                    },\n                                    children: t('plt1.details.unsavedChanges')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    direction: \"row\",\n                                    spacing: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            color: \"inherit\",\n                                            variant: \"outlined\",\n                                            onClick: onCancel,\n                                            disabled: isSaving,\n                                            children: t('common.cancel')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_lab_LoadingButton__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                            variant: \"contained\",\n                                            color: \"primary\",\n                                            loading: isSaving,\n                                            type: \"submit\",\n                                            disabled: !isValid,\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_6__.Iconify, {\n                                                icon: \"eva:save-fill\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            children: t('common.save')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                            lineNumber: 699,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                            lineNumber: 670,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n            lineNumber: 378,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1-order-details-base.tsx\",\n        lineNumber: 377,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1OrderDetailsBase, \"mhc9dLChJASftdQEC0EDcUDgqMc=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_4__.useTranslate,\n        src_routes_hooks__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useFormContext,\n        swr__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n    ];\n});\n_c = PLT1OrderDetailsBase;\nvar _c;\n$RefreshReg$(_c, \"PLT1OrderDetailsBase\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/details/plt1-order-details-base.tsx\n"));

/***/ })

});