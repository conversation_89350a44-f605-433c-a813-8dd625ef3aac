// tabs/sea/plt1sea-house-bill-of-lading-tab.tsx
'use client';

import { useFormContext, useFieldArray } from 'react-hook-form';
import { useState, useRef, useEffect } from 'react';

import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { useTranslate } from 'src/locales';
import PLT1DocumentTabBase from '../plt1-document-tab-base';
import PLT1SeaBillOfLadingForm, {
  PLT1SeaBillOfLadingData,
} from '../../forms/sea/plt1sea-bill-of-lading-form';
import { PLT1Order } from '../../types/plt1-details.types';

// ----------------------------------------------------------------------

interface PLT1SeaHouseBillOfLadingTabProps {
  t1OrderId: string;
  order?: PLT1Order;
  readOnly?: boolean;
}

interface FormValues {
  billsOfLading: PLT1SeaBillOfLadingData[];
}

export default function PLT1SeaHouseBillOfLadingTab({
  t1OrderId,
  order,
  readOnly = false,
}: PLT1SeaHouseBillOfLadingTabProps) {
  const { t } = useTranslate();
  const { control } = useFormContext<FormValues>();

  // Try to restore the expanded state from sessionStorage
  const [expandedHBL, setExpandedHBL] = useState<number | null>(() => {
    const saved = sessionStorage.getItem(`plt1-expanded-hbl-${t1OrderId}`);
    return saved ? parseInt(saved, 10) : null;
  });

  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const deleteButtonRef = useRef<HTMLButtonElement>(null);

  const fieldArray = useFieldArray({
    control,
    name: 'billsOfLading',
  });

  // Save expanded state to sessionStorage whenever it changes
  useEffect(() => {
    if (expandedHBL !== null) {
      sessionStorage.setItem(`plt1-expanded-hbl-${t1OrderId}`, expandedHBL.toString());
    } else {
      sessionStorage.removeItem(`plt1-expanded-hbl-${t1OrderId}`);
    }
  }, [expandedHBL, t1OrderId]);

  const handleAddHBL = (): void => {
    const newBOL: PLT1SeaBillOfLadingData = {
      id: undefined,
      billOfLadingNumber: '',
      containerNumber: '',
      containerType: '',
      grossWeight: 0,
      grossWeightUnit: 'kg',
      numberOfPieces: 0,
      volumeMeasurement: 0,
      shipper: {
        name: '',
        taxNumber: '',
        eoriNumber: '',
        address: {
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          postCode: '',
          country: '',
          countryCode: '',
        },
      },
      consignee: {
        name: '',
        taxNumber: '',
        eoriNumber: '',
        address: {
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          postCode: '',
          country: '',
          countryCode: '',
        },
      },
      plT1SeaOrderId: t1OrderId,
    };

    fieldArray.append(newBOL);
    setExpandedHBL(fieldArray.fields.length);
  };

  const handleToggleExpand = (index: number): void => {
    setExpandedHBL(expandedHBL === index ? null : index);
  };

  const handleOpenConfirm = (index: number): void => {
    setDeleteIndex(index);
    setOpenConfirm(true);
  };

  const handleCloseConfirm = (): void => {
    setOpenConfirm(false);
    if (deleteIndex !== null) {
      deleteButtonRef.current?.focus();
    }
  };

  const handleDeleteHBL = (): void => {
    if (deleteIndex !== null) {
      fieldArray.remove(deleteIndex);
      if (expandedHBL === deleteIndex) {
        setExpandedHBL(null);
      }
    }
    handleCloseConfirm();
  };

  // Render preview of the HBL item when collapsed
  const renderPreview = (hbl: any, _index: number) => (
    <Stack
      direction="row"
      spacing={2}
      sx={{
        px: 3,
        pb: 2,
        display: 'flex',
        flexWrap: 'wrap',
        '& > *': { mr: 3, mb: 1 },
      }}
    >
      {hbl.containerNumber && (
        <Typography variant="body2" color="text.secondary">
          {t('plt1.details.documents.billOfLading.preview.containerNumber')}: {hbl.containerNumber}
        </Typography>
      )}

      {hbl.numberOfPieces !== undefined &&
        hbl.numberOfPieces !== null &&
        hbl.numberOfPieces > 0 && (
          <Typography variant="body2" color="text.secondary">
            {t('plt1.details.documents.billOfLading.preview.numberOfPieces')}:{' '}
            {hbl.numberOfPieces}
          </Typography>
        )}

      {hbl.grossWeight !== undefined && hbl.grossWeight !== null && hbl.grossWeight > 0 && (
        <Typography variant="body2" color="text.secondary">
          {t('plt1.details.documents.billOfLading.preview.grossWeight')}: {hbl.grossWeight}{' '}
          {hbl.grossWeightUnit || 'kg'}
        </Typography>
      )}
    </Stack>
  );

  // Render the form when expanded
  const renderForm = (index: number) => (
    <PLT1SeaBillOfLadingForm formPath="billsOfLading" index={index} readOnly={readOnly} />
  );

  // Render the title of each item
  const getItemTitle = (hbl: any) => (
    <Stack direction="row" alignItems="center" spacing={1}>
      <Typography variant="subtitle1">
        {hbl.billOfLadingNumber || t('plt1.details.documents.billOfLading.preview.newBOL')}
      </Typography>

      {hbl.containerNumber && (
        <Typography variant="body2" color="text.secondary">
          ({t('plt1.details.documents.billOfLading.preview.container')}: {hbl.containerNumber})
        </Typography>
      )}
    </Stack>
  );

  return (
    <PLT1DocumentTabBase
      readOnly={readOnly}
      title={t('plt1.details.documents.billOfLading.heading')}
      emptyTitle={t('plt1.details.documents.billOfLading.noData')}
      emptyDescription={t('plt1.details.documents.billOfLading.addYourFirst')}
      expandedIndex={expandedHBL}
      deleteIndex={deleteIndex}
      openConfirm={openConfirm}
      fieldArray={fieldArray}
      onToggleExpand={handleToggleExpand}
      onOpenConfirm={handleOpenConfirm}
      onCloseConfirm={handleCloseConfirm}
      onDelete={handleDeleteHBL}
      onAdd={handleAddHBL}
      renderPreview={renderPreview}
      renderForm={renderForm}
      getItemTitle={getItemTitle}
      t1OrderId={t1OrderId}
      order={order}
      fieldArrayName="billsOfLading"
    />
  );
}