'use client'

import Stack from '@mui/material/Stack';
import Card from '@mui/material/Card';;
import { SeoIllustration } from 'src/assets/illustrations';
import { useTranslate } from 'src/locales';
import { Welcome } from 'src/components/welcome/welcome';
import { SimpleLayout } from 'src/layouts/simple';
import { SignUpButton } from 'src/layouts/components/sign-up-button';
import { useSearchParams } from 'src/routes/hooks';
import { endSession, INVITATION_TOKEN_STORAGE_KEY } from 'src/auth/context/jwt';
import { useEffect } from 'react';
import { useAuthContext } from 'src/auth/hooks/use-auth-context';


export default function InvitationView() {
  const { t } = useTranslate();
  const { loading } = useAuthContext();

  const searchParams = useSearchParams();

  useEffect(() => {
    endSession();
    if (loading == false) {
      const invitationToken = searchParams.get('invitationToken') ?? "";
      sessionStorage.setItem(INVITATION_TOKEN_STORAGE_KEY, invitationToken);
    }
  }, [loading, searchParams]);

  return (
    <SimpleLayout
      slotProps={{
        content: { compact: true },
      }}
    >
      <Stack spacing={{ xs: 3, md: 5 }} sx={{ mx: 'auto', mb: 20 }}>
        <Card sx={{ m: 1 }}>
          <Welcome
            title={t('invitation.welcomeTitle')}
            description={t('invitation.welcomeDescription')}
            img={<SeoIllustration hideBackground />}
            action={<SignUpButton forceReauthentication={true} />}
          />
        </Card>
      </Stack>
    </SimpleLayout>
  );
}