import { useState, useRef } from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  Divider,
  Typography,
  Button,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import { useTranslate } from 'src/locales';
import { Field } from 'src/components/hook-form';
import { Iconify } from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';
import PartyFormSection from '../../../../components/party-address/party-form-section';
import { PartyType } from 'src/types/parties';
import { usePartyAddressForm } from 'src/components/party-address/hooks/usePartyAddressForm';
import { PartyAddressData } from 'src/components/party-address/party-address-form';
import PartyAddressDialog from 'src/components/party-address/party-address-dialog';

// ----------------------------------------------------------------------

export interface PLT1TransitItemData {
  id?: string | null;
  itemNumber: number;
  packagingType: string;
  grossWeight: number;
  netWeight: number;
  description: string;
  hsCode: string;
  t1TransitDocumentId?: string;
}

export interface PLT1TransitDocumentData {
  id?: string | null;
  mrnNumber: string;
  totalWeight: number;
  weightUnit: string;
  totalPackages: number;

  validityEndDate?: string | null;
  lrnNumber?: string;
  previousDocument?: string;
  storageLocationCode?: string;
  transportDocument?: string;
  attachedDocument?: string;
  dispatchCountry?: string;
  destinationCustomsOffice?: string;
  transportMeansAtExit?: string;

  shipper: PartyAddressData;
  consignee: PartyAddressData;

  items: PLT1TransitItemData[];
  t1OrderId: string;
}

// Default values to initialize form fields
const DEFAULT_ITEM = {
  id: null,
  itemNumber: 1,
  packagingType: '',
  grossWeight: 0,
  netWeight: 0,
  description: '',
  hsCode: '',
  t1TransitDocumentId: null,
};

// ----------------------------------------------------------------------

interface PLT1TransitFormProps {
  formPath: string;
  index?: number;
  readOnly?: boolean;
}

export default function PLT1TransitForm({ formPath, index, readOnly = false }: PLT1TransitFormProps) {
  const { t } = useTranslate();
  const { control, getValues, setValue, watch } = useFormContext();

  const fieldPrefix = index !== undefined ? `${formPath}[${index}]` : formPath;
  const itemsFieldName = `${fieldPrefix}.items`;

  // Use the party address form hook
  const {
    openPartyDialog,
    currentPartyType,
    handleOpenPartyDialog,
    handleClosePartyDialog,
    handleUpdateParty
  } = usePartyAddressForm({ fieldPrefix });

  // State for item dialog
  const [openItemDialog, setOpenItemDialog] = useState(false);
  const [currentItemIndex, setCurrentItemIndex] = useState<number | null>(null);
  const [deleteItemIndex, setDeleteItemIndex] = useState<number | null>(null);
  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const deleteButtonRef = useRef<HTMLButtonElement>(null);

  // Create a temporary form structure for the dialog
  const [tempItem, setTempItem] = useState({ ...DEFAULT_ITEM });

  // Watch values for summary calculations
  const weightUnit = watch(`${fieldPrefix}.weightUnit`) || 'kg';
  const items = watch(`${fieldPrefix}.items`) || [];

  // Watch shipper and consignee to display their info
  const shipper = watch(`${fieldPrefix}.shipper`);
  const consignee = watch(`${fieldPrefix}.consignee`);

  // UseFieldArray hook to manage the items array
  const { fields, append, remove, update } = useFieldArray({
    control,
    name: itemsFieldName,
  });

  // Calculate and update totals based on items
  const updateTotals = () => {
    const items = getValues(`${itemsFieldName}`) || [];

    // Calculate total gross weight
    const totalWeight = items.reduce(
      (sum: number, item: { grossWeight: number }) =>
        sum + (parseFloat(String(item.grossWeight)) || 0),
      0
    );

    // Calculate total packages
    const totalPackages = items.length;

    // Update document totals
    setValue(`${fieldPrefix}.totalWeight`, parseFloat(totalWeight.toFixed(2)), {
      shouldDirty: true,
    });

    setValue(`${fieldPrefix}.totalPackages`, totalPackages, {
      shouldDirty: true,
    });
  };

  // Items Management Functions
  // Handle opening the item dialog for adding a new item
  const handleAddItem = () => {
    setCurrentItemIndex(null);

    // Set next item number
    const nextItemNumber =
      items.length > 0 ? Math.max(...items.map((item: any) => item.itemNumber || 0)) + 1 : 1;

    setTempItem({
      ...DEFAULT_ITEM,
      itemNumber: nextItemNumber,
    });
    setOpenItemDialog(true);
  };

  // Handle opening the item dialog for editing an existing item
  const handleEditItem = (index: number) => {
    const item = getValues(`${itemsFieldName}.${index}`) || {};

    // Ensure all fields are defined with fallbacks
    setTempItem({
      id: item.id || '',
      itemNumber: item.itemNumber || 1,
      packagingType: item.packagingType || '',
      grossWeight: item.grossWeight ?? 0,
      netWeight: item.netWeight ?? 0,
      description: item.description || '',
      hsCode: item.hsCode || '',
      t1TransitDocumentId: item.t1TransitDocumentId || '',
    });

    setCurrentItemIndex(index);
    setOpenItemDialog(true);
  };

  // Handle closing the item dialog
  const handleCloseItemDialog = () => {
    setOpenItemDialog(false);
    setCurrentItemIndex(null);
  };

  // Handle opening the delete confirmation dialog
  const handleOpenDeleteConfirm = (index: number) => {
    setDeleteItemIndex(index);
    setOpenDeleteConfirm(true);
  };

  // Handle closing the delete confirmation dialog
  const handleCloseDeleteConfirm = () => {
    setOpenDeleteConfirm(false);
    if (deleteItemIndex !== null) {
      deleteButtonRef.current?.focus();
    }
  };

  // Handle deleting an item
  const handleDeleteItem = () => {
    if (deleteItemIndex !== null) {
      remove(deleteItemIndex);
      updateTotals();
    }
    handleCloseDeleteConfirm();
  };

  // Handle change for temp form fields
  const handleTempItemChange = (field: string, value: string | number) => {
    setTempItem((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle saving an item (add new or update existing)
  const handleSaveItem = () => {
    if (currentItemIndex === null) {
      // Add new item - remove id and t1TransitDocumentId if they're null
      const newItem = { ...tempItem };
      if (!newItem.id) delete (newItem as any).id;
      if (!newItem.t1TransitDocumentId) delete (newItem as any).t1TransitDocumentId;
      append(newItem);
    } else {
      // Update existing item
      const existingId = getValues(`${itemsFieldName}.${currentItemIndex}.id`);
      const existingT1TransitDocumentId = getValues(
        `${itemsFieldName}.${currentItemIndex}.t1TransitDocumentId`
      );

      const updatedItem = {
        ...tempItem,
        id: existingId || undefined,
        t1TransitDocumentId: existingT1TransitDocumentId || undefined,
      };

      // Remove properties if they're undefined
      if (!updatedItem.id) delete updatedItem.id;
      if (!updatedItem.t1TransitDocumentId) delete updatedItem.t1TransitDocumentId;

      update(currentItemIndex, updatedItem);
    }

    // Update total values
    updateTotals();

    // Close the dialog
    handleCloseItemDialog();
  };

  // Render the main document info
  const renderMainInfo = () => (
    <Card sx={{ boxShadow: 'none' }}>
      <CardHeader
        title={t('plt1.details.documents.transit.form.mainInfoTitle')}
        sx={{ mb: 2 }}
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Field.Text
          name={`${fieldPrefix}.mrnNumber`}
          label={t('plt1.details.documents.transit.form.mrnNumber')}
          disabled={readOnly}
        />

        <Grid container spacing={2}>
          <Grid size={{ xs: 12, md: 6 }}>
            <Field.Text
              name={`${fieldPrefix}.totalWeight`}
              label={t('plt1.details.documents.transit.form.totalWeight')}
              type="number"
              disabled={readOnly}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <Field.Text
              name={`${fieldPrefix}.weightUnit`}
              label={t('plt1.details.documents.transit.form.weightUnit')}
              disabled={readOnly}
            />
          </Grid>
        </Grid>

        <Field.Text
          name={`${fieldPrefix}.totalPackages`}
          label={t('plt1.details.documents.transit.form.totalPackages')}
          type="number"
          disabled={readOnly}
        />
      </Stack>
    </Card>
  );

  // Render additional metadata fields
  const renderAdditionalInfo = () => (
    <Card sx={{ boxShadow: 'none' }}>
      <CardHeader
        title={t('plt1.details.documents.transit.form.additionalInfoTitle')}
        sx={{ mb: 2 }}
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Grid container spacing={2}>
          <Grid size={{ xs: 12, md: 6 }}>
            <Field.DatePicker
              name={`${fieldPrefix}.validityEndDate`}
              label={t('plt1.details.documents.transit.form.validityEndDate')}
              disabled={readOnly}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <Field.Text
              name={`${fieldPrefix}.lrnNumber`}
              label={t('plt1.details.documents.transit.form.lrnNumber')}
              disabled={readOnly}
            />
          </Grid>
        </Grid>

        <Grid container spacing={2}>
          <Grid size={{ xs: 12, md: 6 }}>
            <Field.Text
              name={`${fieldPrefix}.previousDocument`}
              label={t('plt1.details.documents.transit.form.previousDocument')}
              disabled={readOnly}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <Field.Text
              name={`${fieldPrefix}.storageLocationCode`}
              label={t('plt1.details.documents.transit.form.storageLocationCode')}
              disabled={readOnly}
            />
          </Grid>
        </Grid>

        <Grid container spacing={2}>
          <Grid size={{ xs: 12, md: 6 }}>
            <Field.Text
              name={`${fieldPrefix}.transportDocument`}
              label={t('plt1.details.documents.transit.form.transportDocument')}
              disabled={readOnly}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <Field.Text
              name={`${fieldPrefix}.attachedDocument`}
              label={t('plt1.details.documents.transit.form.attachedDocument')}
              disabled={readOnly}
            />
          </Grid>
        </Grid>

        <Grid container spacing={2}>
          <Grid size={{ xs: 12, md: 4 }}>
            <Field.Text
              name={`${fieldPrefix}.dispatchCountry`}
              label={t('plt1.details.documents.transit.form.dispatchCountry')}
              disabled={readOnly}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 4 }}>
            <Field.Text
              name={`${fieldPrefix}.destinationCustomsOffice`}
              label={t('plt1.details.documents.transit.form.destinationCustomsOffice')}
              disabled={readOnly}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 4 }}>
            <Field.Text
              name={`${fieldPrefix}.transportMeansAtExit`}
              label={t('plt1.details.documents.transit.form.transportMeansAtExit')}
              disabled={readOnly}
            />
          </Grid>
        </Grid>
      </Stack>
    </Card>
  );

  // Render party information using your existing components
  const renderPartyInfo = () => (
    <Card sx={{ boxShadow: 'none' }}>
      <CardHeader
        title={t('plt1.details.documents.transit.form.partyInfoTitle')}
        sx={{ mb: 2 }}
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Box
          sx={{
            rowGap: 3,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' },
          }}
        >
          <PartyFormSection
            partyType={PartyType.SHIPPER}
            labelKey="plt1.details.documents.transit.form.shipper"
            party={shipper}
            onOpenPartyDialog={handleOpenPartyDialog}
            readOnly={readOnly}
          />
          <PartyFormSection
            partyType={PartyType.CONSIGNEE}
            labelKey="plt1.details.documents.transit.form.consignee"
            party={consignee}
            onOpenPartyDialog={handleOpenPartyDialog}
            readOnly={readOnly}
          />
        </Box>
      </Stack>
    </Card>
  );

  // Render the item form in the dialog - using controlled components
  const renderItemForm = () => (
    <Stack spacing={3}>
      <Field.Text
        name="temp-itemNumber"
        label={t('plt1.details.documents.transit.item.itemNumber')}
        type="number"
        value={tempItem.itemNumber}
        onChange={(e) => handleTempItemChange('itemNumber', parseInt(e.target.value, 10) || 0)}
      />

      <Field.Text
        name="temp-packagingType"
        label={t('plt1.details.documents.transit.item.packagingType')}
        value={tempItem.packagingType}
        onChange={(e) => handleTempItemChange('packagingType', e.target.value)}
      />

      <Stack direction="row" spacing={2}>
        <Field.Text
          name="temp-grossWeight"
          label={t('plt1.details.documents.transit.item.grossWeight')}
          type="number"
          value={tempItem.grossWeight}
          onChange={(e) => handleTempItemChange('grossWeight', parseFloat(e.target.value) || 0)}
        />
        <Field.Text
          name="temp-netWeight"
          label={t('plt1.details.documents.transit.item.netWeight')}
          type="number"
          value={tempItem.netWeight}
          onChange={(e) => handleTempItemChange('netWeight', parseFloat(e.target.value) || 0)}
        />
      </Stack>

      <Field.Text
        name="temp-description"
        label={t('plt1.details.documents.transit.item.description')}
        value={tempItem.description}
        onChange={(e) => handleTempItemChange('description', e.target.value)}
        multiline
        rows={3}
      />

      <Field.Text
        name="temp-hsCode"
        label={t('plt1.details.documents.transit.item.hsCode')}
        value={tempItem.hsCode}
        onChange={(e) => handleTempItemChange('hsCode', e.target.value)}
      />
    </Stack>
  );

  // Render the item table
  const renderItemsTable = () => (
    <TableContainer component={Paper}>
      <Table sx={{ minWidth: 650 }} aria-label="transit items table">
        <TableHead>
          <TableRow>
            <TableCell>{t('plt1.details.documents.transit.item.itemNumber')}</TableCell>
            <TableCell>{t('plt1.details.documents.transit.item.packagingType')}</TableCell>
            <TableCell>{t('plt1.details.documents.transit.item.description')}</TableCell>
            <TableCell>{t('plt1.details.documents.transit.item.grossWeight')}</TableCell>
            <TableCell>{t('plt1.details.documents.transit.item.hsCode')}</TableCell>
            <TableCell align="right">{t('common.actions')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {fields.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} align="center">
                <Typography variant="body2" color="text.secondary">
                  {t('plt1.details.documents.transit.item.noItems')}
                </Typography>
              </TableCell>
            </TableRow>
          ) : (
            fields.map((field, index) => {
              const item = getValues(`${itemsFieldName}.${index}`) || {};
              return (
                <TableRow key={field.id}>
                  <TableCell>{item.itemNumber || '-'}</TableCell>
                  <TableCell>{item.packagingType || '-'}</TableCell>
                  <TableCell>{item.description || '-'}</TableCell>
                  <TableCell>
                    {item.grossWeight ?? '-'} {weightUnit}
                  </TableCell>
                  <TableCell>{item.hsCode || '-'}</TableCell>
                  <TableCell align="right">
                    <Stack direction="row" spacing={1} justifyContent="flex-end">
                      <Tooltip title={t('common.edit')}>
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleEditItem(index)}
                          disabled={readOnly}
                        >
                          <Iconify icon="eva:edit-fill" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={t('common.delete')}>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleOpenDeleteConfirm(index)}
                          ref={deleteItemIndex === index ? deleteButtonRef : null}
                          disabled={readOnly}
                        >
                          <Iconify icon="eva:trash-2-outline" />
                        </IconButton>
                      </Tooltip>
                    </Stack>
                  </TableCell>
                </TableRow>
              );
            })
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );

  // Render party dialog using your existing component
  const renderPartyDialog = () => (
    <PartyAddressDialog
      open={openPartyDialog}
      onClose={handleClosePartyDialog}
      onSave={handleUpdateParty}
      formPath={fieldPrefix}
      currentPartyType={currentPartyType}
      readOnly={readOnly}
      titlePrefix="plt1.details.documents.transit.partyAddress"
    />
  );

  return (
    <>
      <Stack spacing={3}>
        {renderMainInfo()}
        {renderAdditionalInfo()}
        {renderPartyInfo()}

        {/* Items Section */}
        <Card sx={{ boxShadow: 'none' }}>
          <CardHeader
            title={t('plt1.details.documents.transit.form.itemsTitle')}
            action={
              <Button
                variant="contained"
                size="small"
                startIcon={<Iconify icon="eva:plus-fill" />}
                onClick={handleAddItem}
                sx={{ mb: 2 }}
                disabled={readOnly}
              >
                {t('common.addItem')}
              </Button>
            }
          />
          <Divider sx={{ borderStyle: 'dashed' }} />
          <Box sx={{ p: 3 }}>
            {renderItemsTable()}

            {fields.length > 0 && (
              <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                <Button
                  variant="soft"
                  color="primary"
                  startIcon={<Iconify icon="eva:plus-fill" />}
                  onClick={handleAddItem}
                  disabled={readOnly}
                >
                  {t('common.addItem')}
                </Button>
              </Box>
            )}
          </Box>
        </Card>
      </Stack>

      {renderPartyDialog()}

      {/* Item Dialog */}
      <Dialog open={openItemDialog} onClose={handleCloseItemDialog} fullWidth maxWidth="md">
        <DialogTitle>
          {currentItemIndex === null
            ? t('plt1.details.documents.transit.item.addNew')
            : t('plt1.details.documents.transit.item.edit')}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 3 }}>{renderItemForm()}</Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseItemDialog} color="inherit">
            {t('common.cancel')}
          </Button>
          <Button onClick={handleSaveItem} variant="contained">
            {t('common.save')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={openDeleteConfirm}
        onClose={handleCloseDeleteConfirm}
        title={t('plt1.details.documents.transit.item.confirmDeleteDialog.title')}
        content={t('plt1.details.documents.transit.item.confirmDeleteDialog.content')}
        action={
          <Button variant="contained" color="error" onClick={handleDeleteItem}>
            {t('common.delete')}
          </Button>
        }
      />
    </>
  );
}