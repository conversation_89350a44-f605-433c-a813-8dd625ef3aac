import { DataGrid, DataGridProps, gridClasses, GridColumnVisibilityModel, GridDensity, GridFilterModel, GridLocaleText, GridPaginationModel, GridSortModel, GridToolbar } from '@mui/x-data-grid';
import { useEffect, useMemo, useState } from 'react';
import { useTranslate } from 'src/locales';
import { ODataQueryBuilder } from './odata/odata-query-builder';
import { EnumFilterProvider } from './components/enum-tag/enum-filter-context';
import { gridSettingsStorage, GridSettings } from './utils/grid-settings-storage';

export type AppDataGridProps = Omit<DataGridProps, 'localeText' | 'paginationModel' | 'onPaginationModelChange' | 'paginationMode' | 'rowCount'> & {
  onODataChange?: (odataParams: string) => void;
  totalCount?: number;
  initialSortModel?: GridSortModel;
  enumConfigs?: {
    [field: string]: {
      values: { [key: string]: string };
      config: { [key: string]: { translationKey: string } };
      translationPrefix: string;
    };
  };
  /**
   * Unique identifier for the grid to save/restore settings from local storage
   * If not provided, settings will not be persisted
   */
  gridId?: string;
  /**
   * Whether to persist grid settings to local storage
   * @default true
   */
  persistSettings?: boolean;
};

export function AppDataGrid({
  onODataChange,
  totalCount = 0,
  pageSizeOptions = [25, 50, 100],
  initialSortModel = [],
  enumConfigs = {},
  gridId,
  persistSettings = true,
  ...props
}: AppDataGridProps) {
  const { t } = useTranslate();
  const queryBuilder = new ODataQueryBuilder();

  // Load saved settings from local storage if gridId is provided
  const savedSettings = useMemo(() => {
    if (gridId && persistSettings) {
      return gridSettingsStorage.loadSettings(gridId);
    }
    return null;
  }, [gridId, persistSettings]);

  // Internal pagination state - use saved settings if available
  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>(
    savedSettings?.paginationModel || {
      page: 0,
      pageSize: 25
    }
  );

  // Use saved sort model if available, otherwise use initialSortModel
  const [sortModel, setSortModel] = useState<GridSortModel>(
    savedSettings?.sortModel || initialSortModel
  );

  // Use saved filter model if available
  const [filterModel, setFilterModel] = useState<GridFilterModel>(
    savedSettings?.filterModel || { items: [] }
  );

  // Use saved column visibility model if available
  const [columnVisibilityModel, setColumnVisibilityModel] = useState<GridColumnVisibilityModel>(
    savedSettings?.columnVisibilityModel || {}
  );

  // Use saved density if available
  const [density, setDensity] = useState<GridDensity>(
    savedSettings?.density || 'standard'
  );

  // Initialize OData query when component mounts
  useEffect(() => {
    // Apply initial settings
    updateODataParams({
      pagination: paginationModel,
      sortModel,
      filterModel
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Calculate enum options using the provided configurations
  const enumOptions = useMemo(() => {
    const options: { [field: string]: Array<{ value: string; label: string }> } = {};

    Object.entries(enumConfigs).forEach(([field, config]) => {
      options[field] = Object.values(config.values).map(value => ({
        value,
        label: t(`${config.translationPrefix}.${config.config[value].translationKey}`)
      }));
    });

    return options;
  }, [enumConfigs, t]);

  const updateODataParams = (params: {
    pagination: GridPaginationModel;
    sortModel: GridSortModel;
    filterModel: GridFilterModel;
  }) => {
    onODataChange?.(queryBuilder.buildQuery({
      pagination: params.pagination,
      sortModel: params.sortModel,
      filterModel: params.filterModel,
      columns: props.columns
    }));
  };

  // Helper function to save current settings to local storage
  const saveCurrentSettings = (settings: GridSettings) => {
    if (gridId && persistSettings) {
      gridSettingsStorage.saveSettings(gridId, settings);
    }
  };

  const handlePaginationModelChange = (newModel: GridPaginationModel) => {
    setPaginationModel(newModel);

    const updatedSettings = {
      pagination: newModel,
      sortModel,
      filterModel
    };

    updateODataParams(updatedSettings);

    // Save settings to local storage
    saveCurrentSettings({
      paginationModel: newModel,
      sortModel,
      filterModel,
      columnVisibilityModel,
      density
    });
  };

  const handleSortModelChange = (newModel: GridSortModel) => {
    setSortModel(newModel);

    const updatedSettings = {
      pagination: paginationModel,
      sortModel: newModel,
      filterModel
    };

    updateODataParams(updatedSettings);

    // Save settings to local storage
    saveCurrentSettings({
      paginationModel,
      sortModel: newModel,
      filterModel,
      columnVisibilityModel,
      density
    });
  };

  const handleFilterModelChange = (newModel: GridFilterModel) => {
    setFilterModel(newModel);
    // Reset to first page when filter changes
    const newPaginationModel = { ...paginationModel, page: 0 };
    setPaginationModel(newPaginationModel);

    const updatedSettings = {
      pagination: newPaginationModel,
      sortModel,
      filterModel: newModel
    };

    updateODataParams(updatedSettings);

    // Save settings to local storage
    saveCurrentSettings({
      paginationModel: newPaginationModel,
      sortModel,
      filterModel: newModel,
      columnVisibilityModel,
      density
    });
  };

  const handleColumnVisibilityModelChange = (newModel: GridColumnVisibilityModel) => {
    setColumnVisibilityModel(newModel);

    // Save settings to local storage
    saveCurrentSettings({
      paginationModel,
      sortModel,
      filterModel,
      columnVisibilityModel: newModel,
      density
    });
  };

  const handleDensityChange = (newDensity: GridDensity) => {
    setDensity(newDensity);

    // Save settings to local storage
    saveCurrentSettings({
      paginationModel,
      sortModel,
      filterModel,
      columnVisibilityModel,
      density: newDensity
    });
  };


  const localizationText: GridLocaleText = {
    // No rows
    noRowsLabel: t('dataGrid.noRows'),
    noResultsOverlayLabel: t('dataGrid.noResults'),

    // Toolbar
    toolbarDensity: t('dataGrid.toolbar.density'),
    toolbarDensityLabel: t('dataGrid.toolbar.densityLabel'),
    toolbarDensityCompact: t('dataGrid.toolbar.densityCompact'),
    toolbarDensityStandard: t('dataGrid.toolbar.densityStandard'),
    toolbarDensityComfortable: t('dataGrid.toolbar.densityComfortable'),
    toolbarColumns: t('dataGrid.toolbar.columns'),
    toolbarColumnsLabel: t('dataGrid.toolbar.columnsLabel'),
    toolbarFilters: t('dataGrid.toolbar.filters'),
    toolbarFiltersLabel: t('dataGrid.toolbar.filtersLabel'),
    toolbarFiltersTooltipHide: t('dataGrid.toolbar.filtersTooltipHide'),
    toolbarFiltersTooltipShow: t('dataGrid.toolbar.filtersTooltipShow'),
    toolbarFiltersTooltipActive: (count: any) => t('dataGrid.toolbar.filtersTooltipActive', { count }),
    toolbarQuickFilterPlaceholder: t('dataGrid.toolbar.quickFilterPlaceholder'),
    toolbarQuickFilterLabel: t('dataGrid.toolbar.quickFilterLabel'),
    toolbarQuickFilterDeleteIconLabel: t('dataGrid.toolbar.quickFilterDeleteIconLabel'),
    toolbarExport: t('dataGrid.toolbar.export'),
    toolbarExportLabel: t('dataGrid.toolbar.exportLabel'),
    toolbarExportCSV: t('dataGrid.toolbar.exportCSV'),
    toolbarExportPrint: t('dataGrid.toolbar.exportPrint'),
    toolbarExportExcel: t('dataGrid.toolbar.exportExcel'),

    // Columns Management
    columnsManagementSearchTitle: t('dataGrid.columnsManagement.searchTitle'),
    columnsManagementNoColumns: t('dataGrid.columnsManagement.noColumns'),
    columnsManagementShowHideAllText: t('dataGrid.columnsManagement.showHideAll'),
    columnsManagementReset: t('dataGrid.columnsManagement.reset'),
    columnsManagementDeleteIconLabel: t('dataGrid.columnsManagement.deleteIconLabel'),

    // Filter Panel
    filterPanelAddFilter: t('dataGrid.filterPanel.addFilter'),
    filterPanelRemoveAll: t('dataGrid.filterPanel.removeAll'),
    filterPanelDeleteIconLabel: t('dataGrid.filterPanel.deleteIconLabel'),
    filterPanelLogicOperator: t('dataGrid.filterPanel.logicOperator'),
    filterPanelOperator: t('dataGrid.filterPanel.operator'),
    filterPanelOperatorAnd: t('dataGrid.filterPanel.operatorAnd'),
    filterPanelOperatorOr: t('dataGrid.filterPanel.operatorOr'),
    filterPanelColumns: t('dataGrid.filterPanel.columns'),
    filterPanelInputLabel: t('dataGrid.filterPanel.inputLabel'),
    filterPanelInputPlaceholder: t('dataGrid.filterPanel.inputPlaceholder'),

    // Filter Operators
    filterOperatorContains: t('dataGrid.filterOperator.contains'),
    filterOperatorDoesNotContain: t('dataGrid.filterOperator.doesNotContain'),
    filterOperatorEquals: t('dataGrid.filterOperator.equals'),
    filterOperatorDoesNotEqual: t('dataGrid.filterOperator.doesNotEqual'),
    filterOperatorStartsWith: t('dataGrid.filterOperator.startsWith'),
    filterOperatorEndsWith: t('dataGrid.filterOperator.endsWith'),
    filterOperatorIs: t('dataGrid.filterOperator.is'),
    filterOperatorNot: t('dataGrid.filterOperator.not'),
    filterOperatorAfter: t('dataGrid.filterOperator.after'),
    filterOperatorOnOrAfter: t('dataGrid.filterOperator.onOrAfter'),
    filterOperatorBefore: t('dataGrid.filterOperator.before'),
    filterOperatorOnOrBefore: t('dataGrid.filterOperator.onOrBefore'),
    filterOperatorIsEmpty: t('dataGrid.filterOperator.isEmpty'),
    filterOperatorIsNotEmpty: t('dataGrid.filterOperator.isNotEmpty'),
    filterOperatorIsAnyOf: t('dataGrid.filterOperator.isAnyOf'),
    'filterOperator=': t('dataGrid.filterOperator.equals'),
    'filterOperator!=': t('dataGrid.filterOperator.notEquals'),
    'filterOperator>': t('dataGrid.filterOperator.greaterThan'),
    'filterOperator>=': t('dataGrid.filterOperator.greaterThanOrEqual'),
    'filterOperator<': t('dataGrid.filterOperator.lessThan'),
    'filterOperator<=': t('dataGrid.filterOperator.lessThanOrEqual'),

    // Header Filter Operators
    headerFilterOperatorContains: t('dataGrid.headerFilterOperator.contains'),
    headerFilterOperatorDoesNotContain: t('dataGrid.headerFilterOperator.doesNotContain'),
    headerFilterOperatorEquals: t('dataGrid.headerFilterOperator.equals'),
    headerFilterOperatorDoesNotEqual: t('dataGrid.headerFilterOperator.doesNotEqual'),
    headerFilterOperatorStartsWith: t('dataGrid.headerFilterOperator.startsWith'),
    headerFilterOperatorEndsWith: t('dataGrid.headerFilterOperator.endsWith'),
    headerFilterOperatorIs: t('dataGrid.headerFilterOperator.is'),
    headerFilterOperatorNot: t('dataGrid.headerFilterOperator.not'),
    headerFilterOperatorAfter: t('dataGrid.headerFilterOperator.after'),
    headerFilterOperatorOnOrAfter: t('dataGrid.headerFilterOperator.onOrAfter'),
    headerFilterOperatorBefore: t('dataGrid.headerFilterOperator.before'),
    headerFilterOperatorOnOrBefore: t('dataGrid.headerFilterOperator.onOrBefore'),
    headerFilterOperatorIsEmpty: t('dataGrid.headerFilterOperator.isEmpty'),
    headerFilterOperatorIsNotEmpty: t('dataGrid.headerFilterOperator.isNotEmpty'),
    headerFilterOperatorIsAnyOf: t('dataGrid.headerFilterOperator.isAnyOf'),
    'headerFilterOperator=': t('dataGrid.headerFilterOperator.equals'),
    'headerFilterOperator!=': t('dataGrid.headerFilterOperator.notEquals'),
    'headerFilterOperator>': t('dataGrid.headerFilterOperator.greaterThan'),
    'headerFilterOperator>=': t('dataGrid.headerFilterOperator.greaterThanOrEqual'),
    'headerFilterOperator<': t('dataGrid.headerFilterOperator.lessThan'),
    'headerFilterOperator<=': t('dataGrid.headerFilterOperator.lessThanOrEqual'),

    // Filter Values
    filterValueAny: t('dataGrid.filterValue.any'),
    filterValueTrue: t('dataGrid.filterValue.true'),
    filterValueFalse: t('dataGrid.filterValue.false'),

    // Column Menu
    columnMenuLabel: t('dataGrid.columnMenu.label'),
    columnMenuShowColumns: t('dataGrid.columnMenu.showColumns'),
    columnMenuManageColumns: t('dataGrid.columnMenu.manageColumns'),
    columnMenuFilter: t('dataGrid.columnMenu.filter'),
    columnMenuHideColumn: t('dataGrid.columnMenu.hideColumn'),
    columnMenuUnsort: t('dataGrid.columnMenu.unsort'),
    columnMenuSortAsc: t('dataGrid.columnMenu.sortAsc'),
    columnMenuSortDesc: t('dataGrid.columnMenu.sortDesc'),

    // Column Header
    columnHeaderFiltersTooltipActive: (count: any) =>
      t('dataGrid.columnHeader.filtersTooltipActive', { count }),
    columnHeaderFiltersLabel: t('dataGrid.columnHeader.filtersLabel'),
    columnHeaderSortIconLabel: t('dataGrid.columnHeader.sortIconLabel'),

    // Footer
    footerRowSelected: (count: any) =>
      t('dataGrid.footer.rowSelected', { count }),
    footerTotalRows: t('dataGrid.footer.totalRows'),
    footerTotalVisibleRows: (visibleCount: any, totalCount: any) =>
      t('dataGrid.footer.totalVisibleRows', { visibleCount, totalCount }),

    // Checkbox Selection
    checkboxSelectionHeaderName: t('dataGrid.checkboxSelection.headerName'),
    checkboxSelectionSelectAllRows: t('dataGrid.checkboxSelection.selectAllRows'),
    checkboxSelectionUnselectAllRows: t('dataGrid.checkboxSelection.unselectAllRows'),
    checkboxSelectionSelectRow: t('dataGrid.checkboxSelection.selectRow'),
    checkboxSelectionUnselectRow: t('dataGrid.checkboxSelection.unselectRow'),

    // Boolean Cell
    booleanCellTrueLabel: t('dataGrid.booleanCell.trueLabel'),
    booleanCellFalseLabel: t('dataGrid.booleanCell.falseLabel'),

    // Actions
    actionsCellMore: t('dataGrid.actions.cellMore'),
    pinToLeft: t('dataGrid.actions.pinToLeft'),
    pinToRight: t('dataGrid.actions.pinToRight'),
    unpin: t('dataGrid.actions.unpin'),

    // Tree Data
    treeDataGroupingHeaderName: t('dataGrid.treeData.groupingHeaderName'),
    treeDataExpand: t('dataGrid.treeData.expand'),
    treeDataCollapse: t('dataGrid.treeData.collapse'),

    // Grouping
    groupingColumnHeaderName: t('dataGrid.grouping.columnHeaderName'),
    groupColumn: (name: any) => t('dataGrid.grouping.groupColumn', { name }),
    unGroupColumn: (name: any) => t('dataGrid.grouping.unGroupColumn', { name }),

    // Detail Panel
    detailPanelToggle: t('dataGrid.detailPanel.toggle'),
    expandDetailPanel: t('dataGrid.detailPanel.expand'),
    collapseDetailPanel: t('dataGrid.detailPanel.collapse'),

    // Row Reordering
    rowReorderingHeaderName: t('dataGrid.rowReordering.headerName'),

    // Aggregation
    aggregationMenuItemHeader: t('dataGrid.aggregation.menuItemHeader'),
    aggregationFunctionLabelSum: t('dataGrid.aggregation.sum'),
    aggregationFunctionLabelAvg: t('dataGrid.aggregation.avg'),
    aggregationFunctionLabelMin: t('dataGrid.aggregation.min'),
    aggregationFunctionLabelMax: t('dataGrid.aggregation.max'),
    aggregationFunctionLabelSize: t('dataGrid.aggregation.size'),

    // Pagination
    MuiTablePagination: {
      labelRowsPerPage: t('dataGrid.pagination.rowsPerPage'),
      labelDisplayedRows: ({ from, to, count }: { from: number; to: number; count: number }) =>
        t('dataGrid.pagination.displayedRows', { from, to, count })
    }
  };

  const createEnumProviders = (children: React.ReactNode) => {
    return Object.entries(enumOptions).reduce((wrapped, [field, options]) => {
      return (
        <EnumFilterProvider field={field} options={options}>
          {wrapped}
        </EnumFilterProvider>
      );
    }, children);
  };

  const columnsWithMinWidth = props.columns?.map(column => ({
    ...column,
    minWidth: column.minWidth || 100
  }));

  return createEnumProviders(
    <DataGrid
      {...props}
      columns={columnsWithMinWidth}
      sx={{
        [`& .${gridClasses.cell}:focus, & .${gridClasses.cell}:focus-within`]: {
          outline: 'none',
        },
        [`& .${gridClasses.columnHeader}:focus, & .${gridClasses.columnHeader}:focus-within`]: {
          outline: 'none',
        },
      }}
      paginationModel={paginationModel}
      onPaginationModelChange={handlePaginationModelChange}
      onSortModelChange={handleSortModelChange}
      onFilterModelChange={handleFilterModelChange}
      columnVisibilityModel={columnVisibilityModel}
      onColumnVisibilityModelChange={handleColumnVisibilityModelChange}
      density={density}
      onDensityChange={handleDensityChange}
      pageSizeOptions={pageSizeOptions}
      paginationMode="server"
      sortingMode="server"
      sortModel={sortModel}
      filterMode="server"
      filterModel={filterModel}
      rowCount={totalCount}
      localeText={localizationText}
      slots={{
        toolbar: GridToolbar
      }}
      slotProps={{
        ...props.slotProps,
        toolbar: {
          ...props.slotProps?.toolbar,
          printOptions: { disableToolbarButton: true },
          csvOptions: { disableToolbarButton: true }
        }
      }}
      initialState={{
        ...props.initialState,
        filter: {
          ...props.initialState?.filter,
          filterModel: filterModel,
        },
        columns: {
          ...props.initialState?.columns,
          columnVisibilityModel: columnVisibilityModel,
        },
        density,
      }}
    />
  );
}