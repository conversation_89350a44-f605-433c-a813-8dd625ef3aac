"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/utils/order-details-utils.ts":
/*!***************************************************************!*\
  !*** ./src/sections/orders/plt1/utils/order-details-utils.ts ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanNumericFields: () => (/* binding */ cleanNumericFields),\n/* harmony export */   handlePLT1OrderSave: () => (/* binding */ handlePLT1OrderSave),\n/* harmony export */   hasFormChanges: () => (/* binding */ hasFormChanges),\n/* harmony export */   normalizeValue: () => (/* binding */ normalizeValue)\n/* harmony export */ });\n/* harmony import */ var src_lib_axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/lib/axios */ \"(app-pages-browser)/./src/lib/axios.ts\");\n/* harmony import */ var src_components_snackbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/components/snackbar */ \"(app-pages-browser)/./src/components/snackbar/index.ts\");\n// src/utils/form-utils.ts\n\n\n/**\r\n * Normalizes form values for comparison by handling null/undefined values,\r\n * empty strings, NaN numbers, and nested objects/arrays consistently.\r\n *\r\n * @param value - The value to normalize\r\n * @returns The normalized value\r\n */ const normalizeValue = (value)=>{\n    if (value === null || value === undefined) return '';\n    if (typeof value === 'number' && isNaN(value)) return 0;\n    if (typeof value === 'string' && value.trim() === '') return '';\n    if (typeof value === 'object' && value !== null) {\n        if (Array.isArray(value)) {\n            return value.map(normalizeValue);\n        } else {\n            const normalized = {};\n            Object.keys(value).forEach((key)=>{\n                normalized[key] = normalizeValue(value[key]);\n            });\n            return normalized;\n        }\n    }\n    return value;\n};\n/**\r\n * Cleans numeric fields in form data to prevent backend validation errors.\r\n * Converts empty strings and null values to 0 for numeric fields.\r\n *\r\n * @param value - The value to clean\r\n * @returns The cleaned value\r\n */ const cleanNumericFields = (value)=>{\n    if (value === null || value === undefined) return value;\n    if (typeof value === 'string' && value.trim() === '') {\n        // For empty strings that should be numbers, return 0\n        return 0;\n    }\n    if (typeof value === 'object' && value !== null) {\n        if (Array.isArray(value)) {\n            return value.map(cleanNumericFields);\n        } else {\n            const cleaned = {};\n            Object.keys(value).forEach((key)=>{\n                // List of fields that should be numeric\n                const numericFields = [\n                    'quantity',\n                    'itemNetWeight',\n                    'itemGrossWeight',\n                    'packageNetWeight',\n                    'packageGrossWeight',\n                    'packagesNetWeight',\n                    'packagesGrossWeight',\n                    'packageVolume',\n                    'packagesVolume',\n                    'numberOfPackages',\n                    'totalQuantity',\n                    'totalNumberOfPackages',\n                    'totalNetWeight',\n                    'totalGrossWeight',\n                    'totalVolume',\n                    'grossWeight',\n                    'netWeight',\n                    'numberOfPieces',\n                    'volumeMeasurement'\n                ];\n                if (numericFields.includes(key) && (value[key] === '' || value[key] === null || value[key] === undefined)) {\n                    cleaned[key] = 0;\n                } else {\n                    cleaned[key] = cleanNumericFields(value[key]);\n                }\n            });\n            return cleaned;\n        }\n    }\n    return value;\n};\n/**\r\n * Compares two form value objects by normalizing them first\r\n * \r\n * @param currentValues - The current form values\r\n * @param initialValues - The initial form values to compare against\r\n * @returns true if the values are different, false if they're the same\r\n */ const hasFormChanges = (currentValues, initialValues)=>{\n    const normalizedCurrent = normalizeValue(currentValues);\n    const normalizedInitial = normalizeValue(initialValues);\n    return JSON.stringify(normalizedCurrent) !== JSON.stringify(normalizedInitial);\n};\n/**\r\n * Generic function to handle saving PLT1 orders\r\n * \r\n * @param formData - The form data to save\r\n * @param config - Configuration object containing endpoint, ID field, etc.\r\n * @param setIsSaving - Function to update saving state\r\n * @param setFormChanged - Function to update form changed state\r\n * @param reload - Function to reload order data after successful save\r\n * @returns Promise that resolves when save is complete\r\n */ const handlePLT1OrderSave = async (formData, config, setIsSaving, setFormChanged, reload)=>{\n    try {\n        setIsSaving(true);\n        // Clean numeric fields to prevent backend validation errors\n        const cleanedFormData = cleanNumericFields(formData);\n        const payload = {\n            ...cleanedFormData,\n            [config.idField]: config.orderId\n        };\n        const response = await src_lib_axios__WEBPACK_IMPORTED_MODULE_0__.axiosInstance.put(\"\".concat(config.endpoint, \"/\").concat(config.orderId), payload);\n        if (response.status === 200) {\n            src_components_snackbar__WEBPACK_IMPORTED_MODULE_1__.toast.success(config.t('plt1.saveSuccess'));\n            setFormChanged(false);\n            const reloadResult = reload();\n            // Handle case where reload might return undefined or a promise\n            if (reloadResult && typeof reloadResult.then === 'function') {\n                await reloadResult;\n            }\n        } else {\n            src_components_snackbar__WEBPACK_IMPORTED_MODULE_1__.toast.error(config.t('plt1.saveError'));\n        }\n    } catch (error) {\n        var _error_response_data_errorMessages, _error_response_data, _error_response;\n        console.error('Error saving order:', error);\n        src_components_snackbar__WEBPACK_IMPORTED_MODULE_1__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : (_error_response_data_errorMessages = _error_response_data.errorMessages) === null || _error_response_data_errorMessages === void 0 ? void 0 : _error_response_data_errorMessages[0]) || config.t('plt1.saveError'));\n        throw error;\n    } finally{\n        setIsSaving(false);\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/utils/order-details-utils.ts\n"));

/***/ })

});