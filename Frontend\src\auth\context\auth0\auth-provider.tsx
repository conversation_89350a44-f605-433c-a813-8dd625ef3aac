'use client';

import { useAuth0, Auth0Provider } from '@auth0/auth0-react';
import { useMemo, useState, useEffect } from 'react';

import { axiosInstance, endpoints } from 'src/lib/axios';
import { CONFIG } from 'src/global-config';
import { AuthContext } from '../auth-context';
import { endSession, isSessionActive, requiresOnboarding, setSession } from '../jwt/utils';
import { useRouter } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';
import { INVITATION_TOKEN_STORAGE_KEY } from '../jwt';

type Props = {
  children: React.ReactNode;
};

export function AuthProvider({ children }: Props) {
  const { domain, clientId, callbackUrl } = CONFIG.auth0;

  if (!(domain && clientId && callbackUrl)) {
    return null;
  }

  return (
    <Auth0Provider
      domain={domain}
      clientId={clientId}
      cacheLocation="localstorage"
      useRefreshTokens={true}
      authorizationParams={{ redirect_uri: callbackUrl, scope: 'openid profile email offline_access' }}
    >
      <AuthProviderContainer>{children}</AuthProviderContainer>
    </Auth0Provider>
  );
}

function AuthProviderContainer({ children }: Props) {
  const { 
    user, 
    isAuthenticated, 
    getIdTokenClaims, 
    getAccessTokenSilently, 
    logout, 
    loginWithRedirect 
  } = useAuth0();
  
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [accessToken, setAccessToken] = useState<string | null>(null);

  useEffect(() => {
    const initializeAuth = async () => {
      setLoading(true);
      try {
        if (isAuthenticated) {

          await getAccessTokenSilently({
            detailedResponse: true,
            cacheMode: 'off'  // Force fresh token
          });

          const idTokenClaims = await getIdTokenClaims();
          const idToken = idTokenClaims?.__raw;
          const invitationToken = sessionStorage.getItem(INVITATION_TOKEN_STORAGE_KEY);

          const response = await axiosInstance.post(endpoints.auth.exchangeToken, {
            idToken,
            invitationToken
          });

          if (!response.data.isSuccess) {
            await logout();
            endSession();
            setAccessToken(null);
            return;
          }

          sessionStorage.removeItem(INVITATION_TOKEN_STORAGE_KEY);

          const { token, refreshToken } = response.data;
          setSession(token, refreshToken);
          setAccessToken(token);

          if (requiresOnboarding(token)) {
            router.push(paths.auth.auth0.onboarding);
          }
          return;
        }

        if (isSessionActive()) {
          try {
            await getAccessTokenSilently();
            return;
          } catch (error) {
            loginWithRedirect();
          }
        }

        endSession();
        setAccessToken(null);
      } catch (error) {
        console.error('Auth initialization error:', error);
        await logout();
        endSession();
        setAccessToken(null);
        router.push(paths.auth.auth0.signIn);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, [isAuthenticated, getIdTokenClaims, getAccessTokenSilently, loginWithRedirect, logout, router]);

  const memoizedValue = useMemo(() => {
    const authStatus = loading ? 'loading' : (isAuthenticated ? 'authenticated' : 'unauthenticated');

    return {
      user: user ? {
        ...user,
        id: user.sub,
        accessToken,
        displayName: user.name,
        photoURL: user.picture ?? `${CONFIG.assetsDir}/assets/images/mock/cover/cover-1.webp`,
        role: user.role ?? '',
      } : null,
      loading: authStatus === 'loading',
      authenticated: authStatus === 'authenticated',
      unauthenticated: authStatus === 'unauthenticated',
    };
  }, [user, loading, isAuthenticated, accessToken]);

  return (
    <AuthContext.Provider value={memoizedValue}>
      {children}
    </AuthContext.Provider>
  );
}