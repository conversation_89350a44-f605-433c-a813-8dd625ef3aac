import { Box, Dialog, DialogProps, IconButton, InputBase, InputProps } from "@mui/material";
import Grid from '@mui/material/Grid2';
import { varAlpha } from "minimal-shared/utils";
import { useCallback, useState } from "react";
import { Iconify } from "src/components/iconify";
import { toast } from "src/components/snackbar";
import { CONFIG } from "src/global-config";
import { useTranslate } from "src/locales";
import { axiosInstance, endpoints } from "src/lib/axios";
import LoadingButton from "@mui/lab/LoadingButton";

type InviteUserDialogProps = InputProps &
  DialogProps & {
    onClose: () => void;
    onCopyLink?: () => void;
    onSuccess?: () => void;
  };

export function InviteUserDialog({
  open,
  onClose,
  onSuccess
}: InviteUserDialogProps) {
  const { t } = useTranslate();
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleEmailChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newEmail = event.target.value;
    setEmail(newEmail);

    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleInvite = useCallback(async () => {
    if (!email.trim()) {
      setError(t('user.invite.errorEmpty'));
      return;
    }

    if (!validateEmail(email)) {
      setError(t('user.invite.errorInvalid'));
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await axiosInstance.post(endpoints.identity.invite, {
        email: email.trim()
      });

      if (response.data.isSuccess) {
        toast.success(t('user.invite.success', { email }), {
          style: {
            minWidth: '420px',
          },
        });

        setEmail('');
        onSuccess?.();
        onClose();
      }
    } catch (error) {
      console.error('Failed to send invitation:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [email, onClose, onSuccess, t]);

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !isSubmitting) {
      handleInvite();
    }
  };

  return (
    <Dialog open={open} fullWidth maxWidth="xs" onClose={onClose}>
      <Grid container spacing={{ sm: 2, xs: 0.5 }}
        sx={[
          (theme) => ({
            borderRadius: 2,
            position: 'relative',
            color: 'common.white',
            backgroundImage: `linear-gradient(135deg, ${theme.vars.palette.primary.dark}, ${theme.vars.palette.primary.main})`,
          })
        ]}>
        <Grid size={12} sx={{ pt: 2, alignItems: 'flex-start', justifyContent: 'right', display: { xs: 'flex', sm: 'none' } }}>
          <IconButton color="inherit" onClick={onClose}>
            <Iconify icon="mingcute:close-line" />
          </IconButton>
        </Grid>
        <Grid size={{ sm: 7, xs: 12 }} sx={{ pt: { sm: 4 }, pl: { sm: 4, xs: 4 }, alignItems: 'center' }}>
          <Box>
            <Box sx={{ whiteSpace: 'pre-line', typography: 'h4' }}>{t("user.invite.title")}</Box>
            <Box sx={{ mt: 2, mb: 1, typography: 'body2' }}>{t("user.invite.description")}</Box>
          </Box>
        </Grid>
        <Grid size={{ sm: 3, xs: 12 }} sx={{ pt: { sm: 4 }, pb: { xs: 3 }, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Box
            component="img"
            alt="Invite"
            src={`${CONFIG.assetsDir}/assets/illustrations/characters/character-7.webp`}
            sx={{
              width: 110,
              height: 110,
            }}
          />
        </Grid>
        <Grid size={2} sx={{ pt: 2, alignItems: 'flex-start', display: { xs: 'none', sm: 'flex' } }}>
          <IconButton color="inherit" onClick={onClose} sx={{ pl: 2 }}>
            <Iconify icon="mingcute:close-line" />
          </IconButton>
        </Grid>
        <Grid size={12} sx={{ px: 4, pb: 4 }}>
          <InputBase
            fullWidth
            required={true}
            value={email}
            onChange={handleEmailChange}
            onKeyPress={handleKeyPress}
            error={!!error}
            disabled={isSubmitting}
            placeholder={t("user.invite.emailPlaceholder")}
            endAdornment={
              <LoadingButton
                color="warning"
                variant="contained"
                size="small"
                onClick={handleInvite}
                disabled={isSubmitting}
                sx={{ mr: 0.5 }}
              >
                {t("user.invite.inviteLabel")}
              </LoadingButton>
            }
            inputProps={{
              id: 'email-input',
              type: 'email',
              sx: {
                color: 'common.white',
                '&::placeholder': { opacity: 0.48, color: 'inherit' }
              },
            }}
            sx={[
              (theme) => ({
                pl: 1.5,
                height: 40,
                borderRadius: 1,
                bgcolor: varAlpha(theme.vars.palette.common.blackChannel, 0.12),
                ...(error && {
                  border: `1px solid ${theme.vars.palette.error.main}`,
                })
              }),
            ]}
          />
          {error && (
            <Box sx={{
              color: 'error.dark',
              mt: 1,
              ml: 1.5,
              fontSize: '0.75rem'
            }}>
              {error}
            </Box>
          )}
        </Grid>
      </Grid>
    </Dialog>
  );
}