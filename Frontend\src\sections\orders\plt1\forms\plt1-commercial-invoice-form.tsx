import { useState, useRef } from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  Divider,
  Typography,
  Button,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip
} from '@mui/material';
import { useTranslate } from 'src/locales';
import { Field } from 'src/components/hook-form';
import { Iconify } from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';
import PartyFormSection from '../../../../components/party-address/party-form-section';
import { PartyType } from 'src/types/parties';
import { usePartyAddressForm } from 'src/components/party-address/hooks/usePartyAddressForm';
import { PartyAddressData } from 'src/components/party-address/party-address-form';
import PartyAddressDialog from 'src/components/party-address/party-address-dialog';
// ----------------------------------------------------------------------
export interface PLT1CommercialInvoiceItemData {
  id?: string;
  name?: string;
  translatedName?: string;
  modelNumber?: string;
  quantity?: number;
  unit?: string;
  positionValue?: number;
  itemValue?: number;
  currency?: string;
  hsCode?: string;
  commercialInvoiceId?: string;
}
export interface PLT1CommercialInvoiceData {
  id?: string;
  invoiceNumber: string;
  invoiceDate: string | null;
  incoterms: string;
  countryOfOrigin: string;
  invoiceValue: number;
  invoiceCurrency: string;
  shipper: PartyAddressData;
  consignee: PartyAddressData;
  items: PLT1CommercialInvoiceItemData[];
  t1OrderId: string;
}

const DEFAULT_ITEM = {
  id: null,
  name: '',
  translatedName: '',
  modelNumber: '',
  quantity: 0,
  unit: '',
  positionValue: 0,
  itemValue: 0,
  currency: 'USD',
  hsCode: '',
  commercialInvoiceId: null
};

// ----------------------------------------------------------------------

interface T1CommercialInvoiceFormProps {
  formPath: string;
  index?: number;
  readOnly?: boolean;
}

export default function PLT1CommercialInvoiceForm({ formPath, index, readOnly = false }: T1CommercialInvoiceFormProps) {
  const { t } = useTranslate();
  const { control, getValues, setValue, watch } = useFormContext();

  const fieldPrefix = index !== undefined ? `${formPath}.${index}` : formPath;
  const itemsFieldName = `${fieldPrefix}.items`;
  // Use the party address form hook
  const {
    openPartyDialog,
    currentPartyType,
    handleOpenPartyDialog,
    handleClosePartyDialog,
    handleUpdateParty
  } = usePartyAddressForm({ fieldPrefix });

  // State for item dialog
  const [openItemDialog, setOpenItemDialog] = useState(false);
  const [currentItemIndex, setCurrentItemIndex] = useState<number | null>(null);
  const [deleteItemIndex, setDeleteItemIndex] = useState<number | null>(null);
  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const deleteButtonRef = useRef<HTMLButtonElement>(null);



  // Watch shipper and consignee to display their info
  const shipper = watch(`${fieldPrefix}.shipper`);
  const consignee = watch(`${fieldPrefix}.consignee`);
  const invoiceCurrency = watch(`${fieldPrefix}.invoiceCurrency`);

  // UseFieldArray hook to manage the items array
  const { fields, append, remove, update } = useFieldArray({
    control,
    name: itemsFieldName,
  });

  // Items Management Functions
  // Handle opening the item dialog for adding a new item
  const handleAddItem = () => {
    setCurrentItemIndex(null);
    // Add a new item with default values
    const newItem = {
      ...DEFAULT_ITEM,
      currency: invoiceCurrency || 'USD'
    };
    append(newItem);
    setCurrentItemIndex(fields.length); // Set to the new index
    setOpenItemDialog(true);
  };

  // Handle opening the item dialog for editing an existing item
  const handleEditItem = (index: number) => {
    setCurrentItemIndex(index);
    setOpenItemDialog(true);
  };

  // Handle closing the item dialog
  const handleCloseItemDialog = () => {
    setOpenItemDialog(false);
    // If we were adding a new item and user cancels, remove the empty item
    if (currentItemIndex !== null && currentItemIndex === fields.length - 1) {
      const item = getValues(`${itemsFieldName}.${currentItemIndex}`);
      // Check if it's an empty item (all fields are default values)
      const isEmpty = !item.name && !item.translatedName && !item.modelNumber &&
                     !item.hsCode && item.quantity === 0 && item.value === 0;
      if (isEmpty) {
        remove(currentItemIndex);
      }
    }
    setCurrentItemIndex(null);
  };

  // Handle opening the delete confirmation dialog
  const handleOpenDeleteConfirm = (index: number) => {
    setDeleteItemIndex(index);
    setOpenDeleteConfirm(true);
  };

  // Handle closing the delete confirmation dialog
  const handleCloseDeleteConfirm = () => {
    setOpenDeleteConfirm(false);
    if (deleteItemIndex !== null) {
      deleteButtonRef.current?.focus();
    }
  };

  // Handle deleting an item
  const handleDeleteItem = () => {
    if (deleteItemIndex !== null) {
      remove(deleteItemIndex);
    }
    handleCloseDeleteConfirm();
  };

  // Handle saving an item (just close the dialog since form is already updated)
  const handleSaveItem = () => {
    // Update total value
    updateInvoiceTotalValue();
    // Close the dialog
    handleCloseItemDialog();
  };

  // Calculate and update total value based on items
  const updateInvoiceTotalValue = () => {
    // Get all items
    const items = getValues(`${itemsFieldName}`) || [];

    // Filter items that have the same currency as the invoice
    const currentCurrency = getValues(`${fieldPrefix}.invoiceCurrency`) || 'USD';
    const itemsWithSameCurrency = items.filter((item: { currency: any; value: any; }) =>
      (item.currency === currentCurrency || !item.currency) && item.value
    );

    // Calculate total
    const totalValue = itemsWithSameCurrency.reduce(
      (sum: number, item: { value: string; quantity: string; }) => sum + (parseFloat(item.value) || 0) * (parseFloat(item.quantity) || 1),
      0
    );

    // Update invoice total value if there are items with matching currency
    if (itemsWithSameCurrency.length > 0) {
      setValue(`${fieldPrefix}.invoiceValue`, parseFloat(totalValue.toFixed(2)), {
        shouldDirty: true
      });
    }
  };

  // Render the item form in the dialog - using React Hook Form fields
  const renderItemForm = () => {
    if (currentItemIndex === null) return null;

    return (
      <Stack spacing={2.5}>
        <Field.Text
          name={`${itemsFieldName}.${currentItemIndex}.name`}
          label={t('plt1.details.documents.commercialInvoice.item.name')}
          size="small"
          disabled={readOnly}
        />

        <Field.Text
          name={`${itemsFieldName}.${currentItemIndex}.translatedName`}
          label={t('plt1.details.documents.commercialInvoice.item.translatedName')}
          multiline
          rows={2}
          size="small"
          disabled={readOnly}
        />

        <Field.Text
          name={`${itemsFieldName}.${currentItemIndex}.modelNumber`}
          label={t('plt1.details.documents.commercialInvoice.item.modelNumber')}
          multiline
          rows={2}
          size="small"
          disabled={readOnly}
        />

        <Field.Text
          name={`${itemsFieldName}.${currentItemIndex}.hsCode`}
          label={t('plt1.details.documents.commercialInvoice.item.hsCode')}
          size="small"
          disabled={readOnly}
        />

        <Stack direction="row" spacing={2}>
          <Field.Text
            name={`${itemsFieldName}.${currentItemIndex}.quantity`}
            label={t('plt1.details.documents.commercialInvoice.item.quantity')}
            type="number"
            size="small"
            disabled={readOnly}
          />
          <Field.Text
            name={`${itemsFieldName}.${currentItemIndex}.unit`}
            label={t('plt1.details.documents.commercialInvoice.item.unit')}
            placeholder="pcs, kg, etc."
            size="small"
            disabled={readOnly}
          />
        </Stack>

        <Stack direction="row" spacing={2}>
          <Field.Text
            name={`${itemsFieldName}.${currentItemIndex}.positionValue`}
            label={t('plt1.details.documents.commercialInvoice.item.positionValue')}
            type="number"
            size="small"
            disabled={readOnly}
          />
         <Field.Text
            name={`${itemsFieldName}.${currentItemIndex}.itemValue`}
            label={t('plt1.details.documents.commercialInvoice.item.itemValue')}
            type="number"
            size="small"
            disabled={readOnly}
          />
          <Field.Text
            name={`${itemsFieldName}.${currentItemIndex}.currency`}
            label={t('plt1.details.documents.commercialInvoice.item.currency')}
            size="small"
            disabled={readOnly}
          />
        </Stack>
      </Stack>
    );
  };

  // Render the item table
  const renderItemsTable = () => (
    <TableContainer component={Paper}>
      <Table sx={{ minWidth: 650 }} aria-label="commercial invoice items table">
        <TableHead>
          <TableRow>
            <TableCell>{t('plt1.details.documents.commercialInvoice.item.name')}</TableCell>
            <TableCell>{t('plt1.details.documents.commercialInvoice.item.translatedName')}</TableCell>
            <TableCell>{t('plt1.details.documents.commercialInvoice.item.modelNumber')}</TableCell>
            <TableCell>{t('plt1.details.documents.commercialInvoice.item.hsCode')}</TableCell>
            <TableCell>{t('plt1.details.documents.commercialInvoice.item.quantity')}</TableCell>
            <TableCell>{t('plt1.details.documents.commercialInvoice.item.positionValue')}</TableCell>
            <TableCell align="right">{t('common.actions')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {fields.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} align="center">
                <Typography variant="body2" color="text.secondary">
                  {t('plt1.details.documents.commercialInvoice.item.noItems')}
                </Typography>
              </TableCell>
            </TableRow>
          ) : (
            fields.map((field, index) => {
              const item = getValues(`${itemsFieldName}.${index}`) || {};
              return (
                <TableRow key={field.id}>
                  <TableCell>{item.name || '-'}</TableCell>
                  <TableCell>{item.translatedName || '-'}</TableCell>
                  <TableCell>{item.modelNumber || '-'}</TableCell>
                  <TableCell>{item.hsCode || '-'}</TableCell>
                  <TableCell>
                    {item.quantity ?? '-'} {item.unit || ''}
                  </TableCell>
                  <TableCell>
                    {item.positionValue ?? '-'} {item.currency || ''}
                  </TableCell>
                  <TableCell align="right">
                    <Stack direction="row" spacing={1} justifyContent="flex-end">
                      <Tooltip title={t('common.edit')}>
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleEditItem(index)}
                          disabled={readOnly}
                        >
                          <Iconify icon="eva:edit-fill" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={t('common.delete')}>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleOpenDeleteConfirm(index)}
                          ref={deleteItemIndex === index ? deleteButtonRef : null}
                          disabled={readOnly}
                        >
                          <Iconify icon="eva:trash-2-outline" />
                        </IconButton>
                      </Tooltip>
                    </Stack>
                  </TableCell>
                </TableRow>
              );
            })
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const renderMainInfo = () => (
    <Card sx={{ boxShadow: "none" }}>
      <CardHeader title={t('plt1.details.documents.commercialInvoice.form.mainInfoTitle')} sx={{ mb: 2 }} />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Field.Text
          name={`${fieldPrefix}.invoiceNumber`}
          label={t('plt1.details.documents.commercialInvoice.form.invoiceNumber')}
          disabled={readOnly}
        />
        <Field.DatePicker
          name={`${fieldPrefix}.invoiceDate`}
          label={t('plt1.details.documents.commercialInvoice.form.invoiceDate')}
          disabled={readOnly}
        />
        <Stack direction="row" spacing={2}>
          <Field.Text
            name={`${fieldPrefix}.countryOfOrigin`}
            label={t('plt1.details.documents.commercialInvoice.form.countryOfOrigin')}
            disabled={readOnly}
          />
          <Field.Text
            name={`${fieldPrefix}.incoterms`}
            label={t('plt1.details.documents.commercialInvoice.form.incoterms')}
            disabled={readOnly}
          />
        </Stack>
        <Stack direction="row" spacing={2}>
          <Field.Text
            name={`${fieldPrefix}.invoiceValue`}
            label={t('plt1.details.documents.commercialInvoice.form.totalAmount')}
            type="number"
            disabled={readOnly}
          />
          <Field.Text
            name={`${fieldPrefix}.invoiceCurrency`}
            label={t('plt1.details.documents.commercialInvoice.form.currency')}
            disabled={readOnly}
          />
        </Stack>
      </Stack>
    </Card>
  );
  const renderParties = () => (
    <Card sx={{ mt: 3, boxShadow: "none" }}>
      <CardHeader title={t('plt1.details.documents.commercialInvoice.form.partiesTitle')} sx={{ mb: 2 }} />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Box
          sx={{
            rowGap: 3,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' },
          }}
        >
          <PartyFormSection
            partyType={PartyType.SHIPPER}
            labelKey="plt1.details.documents.commercialInvoice.form.shipper"
            party={shipper}
            onOpenPartyDialog={handleOpenPartyDialog}
            readOnly={readOnly}
          />
          <PartyFormSection
            partyType={PartyType.CONSIGNEE}
            labelKey="plt1.details.documents.commercialInvoice.form.consignee"
            party={consignee}
            onOpenPartyDialog={handleOpenPartyDialog}
            readOnly={readOnly}
          />
        </Box>
      </Stack>
    </Card>
  );

  const renderItems = () => (
    <Card sx={{ mt: 3, boxShadow: "none" }}>
      <CardHeader
        title={t('plt1.details.documents.commercialInvoice.form.itemsTitle')}
        action={
          <Button
            variant="contained"
            size="small"
            startIcon={<Iconify icon="eva:plus-fill" />}
            onClick={handleAddItem}
            sx={{ mb: 2 }}
            disabled={readOnly}
          >
            {t('common.addItem')}
          </Button>
        }
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Box sx={{ p: 3 }}>
        {renderItemsTable()}

        {fields.length > 0 && (
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
            <Button
              variant="soft"
              color="primary"
              startIcon={<Iconify icon="eva:plus-fill" />}
              onClick={handleAddItem}
              disabled={readOnly}
            >
              {t('common.addItem')}
            </Button>
          </Box>
        )}
      </Box>
    </Card>
  );

  const renderPartyDialog = () => (
    <PartyAddressDialog
      open={openPartyDialog}
      onClose={handleClosePartyDialog}
      onSave={handleUpdateParty}
      formPath={fieldPrefix}
      currentPartyType={currentPartyType}
      readOnly={readOnly}
      titlePrefix="plt1.details.documents.commercialInvoice.partyAddress"
    />
  );
  return (
    <>
      <Stack spacing={3}>
        {renderMainInfo()}
        {renderParties()}
        {renderItems()}
      </Stack>

      {renderPartyDialog()}

      {/* Item Dialog */}
      <Dialog
        open={openItemDialog}
        onClose={handleCloseItemDialog}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>
          {currentItemIndex === null
            ? t('plt1.details.documents.commercialInvoice.item.addNew')
            : t('plt1.details.documents.commercialInvoice.item.edit')}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 3 }}>{renderItemForm()}</Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseItemDialog} color="inherit">
            {t('common.cancel')}
          </Button>
          <Button onClick={handleSaveItem} variant="contained">
            {t('common.save')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={openDeleteConfirm}
        onClose={handleCloseDeleteConfirm}
        title={t('plt1.details.documents.commercialInvoice.item.confirmDeleteDialog.title')}
        content={t('plt1.details.documents.commercialInvoice.item.confirmDeleteDialog.content')}
        action={
          <Button variant="contained" color="error" onClick={handleDeleteItem}>
            {t('common.delete')}
          </Button>
        }
      />
    </>
  );
}