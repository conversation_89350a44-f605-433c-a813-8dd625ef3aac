import { useForm<PERSON>ontext, useFieldArray } from 'react-hook-form';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  Divider,
  Box,
} from '@mui/material';
import { useTranslate } from 'src/locales';
import { Field } from 'src/components/hook-form';
import PartyFormSection from '../../../../components/party-address/party-form-section';
import { PartyType } from 'src/types/parties';
import { usePartyAddressForm } from 'src/components/party-address/hooks/usePartyAddressForm';
import { PartyAddressData } from 'src/components/party-address/party-address-form';
import PartyAddressDialog from 'src/components/party-address/party-address-dialog';

// ----------------------------------------------------------------------

export interface PLT1CommercialInvoiceItemData {
  id?: string;
  name?: string;
  translatedName?: string;
  modelNumber?: string;
  quantity?: number;
  unit?: string;
  value?: number;
  currency?: string;
  hsCode?: string;
  commercialInvoiceId?: string;
}

export interface PLT1CommercialInvoiceData {
  id?: string;
  invoiceNumber: string;
  invoiceDate: string | null;
  incoterms: string;
  countryOfOrigin: string;
  invoiceValue: number;
  invoiceCurrency: string;
  shipper: PartyAddressData;
  consignee: PartyAddressData;
  items: PLT1CommercialInvoiceItemData[];
  t1OrderId: string;
}

// ----------------------------------------------------------------------

interface T1CommercialInvoiceFormProps {
  formPath: string;
  index?: number;
  readOnly?: boolean;
}

export default function PLT1CommercialInvoiceForm({ formPath, index, readOnly = false }: T1CommercialInvoiceFormProps) {
  const { t } = useTranslate();
  const { control, watch } = useFormContext();

  const fieldPrefix = index !== undefined ? `${formPath}.${index}` : formPath;
  const itemsFieldName = `${fieldPrefix}.items`;

  // Use the party address form hook
  const {
    openPartyDialog,
    currentPartyType,
    handleOpenPartyDialog,
    handleClosePartyDialog,
    handleUpdateParty
  } = usePartyAddressForm({ fieldPrefix });


  // Watch shipper and consignee to display their info
  const shipper = watch(`${fieldPrefix}.shipper`);
  const consignee = watch(`${fieldPrefix}.consignee`);
  const invoiceCurrency = watch(`${fieldPrefix}.invoiceCurrency`);

  // UseFieldArray hook to manage the items array
  const { fields, append } = useFieldArray({
    control,
    name: itemsFieldName,
  });

  const renderMainInfo = () => (
    <Card sx={{ boxShadow: "none" }}>
      <CardHeader title={t('plt1.details.documents.commercialInvoice.form.mainInfoTitle')} sx={{ mb: 2 }} />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Field.Text
          name={`${fieldPrefix}.invoiceNumber`}
          label={t('plt1.details.documents.commercialInvoice.form.invoiceNumber')}
          disabled={readOnly}
        />

        <Field.DatePicker
          name={`${fieldPrefix}.invoiceDate`}
          label={t('plt1.details.documents.commercialInvoice.form.invoiceDate')}
          disabled={readOnly}
        />

        <Stack direction="row" spacing={2}>
          <Field.Text
            name={`${fieldPrefix}.countryOfOrigin`}
            label={t('plt1.details.documents.commercialInvoice.form.countryOfOrigin')}
            disabled={readOnly}
          />
          <Field.Text
            name={`${fieldPrefix}.incoterms`}
            label={t('plt1.details.documents.commercialInvoice.form.incoterms')}
            disabled={readOnly}
          />
        </Stack>

        <Stack direction="row" spacing={2}>
          <Field.Text
            name={`${fieldPrefix}.invoiceValue`}
            label={t('plt1.details.documents.commercialInvoice.form.totalAmount')}
            type="number"
            disabled={readOnly}
          />
          <Field.Text
            name={`${fieldPrefix}.invoiceCurrency`}
            label={t('plt1.details.documents.commercialInvoice.form.currency')}
            disabled={readOnly}
          />
        </Stack>
      </Stack>
    </Card>
  );

  const renderParties = () => (
    <Card sx={{ mt: 3, boxShadow: "none" }}>
      <CardHeader title={t('plt1.details.documents.commercialInvoice.form.partiesTitle')} sx={{ mb: 2 }} />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        <Box
          sx={{
            rowGap: 3,
            columnGap: 2,
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(2, 1fr)' },
          }}
        >
          <PartyFormSection
            partyType={PartyType.SHIPPER}
            labelKey="plt1.details.documents.commercialInvoice.form.shipper"
            party={shipper}
            onOpenPartyDialog={handleOpenPartyDialog}
            readOnly={readOnly}
          />
          <PartyFormSection
            partyType={PartyType.CONSIGNEE}
            labelKey="plt1.details.documents.commercialInvoice.form.consignee"
            party={consignee}
            onOpenPartyDialog={handleOpenPartyDialog}
            readOnly={readOnly}
          />
        </Box>
      </Stack>
    </Card>
  );

  const renderPartyDialog = () => (
    <PartyAddressDialog
      open={openPartyDialog}
      onClose={handleClosePartyDialog}
      onSave={handleUpdateParty}
      formPath={fieldPrefix}
      currentPartyType={currentPartyType}
      readOnly={readOnly}
      titlePrefix="plt1.details.documents.commercialInvoice.partyAddress"
    />
  );

  return (
    <>
      <Stack spacing={3}>
        {renderMainInfo()}
        {renderParties()}
      </Stack>

      {renderPartyDialog()}
    </>
  );
}