'use client';

import type { GridColDef, GridRenderCellParams, GridSortModel } from '@mui/x-data-grid';

import useSWR from 'swr';
import { useBoolean } from 'minimal-shared/hooks';
import { useState } from 'react';

import Card from '@mui/material/Card';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import IconButton from '@mui/material/IconButton';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';
import { DashboardContent } from 'src/layouts/dashboard';
import { useTranslate } from 'src/locales';
import { endpoints, fetcher } from 'src/lib/axios';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';
import { Iconify } from 'src/components/iconify';
import { ODataResponse } from 'src/types/baseResponse';
import { AppDataGrid } from 'src/layouts/components/app-data-grid/app-data-grid';
import { formatters } from 'src/lib/formatters';
import { gridOperators } from 'src/layouts/components/app-data-grid/utils/operators';
import { useEnumColumn } from 'src/layouts/components/app-data-grid/components/enum-tag/EnumTag';
import { PLT1_ORDER_STATUS, PLT1_ORDER_STATUS_CONFIG, PLT1OrderStatus } from './plt1-status';
import { PLT1BeginningDialog } from './plt1-beginning-dialog';
import { PLT1DeleteConfirmationDialog } from './plt1-delete-confirmation-dialog';
import { PLT1_ORDER_TYPE, PLT1_ORDER_TYPE_CONFIG, PLT1OrderType } from './plt1-order-type';

// Define the IPLT1OrderItem interface
interface IPLT1OrderItem {
  id: string;
  number: string;
  orderingPartyEmail: string;
  orderDate: string;
  confirmationDate: string;
  createdDate: string;
  forwardedToTheCustomsOfficeDate: string;
  completionDate: string;
  status: string;
  orderType: string;
  consigneeName: string;
  transportDocumentNumber: string;
}

export function PLT1OrderListView() {
  const { t } = useTranslate();
  const router = useRouter();
  const createDialog = useBoolean();
  const deleteDialog = useBoolean();
  const [selectedOrder, setSelectedOrder] = useState<{
    id: string;
    number: string;
    orderType: string;
  } | null>(null);

  const [initialSortModel] = useState<GridSortModel>([
    {
      field: 'createdDate',
      sort: 'desc',
    },
  ]);

  const [odataParams, setODataParams] = useState('');

  const { data, isLoading, mutate } = useSWR<ODataResponse<IPLT1OrderItem>>(
    `${endpoints.plt1.list}?${odataParams}`,
    fetcher,
    {
      refreshInterval: 5000,
    }
  );

  const handleUploadSuccess = async () => {
    try {
      await mutate();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
  };

  const handleViewDetails = (id: string, orderType: string) => {
    router.push(paths.dashboard.plt1.details(id, orderType));
  };

  const handleDeleteClick = (order: { id: string; number: string; orderType: string }) => {
    setSelectedOrder(order);
    deleteDialog.onTrue();
  };

  const handleDeleteSuccess = async () => {
    try {
      await mutate();
    } catch (error) {
      console.error('Error refreshing data after delete:', error);
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'number',
      headerName: t('plt1.list.headers.number'),
      flex: 1,
      filterOperators: gridOperators.getStringOperators(),
    },
    useEnumColumn<PLT1OrderType>(PLT1_ORDER_TYPE_CONFIG, 'plt1.type', {
      field: 'orderType',
      headerTranslationKey: 'plt1.list.headers.orderType',
    }),
    {
      field: 'transportDocumentNumber',
      headerName: t('plt1.list.headers.transportDocumentNumber'),
      flex: 1,
      filterOperators: gridOperators.getStringOperators(),
    },
    {
      field: 'consigneeName',
      headerName: t('plt1.list.headers.consigneeName'),
      flex: 1,
      filterOperators: gridOperators.getStringOperators(),
    },
    {
      field: 'createdDate',
      headerName: t('plt1.list.headers.createdDate'),
      type: 'dateTime',
      flex: 1,
      valueFormatter: (value: Date) => formatters.dateTimeOffset(value),
      filterOperators: gridOperators.getDateOperators(),
    },
    {
      field: 'orderingPartyEmail',
      headerName: t('plt1.list.headers.orderingPartyEmail'),
      flex: 1,
      filterOperators: gridOperators.getStringOperators(),
    },
    {
      field: 'orderDate',
      headerName: t('plt1.list.headers.orderDate'),
      flex: 1,
      valueFormatter: (value: Date) => formatters.dateTimeOffset(value),
      filterOperators: gridOperators.getDateOperators(),
    },
    useEnumColumn<PLT1OrderStatus>(PLT1_ORDER_STATUS_CONFIG, 'plt1.status', {
      field: 'status',
      headerTranslationKey: 'plt1.list.headers.status',
    }),
    {
      field: 'confirmationDate',
      headerName: t('plt1.list.headers.confirmationDate'),
      flex: 1,
      valueFormatter: (value: Date) => formatters.dateTimeOffset(value),
    },
    {
      field: 'forwardedToTheCustomsOfficeDate',
      headerName: t('plt1.list.headers.forwardedDate'),
      flex: 1,
      valueFormatter: (value: Date) => formatters.dateTimeOffset(value),
      filterOperators: gridOperators.getDateOperators(),
    },
    {
      field: 'completionDate',
      headerName: t('plt1.list.headers.completionDate'),
      flex: 1,
      valueFormatter: (value: Date) => formatters.dateTimeOffset(value),
      filterOperators: gridOperators.getDateOperators(),
    },
    {
      field: 'actions',
      headerName: t('common.actions'),
      width: 120,
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      renderCell: (params: GridRenderCellParams<IPLT1OrderItem>) => (
        <Stack direction="row" alignItems="center" spacing={1}>
          <Tooltip title={t('common.view')}>
            <IconButton
              color="primary"
              onClick={() => handleViewDetails(params.row.id, params.row.orderType)}
              size="small"
              disabled={params.row.status === PLT1_ORDER_STATUS.Created}
            >
              <Iconify icon="eva:eye-fill" />
            </IconButton>
          </Tooltip>
          <Tooltip title={t('common.delete')}>
            <IconButton
              color="error"
              onClick={() =>
                handleDeleteClick({
                  id: params.row.id,
                  number: params.row.number,
                  orderType: params.row.orderType,
                })
              }
              size="small"
            >
              <Iconify icon="eva:trash-2-outline" />
            </IconButton>
          </Tooltip>
        </Stack>
      ),
    },
  ];

  const renderPLT1BeginningDialog = () => (
    <PLT1BeginningDialog
      open={createDialog.value}
      onClose={createDialog.onFalse}
      title={t('plt1.beginningDialog.header')}
      onSuccess={handleUploadSuccess}
    />
  );

  const renderDeleteConfirmationDialog = () => (
    <PLT1DeleteConfirmationDialog
      open={deleteDialog.value}
      onClose={deleteDialog.onFalse}
      orderId={selectedOrder?.id || ''}
      orderNumber={selectedOrder?.number || ''}
      orderType={selectedOrder?.orderType || 'Air'}
      onSuccess={handleDeleteSuccess}
    />
  );

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading={t('plt1.list.heading')}
        action={
          <Button
            variant="contained"
            onClick={createDialog.onTrue}
            startIcon={<Iconify icon="mingcute:add-line" />}
          >
            {t('plt1.list.actions.create.label')}
          </Button>
        }
        sx={{ mb: 3, mt: 3 }}
      />

      <Card sx={{ width: '100%' }}>
        <AppDataGrid
          rows={data?.value || []}
          columns={columns}
          totalCount={data?.['@odata.count'] || 0}
          loading={isLoading}
          onODataChange={setODataParams}
          disableRowSelectionOnClick
          getRowId={(row) => row.id}
          initialSortModel={initialSortModel}
          gridId="plt1-orders-list"
          enumConfigs={{
            status: {
              values: PLT1_ORDER_STATUS,
              config: PLT1_ORDER_STATUS_CONFIG,
              translationPrefix: 'plt1.status',
            },
            orderType: {
              values: PLT1_ORDER_TYPE,
              config: PLT1_ORDER_TYPE_CONFIG,
              translationPrefix: 'plt1.type',
            },
          }}
        />
      </Card>

      {renderPLT1BeginningDialog()}
      {renderDeleteConfirmationDialog()}
    </DashboardContent>
  );
}
