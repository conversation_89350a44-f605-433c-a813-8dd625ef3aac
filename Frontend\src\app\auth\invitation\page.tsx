import type { Metada<PERSON> } from 'next';
import { CONFIG } from 'src/global-config';
import InvitationView from 'src/invitation/views/invitation-view';
import { getServerTranslations } from 'src/locales/server';

export async function generateMetadata(): Promise<Metadata> {
  const { t } = await getServerTranslations();
  return {
    title: `${t('invitation.pageTitle')} - ${CONFIG.appName}`
  };
}

export default async function Page() {
  return <InvitationView />;
}