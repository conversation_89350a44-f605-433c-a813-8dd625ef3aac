import { useState, SyntheticEvent } from 'react';

import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Card from '@mui/material/Card';
import Divider from '@mui/material/Divider';
import CardHeader from '@mui/material/CardHeader';

import { useTranslate } from 'src/locales';
import PLT1AirHouseAirWaybillsTab from './tabs/air/plt1air-house-airwaybill-tab';
import PLT1AirMasterAirWaybillsTab from './tabs/air/plt1air-master-airwaybill-tab';
import PLT1CommercialInvoicesTab from './tabs/plt1-commercial-invoice-tab';
import PLT1PackingListsTab from './tabs/plt1-packing-list-tab';
import PLT1NotificationsOfArrivalTab from './tabs/plt1-notifications-arrival-tab';
import PLT1TransitTab from './tabs/plt1-transit-tab';
import PLT1MerchandisePositionsTab from './tabs/plt1-merchandise-positions-tab';

// ----------------------------------------------------------------------

enum TabNames {
    HAWB = 'hawb',
    MAWB = 'mawb',
    COMMERCIAL_INVOICES = 'commercialInvoices',
    PACKING_LISTS = 'packingLists',
    NOTIFICATIONS = 'notifications',
    TRANSIT_DOCUMENTS = 'transitDocuments',
    MERCHANDISE_POSITIONS = 'merchandisePositions'
}

// ----------------------------------------------------------------------

export interface PLTAir1DocumentsTabsProps {
    t1OrderId: string;
    order?: any;
    readOnly?: boolean;
}

export default function PLT1AirDocumentsTabs({ t1OrderId, order, readOnly = false }: PLTAir1DocumentsTabsProps) {
    const { t } = useTranslate();
    
    const [currentTab, setCurrentTab] = useState(() => {
        const savedTab = sessionStorage.getItem(`plt1-current-tab-${t1OrderId}`);
        return savedTab && Object.values(TabNames).includes(savedTab as TabNames) 
            ? (savedTab as TabNames) 
            : TabNames.HAWB;
    });

    const handleChangeTab = (event: SyntheticEvent, newValue: TabNames) => {
        setCurrentTab(newValue);
        sessionStorage.setItem(`plt1-current-tab-${t1OrderId}`, newValue);
    };

    const TABS = [
        {
            value: TabNames.HAWB,
            label: t('plt1.details.documents.tabs.houseAirWaybills'),
            component: <PLT1AirHouseAirWaybillsTab t1OrderId={t1OrderId} order={order} readOnly={readOnly} />
        },
        {
            value: TabNames.MAWB,
            label: t('plt1.details.documents.tabs.masterAirWaybills'),
            component: <PLT1AirMasterAirWaybillsTab t1OrderId={t1OrderId} order={order} readOnly={readOnly} />
        },
        {
            value: TabNames.COMMERCIAL_INVOICES,
            label: t('plt1.details.documents.tabs.commercialInvoices'),
            component: <PLT1CommercialInvoicesTab t1OrderId={t1OrderId} order={order} readOnly={readOnly} />
        },
        {
            value: TabNames.PACKING_LISTS,
            label: t('plt1.details.documents.tabs.packingLists'),
            component: <PLT1PackingListsTab t1OrderId={t1OrderId} order={order} readOnly={readOnly} />
        },
        {
            value: TabNames.NOTIFICATIONS,
            label: t('plt1.details.documents.tabs.notificationsOfArrival'),
            component: <PLT1NotificationsOfArrivalTab t1OrderId={t1OrderId} order={order} readOnly={readOnly} />
        },
        {
            value: TabNames.TRANSIT_DOCUMENTS,
            label: t('plt1.details.documents.tabs.transitDocuments'),
            component: <PLT1TransitTab t1OrderId={t1OrderId} order={order} readOnly={readOnly} />
        },
        {
            value: TabNames.MERCHANDISE_POSITIONS,
            label: t('plt1.details.documents.tabs.merchandisePositions'),
            component: <PLT1MerchandisePositionsTab t1OrderId={t1OrderId} order={order} readOnly={readOnly} />
        }
    ];

    return (
        <Card>
            <CardHeader
                title={t('plt1.details.documents.heading')}
                sx={{ mb: 2 }}
            />
            <Divider sx={{ borderStyle: 'dashed' }} />
            <Tabs
                value={currentTab}
                onChange={handleChangeTab}
                sx={{
                    px: 2.5,
                    boxShadow: (theme) => `inset 0 -2px 0 0 ${theme.palette.divider}`,
                }}
            >
                {TABS.map((tab) => (
                    <Tab
                        key={tab.value}
                        value={tab.value}
                        label={tab.label}
                    />
                ))}
            </Tabs>

            {TABS.map(
                (tab) =>
                    tab.value === currentTab && (
                        <Box key={tab.value} sx={{ p: 3 }}>
                            {tab.component}
                        </Box>
                    )
            )}
        </Card>
    );
}
