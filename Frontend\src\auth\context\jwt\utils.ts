import { JWT_STORAGE_KEY, REFRESH_TOKEN_STORAGE_KEY } from './constant';
import { axiosInstance } from 'src/lib/axios';

// ----------------------------------------------------------------------

export function jwtDecode(token: string) {
  try {
    if (!token) return null;

    const parts = token.split('.');
    if (parts.length < 2) {
      throw new Error('Invalid token!');
    }

    const base64Url = parts[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const decoded = JSON.parse(atob(base64));

    return decoded;
  } catch (error) {
    console.error('Error decoding token:', error);
    throw error;
  }
}

// ----------------------------------------------------------------------
export function requiresOnboarding(accessToken: string) {
  const decodedToken =  jwtDecode(accessToken);
  return decodedToken.tenantStatus == "Onboarding";
}

// ----------------------------------------------------------------------

export function isValidToken(accessToken: string) {
  if (!accessToken) {
    return false;
  }

  try {
    const decoded = jwtDecode(accessToken);

    if (!decoded || !('exp' in decoded)) {
      return false;
    }

    const currentTime = Date.now() / 1000;

    return decoded.exp > currentTime;
  } catch (error) {
    console.error('Error during token validation:', error);
    return false;
  }
}

// ----------------------------------------------------------------------

export async function setSession(accessToken: string, refreshToken: string) {
  try {
    if (accessToken) {
      sessionStorage.setItem(JWT_STORAGE_KEY, accessToken);
      sessionStorage.setItem(REFRESH_TOKEN_STORAGE_KEY, refreshToken);

      axiosInstance.defaults.headers.common.Authorization = `Bearer ${accessToken}`;

    } else {
      endSession()
    }
  } catch (error) {
    console.error('Error during set session:', error);
    throw error;
  }
}

export async function endSession() {
  sessionStorage.removeItem(JWT_STORAGE_KEY);
  sessionStorage.removeItem(REFRESH_TOKEN_STORAGE_KEY);
  delete axiosInstance.defaults.headers.common.Authorization;
}

export function isSessionActive() : boolean{
  return sessionStorage.getItem(JWT_STORAGE_KEY) ? true : false;
}