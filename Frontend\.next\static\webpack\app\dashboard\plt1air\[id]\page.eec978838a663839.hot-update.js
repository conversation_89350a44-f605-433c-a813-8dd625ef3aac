"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx":
/*!*******************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1PackingListForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Dialog,DialogActions,DialogContent,DialogTitle,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/custom-dialog */ \"(app-pages-browser)/./src/components/custom-dialog/index.ts\");\n/* harmony import */ var src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/components/party-address/hooks/usePartyAddressForm */ \"(app-pages-browser)/./src/components/party-address/hooks/usePartyAddressForm.tsx\");\n/* harmony import */ var src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/components/party-address/party-address-dialog */ \"(app-pages-browser)/./src/components/party-address/party-address-dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst DEFAULT_PACKED_ITEM = {\n    id: '',\n    name: '',\n    hsCode: '',\n    modelNumber: '',\n    quantity: 0,\n    itemNetWeight: 0,\n    itemNetWeightUnit: 'KGM',\n    itemGrossWeight: 0,\n    hsCodeHints: '',\n    packingListPositionId: ''\n};\nconst DEFAULT_POSITION = {\n    id: '',\n    positionNumber: 1,\n    commercialInvoiceNumber: '',\n    packageNetWeight: 0,\n    packageNetWeightUnit: 'KGM',\n    packageGrossWeight: 0,\n    packageGrossWeightUnit: 'KGM',\n    packagesNetWeight: 0,\n    packagesNetWeightUnit: 'KGM',\n    packagesGrossWeight: 0,\n    packagesGrossWeightUnit: 'KGM',\n    packageUnit: 'CTNS',\n    packageSize: '',\n    packageVolume: 0,\n    packageVolumeUnit: 'CBM',\n    numberOfPackages: 0,\n    packedItems: [],\n    packingListId: ''\n};\nconst DEFAULT_TOTAL = {\n    id: '',\n    shipmentOrPackingId: '',\n    totalQuantity: 0,\n    totalPackagesUnit: 'CTNS',\n    totalNumberOfPackages: 0,\n    totalNumberOfPallets: 0,\n    totalNetWeight: 0,\n    totalNetWeightUnit: 'KGM',\n    totalGrossWeight: 0,\n    totalGrossWeightUnit: 'KGM',\n    totalVolume: 0,\n    totalVolumeMeasurementUnit: 'CBM',\n    packingListId: ''\n};\nfunction PLT1PackingListForm(param) {\n    let { formPath, index, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    const { control, getValues, watch, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext)();\n    const fieldPrefix = index !== undefined ? \"\".concat(formPath, \".\").concat(index) : formPath;\n    const positionsFieldName = \"\".concat(fieldPrefix, \".packingListPositions\");\n    const totalFieldName = \"\".concat(fieldPrefix, \".listTotal\");\n    // Use the party address form hook\n    const { openPartyDialog, currentPartyType, handleClosePartyDialog, handleUpdateParty } = (0,src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm)({\n        fieldPrefix\n    });\n    // State for item dialog\n    const [openItemDialog, setOpenItemDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteItemIndex, setDeleteItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize listTotal if it doesn't exist\n    const listTotal = watch(totalFieldName);\n    if (!listTotal) {\n        setValue(totalFieldName, DEFAULT_TOTAL);\n    }\n    // UseFieldArray hook to manage the positions array\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray)({\n        control,\n        name: positionsFieldName\n    });\n    // Handle opening the position dialog for adding a new position\n    const handleAddPosition = ()=>{\n        setCurrentItemIndex(null);\n        // Add a new position with default values\n        const newPosition = {\n            ...DEFAULT_POSITION\n        };\n        append(newPosition);\n        setCurrentItemIndex(fields.length); // Set to the new index\n        setOpenItemDialog(true);\n    };\n    // Handle opening the position dialog for editing an existing position\n    const handleEditPosition = (index)=>{\n        setCurrentItemIndex(index);\n        setOpenItemDialog(true);\n    };\n    // Handle closing the position dialog\n    const handleClosePositionDialog = ()=>{\n        setOpenItemDialog(false);\n        // If we were adding a new position and user cancels, remove the empty position\n        if (currentItemIndex !== null && currentItemIndex === fields.length - 1) {\n            const position = getValues(\"\".concat(positionsFieldName, \".\").concat(currentItemIndex));\n            // Check if it's an empty position (all fields are default values)\n            const isEmpty = !position.commercialInvoiceNumber && position.numberOfPackages === 0 && position.packageNetWeight === 0 && position.packageGrossWeight === 0 && position.packageVolume === 0 && (!position.packedItems || position.packedItems.length === 0);\n            if (isEmpty) {\n                remove(currentItemIndex);\n            }\n        }\n        setCurrentItemIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeleteItemIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deleteItemIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting an item\n    const handleDeleteItem = ()=>{\n        if (deleteItemIndex !== null) {\n            remove(deleteItemIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving an item (just close the dialog since form is already updated)\n    const handleSaveItem = ()=>{\n        handleCloseItemDialog();\n    };\n    // Render the item form in the dialog - using React Hook Form fields\n    const renderItemForm = ()=>{\n        if (currentItemIndex === null) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".name\"),\n                    label: t('plt1.details.documents.packingList.item.name'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".modelNumber\"),\n                    label: t('plt1.details.documents.packingList.item.modelNumber'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".purchaseOrderNumber\"),\n                            label: t('plt1.details.documents.packingList.item.purchaseOrderNumber'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".commercialInvoiceNumber\"),\n                            label: t('plt1.details.documents.packingList.item.commercialInvoiceNumber'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageNetWeight\"),\n                            label: t('plt1.details.documents.packingList.item.packageNetWeight'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageNetWeightUnit\"),\n                            label: t('plt1.details.documents.packingList.item.packageNetWeightUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageGrossWeight\"),\n                            label: t('plt1.details.documents.packingList.item.packageGrossWeight'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageGrossWeightUnit\"),\n                            label: t('plt1.details.documents.packingList.item.packageGrossWeightUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".quantity\"),\n                            label: t('plt1.details.documents.packingList.item.quantity'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".volume\"),\n                            label: t('plt1.details.documents.packingList.item.volume'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 243,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the item table\n    const renderItemsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            component: _barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"packing list items table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.name')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.modelNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.quantity')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.packageNetWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.packageGrossWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                colSpan: 6,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.packingList.item.noItems')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this) : fields.map((field, index)=>{\n                            const item = getValues(\"\".concat(itemsFieldName, \".\").concat(index)) || {};\n                            var _item_quantity, _item_packageNetWeight, _item_packageGrossWeight;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: item.name || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: item.modelNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: (_item_quantity = item.quantity) !== null && _item_quantity !== void 0 ? _item_quantity : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: [\n                                            (_item_packageNetWeight = item.packageNetWeight) !== null && _item_packageNetWeight !== void 0 ? _item_packageNetWeight : '-',\n                                            \" \",\n                                            item.packageNetWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: [\n                                            (_item_packageGrossWeight = item.packageGrossWeight) !== null && _item_packageGrossWeight !== void 0 ? _item_packageGrossWeight : '-',\n                                            \" \",\n                                            item.packageGrossWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditItem(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deleteItemIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 318,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 317,\n            columnNumber: 5\n        }, this);\n    const renderPartyDialog = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            open: openPartyDialog,\n            onClose: handleClosePartyDialog,\n            onSave: handleUpdateParty,\n            formPath: fieldPrefix,\n            currentPartyType: currentPartyType,\n            readOnly: readOnly,\n            titlePrefix: \"plt1.details.documents.packingList.partyAddress\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 391,\n            columnNumber: 5\n        }, this);\n    // Handle change for totals fields\n    const handleTotalChange = (field, value)=>{\n        setValue(\"\".concat(totalFieldName, \".\").concat(field), value);\n    };\n    // Render the totals section\n    const renderTotalsSection = ()=>{\n        const total = watch(totalFieldName) || DEFAULT_TOTAL;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n            sx: {\n                boxShadow: 'none',\n                mt: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    title: t('plt1.details.documents.packingList.form.totalsTitle')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    sx: {\n                        borderStyle: 'dashed'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        spacing: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalQuantity\"),\n                                        label: t('plt1.details.documents.packingList.total.totalQuantity'),\n                                        type: \"number\",\n                                        value: total.totalQuantity,\n                                        onChange: (e)=>handleTotalChange('totalQuantity', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPallets\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPallets'),\n                                        type: \"number\",\n                                        value: total.totalNumberOfPallets,\n                                        onChange: (e)=>handleTotalChange('totalNumberOfPallets', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPackages\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPackages'),\n                                        type: \"number\",\n                                        value: total.totalNumberOfPackages,\n                                        onChange: (e)=>handleTotalChange('totalNumberOfPackages', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalPackagesUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalPackagesUnit'),\n                                        value: total.totalPackagesUnit,\n                                        onChange: (e)=>handleTotalChange('totalPackagesUnit', e.target.value),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeight'),\n                                        type: \"number\",\n                                        value: total.totalNetWeight,\n                                        onChange: (e)=>handleTotalChange('totalNetWeight', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeightUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeightUnit'),\n                                        value: total.totalNetWeightUnit,\n                                        onChange: (e)=>handleTotalChange('totalNetWeightUnit', e.target.value || 'kg'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeight'),\n                                        type: \"number\",\n                                        value: total.totalGrossWeight,\n                                        onChange: (e)=>handleTotalChange('totalGrossWeight', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeightUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeightUnit'),\n                                        value: total.totalGrossWeightUnit,\n                                        onChange: (e)=>handleTotalChange('totalGrossWeightUnit', e.target.value || 'kg'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolume\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolume'),\n                                        type: \"number\",\n                                        value: total.totalVolume,\n                                        onChange: (e)=>handleTotalChange('totalVolume', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolumeMeasurementUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolumeMeasurementUnit'),\n                                        value: total.totalVolumeMeasurementUnit,\n                                        onChange: (e)=>handleTotalChange('totalVolumeMeasurementUnit', e.target.value),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 410,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        spacing: 3,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        title: t('plt1.details.documents.packingList.form.itemsTitle'),\n                        action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            variant: \"contained\",\n                            size: \"small\",\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                icon: \"eva:plus-fill\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: handleAddItem,\n                            sx: {\n                                mb: 2\n                            },\n                            disabled: readOnly,\n                            children: t('common.addItem')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 13\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        sx: {\n                            borderStyle: 'dashed'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: [\n                            renderItemsTable(),\n                            fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                sx: {\n                                    mt: 2,\n                                    display: 'flex',\n                                    justifyContent: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    variant: \"soft\",\n                                    color: \"primary\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                        icon: \"eva:plus-fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 28\n                                    }, void 0),\n                                    onClick: handleAddItem,\n                                    disabled: readOnly,\n                                    children: t('common.addItem')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 520,\n                columnNumber: 7\n            }, this),\n            renderTotalsSection(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                open: openItemDialog,\n                onClose: handleCloseItemDialog,\n                fullWidth: true,\n                maxWidth: \"md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        children: currentItemIndex === null ? t('plt1.details.documents.packingList.item.addNew') : t('plt1.details.documents.packingList.item.edit')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            sx: {\n                                pt: 3\n                            },\n                            children: renderItemForm()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                onClick: handleCloseItemDialog,\n                                color: \"inherit\",\n                                children: t('common.cancel')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 570,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                onClick: handleSaveItem,\n                                variant: \"contained\",\n                                children: t('common.save')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 569,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 560,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_custom_dialog__WEBPACK_IMPORTED_MODULE_5__.ConfirmDialog, {\n                open: openDeleteConfirm,\n                onClose: handleCloseDeleteConfirm,\n                title: t('plt1.details.documents.packingList.item.confirmDeleteDialog.title'),\n                content: t('plt1.details.documents.packingList.item.confirmDeleteDialog.content'),\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Dialog_DialogActions_DialogContent_DialogTitle_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                    variant: \"contained\",\n                    color: \"error\",\n                    onClick: handleDeleteItem,\n                    children: t('common.delete')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 586,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 580,\n                columnNumber: 7\n            }, this),\n            renderPartyDialog()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n        lineNumber: 518,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1PackingListForm, \"rQqfRn3lyqnpgHcb7yCWKoSvnLA=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFormContext,\n        src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_6__.usePartyAddressForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_8__.useFieldArray\n    ];\n});\n_c = PLT1PackingListForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1PackingListForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx\n"));

/***/ })

});