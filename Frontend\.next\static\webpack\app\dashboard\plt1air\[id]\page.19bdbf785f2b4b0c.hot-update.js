"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx":
/*!*******************************************************************!*\
  !*** ./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1PackingListForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardHeader,Divider,IconButton,Paper,Stack,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/components/hook-form */ \"(app-pages-browser)/./src/components/hook-form/index.ts\");\n/* harmony import */ var src_components_iconify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/components/iconify */ \"(app-pages-browser)/./src/components/iconify/index.ts\");\n/* harmony import */ var src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/components/party-address/hooks/usePartyAddressForm */ \"(app-pages-browser)/./src/components/party-address/hooks/usePartyAddressForm.tsx\");\n/* harmony import */ var src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/components/party-address/party-address-dialog */ \"(app-pages-browser)/./src/components/party-address/party-address-dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst DEFAULT_ITEM = {\n    id: '',\n    name: '',\n    modelNumber: '',\n    purchaseOrderNumber: '',\n    commercialInvoiceNumber: '',\n    packageNetWeight: 0,\n    packageNetWeightUnit: 'kg',\n    packageGrossWeight: 0,\n    packageGrossWeightUnit: 'kg',\n    quantity: 0,\n    volume: 0,\n    packingListId: ''\n};\nconst DEFAULT_TOTAL = {\n    id: '',\n    totalQuantity: 0,\n    totalPackagesUnit: '',\n    totalNumberOfPackages: 0,\n    totalNumberOfPallets: 0,\n    totalNetWeight: 0,\n    totalNetWeightUnit: 'kg',\n    totalGrossWeight: 0,\n    totalGrossWeightUnit: 'kg',\n    totalVolume: 0,\n    totalVolumeMeasurementUnit: '',\n    packingListId: ''\n};\nfunction PLT1PackingListForm(param) {\n    let { formPath, index, readOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    const { control, getValues, watch, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useFormContext)();\n    const fieldPrefix = index !== undefined ? \"\".concat(formPath, \".\").concat(index) : formPath;\n    const itemsFieldName = \"\".concat(fieldPrefix, \".listSummary\");\n    const totalFieldName = \"\".concat(fieldPrefix, \".listTotal\");\n    // Use the party address form hook\n    const { openPartyDialog, currentPartyType, handleClosePartyDialog, handleUpdateParty } = (0,src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_5__.usePartyAddressForm)({\n        fieldPrefix\n    });\n    // State for item dialog\n    const [openItemDialog, setOpenItemDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteItemIndex, setDeleteItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openDeleteConfirm, setOpenDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const deleteButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize listTotal if it doesn't exist\n    const listTotal = watch(totalFieldName);\n    if (!listTotal) {\n        setValue(totalFieldName, DEFAULT_TOTAL);\n    }\n    // UseFieldArray hook to manage the items array\n    const { fields, append, remove, update } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useFieldArray)({\n        control,\n        name: itemsFieldName\n    });\n    // Handle opening the item dialog for adding a new item\n    const handleAddItem = ()=>{\n        setCurrentItemIndex(null);\n        // Add a new item with default values\n        const newItem = {\n            ...DEFAULT_ITEM\n        };\n        append(newItem);\n        setCurrentItemIndex(fields.length); // Set to the new index\n        setOpenItemDialog(true);\n    };\n    // Handle opening the item dialog for editing an existing item\n    const handleEditItem = (index)=>{\n        setCurrentItemIndex(index);\n        setOpenItemDialog(true);\n    };\n    // Handle closing the item dialog\n    const handleCloseItemDialog = ()=>{\n        setOpenItemDialog(false);\n        // If we were adding a new item and user cancels, remove the empty item\n        if (currentItemIndex !== null && currentItemIndex === fields.length - 1) {\n            const item = getValues(\"\".concat(itemsFieldName, \".\").concat(currentItemIndex));\n            // Check if it's an empty item (all fields are default values)\n            const isEmpty = !item.name && !item.modelNumber && !item.purchaseOrderNumber && !item.commercialInvoiceNumber && item.quantity === 0 && item.packageNetWeight === 0 && item.packageGrossWeight === 0 && item.volume === 0;\n            if (isEmpty) {\n                remove(currentItemIndex);\n            }\n        }\n        setCurrentItemIndex(null);\n    };\n    // Handle opening the delete confirmation dialog\n    const handleOpenDeleteConfirm = (index)=>{\n        setDeleteItemIndex(index);\n        setOpenDeleteConfirm(true);\n    };\n    // Handle closing the delete confirmation dialog\n    const handleCloseDeleteConfirm = ()=>{\n        setOpenDeleteConfirm(false);\n        if (deleteItemIndex !== null) {\n            var _deleteButtonRef_current;\n            (_deleteButtonRef_current = deleteButtonRef.current) === null || _deleteButtonRef_current === void 0 ? void 0 : _deleteButtonRef_current.focus();\n        }\n    };\n    // Handle deleting an item\n    const handleDeleteItem = ()=>{\n        if (deleteItemIndex !== null) {\n            remove(deleteItemIndex);\n        }\n        handleCloseDeleteConfirm();\n    };\n    // Handle saving an item (just close the dialog since form is already updated)\n    const handleSaveItem = ()=>{\n        handleCloseItemDialog();\n    };\n    // Render the item form in the dialog - using React Hook Form fields\n    const renderItemForm = ()=>{\n        if (currentItemIndex === null) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            spacing: 3,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".name\"),\n                    label: t('plt1.details.documents.packingList.item.name'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                    name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".modelNumber\"),\n                    label: t('plt1.details.documents.packingList.item.modelNumber'),\n                    disabled: readOnly\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".purchaseOrderNumber\"),\n                            label: t('plt1.details.documents.packingList.item.purchaseOrderNumber'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".commercialInvoiceNumber\"),\n                            label: t('plt1.details.documents.packingList.item.commercialInvoiceNumber'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageNetWeight\"),\n                            label: t('plt1.details.documents.packingList.item.packageNetWeight'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageNetWeightUnit\"),\n                            label: t('plt1.details.documents.packingList.item.packageNetWeightUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageGrossWeight\"),\n                            label: t('plt1.details.documents.packingList.item.packageGrossWeight'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".packageGrossWeightUnit\"),\n                            label: t('plt1.details.documents.packingList.item.packageGrossWeightUnit'),\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    direction: \"row\",\n                    spacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".quantity\"),\n                            label: t('plt1.details.documents.packingList.item.quantity'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                            name: \"\".concat(itemsFieldName, \".\").concat(currentItemIndex, \".volume\"),\n                            label: t('plt1.details.documents.packingList.item.volume'),\n                            type: \"number\",\n                            disabled: readOnly\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, this);\n    };\n    // Render the item table\n    const renderItemsTable = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            component: _barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                sx: {\n                    minWidth: 650\n                },\n                \"aria-label\": \"packing list items table\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.name')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.modelNumber')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.quantity')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.packageNetWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: t('plt1.details.documents.packingList.item.packageGrossWeight')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    align: \"right\",\n                                    children: t('common.actions')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        children: fields.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                colSpan: 6,\n                                align: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    children: t('plt1.details.documents.packingList.item.noItems')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 13\n                        }, this) : fields.map((field, index)=>{\n                            const item = getValues(\"\".concat(itemsFieldName, \".\").concat(index)) || {};\n                            var _item_quantity, _item_packageNetWeight, _item_packageGrossWeight;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: item.name || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: item.modelNumber || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: (_item_quantity = item.quantity) !== null && _item_quantity !== void 0 ? _item_quantity : '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_item_packageNetWeight = item.packageNetWeight) !== null && _item_packageNetWeight !== void 0 ? _item_packageNetWeight : '-',\n                                            \" \",\n                                            item.packageNetWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: [\n                                            (_item_packageGrossWeight = item.packageGrossWeight) !== null && _item_packageGrossWeight !== void 0 ? _item_packageGrossWeight : '-',\n                                            \" \",\n                                            item.packageGrossWeightUnit || ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        align: \"right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            justifyContent: \"flex-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    title: t('common.edit'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"primary\",\n                                                        onClick: ()=>handleEditItem(index),\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:edit-fill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    title: t('common.delete'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        size: \"small\",\n                                                        color: \"error\",\n                                                        onClick: ()=>handleOpenDeleteConfirm(index),\n                                                        ref: deleteItemIndex === index ? deleteButtonRef : null,\n                                                        disabled: readOnly,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                                                            icon: \"eva:trash-2-outline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, field.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 281,\n            columnNumber: 5\n        }, this);\n    const renderPartyDialog = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_party_address_party_address_dialog__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            open: openPartyDialog,\n            onClose: handleClosePartyDialog,\n            onSave: handleUpdateParty,\n            formPath: fieldPrefix,\n            currentPartyType: currentPartyType,\n            readOnly: readOnly,\n            titlePrefix: \"plt1.details.documents.packingList.partyAddress\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 355,\n            columnNumber: 5\n        }, this);\n    // Handle change for totals fields\n    const handleTotalChange = (field, value)=>{\n        setValue(\"\".concat(totalFieldName, \".\").concat(field), value);\n    };\n    // Render the totals section\n    const renderTotalsSection = ()=>{\n        const total = watch(totalFieldName) || DEFAULT_TOTAL;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            sx: {\n                boxShadow: 'none',\n                mt: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    title: t('plt1.details.documents.packingList.form.totalsTitle')\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    sx: {\n                        borderStyle: 'dashed'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        spacing: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalQuantity\"),\n                                        label: t('plt1.details.documents.packingList.total.totalQuantity'),\n                                        type: \"number\",\n                                        value: total.totalQuantity,\n                                        onChange: (e)=>handleTotalChange('totalQuantity', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPallets\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPallets'),\n                                        type: \"number\",\n                                        value: total.totalNumberOfPallets,\n                                        onChange: (e)=>handleTotalChange('totalNumberOfPallets', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNumberOfPackages\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNumberOfPackages'),\n                                        type: \"number\",\n                                        value: total.totalNumberOfPackages,\n                                        onChange: (e)=>handleTotalChange('totalNumberOfPackages', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalPackagesUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalPackagesUnit'),\n                                        value: total.totalPackagesUnit,\n                                        onChange: (e)=>handleTotalChange('totalPackagesUnit', e.target.value),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeight'),\n                                        type: \"number\",\n                                        value: total.totalNetWeight,\n                                        onChange: (e)=>handleTotalChange('totalNetWeight', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalNetWeightUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalNetWeightUnit'),\n                                        value: total.totalNetWeightUnit,\n                                        onChange: (e)=>handleTotalChange('totalNetWeightUnit', e.target.value || 'kg'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeight\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeight'),\n                                        type: \"number\",\n                                        value: total.totalGrossWeight,\n                                        onChange: (e)=>handleTotalChange('totalGrossWeight', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalGrossWeightUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalGrossWeightUnit'),\n                                        value: total.totalGrossWeightUnit,\n                                        onChange: (e)=>handleTotalChange('totalGrossWeightUnit', e.target.value || 'kg'),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                direction: \"row\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolume\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolume'),\n                                        type: \"number\",\n                                        value: total.totalVolume,\n                                        onChange: (e)=>handleTotalChange('totalVolume', parseFloat(e.target.value) || 0),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_hook_form__WEBPACK_IMPORTED_MODULE_3__.Field.Text, {\n                                        name: \"\".concat(totalFieldName, \".totalVolumeMeasurementUnit\"),\n                                        label: t('plt1.details.documents.packingList.total.totalVolumeMeasurementUnit'),\n                                        value: total.totalVolumeMeasurementUnit,\n                                        onChange: (e)=>handleTotalChange('totalVolumeMeasurementUnit', e.target.value),\n                                        disabled: readOnly\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n            lineNumber: 376,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        spacing: 3,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                sx: {\n                    boxShadow: 'none'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    title: t('plt1.details.documents.packingList.form.itemsTitle'),\n                    action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardHeader_Divider_IconButton_Paper_Stack_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        variant: \"contained\",\n                        size: \"small\",\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(src_components_iconify__WEBPACK_IMPORTED_MODULE_4__.Iconify, {\n                            icon: \"eva:plus-fill\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 26\n                        }, void 0),\n                        onClick: handleAddItem,\n                        sx: {\n                            mb: 2\n                        },\n                        disabled: readOnly,\n                        children: t('common.addItem')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                    lineNumber: 491,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n                lineNumber: 490,\n                columnNumber: 7\n            }, this),\n            renderTotalsSection(),\n            renderPartyDialog()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\forms\\\\plt1-packing-list-form.tsx\",\n        lineNumber: 488,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1PackingListForm, \"eC5q2XofSeE2jFgvJlQweZHYME0=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useFormContext,\n        src_components_party_address_hooks_usePartyAddressForm__WEBPACK_IMPORTED_MODULE_5__.usePartyAddressForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useFieldArray\n    ];\n});\n_c = PLT1PackingListForm;\nvar _c;\n$RefreshReg$(_c, \"PLT1PackingListForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/forms/plt1-packing-list-form.tsx\n"));

/***/ })

});