import { Stack } from '@mui/material';
import { useTranslate } from 'src/locales';
import { Field } from 'src/components/hook-form';

// ----------------------------------------------------------------------

export interface PLT1AirMasterAirWaybillData {
  id?: string;
  mawbNumber: string;
  grossWeight: number;
  grossWeightUnit: string;
  numberOfPieces: number;
  t1OrderId: string;
}

// ----------------------------------------------------------------------

interface PLT1AirMasterAirWaybillFormProps {
  formPath: string;
  index?: number;
  readOnly?: boolean;
}

export default function PLT1AirMasterAirWaybillForm({ formPath, index, readOnly = false }: PLT1AirMasterAirWaybillFormProps) {
  const { t } = useTranslate();
  const fieldPrefix = index !== undefined ? `${formPath}.${index}` : formPath;

  return (
    <Stack spacing={3}>
      <Field.Text
        name={`${fieldPrefix}.mawbNumber`}
        label={t('plt1.details.documents.masterAirWaybill.form.mawbNumber')}
        disabled={readOnly}
      />

      <Stack direction="row" spacing={2}>
        <Field.Text
          name={`${fieldPrefix}.grossWeight`}
          label={t('plt1.details.documents.masterAirWaybill.form.grossWeight')}
          type="number"
          disabled={readOnly}
        />
        <Field.Text
          name={`${fieldPrefix}.grossWeightUnit`}
          label={t('plt1.details.documents.masterAirWaybill.form.grossWeightUnit')}
          disabled={readOnly}
        />
      </Stack>
      <Field.Text
        name={`${fieldPrefix}.numberOfPieces`}
        label={t('plt1.details.documents.masterAirWaybill.form.numberOfPieces')}
        type="number"
        disabled={readOnly}
      />
    </Stack>
  );
}
