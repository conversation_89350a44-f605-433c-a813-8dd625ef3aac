'use client';

import { useState, useEffect, useRef, KeyboardEvent, forwardRef, useImperativeHandle } from 'react';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';
import { Scrollbar } from 'src/components/scrollbar';
import { Iconify } from 'src/components/iconify';
import { useTranslate } from 'src/locales';
import { PLT1OrderFile } from './plt1-file-management';
import { fileCache } from './plt1-file-cache';
import { Tooltip } from '@mui/material';

interface PLT1FileListSidebarProps {
  files: PLT1OrderFile[];
  selectedFile: PLT1OrderFile | null;
  onSelectFile: (file: PLT1OrderFile) => void;
  autoFocus?: boolean;
}

export interface PLT1FileListSidebarRef {
  focus: () => void;
}

export const PLT1FileListSidebar = forwardRef<PLT1FileListSidebarRef, PLT1FileListSidebarProps>(
  ({ files, selectedFile, onSelectFile, autoFocus = false }, ref) => {
    const { t } = useTranslate();
    const [focusedIndex, setFocusedIndex] = useState<number>(-1);
    const [fileStatuses, setFileStatuses] = useState<
      Record<string, 'not-cached' | 'prefetching' | 'cached'>
    >({});
    const containerRef = useRef<HTMLDivElement | null>(null);
    const fileListRef = useRef<HTMLDivElement | null>(null);

    // Check file cache status periodically
    useEffect(() => {
      if (files.length === 0) return;

      // Initial check
      const checkFileStatuses = () => {
        const newStatuses: Record<string, 'not-cached' | 'prefetching' | 'cached'> = {};

        files.forEach((file) => {
          newStatuses[file.id] = fileCache.getFileStatus(file.id);
        });

        setFileStatuses(newStatuses);
      };

      // Check immediately
      checkFileStatuses();

      // Then check periodically
      const intervalId = setInterval(checkFileStatuses, 1000);

      return () => {
        clearInterval(intervalId);
      };
    }, [files]);

    // Expose focus method to parent component
    useImperativeHandle(ref, () => ({
      focus: () => {
        if (fileListRef.current) {
          fileListRef.current.focus();
        }
      },
    }));

    // Set initial focus to the selected file or the first file
    useEffect(() => {
      if (files.length > 0) {
        const selectedIndex = selectedFile
          ? files.findIndex((file) => file.id === selectedFile.id)
          : 0;

        if (selectedIndex >= 0) {
          setFocusedIndex(selectedIndex);
        }
      }
    }, [files, selectedFile]);

    // Auto-focus the file list when the component mounts or when autoFocus prop changes
    useEffect(() => {
      if (autoFocus && fileListRef.current) {
        // Use a small timeout to ensure the DOM is fully rendered
        const timeoutId = setTimeout(() => {
          fileListRef.current?.focus();
        }, 100);

        return () => clearTimeout(timeoutId);
      }
    }, [autoFocus]);

    // Handle keyboard navigation for the entire file list
    const handleKeyDown = (event: KeyboardEvent<HTMLDivElement>) => {
      if (files.length === 0) return;

      const currentIndex =
        focusedIndex >= 0
          ? focusedIndex
          : selectedFile
            ? files.findIndex((file) => file.id === selectedFile.id)
            : 0;

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          if (currentIndex < files.length - 1) {
            const newIndex = currentIndex + 1;
            setFocusedIndex(newIndex);
            // Automatically select the file when navigating with arrow keys
            onSelectFile(files[newIndex]);
          }
          break;
        case 'ArrowUp':
          event.preventDefault();
          if (currentIndex > 0) {
            const newIndex = currentIndex - 1;
            setFocusedIndex(newIndex);
            // Automatically select the file when navigating with arrow keys
            onSelectFile(files[newIndex]);
          }
          break;
        case 'Home':
          event.preventDefault();
          if (files.length > 0 && currentIndex !== 0) {
            setFocusedIndex(0);
            onSelectFile(files[0]);
          }
          break;
        case 'End':
          event.preventDefault();
          if (files.length > 0 && currentIndex !== files.length - 1) {
            const lastIndex = files.length - 1;
            setFocusedIndex(lastIndex);
            onSelectFile(files[lastIndex]);
          }
          break;
        default:
          break;
      }
    };

    // Get file icon based on file type
    const getFileIcon = (fileType: string) => {
      if (fileType.includes('image')) {
        return 'eva:image-fill';
      } else if (fileType.includes('pdf')) {
        return 'eva:file-text-fill';
      } else {
        return 'eva:file-fill';
      }
    };

    return (
      <Box
        ref={containerRef}
        sx={{
          width: { xs: '100%', md: '280px' },
          height: { xs: '200px', md: '100%' },
          borderRight: { md: '1px solid' },
          borderBottom: { xs: '1px solid', md: 'none' },
          borderColor: 'divider',
        }}
      >
        <Scrollbar>
          <Box sx={{ p: 2 }}>
            <Box sx={{ mb: 2 }}>
              <Tooltip title={t('plt1.details.filePreview.arrowNavHint', { defaultValue: 'Use ↑↓ arrows, Home, End to navigate' })}>
                <Typography variant="subtitle2">
                  {t('plt1.details.filePreview.fileList')} ({files.length})
                </Typography>
              </Tooltip>
            </Box>

            <Stack
              spacing={1}
              ref={fileListRef}
              tabIndex={0}
              role="listbox"
              aria-label={t('plt1.details.filePreview.fileList')}
              onKeyDown={handleKeyDown}
            >
              {files.map((file, index) => (
                <Box
                  key={file.id}
                  role="option"
                  aria-selected={selectedFile?.id === file.id}
                  sx={{
                    p: 1.5,
                    borderRadius: 1,
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    bgcolor:
                      selectedFile?.id === file.id
                        ? 'action.selected'
                        : focusedIndex === index
                          ? 'action.hover'
                          : 'transparent',
                    '&:hover': { bgcolor: 'action.hover' },
                    outline:
                      focusedIndex === index && selectedFile?.id !== file.id
                        ? '1px dashed'
                        : 'none',
                    outlineColor: 'primary.main',
                  }}
                  onClick={() => {
                    setFocusedIndex(index);
                    onSelectFile(file);
                    // Focus the container to enable keyboard navigation
                    fileListRef.current?.focus();
                  }}
                >
                  {fileStatuses[file.id] === 'prefetching' ? (
                    <CircularProgress size={20} sx={{ mr: 1 }} />
                  ) : (
                    <Iconify
                      icon={getFileIcon(file.type)}
                      sx={{ mr: 1, color: 'text.secondary' }}
                    />
                  )}
                  <Typography
                    variant="body2"
                    noWrap
                    sx={{
                      flexGrow: 1,
                      color:
                        fileStatuses[file.id] === 'not-cached' ? 'text.disabled' : 'text.primary',
                    }}
                  >
                    {file.name}
                  </Typography>
                </Box>
              ))}
            </Stack>
          </Box>
        </Scrollbar>
      </Box>
    );
  }
);
