'use client';

import Box from '@mui/material/Box';
import { FormHead } from 'src/auth/components/form-head';
import { useTranslate } from 'src/locales';
import { SignInButton } from 'src/layouts/components/sign-in-button';
import { SignUpLink } from 'src/layouts/components/sign-up-link';
import { Button } from '@mui/material';

// ----------------------------------------------------------------------

export function Auth0SignInView() {
  const { t } = useTranslate();

  return (
    <Box sx={{ gap: 3, display: 'flex', flexDirection: 'column' }}>
      <FormHead
        title={t('signIn.signInView.formTitle')}
        description={
          <>
            {t('signIn.signInView.singUpDescription')}
            <SignUpLink component={Button} forceReauthentication={true} />
            <SignInButton forceReauthentication={true} />
          </>
        }
        sx={{ textAlign: { xs: 'center', md: 'left' } }}
      />

    </Box>
  );
}
