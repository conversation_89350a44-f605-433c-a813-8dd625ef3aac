"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/components/hook-form/rhf-text-field.tsx":
/*!*****************************************************!*\
  !*** ./src/components/hook-form/rhf-text-field.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RHFTextField: () => (/* binding */ RHFTextField)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var minimal_shared_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! minimal-shared/utils */ \"(app-pages-browser)/./node_modules/minimal-shared/dist/utils/index.js\");\n/* harmony import */ var _mui_material_TextField__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/TextField */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n// ----------------------------------------------------------------------\n/**\n * Custom transform function for number fields on blur\n * Uses the package's transformValueOnBlur but ensures empty values become 0\n * to prevent backend validation errors\n */ function transformNumberOnBlur(value) {\n    const result = (0,minimal_shared_utils__WEBPACK_IMPORTED_MODULE_1__.transformValueOnBlur)(value, undefined); // Use 0 as fallback instead of empty string\n    return typeof result === 'number' ? result : undefined;\n}\nfunction RHFTextField(param) {\n    let { name, helperText, slotProps, type = 'text', ...other } = param;\n    _s();\n    const { control } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_2__.useFormContext)();\n    const isNumberType = type === 'number';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_2__.Controller, {\n        name: name,\n        control: control,\n        render: (param)=>{\n            let { field, fieldState: { error } } = param;\n            var _error_message;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_TextField__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                ...field,\n                fullWidth: true,\n                value: isNumberType ? (0,minimal_shared_utils__WEBPACK_IMPORTED_MODULE_1__.transformValue)(field.value) : field.value,\n                onChange: (event)=>{\n                    const transformedValue = isNumberType ? (0,minimal_shared_utils__WEBPACK_IMPORTED_MODULE_1__.transformValueOnChange)(event.target.value) : event.target.value;\n                    field.onChange(transformedValue);\n                },\n                onBlur: (event)=>{\n                    const transformedValue = isNumberType ? transformNumberOnBlur(event.target.value) : event.target.value;\n                    field.onChange(transformedValue);\n                },\n                type: isNumberType ? 'text' : type,\n                error: !!error,\n                helperText: (_error_message = error === null || error === void 0 ? void 0 : error.message) !== null && _error_message !== void 0 ? _error_message : helperText,\n                slotProps: {\n                    ...slotProps,\n                    htmlInput: {\n                        autoComplete: 'off',\n                        ...slotProps === null || slotProps === void 0 ? void 0 : slotProps.htmlInput,\n                        ...isNumberType && {\n                            inputMode: 'decimal',\n                            pattern: '[0-9]*\\\\.?[0-9]*'\n                        }\n                    }\n                },\n                ...other\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\components\\\\hook-form\\\\rhf-text-field.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, void 0);\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\components\\\\hook-form\\\\rhf-text-field.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(RHFTextField, \"zyAxkz+Wq3InUdCKNlVVi99oElQ=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_2__.useFormContext\n    ];\n});\n_c = RHFTextField;\nvar _c;\n$RefreshReg$(_c, \"RHFTextField\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/hook-form/rhf-text-field.tsx\n"));

/***/ })

});