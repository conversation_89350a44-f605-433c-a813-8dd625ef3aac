"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/details/plt1air-details.tsx":
/*!**************************************************************!*\
  !*** ./src/sections/orders/plt1/details/plt1air-details.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLT1AirOrderDetailsView: () => (/* binding */ PLT1AirOrderDetailsView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var src_lib_axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/lib/axios */ \"(app-pages-browser)/./src/lib/axios.ts\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var _hooks_use_plt1_status__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/use-plt1-status */ \"(app-pages-browser)/./src/sections/orders/plt1/hooks/use-plt1-status.ts\");\n/* harmony import */ var _plt1_order_details_base__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./plt1-order-details-base */ \"(app-pages-browser)/./src/sections/orders/plt1/details/plt1-order-details-base.tsx\");\n/* harmony import */ var _plt1air_document_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../plt1air-document-tabs */ \"(app-pages-browser)/./src/sections/orders/plt1/plt1air-document-tabs.tsx\");\n/* harmony import */ var _hooks_use_plt1air_details__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../hooks/use-plt1air-details */ \"(app-pages-browser)/./src/sections/orders/plt1/hooks/use-plt1air-details.ts\");\n/* harmony import */ var _utils_order_details_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/order-details-utils */ \"(app-pages-browser)/./src/sections/orders/plt1/utils/order-details-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ PLT1AirOrderDetailsView auto */ \nvar _s = $RefreshSig$();\n// src/sections/plt1-air-order-details/PLT1AirOrderDetailsView.tsx\n\n\n\n\n\n\n\n\n\nfunction PLT1AirOrderDetailsView(param) {\n    let { orderId, readOnly: propReadOnly = false } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_3__.useTranslate)();\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formChanged, setFormChanged] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [initialFormValues, setInitialFormValues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Create form methods with the specific AirFormValues type\n    const methods = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm)({\n        // Initialize default values in the derived component\n        defaultValues: {\n            houseAirWaybills: [],\n            masterAirWaybills: [],\n            commercialInvoices: [],\n            packingLists: [],\n            notificationsOfArrivals: [],\n            transitDocuments: [],\n            merchandisePositions: {\n                id: null,\n                positions: [],\n                t1OrderId: orderId\n            },\n            customsOffice: {\n                customsOfficeCode: ''\n            },\n            vehicleRegistration: {\n                vehicleRegistrationNumber: '',\n                vehicleCountryCode: '',\n                trailerRegistrationNumber: '',\n                trailerCountryCode: ''\n            }\n        },\n        mode: 'onChange'\n    });\n    const { formState, watch, reset } = methods;\n    const { isValid } = formState;\n    // Load order data\n    const { order, error: orderError, isLoading: isOrderLoading, reload } = (0,_hooks_use_plt1air_details__WEBPACK_IMPORTED_MODULE_7__.usePLTAir1OrderDetails)(orderId);\n    // Update form values when order data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1AirOrderDetailsView.useEffect\": ()=>{\n            if (order) {\n                const formData = {\n                    houseAirWaybills: order.houseAirWaybills || [],\n                    masterAirWaybills: order.masterAirWaybills || [],\n                    commercialInvoices: order.commercialInvoices || [],\n                    packingLists: order.packingLists || [],\n                    notificationsOfArrivals: order.notificationsOfArrivals || [],\n                    transitDocuments: order.transitDocuments || [],\n                    merchandisePositions: order.merchandisePositions || {\n                        id: null,\n                        positions: [],\n                        t1OrderId: orderId\n                    },\n                    customsOffice: order.customsOffice || {\n                        customsOfficeCode: ''\n                    },\n                    vehicleRegistration: order.vehicleRegistration || {\n                        vehicleRegistrationNumber: '',\n                        vehicleCountryCode: '',\n                        trailerRegistrationNumber: '',\n                        trailerCountryCode: ''\n                    }\n                };\n                reset(formData);\n                setInitialFormValues(formData);\n                setFormChanged(false);\n            }\n        }\n    }[\"PLT1AirOrderDetailsView.useEffect\"], [\n        order,\n        reset\n    ]);\n    // Helper function to check if values are actually different\n    const hasRealChanges = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PLT1AirOrderDetailsView.useCallback[hasRealChanges]\": ()=>{\n            if (!initialFormValues) return false;\n            const currentValues = methods.getValues();\n            return (0,_utils_order_details_utils__WEBPACK_IMPORTED_MODULE_8__.hasFormChanges)(currentValues, initialFormValues);\n        }\n    }[\"PLT1AirOrderDetailsView.useCallback[hasRealChanges]\"], [\n        initialFormValues,\n        methods\n    ]);\n    // Watch for form changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PLT1AirOrderDetailsView.useEffect\": ()=>{\n            const subscription = watch({\n                \"PLT1AirOrderDetailsView.useEffect.subscription\": ()=>{\n                    setFormChanged(hasRealChanges());\n                }\n            }[\"PLT1AirOrderDetailsView.useEffect.subscription\"]);\n            return ({\n                \"PLT1AirOrderDetailsView.useEffect\": ()=>subscription.unsubscribe()\n            })[\"PLT1AirOrderDetailsView.useEffect\"];\n        }\n    }[\"PLT1AirOrderDetailsView.useEffect\"], [\n        watch,\n        hasRealChanges\n    ]);\n    // Handle status change callback\n    const handleStatusChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PLT1AirOrderDetailsView.useCallback[handleStatusChange]\": ()=>{\n            reload();\n        }\n    }[\"PLT1AirOrderDetailsView.useCallback[handleStatusChange]\"], [\n        reload,\n        t\n    ]);\n    // Get order status\n    const { status: orderStatus, orderNumber, error: statusError } = (0,_hooks_use_plt1_status__WEBPACK_IMPORTED_MODULE_4__.usePLT1OrderStatus)(orderId, 1000, handleStatusChange);\n    // Save order data\n    const handleSaveOrder = async (formData)=>{\n        await (0,_utils_order_details_utils__WEBPACK_IMPORTED_MODULE_8__.handlePLT1OrderSave)(formData, {\n            orderId,\n            endpoint: src_lib_axios__WEBPACK_IMPORTED_MODULE_2__.endpoints.plt1.airUpdate,\n            idField: 'PLT1AirId',\n            t\n        }, setIsSaving, setFormChanged, reload);\n    };\n    // Handle form cancel\n    const handleCancel = ()=>{\n        if (initialFormValues) {\n            reset(initialFormValues);\n            setFormChanged(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_9__.FormProvider, {\n        ...methods,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plt1_order_details_base__WEBPACK_IMPORTED_MODULE_5__.PLT1OrderDetailsBase, {\n            orderId: orderId,\n            readOnly: propReadOnly,\n            isLoading: isOrderLoading,\n            error: orderError,\n            order: order,\n            orderStatus: orderStatus,\n            orderNumber: orderNumber,\n            statusError: statusError,\n            onSaveOrder: handleSaveOrder,\n            formChanged: formChanged,\n            isSaving: isSaving,\n            onCancel: handleCancel,\n            isValid: isValid,\n            documentTabs: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plt1air_document_tabs__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                t1OrderId: orderId,\n                order: order,\n                readOnly: propReadOnly || orderStatus === 'Scanning',\n                reload: reload\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1air-details.tsx\",\n                lineNumber: 190,\n                columnNumber: 11\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1air-details.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\details\\\\plt1air-details.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n_s(PLT1AirOrderDetailsView, \"e3kiB9Z9dpXY8Qh27wAaeWV3J8k=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_3__.useTranslate,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm,\n        _hooks_use_plt1air_details__WEBPACK_IMPORTED_MODULE_7__.usePLTAir1OrderDetails,\n        _hooks_use_plt1_status__WEBPACK_IMPORTED_MODULE_4__.usePLT1OrderStatus\n    ];\n});\n_c = PLT1AirOrderDetailsView;\nvar _c;\n$RefreshReg$(_c, \"PLT1AirOrderDetailsView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZWN0aW9ucy9vcmRlcnMvcGx0MS9kZXRhaWxzL3BsdDFhaXItZGV0YWlscy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFQSxrRUFBa0U7QUFDVDtBQUNEO0FBRWQ7QUFDQztBQVltQjtBQUNHO0FBQ0w7QUFDVTtBQUNhO0FBc0I1RSxTQUFTYSx3QkFBd0IsS0FHVDtRQUhTLEVBQ3RDQyxPQUFPLEVBQ1BDLFVBQVVDLGVBQWUsS0FBSyxFQUNELEdBSFM7O0lBSXRDLE1BQU0sRUFBRUMsQ0FBQyxFQUFFLEdBQUdYLHlEQUFZQTtJQUMxQixNQUFNLENBQUNZLFVBQVVDLFlBQVksR0FBR25CLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ29CLGFBQWFDLGVBQWUsR0FBR3JCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3NCLG1CQUFtQkMscUJBQXFCLEdBQUd2QiwrQ0FBUUEsQ0FBdUI7SUFFakYsMkRBQTJEO0lBQzNELE1BQU13QixVQUFVcEIsd0RBQU9BLENBQWdCO1FBQ3JDLHFEQUFxRDtRQUNyRHFCLGVBQWU7WUFDYkMsa0JBQWtCLEVBQUU7WUFDcEJDLG1CQUFtQixFQUFFO1lBQ3JCQyxvQkFBb0IsRUFBRTtZQUN0QkMsY0FBYyxFQUFFO1lBQ2hCQyx5QkFBeUIsRUFBRTtZQUMzQkMsa0JBQWtCLEVBQUU7WUFDcEJDLHNCQUFzQjtnQkFDcEJDLElBQUk7Z0JBQ0pDLFdBQVcsRUFBRTtnQkFDYkMsV0FBV3JCO1lBQ2I7WUFDQXNCLGVBQWU7Z0JBQUVDLG1CQUFtQjtZQUFHO1lBQ3ZDQyxxQkFBcUI7Z0JBQ25CQywyQkFBMkI7Z0JBQzNCQyxvQkFBb0I7Z0JBQ3BCQywyQkFBMkI7Z0JBQzNCQyxvQkFBb0I7WUFDdEI7UUFDRjtRQUNBQyxNQUFNO0lBQ1I7SUFFQSxNQUFNLEVBQUVDLFNBQVMsRUFBRUMsS0FBSyxFQUFFQyxLQUFLLEVBQUUsR0FBR3RCO0lBQ3BDLE1BQU0sRUFBRXVCLE9BQU8sRUFBRSxHQUFHSDtJQUVwQixrQkFBa0I7SUFDbEIsTUFBTSxFQUNKSSxLQUFLLEVBQ0xDLE9BQU9DLFVBQVUsRUFDakJDLFdBQVdDLGNBQWMsRUFDekJDLE1BQU0sRUFDUCxHQUFHM0Msa0ZBQXNCQSxDQUFDSTtJQUUzQiwrQ0FBK0M7SUFDL0NaLGdEQUFTQTs2Q0FBQztZQUNSLElBQUk4QyxPQUFPO2dCQUNULE1BQU1NLFdBQTBCO29CQUM5QjVCLGtCQUFrQnNCLE1BQU10QixnQkFBZ0IsSUFBSSxFQUFFO29CQUM5Q0MsbUJBQW1CcUIsTUFBTXJCLGlCQUFpQixJQUFJLEVBQUU7b0JBQ2hEQyxvQkFBb0JvQixNQUFNcEIsa0JBQWtCLElBQUksRUFBRTtvQkFDbERDLGNBQWNtQixNQUFNbkIsWUFBWSxJQUFJLEVBQUU7b0JBQ3RDQyx5QkFBeUJrQixNQUFNbEIsdUJBQXVCLElBQUksRUFBRTtvQkFDNURDLGtCQUFrQmlCLE1BQU1qQixnQkFBZ0IsSUFBSSxFQUFFO29CQUM5Q0Msc0JBQXNCZ0IsTUFBTWhCLG9CQUFvQixJQUFJO3dCQUNsREMsSUFBSTt3QkFDSkMsV0FBVyxFQUFFO3dCQUNiQyxXQUFXckI7b0JBQ2I7b0JBQ0FzQixlQUFlWSxNQUFNWixhQUFhLElBQUk7d0JBQUVDLG1CQUFtQjtvQkFBRztvQkFDOURDLHFCQUFxQlUsTUFBTVYsbUJBQW1CLElBQUk7d0JBQ2hEQywyQkFBMkI7d0JBQzNCQyxvQkFBb0I7d0JBQ3BCQywyQkFBMkI7d0JBQzNCQyxvQkFBb0I7b0JBQ3RCO2dCQUNGO2dCQUNBSSxNQUFNUTtnQkFDTi9CLHFCQUFxQitCO2dCQUNyQmpDLGVBQWU7WUFDakI7UUFDRjs0Q0FBRztRQUFDMkI7UUFBT0Y7S0FBTTtJQUVqQiw0REFBNEQ7SUFDNUQsTUFBTVMsaUJBQWlCdEQsa0RBQVdBOytEQUFDO1lBQ2pDLElBQUksQ0FBQ3FCLG1CQUFtQixPQUFPO1lBQy9CLE1BQU1rQyxnQkFBZ0JoQyxRQUFRaUMsU0FBUztZQUN2QyxPQUFPN0MsMEVBQWNBLENBQUM0QyxlQUFlbEM7UUFDdkM7OERBQUc7UUFBQ0E7UUFBbUJFO0tBQVE7SUFFL0IseUJBQXlCO0lBQ3pCdEIsZ0RBQVNBOzZDQUFDO1lBQ1IsTUFBTXdELGVBQWViO2tFQUFNO29CQUN6QnhCLGVBQWVrQztnQkFDakI7O1lBRUE7cURBQU8sSUFBTUcsYUFBYUMsV0FBVzs7UUFDdkM7NENBQUc7UUFBQ2Q7UUFBT1U7S0FBZTtJQUUxQixnQ0FBZ0M7SUFDaEMsTUFBTUsscUJBQXFCM0Qsa0RBQVdBO21FQUFDO1lBQ3JDb0Q7UUFDRjtrRUFBRztRQUFDQTtRQUFRcEM7S0FBRTtJQUVkLG1CQUFtQjtJQUNuQixNQUFNLEVBQ0o0QyxRQUFRQyxXQUFXLEVBQ25CQyxXQUFXLEVBQ1hkLE9BQU9lLFdBQVcsRUFDbkIsR0FBR3pELDBFQUFrQkEsQ0FBQ08sU0FBUyxNQUFNOEM7SUFFdEMsa0JBQWtCO0lBQ2xCLE1BQU1LLGtCQUFrQixPQUFPWDtRQUM3QixNQUFNM0MsK0VBQW1CQSxDQUN2QjJDLFVBQ0E7WUFDRXhDO1lBQ0FvRCxVQUFVN0Qsb0RBQVNBLENBQUM4RCxJQUFJLENBQUNDLFNBQVM7WUFDbENDLFNBQVM7WUFDVHBEO1FBQ0YsR0FDQUUsYUFDQUUsZ0JBQ0FnQztJQUVKO0lBRUEscUJBQXFCO0lBQ3JCLE1BQU1pQixlQUFlO1FBQ25CLElBQUloRCxtQkFBbUI7WUFDckJ3QixNQUFNeEI7WUFDTkQsZUFBZTtRQUNqQjtJQUNGO0lBRUEscUJBQ0UsOERBQUNsQix5REFBWUE7UUFBRSxHQUFHcUIsT0FBTztrQkFDdkIsNEVBQUNoQiwwRUFBb0JBO1lBQ25CTSxTQUFTQTtZQUNUQyxVQUFVQztZQUNWbUMsV0FBV0M7WUFDWEgsT0FBT0M7WUFDUEYsT0FBT0E7WUFDUGMsYUFBYUE7WUFDYkMsYUFBYUE7WUFDYkMsYUFBYUE7WUFDYk8sYUFBYU47WUFDYjdDLGFBQWFBO1lBQ2JGLFVBQVVBO1lBQ1ZzRCxVQUFVRjtZQUNWdkIsU0FBU0E7WUFDVDBCLDRCQUNFLDhEQUFDaEUsOERBQW9CQTtnQkFDbkIwQixXQUFXckI7Z0JBQ1hrQyxPQUFPQTtnQkFDUGpDLFVBQVVDLGdCQUFnQjhDLGdCQUFnQjtnQkFDMUNULFFBQVFBOzs7Ozs7Ozs7Ozs7Ozs7O0FBTXBCO0dBMUpnQnhDOztRQUlBUCxxREFBWUE7UUFNVkYsb0RBQU9BO1FBa0NuQk0sOEVBQXNCQTtRQXlEdEJILHNFQUFrQkE7OztLQXJHUk0iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbS5tYWxpa1xcc291cmNlXFxyZXBvc1xcUm9zc2V0YVxcRnJvbnRlbmRcXHNyY1xcc2VjdGlvbnNcXG9yZGVyc1xccGx0MVxcZGV0YWlsc1xccGx0MWFpci1kZXRhaWxzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG4vLyBzcmMvc2VjdGlvbnMvcGx0MS1haXItb3JkZXItZGV0YWlscy9QTFQxQWlyT3JkZXJEZXRhaWxzVmlldy50c3hcclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUNhbGxiYWNrLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IEZvcm1Qcm92aWRlciwgdXNlRm9ybSB9IGZyb20gJ3JlYWN0LWhvb2stZm9ybSc7XHJcblxyXG5pbXBvcnQgeyBlbmRwb2ludHMgfSBmcm9tICdzcmMvbGliL2F4aW9zJztcclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRlIH0gZnJvbSAnc3JjL2xvY2FsZXMnO1xyXG5pbXBvcnQge1xyXG4gIFBMVDFBaXJIb3VzZUFpcldheWJpbGwsXHJcbiAgUExUMUFpck1hc3RlckFpcldheWJpbGwsXHJcbiAgUExUMUNvbW1lcmNpYWxJbnZvaWNlLFxyXG4gIFBMVDFDdXN0b21zT2ZmaWNlLFxyXG4gIFBMVDFNZXJjaGFuZGlzZVBvc2l0aW9ucyxcclxuICBQTFQxTm90aWZpY2F0aW9uT2ZBcnJpdmFsLFxyXG4gIFBMVDFQYWNraW5nTGlzdCxcclxuICBQTFQxVHJhbnNpdERvY3VtZW50LFxyXG4gIFBMVDFWZWhpY2xlUmVnaXN0cmF0aW9uXHJcbn0gZnJvbSAnLi4vdHlwZXMvcGx0MS1kZXRhaWxzLnR5cGVzJztcclxuaW1wb3J0IHsgdXNlUExUMU9yZGVyU3RhdHVzIH0gZnJvbSAnLi4vaG9va3MvdXNlLXBsdDEtc3RhdHVzJztcclxuaW1wb3J0IHsgUExUMU9yZGVyRGV0YWlsc0Jhc2UgfSBmcm9tICcuL3BsdDEtb3JkZXItZGV0YWlscy1iYXNlJztcclxuaW1wb3J0IFBMVDFBaXJEb2N1bWVudHNUYWJzIGZyb20gJy4uL3BsdDFhaXItZG9jdW1lbnQtdGFicyc7XHJcbmltcG9ydCB7IHVzZVBMVEFpcjFPcmRlckRldGFpbHMgfSBmcm9tICcuLi9ob29rcy91c2UtcGx0MWFpci1kZXRhaWxzJztcclxuaW1wb3J0IHsgaGFuZGxlUExUMU9yZGVyU2F2ZSwgaGFzRm9ybUNoYW5nZXMgfSBmcm9tICcuLi91dGlscy9vcmRlci1kZXRhaWxzLXV0aWxzJztcclxuXHJcbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuXHJcbmludGVyZmFjZSBQTFQxQWlyT3JkZXJEZXRhaWxzVmlld1Byb3BzIHtcclxuICBvcmRlcklkOiBzdHJpbmc7XHJcbiAgcmVhZE9ubHk/OiBib29sZWFuO1xyXG59XHJcblxyXG4vLyBEZWZpbmUgc3BlY2lmaWMgZm9ybSB2YWx1ZXMgdHlwZSBmb3IgQWlyIG9yZGVyc1xyXG5leHBvcnQgaW50ZXJmYWNlIEFpckZvcm1WYWx1ZXMge1xyXG4gIGhvdXNlQWlyV2F5YmlsbHM6IFBMVDFBaXJIb3VzZUFpcldheWJpbGxbXTtcclxuICBtYXN0ZXJBaXJXYXliaWxsczogUExUMUFpck1hc3RlckFpcldheWJpbGxbXTtcclxuICBjb21tZXJjaWFsSW52b2ljZXM6IFBMVDFDb21tZXJjaWFsSW52b2ljZVtdO1xyXG4gIHBhY2tpbmdMaXN0czogUExUMVBhY2tpbmdMaXN0W107XHJcbiAgbm90aWZpY2F0aW9uc09mQXJyaXZhbHM6IFBMVDFOb3RpZmljYXRpb25PZkFycml2YWxbXTtcclxuICB0cmFuc2l0RG9jdW1lbnRzOiBQTFQxVHJhbnNpdERvY3VtZW50W107XHJcbiAgbWVyY2hhbmRpc2VQb3NpdGlvbnM/OiBQTFQxTWVyY2hhbmRpc2VQb3NpdGlvbnM7XHJcbiAgY3VzdG9tc09mZmljZT86IFBMVDFDdXN0b21zT2ZmaWNlO1xyXG4gIHZlaGljbGVSZWdpc3RyYXRpb24/OiBQTFQxVmVoaWNsZVJlZ2lzdHJhdGlvbjtcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIFBMVDFBaXJPcmRlckRldGFpbHNWaWV3KHtcclxuICBvcmRlcklkLFxyXG4gIHJlYWRPbmx5OiBwcm9wUmVhZE9ubHkgPSBmYWxzZSxcclxufTogUExUMUFpck9yZGVyRGV0YWlsc1ZpZXdQcm9wcykge1xyXG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRlKCk7XHJcbiAgY29uc3QgW2lzU2F2aW5nLCBzZXRJc1NhdmluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2Zvcm1DaGFuZ2VkLCBzZXRGb3JtQ2hhbmdlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2luaXRpYWxGb3JtVmFsdWVzLCBzZXRJbml0aWFsRm9ybVZhbHVlc10gPSB1c2VTdGF0ZTxBaXJGb3JtVmFsdWVzIHwgbnVsbD4obnVsbCk7XHJcblxyXG4gIC8vIENyZWF0ZSBmb3JtIG1ldGhvZHMgd2l0aCB0aGUgc3BlY2lmaWMgQWlyRm9ybVZhbHVlcyB0eXBlXHJcbiAgY29uc3QgbWV0aG9kcyA9IHVzZUZvcm08QWlyRm9ybVZhbHVlcz4oe1xyXG4gICAgLy8gSW5pdGlhbGl6ZSBkZWZhdWx0IHZhbHVlcyBpbiB0aGUgZGVyaXZlZCBjb21wb25lbnRcclxuICAgIGRlZmF1bHRWYWx1ZXM6IHtcclxuICAgICAgaG91c2VBaXJXYXliaWxsczogW10sXHJcbiAgICAgIG1hc3RlckFpcldheWJpbGxzOiBbXSxcclxuICAgICAgY29tbWVyY2lhbEludm9pY2VzOiBbXSxcclxuICAgICAgcGFja2luZ0xpc3RzOiBbXSxcclxuICAgICAgbm90aWZpY2F0aW9uc09mQXJyaXZhbHM6IFtdLFxyXG4gICAgICB0cmFuc2l0RG9jdW1lbnRzOiBbXSxcclxuICAgICAgbWVyY2hhbmRpc2VQb3NpdGlvbnM6IHtcclxuICAgICAgICBpZDogbnVsbCxcclxuICAgICAgICBwb3NpdGlvbnM6IFtdLFxyXG4gICAgICAgIHQxT3JkZXJJZDogb3JkZXJJZCxcclxuICAgICAgfSxcclxuICAgICAgY3VzdG9tc09mZmljZTogeyBjdXN0b21zT2ZmaWNlQ29kZTogJycgfSxcclxuICAgICAgdmVoaWNsZVJlZ2lzdHJhdGlvbjoge1xyXG4gICAgICAgIHZlaGljbGVSZWdpc3RyYXRpb25OdW1iZXI6ICcnLFxyXG4gICAgICAgIHZlaGljbGVDb3VudHJ5Q29kZTogJycsXHJcbiAgICAgICAgdHJhaWxlclJlZ2lzdHJhdGlvbk51bWJlcjogJycsXHJcbiAgICAgICAgdHJhaWxlckNvdW50cnlDb2RlOiAnJyxcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICBtb2RlOiAnb25DaGFuZ2UnLFxyXG4gIH0pO1xyXG5cclxuICBjb25zdCB7IGZvcm1TdGF0ZSwgd2F0Y2gsIHJlc2V0IH0gPSBtZXRob2RzO1xyXG4gIGNvbnN0IHsgaXNWYWxpZCB9ID0gZm9ybVN0YXRlO1xyXG5cclxuICAvLyBMb2FkIG9yZGVyIGRhdGFcclxuICBjb25zdCB7XHJcbiAgICBvcmRlcixcclxuICAgIGVycm9yOiBvcmRlckVycm9yLFxyXG4gICAgaXNMb2FkaW5nOiBpc09yZGVyTG9hZGluZyxcclxuICAgIHJlbG9hZCxcclxuICB9ID0gdXNlUExUQWlyMU9yZGVyRGV0YWlscyhvcmRlcklkKTtcclxuXHJcbiAgLy8gVXBkYXRlIGZvcm0gdmFsdWVzIHdoZW4gb3JkZXIgZGF0YSBpcyBsb2FkZWRcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKG9yZGVyKSB7XHJcbiAgICAgIGNvbnN0IGZvcm1EYXRhOiBBaXJGb3JtVmFsdWVzID0ge1xyXG4gICAgICAgIGhvdXNlQWlyV2F5YmlsbHM6IG9yZGVyLmhvdXNlQWlyV2F5YmlsbHMgfHwgW10sXHJcbiAgICAgICAgbWFzdGVyQWlyV2F5YmlsbHM6IG9yZGVyLm1hc3RlckFpcldheWJpbGxzIHx8IFtdLFxyXG4gICAgICAgIGNvbW1lcmNpYWxJbnZvaWNlczogb3JkZXIuY29tbWVyY2lhbEludm9pY2VzIHx8IFtdLFxyXG4gICAgICAgIHBhY2tpbmdMaXN0czogb3JkZXIucGFja2luZ0xpc3RzIHx8IFtdLFxyXG4gICAgICAgIG5vdGlmaWNhdGlvbnNPZkFycml2YWxzOiBvcmRlci5ub3RpZmljYXRpb25zT2ZBcnJpdmFscyB8fCBbXSxcclxuICAgICAgICB0cmFuc2l0RG9jdW1lbnRzOiBvcmRlci50cmFuc2l0RG9jdW1lbnRzIHx8IFtdLFxyXG4gICAgICAgIG1lcmNoYW5kaXNlUG9zaXRpb25zOiBvcmRlci5tZXJjaGFuZGlzZVBvc2l0aW9ucyB8fCB7XHJcbiAgICAgICAgICBpZDogbnVsbCxcclxuICAgICAgICAgIHBvc2l0aW9uczogW10sXHJcbiAgICAgICAgICB0MU9yZGVySWQ6IG9yZGVySWQsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBjdXN0b21zT2ZmaWNlOiBvcmRlci5jdXN0b21zT2ZmaWNlIHx8IHsgY3VzdG9tc09mZmljZUNvZGU6ICcnIH0sXHJcbiAgICAgICAgdmVoaWNsZVJlZ2lzdHJhdGlvbjogb3JkZXIudmVoaWNsZVJlZ2lzdHJhdGlvbiB8fCB7XHJcbiAgICAgICAgICB2ZWhpY2xlUmVnaXN0cmF0aW9uTnVtYmVyOiAnJyxcclxuICAgICAgICAgIHZlaGljbGVDb3VudHJ5Q29kZTogJycsXHJcbiAgICAgICAgICB0cmFpbGVyUmVnaXN0cmF0aW9uTnVtYmVyOiAnJyxcclxuICAgICAgICAgIHRyYWlsZXJDb3VudHJ5Q29kZTogJycsXHJcbiAgICAgICAgfSxcclxuICAgICAgfTtcclxuICAgICAgcmVzZXQoZm9ybURhdGEpO1xyXG4gICAgICBzZXRJbml0aWFsRm9ybVZhbHVlcyhmb3JtRGF0YSk7XHJcbiAgICAgIHNldEZvcm1DaGFuZ2VkKGZhbHNlKTtcclxuICAgIH1cclxuICB9LCBbb3JkZXIsIHJlc2V0XSk7XHJcblxyXG4gIC8vIEhlbHBlciBmdW5jdGlvbiB0byBjaGVjayBpZiB2YWx1ZXMgYXJlIGFjdHVhbGx5IGRpZmZlcmVudFxyXG4gIGNvbnN0IGhhc1JlYWxDaGFuZ2VzID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgaWYgKCFpbml0aWFsRm9ybVZhbHVlcykgcmV0dXJuIGZhbHNlO1xyXG4gICAgY29uc3QgY3VycmVudFZhbHVlcyA9IG1ldGhvZHMuZ2V0VmFsdWVzKCk7XHJcbiAgICByZXR1cm4gaGFzRm9ybUNoYW5nZXMoY3VycmVudFZhbHVlcywgaW5pdGlhbEZvcm1WYWx1ZXMpO1xyXG4gIH0sIFtpbml0aWFsRm9ybVZhbHVlcywgbWV0aG9kc10pO1xyXG5cclxuICAvLyBXYXRjaCBmb3IgZm9ybSBjaGFuZ2VzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IHN1YnNjcmlwdGlvbiA9IHdhdGNoKCgpID0+IHtcclxuICAgICAgc2V0Rm9ybUNoYW5nZWQoaGFzUmVhbENoYW5nZXMoKSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICByZXR1cm4gKCkgPT4gc3Vic2NyaXB0aW9uLnVuc3Vic2NyaWJlKCk7XHJcbiAgfSwgW3dhdGNoLCBoYXNSZWFsQ2hhbmdlc10pO1xyXG5cclxuICAvLyBIYW5kbGUgc3RhdHVzIGNoYW5nZSBjYWxsYmFja1xyXG4gIGNvbnN0IGhhbmRsZVN0YXR1c0NoYW5nZSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgIHJlbG9hZCgpO1xyXG4gIH0sIFtyZWxvYWQsIHRdKTtcclxuXHJcbiAgLy8gR2V0IG9yZGVyIHN0YXR1c1xyXG4gIGNvbnN0IHtcclxuICAgIHN0YXR1czogb3JkZXJTdGF0dXMsXHJcbiAgICBvcmRlck51bWJlcixcclxuICAgIGVycm9yOiBzdGF0dXNFcnJvcixcclxuICB9ID0gdXNlUExUMU9yZGVyU3RhdHVzKG9yZGVySWQsIDEwMDAsIGhhbmRsZVN0YXR1c0NoYW5nZSk7XHJcblxyXG4gIC8vIFNhdmUgb3JkZXIgZGF0YVxyXG4gIGNvbnN0IGhhbmRsZVNhdmVPcmRlciA9IGFzeW5jIChmb3JtRGF0YTogQWlyRm9ybVZhbHVlcykgPT4ge1xyXG4gICAgYXdhaXQgaGFuZGxlUExUMU9yZGVyU2F2ZShcclxuICAgICAgZm9ybURhdGEsXHJcbiAgICAgIHtcclxuICAgICAgICBvcmRlcklkLFxyXG4gICAgICAgIGVuZHBvaW50OiBlbmRwb2ludHMucGx0MS5haXJVcGRhdGUsXHJcbiAgICAgICAgaWRGaWVsZDogJ1BMVDFBaXJJZCcsXHJcbiAgICAgICAgdCxcclxuICAgICAgfSxcclxuICAgICAgc2V0SXNTYXZpbmcsXHJcbiAgICAgIHNldEZvcm1DaGFuZ2VkLFxyXG4gICAgICByZWxvYWRcclxuICAgICk7XHJcbiAgfTtcclxuICBcclxuICAvLyBIYW5kbGUgZm9ybSBjYW5jZWxcclxuICBjb25zdCBoYW5kbGVDYW5jZWwgPSAoKSA9PiB7XHJcbiAgICBpZiAoaW5pdGlhbEZvcm1WYWx1ZXMpIHtcclxuICAgICAgcmVzZXQoaW5pdGlhbEZvcm1WYWx1ZXMpO1xyXG4gICAgICBzZXRGb3JtQ2hhbmdlZChmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxGb3JtUHJvdmlkZXIgey4uLm1ldGhvZHN9PlxyXG4gICAgICA8UExUMU9yZGVyRGV0YWlsc0Jhc2U8QWlyRm9ybVZhbHVlcz5cclxuICAgICAgICBvcmRlcklkPXtvcmRlcklkfVxyXG4gICAgICAgIHJlYWRPbmx5PXtwcm9wUmVhZE9ubHl9XHJcbiAgICAgICAgaXNMb2FkaW5nPXtpc09yZGVyTG9hZGluZ31cclxuICAgICAgICBlcnJvcj17b3JkZXJFcnJvcn1cclxuICAgICAgICBvcmRlcj17b3JkZXJ9XHJcbiAgICAgICAgb3JkZXJTdGF0dXM9e29yZGVyU3RhdHVzfVxyXG4gICAgICAgIG9yZGVyTnVtYmVyPXtvcmRlck51bWJlcn1cclxuICAgICAgICBzdGF0dXNFcnJvcj17c3RhdHVzRXJyb3J9XHJcbiAgICAgICAgb25TYXZlT3JkZXI9e2hhbmRsZVNhdmVPcmRlcn1cclxuICAgICAgICBmb3JtQ2hhbmdlZD17Zm9ybUNoYW5nZWR9XHJcbiAgICAgICAgaXNTYXZpbmc9e2lzU2F2aW5nfVxyXG4gICAgICAgIG9uQ2FuY2VsPXtoYW5kbGVDYW5jZWx9XHJcbiAgICAgICAgaXNWYWxpZD17aXNWYWxpZH1cclxuICAgICAgICBkb2N1bWVudFRhYnM9e1xyXG4gICAgICAgICAgPFBMVDFBaXJEb2N1bWVudHNUYWJzXHJcbiAgICAgICAgICAgIHQxT3JkZXJJZD17b3JkZXJJZH1cclxuICAgICAgICAgICAgb3JkZXI9e29yZGVyfVxyXG4gICAgICAgICAgICByZWFkT25seT17cHJvcFJlYWRPbmx5IHx8IG9yZGVyU3RhdHVzID09PSAnU2Nhbm5pbmcnfVxyXG4gICAgICAgICAgICByZWxvYWQ9e3JlbG9hZH1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgfVxyXG4gICAgICAvPlxyXG4gICAgPC9Gb3JtUHJvdmlkZXI+XHJcbiAgKTtcclxufSJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUNhbGxiYWNrIiwidXNlRWZmZWN0IiwiRm9ybVByb3ZpZGVyIiwidXNlRm9ybSIsImVuZHBvaW50cyIsInVzZVRyYW5zbGF0ZSIsInVzZVBMVDFPcmRlclN0YXR1cyIsIlBMVDFPcmRlckRldGFpbHNCYXNlIiwiUExUMUFpckRvY3VtZW50c1RhYnMiLCJ1c2VQTFRBaXIxT3JkZXJEZXRhaWxzIiwiaGFuZGxlUExUMU9yZGVyU2F2ZSIsImhhc0Zvcm1DaGFuZ2VzIiwiUExUMUFpck9yZGVyRGV0YWlsc1ZpZXciLCJvcmRlcklkIiwicmVhZE9ubHkiLCJwcm9wUmVhZE9ubHkiLCJ0IiwiaXNTYXZpbmciLCJzZXRJc1NhdmluZyIsImZvcm1DaGFuZ2VkIiwic2V0Rm9ybUNoYW5nZWQiLCJpbml0aWFsRm9ybVZhbHVlcyIsInNldEluaXRpYWxGb3JtVmFsdWVzIiwibWV0aG9kcyIsImRlZmF1bHRWYWx1ZXMiLCJob3VzZUFpcldheWJpbGxzIiwibWFzdGVyQWlyV2F5YmlsbHMiLCJjb21tZXJjaWFsSW52b2ljZXMiLCJwYWNraW5nTGlzdHMiLCJub3RpZmljYXRpb25zT2ZBcnJpdmFscyIsInRyYW5zaXREb2N1bWVudHMiLCJtZXJjaGFuZGlzZVBvc2l0aW9ucyIsImlkIiwicG9zaXRpb25zIiwidDFPcmRlcklkIiwiY3VzdG9tc09mZmljZSIsImN1c3RvbXNPZmZpY2VDb2RlIiwidmVoaWNsZVJlZ2lzdHJhdGlvbiIsInZlaGljbGVSZWdpc3RyYXRpb25OdW1iZXIiLCJ2ZWhpY2xlQ291bnRyeUNvZGUiLCJ0cmFpbGVyUmVnaXN0cmF0aW9uTnVtYmVyIiwidHJhaWxlckNvdW50cnlDb2RlIiwibW9kZSIsImZvcm1TdGF0ZSIsIndhdGNoIiwicmVzZXQiLCJpc1ZhbGlkIiwib3JkZXIiLCJlcnJvciIsIm9yZGVyRXJyb3IiLCJpc0xvYWRpbmciLCJpc09yZGVyTG9hZGluZyIsInJlbG9hZCIsImZvcm1EYXRhIiwiaGFzUmVhbENoYW5nZXMiLCJjdXJyZW50VmFsdWVzIiwiZ2V0VmFsdWVzIiwic3Vic2NyaXB0aW9uIiwidW5zdWJzY3JpYmUiLCJoYW5kbGVTdGF0dXNDaGFuZ2UiLCJzdGF0dXMiLCJvcmRlclN0YXR1cyIsIm9yZGVyTnVtYmVyIiwic3RhdHVzRXJyb3IiLCJoYW5kbGVTYXZlT3JkZXIiLCJlbmRwb2ludCIsInBsdDEiLCJhaXJVcGRhdGUiLCJpZEZpZWxkIiwiaGFuZGxlQ2FuY2VsIiwib25TYXZlT3JkZXIiLCJvbkNhbmNlbCIsImRvY3VtZW50VGFicyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/details/plt1air-details.tsx\n"));

/***/ })

});