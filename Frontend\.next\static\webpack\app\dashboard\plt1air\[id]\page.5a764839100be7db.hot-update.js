"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/plt1air/[id]/page",{

/***/ "(app-pages-browser)/./src/sections/orders/plt1/plt1air-document-tabs.tsx":
/*!************************************************************!*\
  !*** ./src/sections/orders/plt1/plt1air-document-tabs.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PLT1AirDocumentsTabs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mui/material/Box */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _mui_material_Tab__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material/Tab */ \"(app-pages-browser)/./node_modules/@mui/material/Tab/Tab.js\");\n/* harmony import */ var _mui_material_Tabs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/Tabs */ \"(app-pages-browser)/./node_modules/@mui/material/Tabs/Tabs.js\");\n/* harmony import */ var _mui_material_Card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/Card */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/Divider */ \"(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material/CardHeader */ \"(app-pages-browser)/./node_modules/@mui/material/CardHeader/CardHeader.js\");\n/* harmony import */ var src_locales__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/locales */ \"(app-pages-browser)/./src/locales/index.ts\");\n/* harmony import */ var _tabs_air_plt1air_house_airwaybill_tab__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tabs/air/plt1air-house-airwaybill-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/air/plt1air-house-airwaybill-tab.tsx\");\n/* harmony import */ var _tabs_air_plt1air_master_airwaybill_tab__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tabs/air/plt1air-master-airwaybill-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/air/plt1air-master-airwaybill-tab.tsx\");\n/* harmony import */ var _tabs_plt1_commercial_invoice_tab__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tabs/plt1-commercial-invoice-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-commercial-invoice-tab.tsx\");\n/* harmony import */ var _tabs_plt1_packing_list_tab__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./tabs/plt1-packing-list-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-packing-list-tab.tsx\");\n/* harmony import */ var _tabs_plt1_notifications_arrival_tab__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./tabs/plt1-notifications-arrival-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-notifications-arrival-tab.tsx\");\n/* harmony import */ var _tabs_plt1_transit_tab__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./tabs/plt1-transit-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-transit-tab.tsx\");\n/* harmony import */ var _tabs_plt1_merchandise_positions_tab__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./tabs/plt1-merchandise-positions-tab */ \"(app-pages-browser)/./src/sections/orders/plt1/tabs/plt1-merchandise-positions-tab.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// ----------------------------------------------------------------------\nvar TabNames = /*#__PURE__*/ function(TabNames) {\n    TabNames[\"HAWB\"] = \"hawb\";\n    TabNames[\"MAWB\"] = \"mawb\";\n    TabNames[\"COMMERCIAL_INVOICES\"] = \"commercialInvoices\";\n    TabNames[\"PACKING_LISTS\"] = \"packingLists\";\n    TabNames[\"NOTIFICATIONS\"] = \"notifications\";\n    TabNames[\"TRANSIT_DOCUMENTS\"] = \"transitDocuments\";\n    TabNames[\"MERCHANDISE_POSITIONS\"] = \"merchandisePositions\";\n    return TabNames;\n}(TabNames || {});\nfunction PLT1AirDocumentsTabs(param) {\n    let { t1OrderId, order, readOnly = false, reload } = param;\n    _s();\n    const { t } = (0,src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate)();\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"PLT1AirDocumentsTabs.useState\": ()=>{\n            const savedTab = sessionStorage.getItem(\"plt1-current-tab-\".concat(t1OrderId));\n            return savedTab && Object.values(TabNames).includes(savedTab) ? savedTab : \"hawb\";\n        }\n    }[\"PLT1AirDocumentsTabs.useState\"]);\n    const handleChangeTab = (event, newValue)=>{\n        setCurrentTab(newValue);\n        sessionStorage.setItem(\"plt1-current-tab-\".concat(t1OrderId), newValue);\n    };\n    const TABS = [\n        {\n            value: \"hawb\",\n            label: t('plt1.details.documents.tabs.houseAirWaybills'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_air_plt1air_house_airwaybill_tab__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 59,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"mawb\",\n            label: t('plt1.details.documents.tabs.masterAirWaybills'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_air_plt1air_master_airwaybill_tab__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 64,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"commercialInvoices\",\n            label: t('plt1.details.documents.tabs.commercialInvoices'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_commercial_invoice_tab__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 69,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"packingLists\",\n            label: t('plt1.details.documents.tabs.packingLists'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_packing_list_tab__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 74,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"notifications\",\n            label: t('plt1.details.documents.tabs.notificationsOfArrival'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_notifications_arrival_tab__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 79,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"transitDocuments\",\n            label: t('plt1.details.documents.tabs.transitDocuments'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_transit_tab__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 84,\n                columnNumber: 24\n            }, this)\n        },\n        {\n            value: \"merchandisePositions\",\n            label: t('plt1.details.documents.tabs.merchandisePositions'),\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tabs_plt1_merchandise_positions_tab__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                t1OrderId: t1OrderId,\n                order: order,\n                readOnly: readOnly,\n                reload: reload\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 89,\n                columnNumber: 24\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Card__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CardHeader__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                title: t('plt1.details.documents.heading'),\n                sx: {\n                    mb: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 95,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                sx: {\n                    borderStyle: 'dashed'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 99,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tabs__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                value: currentTab,\n                onChange: handleChangeTab,\n                sx: {\n                    px: 2.5,\n                    boxShadow: (theme)=>\"inset 0 -2px 0 0 \".concat(theme.palette.divider)\n                },\n                children: TABS.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Tab__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        value: tab.value,\n                        label: tab.label\n                    }, tab.value, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 21\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                lineNumber: 100,\n                columnNumber: 13\n            }, this),\n            TABS.map((tab)=>tab.value === currentTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    sx: {\n                        p: 3\n                    },\n                    children: tab.component\n                }, tab.value, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 25\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\Rosseta\\\\Frontend\\\\src\\\\sections\\\\orders\\\\plt1\\\\plt1air-document-tabs.tsx\",\n        lineNumber: 94,\n        columnNumber: 9\n    }, this);\n}\n_s(PLT1AirDocumentsTabs, \"kaSynK7N1TfUw/LWa0d5ys3mM1E=\", false, function() {\n    return [\n        src_locales__WEBPACK_IMPORTED_MODULE_2__.useTranslate\n    ];\n});\n_c = PLT1AirDocumentsTabs;\nvar _c;\n$RefreshReg$(_c, \"PLT1AirDocumentsTabs\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/sections/orders/plt1/plt1air-document-tabs.tsx\n"));

/***/ })

});