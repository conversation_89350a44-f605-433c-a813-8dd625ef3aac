'use client';

import { useFormContext, useFieldArray } from 'react-hook-form';
import { useState, useRef, useEffect } from 'react';

import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { useTranslate } from 'src/locales';
import PLT1DocumentTabBase from '../plt1-document-tab-base';
import PLT1AirHouseAirWaybillForm, {
  PLT1AirHouseAirWaybillData as PLTAir1HouseAirWaybillData,
} from '../../forms/air/plt1air-house-airwaybill-form';
import { PLT1Order } from '../../types/plt1-details.types';

// ----------------------------------------------------------------------

interface PLT1AirHouseAirWaybillsTabProps {
  t1OrderId: string;
  order?: PLT1Order;
  readOnly?: boolean;
}

interface FormValues {
  houseAirWaybills: PLTAir1HouseAirWaybillData[];
}

export default function PLT1AirHouseAirWaybillsTab({
  t1OrderId,
  order,
  readOnly = false,
}: PLT1AirHouseAirWaybillsTabProps) {
  const { t } = useTranslate();
  const { control } = useFormContext<FormValues>();

  // Try to restore the expanded state from sessionStorage
  const [expandedHawb, setExpandedHawb] = useState<number | null>(() => {
    const saved = sessionStorage.getItem(`plt1-expanded-hawb-${t1OrderId}`);
    return saved ? parseInt(saved, 10) : null;
  });

  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const deleteButtonRef = useRef<HTMLButtonElement>(null);

  const fieldArray = useFieldArray({
    control,
    name: 'houseAirWaybills',
  });

  // Save expanded state to sessionStorage whenever it changes
  useEffect(() => {
    if (expandedHawb !== null) {
      sessionStorage.setItem(`plt1-expanded-hawb-${t1OrderId}`, expandedHawb.toString());
    } else {
      sessionStorage.removeItem(`plt1-expanded-hawb-${t1OrderId}`);
    }
  }, [expandedHawb, t1OrderId]);

  const handleAddHawb = (): void => {
    const newHawb: PLTAir1HouseAirWaybillData = {
      id: undefined,
      hawbNumber: '',
      mawbNumber: '',
      grossWeight: 0,
      grossWeightUnit: 'kg',
      numberOfPieces: 0,
      shipper: {
        name: '',
        taxNumber: '',
        eoriNumber: '',
        address: {
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          postCode: '',
          country: '',
          countryCode: '',
        },
      },
      consignee: {
        name: '',
        taxNumber: '',
        eoriNumber: '',
        address: {
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          postCode: '',
          country: '',
          countryCode: '',
        },
      },
      t1OrderId,
    };

    fieldArray.append(newHawb);
    setExpandedHawb(fieldArray.fields.length);
  };

  const handleToggleExpand = (index: number): void => {
    setExpandedHawb(expandedHawb === index ? null : index);
  };

  const handleOpenConfirm = (index: number): void => {
    setDeleteIndex(index);
    setOpenConfirm(true);
  };

  const handleCloseConfirm = (): void => {
    setOpenConfirm(false);
    if (deleteIndex !== null) {
      deleteButtonRef.current?.focus();
    }
  };

  const handleDeleteHawb = (): void => {
    if (deleteIndex !== null) {
      fieldArray.remove(deleteIndex);
      if (expandedHawb === deleteIndex) {
        setExpandedHawb(null);
      }
    }
    handleCloseConfirm();
  };

  // Render preview of the HAWB item when collapsed
  const renderPreview = (hawb: any, _index: number) => (
    <Stack
      direction="row"
      spacing={2}
      sx={{
        px: 3,
        pb: 2,
        display: 'flex',
        flexWrap: 'wrap',
        '& > *': { mr: 3, mb: 1 },
      }}
    >
      {hawb.numberOfPieces !== undefined &&
        hawb.numberOfPieces !== null &&
        hawb.numberOfPieces > 0 && (
          <Typography variant="body2" color="text.secondary">
            {t('plt1.details.documents.houseAirWaybill.preview.numberOfPieces')}:{' '}
            {hawb.numberOfPieces}
          </Typography>
        )}

      {hawb.grossWeight !== undefined && hawb.grossWeight !== null && hawb.grossWeight > 0 && (
        <Typography variant="body2" color="text.secondary">
          {t('plt1.details.documents.houseAirWaybill.preview.grossWeight')}: {hawb.grossWeight}{' '}
          {hawb.grossWeightUnit || 'kg'}
        </Typography>
      )}
    </Stack>
  );

  // Render the form when expanded
  const renderForm = (index: number) => (
    <PLT1AirHouseAirWaybillForm formPath="houseAirWaybills" index={index} readOnly={readOnly} />
  );

  // Render the title of each item
  const getItemTitle = (hawb: any) => (
    <Stack direction="row" alignItems="center" spacing={1}>
      <Typography variant="subtitle1">
        {hawb.hawbNumber || t('plt1.details.documents.houseAirWaybill.preview.newHawb')}
      </Typography>

      {hawb.mawbNumber && (
        <Typography variant="body2" color="text.secondary">
          (MAWB: {hawb.mawbNumber})
        </Typography>
      )}
    </Stack>
  );

  return (
    <PLT1DocumentTabBase
      readOnly={readOnly}
      title={t('plt1.details.documents.houseAirWaybill.heading')}
      emptyTitle={t('plt1.details.documents.houseAirWaybill.noData')}
      emptyDescription={t('plt1.details.documents.houseAirWaybill.addYourFirst')}
      expandedIndex={expandedHawb}
      deleteIndex={deleteIndex}
      openConfirm={openConfirm}
      fieldArray={fieldArray}
      onToggleExpand={handleToggleExpand}
      onOpenConfirm={handleOpenConfirm}
      onCloseConfirm={handleCloseConfirm}
      onDelete={handleDeleteHawb}
      onAdd={handleAddHawb}
      renderPreview={renderPreview}
      renderForm={renderForm}
      getItemTitle={getItemTitle}
      t1OrderId={t1OrderId}
      order={order}
      fieldArrayName="houseAirWaybills"
    />
  );
}
