'use client';

// src/sections/plt1-air-order-details/PLT1AirOrderDetailsView.tsx
import { useState, useCallback, useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { endpoints } from 'src/lib/axios';
import { useTranslate } from 'src/locales';
import {
  PLT1AirHouseAirWaybill,
  PLT1AirMasterAirWaybill,
  PLT1CommercialInvoice,
  PLT1CustomsOffice,
  PLT1MerchandisePositions,
  PLT1NotificationOfArrival,
  PLT1PackingList,
  PLT1TransitDocument,
  PLT1VehicleRegistration
} from '../types/plt1-details.types';
import { usePLT1OrderStatus } from '../hooks/use-plt1-status';
import { PLT1OrderDetailsBase } from './plt1-order-details-base';
import PLT1AirDocumentsTabs from '../plt1air-document-tabs';
import { usePLTAir1OrderDetails } from '../hooks/use-plt1air-details';
import { handlePLT1OrderSave, hasFormChanges } from '../utils/order-details-utils';

// ----------------------------------------------------------------------

interface PLT1AirOrderDetailsViewProps {
  orderId: string;
  readOnly?: boolean;
}

// Define specific form values type for Air orders
export interface AirFormValues {
  houseAirWaybills: PLT1AirHouseAirWaybill[];
  masterAirWaybills: PLT1AirMasterAirWaybill[];
  commercialInvoices: PLT1CommercialInvoice[];
  packingLists: PLT1PackingList[];
  notificationsOfArrivals: PLT1NotificationOfArrival[];
  transitDocuments: PLT1TransitDocument[];
  merchandisePositions?: PLT1MerchandisePositions;
  customsOffice?: PLT1CustomsOffice;
  vehicleRegistration?: PLT1VehicleRegistration;
}

export function PLT1AirOrderDetailsView({
  orderId,
  readOnly: propReadOnly = false,
}: PLT1AirOrderDetailsViewProps) {
  const { t } = useTranslate();
  const [isSaving, setIsSaving] = useState(false);
  const [formChanged, setFormChanged] = useState(false);
  const [initialFormValues, setInitialFormValues] = useState<AirFormValues | null>(null);

  // Create form methods with the specific AirFormValues type
  const methods = useForm<AirFormValues>({
    // Initialize default values in the derived component
    defaultValues: {
      houseAirWaybills: [],
      masterAirWaybills: [],
      commercialInvoices: [],
      packingLists: [],
      notificationsOfArrivals: [],
      transitDocuments: [],
      merchandisePositions: {
        id: null,
        positions: [],
        t1OrderId: orderId,
      },
      customsOffice: { customsOfficeCode: '' },
      vehicleRegistration: {
        vehicleRegistrationNumber: '',
        vehicleCountryCode: '',
        trailerRegistrationNumber: '',
        trailerCountryCode: '',
      },
    },
    mode: 'onChange',
  });

  const { formState, watch, reset } = methods;
  const { isValid } = formState;

  // Load order data
  const {
    order,
    error: orderError,
    isLoading: isOrderLoading,
    reload,
  } = usePLTAir1OrderDetails(orderId);

  // Update form values when order data is loaded
  useEffect(() => {
    if (order) {
      const formData: AirFormValues = {
        houseAirWaybills: order.houseAirWaybills || [],
        masterAirWaybills: order.masterAirWaybills || [],
        commercialInvoices: order.commercialInvoices || [],
        packingLists: order.packingLists || [],
        notificationsOfArrivals: order.notificationsOfArrivals || [],
        transitDocuments: order.transitDocuments || [],
        merchandisePositions: order.merchandisePositions || {
          id: null,
          positions: [],
          t1OrderId: orderId,
        },
        customsOffice: order.customsOffice || { customsOfficeCode: '' },
        vehicleRegistration: order.vehicleRegistration || {
          vehicleRegistrationNumber: '',
          vehicleCountryCode: '',
          trailerRegistrationNumber: '',
          trailerCountryCode: '',
        },
      };
      reset(formData);
      setInitialFormValues(formData);
      setFormChanged(false);
    }
  }, [order, reset]);

  // Helper function to check if values are actually different
  const hasRealChanges = useCallback(() => {
    if (!initialFormValues) return false;
    const currentValues = methods.getValues();
    return hasFormChanges(currentValues, initialFormValues);
  }, [initialFormValues, methods]);

  // Watch for form changes
  useEffect(() => {
    const subscription = watch(() => {
      setFormChanged(hasRealChanges());
    });

    return () => subscription.unsubscribe();
  }, [watch, hasRealChanges]);

  // Handle status change callback
  const handleStatusChange = useCallback(() => {
    reload();
  }, [reload, t]);

  // Get order status
  const {
    status: orderStatus,
    orderNumber,
    error: statusError,
  } = usePLT1OrderStatus(orderId, 1000, handleStatusChange);

  // Save order data
  const handleSaveOrder = async (formData: AirFormValues) => {
    await handlePLT1OrderSave(
      formData,
      {
        orderId,
        endpoint: endpoints.plt1.airUpdate,
        idField: 'PLT1AirId',
        t,
      },
      setIsSaving,
      setFormChanged,
      reload
    );
  };
  
  // Handle form cancel
  const handleCancel = () => {
    if (initialFormValues) {
      reset(initialFormValues);
      setFormChanged(false);
    }
  };

  return (
    <FormProvider {...methods}>
      <PLT1OrderDetailsBase<AirFormValues>
        orderId={orderId}
        readOnly={propReadOnly}
        isLoading={isOrderLoading}
        error={orderError}
        order={order}
        orderStatus={orderStatus}
        orderNumber={orderNumber}
        statusError={statusError}
        onSaveOrder={handleSaveOrder}
        formChanged={formChanged}
        isSaving={isSaving}
        onCancel={handleCancel}
        isValid={isValid}
        documentTabs={
          <PLT1AirDocumentsTabs
            t1OrderId={orderId}
            order={order}
            readOnly={propReadOnly || orderStatus === 'Scanning'}
            reload={reload}
          />
        }
      />
    </FormProvider>
  );
}