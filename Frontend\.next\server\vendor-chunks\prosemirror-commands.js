"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-commands";
exports.ids = ["vendor-chunks/prosemirror-commands"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-commands/dist/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/prosemirror-commands/dist/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   autoJoin: () => (/* binding */ autoJoin),\n/* harmony export */   baseKeymap: () => (/* binding */ baseKeymap),\n/* harmony export */   chainCommands: () => (/* binding */ chainCommands),\n/* harmony export */   createParagraphNear: () => (/* binding */ createParagraphNear),\n/* harmony export */   deleteSelection: () => (/* binding */ deleteSelection),\n/* harmony export */   exitCode: () => (/* binding */ exitCode),\n/* harmony export */   joinBackward: () => (/* binding */ joinBackward),\n/* harmony export */   joinDown: () => (/* binding */ joinDown),\n/* harmony export */   joinForward: () => (/* binding */ joinForward),\n/* harmony export */   joinTextblockBackward: () => (/* binding */ joinTextblockBackward),\n/* harmony export */   joinTextblockForward: () => (/* binding */ joinTextblockForward),\n/* harmony export */   joinUp: () => (/* binding */ joinUp),\n/* harmony export */   lift: () => (/* binding */ lift),\n/* harmony export */   liftEmptyBlock: () => (/* binding */ liftEmptyBlock),\n/* harmony export */   macBaseKeymap: () => (/* binding */ macBaseKeymap),\n/* harmony export */   newlineInCode: () => (/* binding */ newlineInCode),\n/* harmony export */   pcBaseKeymap: () => (/* binding */ pcBaseKeymap),\n/* harmony export */   selectAll: () => (/* binding */ selectAll),\n/* harmony export */   selectNodeBackward: () => (/* binding */ selectNodeBackward),\n/* harmony export */   selectNodeForward: () => (/* binding */ selectNodeForward),\n/* harmony export */   selectParentNode: () => (/* binding */ selectParentNode),\n/* harmony export */   selectTextblockEnd: () => (/* binding */ selectTextblockEnd),\n/* harmony export */   selectTextblockStart: () => (/* binding */ selectTextblockStart),\n/* harmony export */   setBlockType: () => (/* binding */ setBlockType),\n/* harmony export */   splitBlock: () => (/* binding */ splitBlock),\n/* harmony export */   splitBlockAs: () => (/* binding */ splitBlockAs),\n/* harmony export */   splitBlockKeepMarks: () => (/* binding */ splitBlockKeepMarks),\n/* harmony export */   toggleMark: () => (/* binding */ toggleMark),\n/* harmony export */   wrapIn: () => (/* binding */ wrapIn)\n/* harmony export */ });\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/prosemirror-transform/dist/index.js\");\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/prosemirror-state/dist/index.js\");\n\n\n\n\n/**\nDelete the selection, if there is one.\n*/\nconst deleteSelection = (state, dispatch) => {\n    if (state.selection.empty)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.deleteSelection().scrollIntoView());\n    return true;\n};\nfunction atBlockStart(state, view) {\n    let { $cursor } = state.selection;\n    if (!$cursor || (view ? !view.endOfTextblock(\"backward\", state)\n        : $cursor.parentOffset > 0))\n        return null;\n    return $cursor;\n}\n/**\nIf the selection is empty and at the start of a textblock, try to\nreduce the distance between that block and the one before it—if\nthere's a block directly before it that can be joined, join them.\nIf not, try to move the selected block closer to the next one in\nthe document structure by lifting it out of its parent or moving it\ninto a parent of the previous block. Will use the view for accurate\n(bidi-aware) start-of-textblock detection if given.\n*/\nconst joinBackward = (state, dispatch, view) => {\n    let $cursor = atBlockStart(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutBefore($cursor);\n    // If there is no node before this, try to lift\n    if (!$cut) {\n        let range = $cursor.blockRange(), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n        if (target == null)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.lift(range, target).scrollIntoView());\n        return true;\n    }\n    let before = $cut.nodeBefore;\n    // Apply the joining algorithm\n    if (deleteBarrier(state, $cut, dispatch, -1))\n        return true;\n    // If the node below has no content and the node above is\n    // selectable, delete the node below and select the one above.\n    if ($cursor.parent.content.size == 0 &&\n        (textblockAt(before, \"end\") || prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(before))) {\n        for (let depth = $cursor.depth;; depth--) {\n            let delStep = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.replaceStep)(state.doc, $cursor.before(depth), $cursor.after(depth), prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice.empty);\n            if (delStep && delStep.slice.size < delStep.to - delStep.from) {\n                if (dispatch) {\n                    let tr = state.tr.step(delStep);\n                    tr.setSelection(textblockAt(before, \"end\")\n                        ? prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.findFrom(tr.doc.resolve(tr.mapping.map($cut.pos, -1)), -1)\n                        : prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(tr.doc, $cut.pos - before.nodeSize));\n                    dispatch(tr.scrollIntoView());\n                }\n                return true;\n            }\n            if (depth == 1 || $cursor.node(depth - 1).childCount > 1)\n                break;\n        }\n    }\n    // If the node before is an atom, delete it\n    if (before.isAtom && $cut.depth == $cursor.depth - 1) {\n        if (dispatch)\n            dispatch(state.tr.delete($cut.pos - before.nodeSize, $cut.pos).scrollIntoView());\n        return true;\n    }\n    return false;\n};\n/**\nA more limited form of [`joinBackward`]($commands.joinBackward)\nthat only tries to join the current textblock to the one before\nit, if the cursor is at the start of a textblock.\n*/\nconst joinTextblockBackward = (state, dispatch, view) => {\n    let $cursor = atBlockStart(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutBefore($cursor);\n    return $cut ? joinTextblocksAround(state, $cut, dispatch) : false;\n};\n/**\nA more limited form of [`joinForward`]($commands.joinForward)\nthat only tries to join the current textblock to the one after\nit, if the cursor is at the end of a textblock.\n*/\nconst joinTextblockForward = (state, dispatch, view) => {\n    let $cursor = atBlockEnd(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutAfter($cursor);\n    return $cut ? joinTextblocksAround(state, $cut, dispatch) : false;\n};\nfunction joinTextblocksAround(state, $cut, dispatch) {\n    let before = $cut.nodeBefore, beforeText = before, beforePos = $cut.pos - 1;\n    for (; !beforeText.isTextblock; beforePos--) {\n        if (beforeText.type.spec.isolating)\n            return false;\n        let child = beforeText.lastChild;\n        if (!child)\n            return false;\n        beforeText = child;\n    }\n    let after = $cut.nodeAfter, afterText = after, afterPos = $cut.pos + 1;\n    for (; !afterText.isTextblock; afterPos++) {\n        if (afterText.type.spec.isolating)\n            return false;\n        let child = afterText.firstChild;\n        if (!child)\n            return false;\n        afterText = child;\n    }\n    let step = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.replaceStep)(state.doc, beforePos, afterPos, prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice.empty);\n    if (!step || step.from != beforePos ||\n        step instanceof prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.ReplaceStep && step.slice.size >= afterPos - beforePos)\n        return false;\n    if (dispatch) {\n        let tr = state.tr.step(step);\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection.create(tr.doc, beforePos));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n}\nfunction textblockAt(node, side, only = false) {\n    for (let scan = node; scan; scan = (side == \"start\" ? scan.firstChild : scan.lastChild)) {\n        if (scan.isTextblock)\n            return true;\n        if (only && scan.childCount != 1)\n            return false;\n    }\n    return false;\n}\n/**\nWhen the selection is empty and at the start of a textblock, select\nthe node before that textblock, if possible. This is intended to be\nbound to keys like backspace, after\n[`joinBackward`](https://prosemirror.net/docs/ref/#commands.joinBackward) or other deleting\ncommands, as a fall-back behavior when the schema doesn't allow\ndeletion at the selected point.\n*/\nconst selectNodeBackward = (state, dispatch, view) => {\n    let { $head, empty } = state.selection, $cut = $head;\n    if (!empty)\n        return false;\n    if ($head.parent.isTextblock) {\n        if (view ? !view.endOfTextblock(\"backward\", state) : $head.parentOffset > 0)\n            return false;\n        $cut = findCutBefore($head);\n    }\n    let node = $cut && $cut.nodeBefore;\n    if (!node || !prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(node))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(state.doc, $cut.pos - node.nodeSize)).scrollIntoView());\n    return true;\n};\nfunction findCutBefore($pos) {\n    if (!$pos.parent.type.spec.isolating)\n        for (let i = $pos.depth - 1; i >= 0; i--) {\n            if ($pos.index(i) > 0)\n                return $pos.doc.resolve($pos.before(i + 1));\n            if ($pos.node(i).type.spec.isolating)\n                break;\n        }\n    return null;\n}\nfunction atBlockEnd(state, view) {\n    let { $cursor } = state.selection;\n    if (!$cursor || (view ? !view.endOfTextblock(\"forward\", state)\n        : $cursor.parentOffset < $cursor.parent.content.size))\n        return null;\n    return $cursor;\n}\n/**\nIf the selection is empty and the cursor is at the end of a\ntextblock, try to reduce or remove the boundary between that block\nand the one after it, either by joining them or by moving the other\nblock closer to this one in the tree structure. Will use the view\nfor accurate start-of-textblock detection if given.\n*/\nconst joinForward = (state, dispatch, view) => {\n    let $cursor = atBlockEnd(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutAfter($cursor);\n    // If there is no node after this, there's nothing to do\n    if (!$cut)\n        return false;\n    let after = $cut.nodeAfter;\n    // Try the joining algorithm\n    if (deleteBarrier(state, $cut, dispatch, 1))\n        return true;\n    // If the node above has no content and the node below is\n    // selectable, delete the node above and select the one below.\n    if ($cursor.parent.content.size == 0 &&\n        (textblockAt(after, \"start\") || prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(after))) {\n        let delStep = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.replaceStep)(state.doc, $cursor.before(), $cursor.after(), prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice.empty);\n        if (delStep && delStep.slice.size < delStep.to - delStep.from) {\n            if (dispatch) {\n                let tr = state.tr.step(delStep);\n                tr.setSelection(textblockAt(after, \"start\") ? prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.findFrom(tr.doc.resolve(tr.mapping.map($cut.pos)), 1)\n                    : prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(tr.doc, tr.mapping.map($cut.pos)));\n                dispatch(tr.scrollIntoView());\n            }\n            return true;\n        }\n    }\n    // If the next node is an atom, delete it\n    if (after.isAtom && $cut.depth == $cursor.depth - 1) {\n        if (dispatch)\n            dispatch(state.tr.delete($cut.pos, $cut.pos + after.nodeSize).scrollIntoView());\n        return true;\n    }\n    return false;\n};\n/**\nWhen the selection is empty and at the end of a textblock, select\nthe node coming after that textblock, if possible. This is intended\nto be bound to keys like delete, after\n[`joinForward`](https://prosemirror.net/docs/ref/#commands.joinForward) and similar deleting\ncommands, to provide a fall-back behavior when the schema doesn't\nallow deletion at the selected point.\n*/\nconst selectNodeForward = (state, dispatch, view) => {\n    let { $head, empty } = state.selection, $cut = $head;\n    if (!empty)\n        return false;\n    if ($head.parent.isTextblock) {\n        if (view ? !view.endOfTextblock(\"forward\", state) : $head.parentOffset < $head.parent.content.size)\n            return false;\n        $cut = findCutAfter($head);\n    }\n    let node = $cut && $cut.nodeAfter;\n    if (!node || !prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(node))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(state.doc, $cut.pos)).scrollIntoView());\n    return true;\n};\nfunction findCutAfter($pos) {\n    if (!$pos.parent.type.spec.isolating)\n        for (let i = $pos.depth - 1; i >= 0; i--) {\n            let parent = $pos.node(i);\n            if ($pos.index(i) + 1 < parent.childCount)\n                return $pos.doc.resolve($pos.after(i + 1));\n            if (parent.type.spec.isolating)\n                break;\n        }\n    return null;\n}\n/**\nJoin the selected block or, if there is a text selection, the\nclosest ancestor block of the selection that can be joined, with\nthe sibling above it.\n*/\nconst joinUp = (state, dispatch) => {\n    let sel = state.selection, nodeSel = sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection, point;\n    if (nodeSel) {\n        if (sel.node.isTextblock || !(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(state.doc, sel.from))\n            return false;\n        point = sel.from;\n    }\n    else {\n        point = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.joinPoint)(state.doc, sel.from, -1);\n        if (point == null)\n            return false;\n    }\n    if (dispatch) {\n        let tr = state.tr.join(point);\n        if (nodeSel)\n            tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(tr.doc, point - state.doc.resolve(point).nodeBefore.nodeSize));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nJoin the selected block, or the closest ancestor of the selection\nthat can be joined, with the sibling after it.\n*/\nconst joinDown = (state, dispatch) => {\n    let sel = state.selection, point;\n    if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection) {\n        if (sel.node.isTextblock || !(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(state.doc, sel.to))\n            return false;\n        point = sel.to;\n    }\n    else {\n        point = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.joinPoint)(state.doc, sel.to, 1);\n        if (point == null)\n            return false;\n    }\n    if (dispatch)\n        dispatch(state.tr.join(point).scrollIntoView());\n    return true;\n};\n/**\nLift the selected block, or the closest ancestor block of the\nselection that can be lifted, out of its parent node.\n*/\nconst lift = (state, dispatch) => {\n    let { $from, $to } = state.selection;\n    let range = $from.blockRange($to), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n    if (target == null)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.lift(range, target).scrollIntoView());\n    return true;\n};\n/**\nIf the selection is in a node whose type has a truthy\n[`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) property in its spec, replace the\nselection with a newline character.\n*/\nconst newlineInCode = (state, dispatch) => {\n    let { $head, $anchor } = state.selection;\n    if (!$head.parent.type.spec.code || !$head.sameParent($anchor))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.insertText(\"\\n\").scrollIntoView());\n    return true;\n};\nfunction defaultBlockAt(match) {\n    for (let i = 0; i < match.edgeCount; i++) {\n        let { type } = match.edge(i);\n        if (type.isTextblock && !type.hasRequiredAttrs())\n            return type;\n    }\n    return null;\n}\n/**\nWhen the selection is in a node with a truthy\n[`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) property in its spec, create a\ndefault block after the code block, and move the cursor there.\n*/\nconst exitCode = (state, dispatch) => {\n    let { $head, $anchor } = state.selection;\n    if (!$head.parent.type.spec.code || !$head.sameParent($anchor))\n        return false;\n    let above = $head.node(-1), after = $head.indexAfter(-1), type = defaultBlockAt(above.contentMatchAt(after));\n    if (!type || !above.canReplaceWith(after, after, type))\n        return false;\n    if (dispatch) {\n        let pos = $head.after(), tr = state.tr.replaceWith(pos, pos, type.createAndFill());\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.near(tr.doc.resolve(pos), 1));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nIf a block node is selected, create an empty paragraph before (if\nit is its parent's first child) or after it.\n*/\nconst createParagraphNear = (state, dispatch) => {\n    let sel = state.selection, { $from, $to } = sel;\n    if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.AllSelection || $from.parent.inlineContent || $to.parent.inlineContent)\n        return false;\n    let type = defaultBlockAt($to.parent.contentMatchAt($to.indexAfter()));\n    if (!type || !type.isTextblock)\n        return false;\n    if (dispatch) {\n        let side = (!$from.parentOffset && $to.index() < $to.parent.childCount ? $from : $to).pos;\n        let tr = state.tr.insert(side, type.createAndFill());\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection.create(tr.doc, side + 1));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nIf the cursor is in an empty textblock that can be lifted, lift the\nblock.\n*/\nconst liftEmptyBlock = (state, dispatch) => {\n    let { $cursor } = state.selection;\n    if (!$cursor || $cursor.parent.content.size)\n        return false;\n    if ($cursor.depth > 1 && $cursor.after() != $cursor.end(-1)) {\n        let before = $cursor.before();\n        if ((0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(state.doc, before)) {\n            if (dispatch)\n                dispatch(state.tr.split(before).scrollIntoView());\n            return true;\n        }\n    }\n    let range = $cursor.blockRange(), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n    if (target == null)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.lift(range, target).scrollIntoView());\n    return true;\n};\n/**\nCreate a variant of [`splitBlock`](https://prosemirror.net/docs/ref/#commands.splitBlock) that uses\na custom function to determine the type of the newly split off block.\n*/\nfunction splitBlockAs(splitNode) {\n    return (state, dispatch) => {\n        let { $from, $to } = state.selection;\n        if (state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection && state.selection.node.isBlock) {\n            if (!$from.parentOffset || !(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(state.doc, $from.pos))\n                return false;\n            if (dispatch)\n                dispatch(state.tr.split($from.pos).scrollIntoView());\n            return true;\n        }\n        if (!$from.depth)\n            return false;\n        let types = [];\n        let splitDepth, deflt, atEnd = false, atStart = false;\n        for (let d = $from.depth;; d--) {\n            let node = $from.node(d);\n            if (node.isBlock) {\n                atEnd = $from.end(d) == $from.pos + ($from.depth - d);\n                atStart = $from.start(d) == $from.pos - ($from.depth - d);\n                deflt = defaultBlockAt($from.node(d - 1).contentMatchAt($from.indexAfter(d - 1)));\n                let splitType = splitNode && splitNode($to.parent, atEnd, $from);\n                types.unshift(splitType || (atEnd && deflt ? { type: deflt } : null));\n                splitDepth = d;\n                break;\n            }\n            else {\n                if (d == 1)\n                    return false;\n                types.unshift(null);\n            }\n        }\n        let tr = state.tr;\n        if (state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection || state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.AllSelection)\n            tr.deleteSelection();\n        let splitPos = tr.mapping.map($from.pos);\n        let can = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(tr.doc, splitPos, types.length, types);\n        if (!can) {\n            types[0] = deflt ? { type: deflt } : null;\n            can = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(tr.doc, splitPos, types.length, types);\n        }\n        tr.split(splitPos, types.length, types);\n        if (!atEnd && atStart && $from.node(splitDepth).type != deflt) {\n            let first = tr.mapping.map($from.before(splitDepth)), $first = tr.doc.resolve(first);\n            if (deflt && $from.node(splitDepth - 1).canReplaceWith($first.index(), $first.index() + 1, deflt))\n                tr.setNodeMarkup(tr.mapping.map($from.before(splitDepth)), deflt);\n        }\n        if (dispatch)\n            dispatch(tr.scrollIntoView());\n        return true;\n    };\n}\n/**\nSplit the parent block of the selection. If the selection is a text\nselection, also delete its content.\n*/\nconst splitBlock = splitBlockAs();\n/**\nActs like [`splitBlock`](https://prosemirror.net/docs/ref/#commands.splitBlock), but without\nresetting the set of active marks at the cursor.\n*/\nconst splitBlockKeepMarks = (state, dispatch) => {\n    return splitBlock(state, dispatch && (tr => {\n        let marks = state.storedMarks || (state.selection.$to.parentOffset && state.selection.$from.marks());\n        if (marks)\n            tr.ensureMarks(marks);\n        dispatch(tr);\n    }));\n};\n/**\nMove the selection to the node wrapping the current selection, if\nany. (Will not select the document node.)\n*/\nconst selectParentNode = (state, dispatch) => {\n    let { $from, to } = state.selection, pos;\n    let same = $from.sharedDepth(to);\n    if (same == 0)\n        return false;\n    pos = $from.before(same);\n    if (dispatch)\n        dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(state.doc, pos)));\n    return true;\n};\n/**\nSelect the whole document.\n*/\nconst selectAll = (state, dispatch) => {\n    if (dispatch)\n        dispatch(state.tr.setSelection(new prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.AllSelection(state.doc)));\n    return true;\n};\nfunction joinMaybeClear(state, $pos, dispatch) {\n    let before = $pos.nodeBefore, after = $pos.nodeAfter, index = $pos.index();\n    if (!before || !after || !before.type.compatibleContent(after.type))\n        return false;\n    if (!before.content.size && $pos.parent.canReplace(index - 1, index)) {\n        if (dispatch)\n            dispatch(state.tr.delete($pos.pos - before.nodeSize, $pos.pos).scrollIntoView());\n        return true;\n    }\n    if (!$pos.parent.canReplace(index, index + 1) || !(after.isTextblock || (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(state.doc, $pos.pos)))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.join($pos.pos).scrollIntoView());\n    return true;\n}\nfunction deleteBarrier(state, $cut, dispatch, dir) {\n    let before = $cut.nodeBefore, after = $cut.nodeAfter, conn, match;\n    let isolated = before.type.spec.isolating || after.type.spec.isolating;\n    if (!isolated && joinMaybeClear(state, $cut, dispatch))\n        return true;\n    let canDelAfter = !isolated && $cut.parent.canReplace($cut.index(), $cut.index() + 1);\n    if (canDelAfter &&\n        (conn = (match = before.contentMatchAt(before.childCount)).findWrapping(after.type)) &&\n        match.matchType(conn[0] || after.type).validEnd) {\n        if (dispatch) {\n            let end = $cut.pos + after.nodeSize, wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.empty;\n            for (let i = conn.length - 1; i >= 0; i--)\n                wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.from(conn[i].create(null, wrap));\n            wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.from(before.copy(wrap));\n            let tr = state.tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.ReplaceAroundStep($cut.pos - 1, end, $cut.pos, end, new prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice(wrap, 1, 0), conn.length, true));\n            let $joinAt = tr.doc.resolve(end + 2 * conn.length);\n            if ($joinAt.nodeAfter && $joinAt.nodeAfter.type == before.type &&\n                (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(tr.doc, $joinAt.pos))\n                tr.join($joinAt.pos);\n            dispatch(tr.scrollIntoView());\n        }\n        return true;\n    }\n    let selAfter = after.type.spec.isolating || (dir > 0 && isolated) ? null : prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.findFrom($cut, 1);\n    let range = selAfter && selAfter.$from.blockRange(selAfter.$to), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n    if (target != null && target >= $cut.depth) {\n        if (dispatch)\n            dispatch(state.tr.lift(range, target).scrollIntoView());\n        return true;\n    }\n    if (canDelAfter && textblockAt(after, \"start\", true) && textblockAt(before, \"end\")) {\n        let at = before, wrap = [];\n        for (;;) {\n            wrap.push(at);\n            if (at.isTextblock)\n                break;\n            at = at.lastChild;\n        }\n        let afterText = after, afterDepth = 1;\n        for (; !afterText.isTextblock; afterText = afterText.firstChild)\n            afterDepth++;\n        if (at.canReplace(at.childCount, at.childCount, afterText.content)) {\n            if (dispatch) {\n                let end = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.empty;\n                for (let i = wrap.length - 1; i >= 0; i--)\n                    end = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.from(wrap[i].copy(end));\n                let tr = state.tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.ReplaceAroundStep($cut.pos - wrap.length, $cut.pos + after.nodeSize, $cut.pos + afterDepth, $cut.pos + after.nodeSize - afterDepth, new prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice(end, wrap.length, 0), 0, true));\n                dispatch(tr.scrollIntoView());\n            }\n            return true;\n        }\n    }\n    return false;\n}\nfunction selectTextblockSide(side) {\n    return function (state, dispatch) {\n        let sel = state.selection, $pos = side < 0 ? sel.$from : sel.$to;\n        let depth = $pos.depth;\n        while ($pos.node(depth).isInline) {\n            if (!depth)\n                return false;\n            depth--;\n        }\n        if (!$pos.node(depth).isTextblock)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection.create(state.doc, side < 0 ? $pos.start(depth) : $pos.end(depth))));\n        return true;\n    };\n}\n/**\nMoves the cursor to the start of current text block.\n*/\nconst selectTextblockStart = selectTextblockSide(-1);\n/**\nMoves the cursor to the end of current text block.\n*/\nconst selectTextblockEnd = selectTextblockSide(1);\n// Parameterized commands\n/**\nWrap the selection in a node of the given type with the given\nattributes.\n*/\nfunction wrapIn(nodeType, attrs = null) {\n    return function (state, dispatch) {\n        let { $from, $to } = state.selection;\n        let range = $from.blockRange($to), wrapping = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.findWrapping)(range, nodeType, attrs);\n        if (!wrapping)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.wrap(range, wrapping).scrollIntoView());\n        return true;\n    };\n}\n/**\nReturns a command that tries to set the selected textblocks to the\ngiven node type with the given attributes.\n*/\nfunction setBlockType(nodeType, attrs = null) {\n    return function (state, dispatch) {\n        let applicable = false;\n        for (let i = 0; i < state.selection.ranges.length && !applicable; i++) {\n            let { $from: { pos: from }, $to: { pos: to } } = state.selection.ranges[i];\n            state.doc.nodesBetween(from, to, (node, pos) => {\n                if (applicable)\n                    return false;\n                if (!node.isTextblock || node.hasMarkup(nodeType, attrs))\n                    return;\n                if (node.type == nodeType) {\n                    applicable = true;\n                }\n                else {\n                    let $pos = state.doc.resolve(pos), index = $pos.index();\n                    applicable = $pos.parent.canReplaceWith(index, index + 1, nodeType);\n                }\n            });\n        }\n        if (!applicable)\n            return false;\n        if (dispatch) {\n            let tr = state.tr;\n            for (let i = 0; i < state.selection.ranges.length; i++) {\n                let { $from: { pos: from }, $to: { pos: to } } = state.selection.ranges[i];\n                tr.setBlockType(from, to, nodeType, attrs);\n            }\n            dispatch(tr.scrollIntoView());\n        }\n        return true;\n    };\n}\nfunction markApplies(doc, ranges, type, enterAtoms) {\n    for (let i = 0; i < ranges.length; i++) {\n        let { $from, $to } = ranges[i];\n        let can = $from.depth == 0 ? doc.inlineContent && doc.type.allowsMarkType(type) : false;\n        doc.nodesBetween($from.pos, $to.pos, (node, pos) => {\n            if (can || !enterAtoms && node.isAtom && node.isInline && pos >= $from.pos && pos + node.nodeSize <= $to.pos)\n                return false;\n            can = node.inlineContent && node.type.allowsMarkType(type);\n        });\n        if (can)\n            return true;\n    }\n    return false;\n}\nfunction removeInlineAtoms(ranges) {\n    let result = [];\n    for (let i = 0; i < ranges.length; i++) {\n        let { $from, $to } = ranges[i];\n        $from.doc.nodesBetween($from.pos, $to.pos, (node, pos) => {\n            if (node.isAtom && node.content.size && node.isInline && pos >= $from.pos && pos + node.nodeSize <= $to.pos) {\n                if (pos + 1 > $from.pos)\n                    result.push(new prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.SelectionRange($from, $from.doc.resolve(pos + 1)));\n                $from = $from.doc.resolve(pos + 1 + node.content.size);\n                return false;\n            }\n        });\n        if ($from.pos < $to.pos)\n            result.push(new prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.SelectionRange($from, $to));\n    }\n    return result;\n}\n/**\nCreate a command function that toggles the given mark with the\ngiven attributes. Will return `false` when the current selection\ndoesn't support that mark. This will remove the mark if any marks\nof that type exist in the selection, or add it otherwise. If the\nselection is empty, this applies to the [stored\nmarks](https://prosemirror.net/docs/ref/#state.EditorState.storedMarks) instead of a range of the\ndocument.\n*/\nfunction toggleMark(markType, attrs = null, options) {\n    let removeWhenPresent = (options && options.removeWhenPresent) !== false;\n    let enterAtoms = (options && options.enterInlineAtoms) !== false;\n    return function (state, dispatch) {\n        let { empty, $cursor, ranges } = state.selection;\n        if ((empty && !$cursor) || !markApplies(state.doc, ranges, markType, enterAtoms))\n            return false;\n        if (dispatch) {\n            if ($cursor) {\n                if (markType.isInSet(state.storedMarks || $cursor.marks()))\n                    dispatch(state.tr.removeStoredMark(markType));\n                else\n                    dispatch(state.tr.addStoredMark(markType.create(attrs)));\n            }\n            else {\n                let add, tr = state.tr;\n                if (!enterAtoms)\n                    ranges = removeInlineAtoms(ranges);\n                if (removeWhenPresent) {\n                    add = !ranges.some(r => state.doc.rangeHasMark(r.$from.pos, r.$to.pos, markType));\n                }\n                else {\n                    add = !ranges.every(r => {\n                        let missing = false;\n                        tr.doc.nodesBetween(r.$from.pos, r.$to.pos, (node, pos, parent) => {\n                            if (missing)\n                                return false;\n                            missing = !markType.isInSet(node.marks) && !!parent && parent.type.allowsMarkType(markType) &&\n                                !(node.isText && /^\\s*$/.test(node.textBetween(Math.max(0, r.$from.pos - pos), Math.min(node.nodeSize, r.$to.pos - pos))));\n                        });\n                        return !missing;\n                    });\n                }\n                for (let i = 0; i < ranges.length; i++) {\n                    let { $from, $to } = ranges[i];\n                    if (!add) {\n                        tr.removeMark($from.pos, $to.pos, markType);\n                    }\n                    else {\n                        let from = $from.pos, to = $to.pos, start = $from.nodeAfter, end = $to.nodeBefore;\n                        let spaceStart = start && start.isText ? /^\\s*/.exec(start.text)[0].length : 0;\n                        let spaceEnd = end && end.isText ? /\\s*$/.exec(end.text)[0].length : 0;\n                        if (from + spaceStart < to) {\n                            from += spaceStart;\n                            to -= spaceEnd;\n                        }\n                        tr.addMark(from, to, markType.create(attrs));\n                    }\n                }\n                dispatch(tr.scrollIntoView());\n            }\n        }\n        return true;\n    };\n}\nfunction wrapDispatchForJoin(dispatch, isJoinable) {\n    return (tr) => {\n        if (!tr.isGeneric)\n            return dispatch(tr);\n        let ranges = [];\n        for (let i = 0; i < tr.mapping.maps.length; i++) {\n            let map = tr.mapping.maps[i];\n            for (let j = 0; j < ranges.length; j++)\n                ranges[j] = map.map(ranges[j]);\n            map.forEach((_s, _e, from, to) => ranges.push(from, to));\n        }\n        // Figure out which joinable points exist inside those ranges,\n        // by checking all node boundaries in their parent nodes.\n        let joinable = [];\n        for (let i = 0; i < ranges.length; i += 2) {\n            let from = ranges[i], to = ranges[i + 1];\n            let $from = tr.doc.resolve(from), depth = $from.sharedDepth(to), parent = $from.node(depth);\n            for (let index = $from.indexAfter(depth), pos = $from.after(depth + 1); pos <= to; ++index) {\n                let after = parent.maybeChild(index);\n                if (!after)\n                    break;\n                if (index && joinable.indexOf(pos) == -1) {\n                    let before = parent.child(index - 1);\n                    if (before.type == after.type && isJoinable(before, after))\n                        joinable.push(pos);\n                }\n                pos += after.nodeSize;\n            }\n        }\n        // Join the joinable points\n        joinable.sort((a, b) => a - b);\n        for (let i = joinable.length - 1; i >= 0; i--) {\n            if ((0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(tr.doc, joinable[i]))\n                tr.join(joinable[i]);\n        }\n        dispatch(tr);\n    };\n}\n/**\nWrap a command so that, when it produces a transform that causes\ntwo joinable nodes to end up next to each other, those are joined.\nNodes are considered joinable when they are of the same type and\nwhen the `isJoinable` predicate returns true for them or, if an\narray of strings was passed, if their node type name is in that\narray.\n*/\nfunction autoJoin(command, isJoinable) {\n    let canJoin = Array.isArray(isJoinable) ? (node) => isJoinable.indexOf(node.type.name) > -1\n        : isJoinable;\n    return (state, dispatch, view) => command(state, dispatch && wrapDispatchForJoin(dispatch, canJoin), view);\n}\n/**\nCombine a number of command functions into a single function (which\ncalls them one by one until one returns true).\n*/\nfunction chainCommands(...commands) {\n    return function (state, dispatch, view) {\n        for (let i = 0; i < commands.length; i++)\n            if (commands[i](state, dispatch, view))\n                return true;\n        return false;\n    };\n}\nlet backspace = chainCommands(deleteSelection, joinBackward, selectNodeBackward);\nlet del = chainCommands(deleteSelection, joinForward, selectNodeForward);\n/**\nA basic keymap containing bindings not specific to any schema.\nBinds the following keys (when multiple commands are listed, they\nare chained with [`chainCommands`](https://prosemirror.net/docs/ref/#commands.chainCommands)):\n\n* **Enter** to `newlineInCode`, `createParagraphNear`, `liftEmptyBlock`, `splitBlock`\n* **Mod-Enter** to `exitCode`\n* **Backspace** and **Mod-Backspace** to `deleteSelection`, `joinBackward`, `selectNodeBackward`\n* **Delete** and **Mod-Delete** to `deleteSelection`, `joinForward`, `selectNodeForward`\n* **Mod-Delete** to `deleteSelection`, `joinForward`, `selectNodeForward`\n* **Mod-a** to `selectAll`\n*/\nconst pcBaseKeymap = {\n    \"Enter\": chainCommands(newlineInCode, createParagraphNear, liftEmptyBlock, splitBlock),\n    \"Mod-Enter\": exitCode,\n    \"Backspace\": backspace,\n    \"Mod-Backspace\": backspace,\n    \"Shift-Backspace\": backspace,\n    \"Delete\": del,\n    \"Mod-Delete\": del,\n    \"Mod-a\": selectAll\n};\n/**\nA copy of `pcBaseKeymap` that also binds **Ctrl-h** like Backspace,\n**Ctrl-d** like Delete, **Alt-Backspace** like Ctrl-Backspace, and\n**Ctrl-Alt-Backspace**, **Alt-Delete**, and **Alt-d** like\nCtrl-Delete.\n*/\nconst macBaseKeymap = {\n    \"Ctrl-h\": pcBaseKeymap[\"Backspace\"],\n    \"Alt-Backspace\": pcBaseKeymap[\"Mod-Backspace\"],\n    \"Ctrl-d\": pcBaseKeymap[\"Delete\"],\n    \"Ctrl-Alt-Backspace\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Alt-Delete\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Alt-d\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Ctrl-a\": selectTextblockStart,\n    \"Ctrl-e\": selectTextblockEnd\n};\nfor (let key in pcBaseKeymap)\n    macBaseKeymap[key] = pcBaseKeymap[key];\nconst mac = typeof navigator != \"undefined\" ? /Mac|iP(hone|[oa]d)/.test(navigator.platform)\n    // @ts-ignore\n    : typeof os != \"undefined\" && os.platform ? os.platform() == \"darwin\" : false;\n/**\nDepending on the detected platform, this will hold\n[`pcBasekeymap`](https://prosemirror.net/docs/ref/#commands.pcBaseKeymap) or\n[`macBaseKeymap`](https://prosemirror.net/docs/ref/#commands.macBaseKeymap).\n*/\nconst baseKeymap = mac ? macBaseKeymap : pcBaseKeymap;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-commands/dist/index.js\n");

/***/ })

};
;