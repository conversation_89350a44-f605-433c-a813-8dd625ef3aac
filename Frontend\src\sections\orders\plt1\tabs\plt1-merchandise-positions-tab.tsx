'use client';

import { useState } from 'react';
import { useFormContext } from 'react-hook-form';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import CardHeader from '@mui/material/CardHeader';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';

import { useTranslate } from 'src/locales';
import { Iconify } from 'src/components/iconify';
import { axiosInstance, endpoints } from 'src/lib/axios';
import { PLT1Order } from '../types/plt1-details.types';
import { PLT1_ORDER_STATUS } from '../plt1-status';
import PLT1MerchandisePositionsForm from '../forms/plt1-merchandise-positions-form';

// ----------------------------------------------------------------------

interface PLT1MerchandisePositionsTabProps {
  t1OrderId: string;
  order?: PLT1Order;
  readOnly?: boolean;
}

export default function PLT1MerchandisePositionsTab({
  t1OrderId,
  order,
  readOnly = false,
}: PLT1MerchandisePositionsTabProps) {
  const { t } = useTranslate();
  const { watch } = useFormContext();
  const [isRegenerating, setIsRegenerating] = useState(false);

  // Watch the merchandise positions data
  const merchandisePositions = watch('merchandisePositions');
  const commercialInvoices = watch('commercialInvoices');
  const packingLists = watch('packingLists');

  // Check if merchandise positions exist
  const hasMerchandisePositions = merchandisePositions && merchandisePositions.positions && merchandisePositions.positions.length > 0;

  // Check if regenerate button should be enabled
  const canRegenerate =
    order?.status === PLT1_ORDER_STATUS.Scanned &&
    commercialInvoices && commercialInvoices.length > 0 &&
    packingLists && packingLists.length > 0;

  // Handle regenerate merchandise positions
  const handleRegeneratePositions = async () => {
    if (!t1OrderId || isRegenerating) return;

    setIsRegenerating(true);
    try {
      await axiosInstance.post(endpoints.plt1.generateMerchandisePositions, {
        orderId: t1OrderId
      });

      // Show success message (you might want to add a toast notification here)
      console.log('Merchandise positions regeneration started successfully');
    } catch (error) {
      console.error('Error regenerating merchandise positions:', error);
      // Handle error (you might want to add error notification here)
    } finally {
      setIsRegenerating(false);
    }
  };

  const renderEmptyState = () => (
    <Box
      sx={{
        textAlign: 'center',
        py: 8,
        px: 3,
      }}
    >
      <Typography variant="h6" sx={{ mb: 1 }}>
        {t('plt1.details.documents.merchandisePositions.noData')}
      </Typography>
      <Typography variant="body2" color="text.secondary">
        {t('plt1.details.documents.merchandisePositions.addYourFirst')}
      </Typography>
    </Box>
  );

  const renderContent = () => {
    if (!hasMerchandisePositions && readOnly) {
      return renderEmptyState();
    }

    return (
      <PLT1MerchandisePositionsForm
        t1OrderId={t1OrderId}
        readOnly={readOnly}
      />
    );
  };

  return (
    <Card>
      <CardHeader
        title={t('plt1.details.documents.merchandisePositions.heading')}
        action={
          canRegenerate && (
            <LoadingButton
              variant="outlined"
              size="small"
              loading={isRegenerating}
              startIcon={<Iconify icon="eva:refresh-fill" />}
              onClick={handleRegeneratePositions}
              disabled={readOnly}
            >
              {t('plt1.details.documents.merchandisePositions.regeneratePositions')}
            </LoadingButton>
          )
        }
        sx={{ mb: 2 }}
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        {renderContent()}
      </Stack>
    </Card>
  );
}
