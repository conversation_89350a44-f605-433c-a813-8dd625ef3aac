'use client';

import { useFormContext } from 'react-hook-form';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Divider from '@mui/material/Divider';
import CardHeader from '@mui/material/CardHeader';
import Typography from '@mui/material/Typography';

import { useTranslate } from 'src/locales';
import { PLT1Order } from '../types/plt1-details.types';
import PLT1MerchandisePositionsForm from '../forms/plt1-merchandise-positions-form';

// ----------------------------------------------------------------------

interface PLT1MerchandisePositionsTabProps {
  t1OrderId: string;
  order?: PLT1Order;
  readOnly?: boolean;
}

export default function PLT1MerchandisePositionsTab({
  t1OrderId,
  order,
  readOnly = false,
}: PLT1MerchandisePositionsTabProps) {
  const { t } = useTranslate();
  const { watch } = useFormContext();

  // Watch the merchandise positions data
  const merchandisePositions = watch('merchandisePositions');

  // Check if merchandise positions exist
  const hasMerchandisePositions = merchandisePositions && merchandisePositions.positions && merchandisePositions.positions.length > 0;

  const renderEmptyState = () => (
    <Box
      sx={{
        textAlign: 'center',
        py: 8,
        px: 3,
      }}
    >
      <Typography variant="h6" sx={{ mb: 1 }}>
        {t('plt1.details.documents.merchandisePositions.noData')}
      </Typography>
      <Typography variant="body2" color="text.secondary">
        {t('plt1.details.documents.merchandisePositions.addYourFirst')}
      </Typography>
    </Box>
  );

  const renderContent = () => {
    if (!hasMerchandisePositions && readOnly) {
      return renderEmptyState();
    }

    return (
      <PLT1MerchandisePositionsForm
        t1OrderId={t1OrderId}
        readOnly={readOnly}
      />
    );
  };

  return (
    <Card>
      <CardHeader
        title={t('plt1.details.documents.merchandisePositions.heading')}
        sx={{ mb: 2 }}
      />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack spacing={3} sx={{ p: 3 }}>
        {renderContent()}
      </Stack>
    </Card>
  );
}
